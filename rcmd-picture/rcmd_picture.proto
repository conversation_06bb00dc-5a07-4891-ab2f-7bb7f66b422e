syntax = "proto3";

package rcmd.rcmd_picture;

option go_package = "golang.52tt.com/protocol/services/rcmd/rcmd_picture";

service RCMDPicture {
  rpc GetPictures(GetPicturesReq) returns (GetPicturesResp);
  rpc CreatePictures(CreatePicturesReq) returns (CreatePicturesResp);
}

message CreatePicturesReq {
    repeated CreatePicture pictures = 1;
}

message CreatePicturesResp {
    uint64 success = 1; // 成功总数
    uint64 duplicate = 2; // 重复总数
    uint64 create_failed_count = 3; // 创建失败总数
    uint64 invalid_count = 4; // 参数无效总数
    repeated string invalid_info = 5; // 错误信息
}

message CreatePicture {
    string id = 1;
    string url = 2;
    string title = 3; // 称号
    string gender = 4; // 性别 女性:female 男性: male
    string style = 5; // 风格 anime, watercolor, flatAnime
}

message GetPicturesReq {
    uint32 uid = 1;
    // 图片选择条件，每个PictureSelector, 代表一张图片，如果要3张图片，就需要传3个PictureSelector
    repeated PictureSelector selectors = 2;
    uint32 app_id = 3; // 0 tt， 2 欢游，6 迷境
}

message PictureSelector {
    string title = 2; // 称号
    enum Gender {
        Gender_Whatever = 0; // 不区分男性女性
        Gender_Female = 1; // 女性
        Gender_Male = 2; // 男性
    }
    Gender gender = 3; // 性别
    uint32 style = 4 ; // 图片风格，不传则随机选择风格 1: 动漫风格 2: 水墨水彩风格 3: 平面动画风格
}

message GetPicturesResp {
    repeated Picture pictures = 1;
}

message Picture {
    string id = 1;
    string url = 2;
    uint32 style = 3;
}

