package dfa_word_filter
import (
    "testing"

    "github.com/stretchr/testify/assert"
)

// 测试用的关键词列表
var testKeyWordList = []string{"六合彩","磕泡泡","yyds", "外挂", "银行卡", "转账", "我有A" ,"有B"}
var simpleKeyLoader = NewSimpleDirectKeyWordLoader(testKeyWordList)

func TestSimpleKeyWordLoader_Match(t *testing.T) {
    assert := assert.New(t)


    filterTree := simpleKeyLoader.GetFilterTree() // 获取匹配树

    testSourceMsg := "六合彩是违法行为" // 六合彩 是 敏感词
    err, isMatch, matchedKey := filterTree.FilterKeyWord(testSourceMsg)

    assert.Nil(err) // err应该为nil
    assert.Equal(isMatch, true)// match应该匹配到

    t.Logf("source %s matchedKey %s \n",testSourceMsg, matchedKey)

}
func TestSimpleKeyWordLoader_Match2(t *testing.T) {
    assert := assert.New(t)


    filterTree := simpleKeyLoader.GetFilterTree() // 获取匹配树

    testSourceMsg := "要银行转账吗？" // 包含 银行 （只是一部分敏感词），和 "转账" 敏感词
    //filterTree.DumpTree()
    err, isMatch, matchedKey := filterTree.FilterKeyWord(testSourceMsg)

    assert.Nil(err) // err应该为nil
    assert.Equal(isMatch, true)// match应该匹配到

    t.Logf("source %s matchedKey %s \n",testSourceMsg, matchedKey)


    testSourceMsg = "我有B" //
    err, isMatch, matchedKey = filterTree.FilterKeyWord(testSourceMsg)

    assert.Nil(err) // err应该为nil
    assert.Equal(isMatch, true)// match应该匹配到

    t.Logf("source %s matchedKey %s \n",testSourceMsg, matchedKey)

}
func TestSimpleKeyWordLoader_NotMatch(t *testing.T) {
    assert := assert.New(t)


    filterTree := simpleKeyLoader.GetFilterTree() // 获取匹配树

    testSourceMsg2 := "好好学习天天向上" // 这里没有铭感词
    err, isMatch2, _ := filterTree.FilterKeyWord(testSourceMsg2)

    assert.Nil(err)
    assert.Equal(isMatch2, false) // 不应该匹配到

    t.Logf("source %s isMatch %v \n",testSourceMsg2, isMatch2)

}