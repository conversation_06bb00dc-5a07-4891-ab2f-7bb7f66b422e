package oa

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/bitly/go-simplejson"
	"golang.52tt.com/pkg/log"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

type Client struct {
	Host         string
	ClientID     string
	ClientSecret string
	UserAccount  string
	httpClient   *http.Client
}

func NewOAClient(host, id, secret, account string) *Client {
	// proxyUrl, _ := url.Parse("http://127.0.0.1:8888")
	c := &Client{
		Host:         host,
		ClientID:     id,
		ClientSecret: secret,
		UserAccount:  account,
		httpClient:   &http.Client{
			// Transport: &http.Transport{Proxy: http.ProxyURL(proxyUrl)},
		},
	}
	return c
}

func (c *Client) GetHost() string {
	if c == nil {
		return ""
	}
	return c.Host
}

func (c *Client) requestGet(ctx context.Context, url string) (j *simplejson.Json, err error) {
	req, _ := http.NewRequest("GET", url, nil)
	req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json")
	NewSigner(c.ClientID, c.ClientSecret).AppendSignature(req)

	r, err := (c.httpClient).Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "requestGet http err: %v", err)
		return
	}
	defer func() {
		_ = r.Body.Close()
	}()

	if r.StatusCode != http.StatusOK {
		err = fmt.Errorf("requestGet status %s", r.Status)
		return
	}
	respBody, err := io.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "requestGet %s %s read body failed: %+v", url, respBody, err)
		return
	}
	log.InfoWithCtx(ctx, "requestGet url: %s, status: %s, resp: %s", url, r.Status, string(respBody))

	j, err = simplejson.NewJson(respBody)
	if err != nil {
		return nil, err
	}
	ret, _ := j.Get("success").Bool()
	msg, _ := j.Get("message").String()
	if ret != true {
		log.ErrorWithCtx(ctx, "requestGet oa resp ret err, msg:%v", msg)
		return nil, fmt.Errorf(msg)
	}
	j = j.Get("data")
	return
}

func (c *Client) requestPost(ctx context.Context, url string, body []byte) (j *simplejson.Json, err error) {
	req, _ := http.NewRequest("POST", url, bytes.NewReader(body))
	req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json")
	NewSigner(c.ClientID, c.ClientSecret).AppendSignature(req)

	startTime := time.Now()

	r, err := (c.httpClient).Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "requestPost http err: %v", err)
		return
	}
	defer func() {
		_ = r.Body.Close()
	}()

	//if r.StatusCode != http.StatusOK {
	//	err = fmt.Errorf("requestPost status %s", r.Status)
	//	return
	//}
	respBody, err := io.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "requestPost read body failed: %+v", err)
		return
	}

	respTime := (time.Now().UnixNano() - startTime.UnixNano()) / 1e6
	log.InfoWithCtx(ctx, "requestPost url: %s, status: %s, req: %s, resp: %s, time: %d", url, r.Status, string(body), string(respBody), respTime)

	j, err = simplejson.NewJson(respBody)
	if err != nil {
		return nil, err
	}
	ret, _ := j.Get("success").Bool()
	msg, _ := j.Get("message").String()
	if ret != true {
		log.ErrorWithCtx(ctx, "requestPost oa resp ret err, msg:%v", msg)
		return nil, fmt.Errorf(msg)
	}
	j = j.Get("data")
	return
}

// Upload 上传文件
func (c *Client) Upload(ctx context.Context, f io.Reader, filename string) (*FileInfo, error) {
	url := fmt.Sprintf("%s/ebc-api/api-system/restApi/system/app/files/upload?", c.GetHost())
	url += fmt.Sprintf("&clientId=%s", c.ClientID)

	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	part1, err := writer.CreateFormFile("file", filename)
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFile CreateFormFile err")
		return nil, err
	}
	_, _ = io.Copy(part1, f)
	err = writer.Close()
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFile writer.Close err")
		return nil, err
	}

	startTime := time.Now()

	req, _ := http.NewRequest("POST", url, payload)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	NewSigner(c.ClientID, c.ClientSecret).AppendSignature(req)

	r, err := (c.httpClient).Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "http err: %v", err)
		return nil, err
	}

	respTime := (time.Now().UnixNano() - startTime.UnixNano()) / 1e6
	log.InfoWithCtx(ctx, "UploadFile url: %s, status: %s, time: %d", url, r.Status, respTime)

	if r.StatusCode != http.StatusOK {
		err = fmt.Errorf("POST %s status %s", url, r.Status)
		return nil, err
	}
	respBody, err := io.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s read body failed: %+v", url, respBody, err)
		return nil, err
	}
	log.InfoWithCtx(ctx, "UploadFile resp: %s", string(respBody))

	j, err := simplejson.NewJson(respBody)
	if err != nil {
		return nil, err
	}
	ret, _ := j.Get("success").Bool()
	msg, _ := j.Get("message").String()
	if ret != true {
		log.ErrorWithCtx(ctx, "UploadFile oa resp err, msg:%v", msg)
		return nil, fmt.Errorf(msg)
	}
	first := j.Get("data").GetIndex(0)
	fileId, err := first.Get("fileId").String()
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFile oa resp fileID err")
		return nil, err
	}
	size, _ := first.Get("fileSize").Int()
	showSize, _ := first.Get("showSize").String()
	info := &FileInfo{
		FileId:    fileId,
		FileName:  filename,
		Size:      size,
		ShowSize:  showSize,
		Timestamp: int(time.Now().Unix()),
		FileClass: "iconBox pdfBox",
	}
	return info, nil
}

// UploadInvoiceVerify 上传发票OCR校验
func (c *Client) UploadInvoiceVerify(ctx context.Context, f io.Reader, filename string) (
	fileInfo *FileInfo, invoice *Invoice, err error) {
	url := fmt.Sprintf("%s/ebc-api/api-system/restApi/invoices/file-inspection?", c.GetHost())
	url += fmt.Sprintf("&clientId=%s", c.ClientID)

	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)

	part1, err := writer.CreateFormFile("file", filename)
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFile CreateFormFile err")
		return
	}
	_, _ = io.Copy(part1, f)
	err = writer.Close()
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFile writer.Close err")
		return
	}

	startTime := time.Now()

	req, _ := http.NewRequest("POST", url, payload)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	NewSigner(c.ClientID, c.ClientSecret).AppendSignature(req)

	r, err := (c.httpClient).Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "http err: %v", err)
		return
	}

	respTime := (time.Now().UnixNano() - startTime.UnixNano()) / 1e6
	log.InfoWithCtx(ctx, "UploadInvoiceVerify url: %s, status: %s, time: %d", url, r.Status, respTime)

	if r.StatusCode != http.StatusOK {
		err = fmt.Errorf("POST %s status %s", url, r.Status)
		return
	}
	respBody, err := io.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s read body failed: %+v", url, respBody, err)
		return
	}
	log.InfoWithCtx(ctx, "UploadInvoiceVerify resp: %s", string(respBody))

	resp := new(UploadInvoiceResp)
	if err = json.Unmarshal(respBody, resp); err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s json decode failed: %+v", url, respBody, err)
		return
	}

	// 系统错误
	if resp.Code == CodeServerErr {
		log.ErrorWithCtx(ctx, "oa resp err, code:%d, msg:%s", resp.Code, resp.Message)
		err = NewOAErr(resp.Code, resp.Message)
		return
	}

	// 业务错误，发票校验失败，也通过错误返回
	if resp.Code != CodeSuccess {
		log.ErrorWithCtx(ctx, "oa invoice verify err, code:%d, msg:%s", resp.Code, resp.Message)
		err = NewOAErr(resp.Code, resp.Message)
		return
	}

	// if resp.Data.Invoice.InvalidFlag != ReceiptStatusNormal {
	// 	log.ErrorWithCtx(ctx, "oa invoice verify err flag:%s", resp.Data.Invoice.InvalidFlag)
	// 	err = NewOAErr(ReceiptStatusErr, "不符合平台要求")
	// 	return
	// }

	file := resp.Data.FileInfo
	var fileClass string
	switch strings.Trim(filepath.Ext(file.FileName), ".") {
	case "pdf", "jpg", "jpeg":
		fileClass = "iconBox imageBox"
	case "png":
		fileClass = "iconBox pdfBox"
	default:
		fileClass = "iconBox"
	}
	info := &FileInfo{
		FileId:    file.FileId,
		FileName:  file.FileName,
		Size:      file.Size,
		ShowSize:  file.ShowSize,
		Timestamp: int(time.Now().Unix()),
		FileClass: fileClass,
	}
	return info, &resp.Data.Invoice, nil
}

// UploadFileByPath 上传本地文件
func (c *Client) UploadFileByPath(ctx context.Context, path string) (*FileInfo, error) {
	file, err := os.Open(path)
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFileByPath open file err")
		return nil, err
	}
	defer func() {
		_ = file.Close()
	}()

	info, err := c.Upload(ctx, file, filepath.Base(file.Name()))
	if err != nil {
		log.ErrorWithCtx(ctx, "UploadFileByPath upload err: %v", err)
		return nil, err
	}

	return info, nil
}

// GetFileUrl 获取文件URL
func (c *Client) GetFileUrl(ctx context.Context, fileId string) (string, error) {
	url := fmt.Sprintf("%s/ebc-api/api-system/restApi/system/app/files/%s", c.GetHost(), fileId)

	j, err := c.requestGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFileUrl requestGet err:%v", err)
		return "", err
	}

	fileUrl, _ := j.String()
	return fileUrl, nil
}

// StartProcess 发起审批流程（初始化的默认用户发起）
func (c *Client) StartProcess(ctx context.Context, body []byte) (string, error) {
	url := fmt.Sprintf("%s/ebc-api/api-bpm/restApi/bpm/startProcess?", c.GetHost())
	url += fmt.Sprintf("&userAccount=%s", c.UserAccount)

	j, err := c.requestPost(ctx, url, body)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartProcess requestPost err:%v", err)
		return "", err
	}

	instId, _ := j.Get("instId").String()
	if instId == "" {
		log.ErrorWithCtx(ctx, "StartProcess instId empty")
		return "", fmt.Errorf("instId empty")
	}
	return instId, nil
}

// StartProcessByAccount 指定用户发起审批流程
func (c *Client) StartProcessByAccount(ctx context.Context, body []byte, userAccount string) (string, error) {
	url := fmt.Sprintf("%s/ebc-api/api-bpm/restApi/bpm/startProcess?", c.GetHost())
	url += fmt.Sprintf("&userAccount=%s", userAccount)

	j, err := c.requestPost(ctx, url, body)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartProcess requestPost err:%v", err)
		return "", err
	}

	instId, _ := j.Get("instId").String()
	if instId == "" {
		log.ErrorWithCtx(ctx, "StartProcess instId empty")
		return "", fmt.Errorf("instId empty")
	}
	return instId, nil
}

// ProcessNodeInfo 查询审批进度
func (c *Client) ProcessNodeInfo(ctx context.Context, instId string) (*NodeInfo, error) {
	url := fmt.Sprintf("%s/ebc-api/api-bpm/restApi/bpm/processNodeInfo?", c.GetHost())
	url += fmt.Sprintf("&instId=%s", instId)

	j, err := c.requestGet(ctx, url)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcessNodeInfo requestGet err:%v", err)
		return nil, err
	}

	first := j.GetIndex(0)
	info := new(NodeInfo)
	info.CurrentNodeKey, _ = first.Get("currentNodeKey").String()
	info.CurrentAuditor, _ = first.Get("currentAuditor").String()
	info.TaskId, _ = first.Get("taskId").String()
	info.ApprovalOpinion, _ = first.Get("approvalOpinion").String()
	return info, nil
}

// CompleteTask 重新发起流程
func (c *Client) CompleteTask(ctx context.Context, body []byte) error {
	url := fmt.Sprintf("%s/ebc-api/api-bpm/restApi/bpm/completeTask?", c.GetHost())
	url += fmt.Sprintf("&userAccount=%s", c.UserAccount)
	url += fmt.Sprintf("&isReStartProcess=true")

	_, err := c.requestPost(ctx, url, body)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartProcess requestPost err:%v", err)
		return err
	}
	return nil
}

// GetAccountInfo 获取供应商信息
func (c *Client) GetAccountInfo(ctx context.Context, companyName string, accountId string) ([]AccountInfo, error) {
	var list []AccountInfo
	var err error
	url := fmt.Sprintf("%s/ebc-api/api-form/restApi/form/list/supplier", c.GetHost())
	req := &AccountReq{
		PageNo:   1,
		PageSize: 20,
		Params: AccountReqParam{
			CompanyName: companyName,
			Account:     "",
			AccountId:   accountId,
		},
	}
	body, err := json.Marshal(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAccountInfo json.Marshal err:%v, companyName:%s, accountId:%s", err, companyName, accountId)
		return list, err
	}
	r, err := c.requestPost(ctx, url, body)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAccountInfo requestPost err:%v, companyName:%s, accountId:%s", err, companyName, accountId)
		return list, err
	}

	array, err := r.Get("data").Array()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAccountInfo data array err:%v, companyName:%s, accountId:%s", err, companyName, accountId)
		return list, err
	}

	for index := range array {
		var account AccountInfo
		item := r.Get("data").GetIndex(index)
		account.Id, _ = item.Get("SUB_ID_").String()
		account.Bank, _ = item.Get("F_KHX_display").String()
		account.SubBank, _ = item.Get("F_KHZX").String()
		account.Account, _ = item.Get("F_ZH").String()
		account.CompanyName, _ = item.Get("F_GYSMC").String()
		account.Province, _ = item.Get("F_KHSS_PROVINCE").String()
		account.City, _ = item.Get("F_KHSS_CITY").String()
		list = append(list, account)
	}

	return list, nil
}

// GenSeqNo 生成流水号
func (c *Client) GenSeqNo(ctx context.Context) (string, error) {
	url := fmt.Sprintf("%s/ebc-api/api-system/restApi/system/sysSeqId/genSeqNo?", c.GetHost())
	url += fmt.Sprintf("&alias=%s", "contract_apply")

	req, _ := http.NewRequest("GET", url, nil)
	req.WithContext(ctx)
	req.Header.Set("Content-Type", "application/json")
	NewSigner(c.ClientID, c.ClientSecret).AppendSignature(req)

	r, err := (c.httpClient).Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenSeqNo http err: %v", err)
		return "", err
	}
	defer func() {
		_ = r.Body.Close()
	}()

	respBody, err := io.ReadAll(r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenSeqNo %s %s read body failed: %+v", url, respBody, err)
		return "", err
	}

	log.DebugWithCtx(ctx, "GenSeqNo url: %s, status: %, resp: %s", url, r.Status, string(respBody))

	if r.StatusCode != http.StatusOK {
		err = fmt.Errorf("GenSeqNo status %s", r.Status)
		return "", err
	}

	log.InfoWithCtx(ctx, "GenSeqNo url: %s, status: %s, resp: %s", url, r.Status, string(respBody))

	return string(respBody), nil
}

// 从url下载文件
func (c *Client) downloadFileByUrl(ctx context.Context, url, fileName string) (string, error) {
	req, _ := http.NewRequest("GET", url, nil)
	req.WithContext(ctx)
	NewSigner(c.ClientID, c.ClientSecret).AppendSignature(req)

	r, err := (c.httpClient).Do(req)
	if err != nil {
		log.ErrorWithCtx(ctx, "downloadFileByUrl http err: %v", err)
		return "", err
	}
	defer func() {
		_ = r.Body.Close()
	}()

	log.DebugWithCtx(ctx, "downloadFileByUrl url: %s, status: %s, size: %d", url, r.Status, r.ContentLength)

	if r.StatusCode != http.StatusOK {
		err = fmt.Errorf("downloadFileByUrl status %s", r.Status)
		return "", err
	}

	// save tmp file
	path := filepath.Join(os.TempDir(), fileName)
	file, err := os.Create(path)
	if err != nil {
		log.ErrorWithCtx(ctx, "downloadFileByUrl create file err: %v", err)
		return "", err
	}
	// copy file
	_, err = io.Copy(file, r.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "downloadFileByUrl copy file err: %v", err)
		return "", err
	}
	_ = file.Close()
	_ = r.Body.Close()

	log.InfoWithCtx(ctx, "downloadFileByUrl url: %s, status: %s, path: %s, size: %d", url, r.Status, path, r.ContentLength)
	return path, nil
}

func (c *Client) StartCorpApplyProcess(ctx context.Context, req *ContractApplyReq) (string, error) {
	if req == nil || req.CompanyName == "" {
		log.ErrorWithCtx(ctx, "StartCorpApplyProcess req or CompanyName is nil")
		return "", fmt.Errorf("req or CompanyName is nil")
	}

	// 对公申请用lace账号发起
	userAccount := CropApplyStartAccount

	// 固定的合同模板文档
	fileInfo := &FileInfo{
		FileName:  "TT语音公会合作服务协议********【清洁版】-e签宝模板.docx",
		Timestamp: int(time.Now().Unix()),
		Size:      57258,
		FileId:    "1890283239764766722", // prod
		ShowSize:  "58KB",
		FileClass: "iconBox wordBox",
	}

	// 测试环境
	if os.Getenv("MY_CLUSTER") == "testing" {
		fileInfo.FileId = "1889954738712088578" // test
	}

	// 生成流程单号
	seqNo, err := c.GenSeqNo(ctx)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartCorpApplyProcess GenSeqNo err:%v", err)
		return "", err
	}

	// 获取供应商信息
	var supplierList []AccountInfo
	if err = retry(5, 3*time.Second, func() error {
		supplierList, err = c.GetAccountInfo(ctx, req.CompanyName, "")
		if err != nil {
			log.ErrorWithCtx(ctx, "StartCorpApplyProcess GetAccountInfo err:%v", err)
			return err
		}
		return nil
	}); err != nil {
		log.ErrorWithCtx(ctx, "StartCorpApplyProcess GetAccountInfo retry err:%v", err)
		return "", err
	}
	if len(supplierList) == 0 {
		log.ErrorWithCtx(ctx, "StartCorpApplyProcess GetAccountInfo empty")
		return "", fmt.Errorf("GetAccountInfo empty")
	}
	supplierData := supplierList[0]
	applyForm := NewContractApply(seqNo, req, &supplierData, fileInfo)
	if c.ClientID != "" {
		applyForm.ContractApply.CreatedByApp = c.ClientID
	}

	log.InfoWithCtx(ctx, "StartCorpApplyProcess req: %+v, seqNo: %s, applyForm: %+v", req, seqNo, applyForm)

	applyFormJson, err := json.Marshal(applyForm)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartCorpApplyProcess json.Marshal err:%v", err)
		return "", err
	}
	processReq := Process{
		DefKey:     "Process_65255297627349",
		SystemHand: true,
		HasPk:      false,
		FormJson:   string(applyFormJson),
	}
	body, err := json.Marshal(processReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartCorpApplyProcess json.Marshal err:%v", err)
		return "", err
	}
	instId, err := c.StartProcessByAccount(ctx, body, userAccount)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartCorpApplyProcess StartProcess err:%v", err)
		return "", err
	}
	return instId, nil
}

// AppendCorpApplicationArchive 添加对公申请合同归档
func (c *Client) AppendCorpApplicationArchive(ctx context.Context, instId, archiveUrl, fileName string) error {
	if instId == "" || archiveUrl == "" || fileName == "" {
		log.ErrorWithCtx(ctx, "AppendCorpApplicationArchive instId or archiveUrl or fileName is empty")
		return fmt.Errorf("param empty")
	}

	// 对公申请用lace账号发起
	userAccount := CropApplyStartAccount

	log.InfoWithCtx(ctx, "AppendCorpApplicationArchive instId: %s, archiveUrl: %s, fileName: %s", instId, archiveUrl, fileName)

	var fileInfo *FileInfo
	// 下载合同
	contractFilePath, err := c.downloadFileByUrl(ctx, archiveUrl, fileName)
	if err != nil {
		log.ErrorWithCtx(ctx, "AppendCorpApplicationArchive downloadFileByUrl err:%v", err)
		return err
	}

	// 上传合同到灵犀
	fileInfo, err = c.UploadFileByPath(ctx, contractFilePath)
	if err != nil {
		log.ErrorWithCtx(ctx, "AppendCorpApplicationArchive UploadFileByPath err:%v", err)
		return err
	}

	log.InfoWithCtx(ctx, "AppendCorpApplicationArchive contractFileInfo: %s, fileInfo: %+v", contractFilePath, fileInfo)

	defer func() {
		// 删除本地合同文件
		//_ = os.Remove(contractFilePath)
	}()

	var filesJson []byte
	if fileInfo != nil {
		files := []FileInfo{*fileInfo}
		filesJson, _ = json.Marshal(files)
	}

	reqBody := &AddCorpApplicationArchiveReq{
		InstID:   instId,
		IsFile:   "1",
		FileType: "1",
		Files:    string(filesJson),
	}
	body, err := json.Marshal(reqBody)
	if err != nil {
		log.ErrorWithCtx(ctx, "AppendCorpApplicationArchive json.Marshal err:%v", err)
		return err
	}

	url := fmt.Sprintf("%s/ebc-api/api-bpm/restApi/bpm/contractApply/addArchive?", c.GetHost())
	url += fmt.Sprintf("&userAccount=%s", userAccount)

	_, err = c.requestPost(ctx, url, body)
	if err != nil {
		log.ErrorWithCtx(ctx, "AppendCorpApplicationArchive requestPost err:%v", err)
		return err
	}
	return nil
}

func retry(attempts int, sleep time.Duration, f func() error) (err error) {
	for i := 0; i < attempts; i++ {
		if i > 0 {
			log.Errorf("retrying after error: %s", err)
			time.Sleep(sleep)
			sleep *= 2
		}
		err = f()
		if err == nil {
			return nil
		}
	}
	return fmt.Errorf("after %d attempts, last error: %s", attempts, err)
}
