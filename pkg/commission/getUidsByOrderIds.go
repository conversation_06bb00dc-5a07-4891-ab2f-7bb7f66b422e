package commission

import (
	"golang.52tt.com/pkg/log"
	"golang.org/x/net/context"
)

type GetUidsByOrderIdsRequest struct {
	OrderIds []string `json:"orderIds"`
}

type GetUidsByOrderIdsResponse struct {
	EncashTime string  `json:"encashTime"`
	OrderId    string  `json:"orderId"`
	UserId     int64   `json:"userId"`
	Amount     float64 `json:"amount"`
}

const (
	getUidsByOrderIdsURI = "commision/getUidsByOrderIds.do"
)

func (c *client) GetUidsByOrderIds(ctx context.Context, orderIds []string) (resp []*GetUidsByOrderIdsResponse, err error) {
	req := &GetUidsByOrderIdsRequest{
		OrderIds: orderIds,
	}
	_, err = c.post(ctx, getUidsByOrderIdsURI, req, &resp)
	if err != nil {

		log.ErrorWithCtx(ctx, "GetUidsByOrderIds %+v", err)
		return
	}

	log.DebugfWithCtx(ctx, "GetUidsByOrderIds OK ")
	return
}
