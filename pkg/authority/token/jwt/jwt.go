package jwt

import (
	"encoding/json"
	"errors"
	"fmt"
	jwtpkg "github.com/dgrijalva/jwt-go"
	"golang.52tt.com/pkg/authority/model"
	"golang.52tt.com/pkg/log"
	"time"
)

const JwtHandlerName  = "jwt"
var (
	jwtSigningMethod = jwtpkg.SigningMethodHS256
)

type JwtHandler struct{
}

func (j *JwtHandler) Name()string{
	return "jwt"
}
func (j *JwtHandler) Parser(tokenStr string)(*model.TokenInfo,error){
	return j.parse(tokenStr,"",false)
}
func (j *JwtHandler) Valid(tokenStr,secret string)error{
	_,err:= j.parse(tokenStr,secret,true)
	return err
}

func (j *JwtHandler) Generate(secret string,sid uint,exp time.Time)(string,error){
	tk := jwtpkg.NewWithClaims(jwtSigningMethod, jwtpkg.MapClaims{
		"sid": sid,
		"exp": exp.Unix(),
		"nbf":time.Now(),
	})
	return tk.SignedString([]byte(secret))
}

func (j *JwtHandler) parse(tokenStr string,secret string,valid bool)(*model.TokenInfo,error){
	info,err:=jwtpkg.Parse(tokenStr, func(jwtToken *jwtpkg.Token) (interface{}, error) {
		if _, ok := jwtToken.Method.(*jwtpkg.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("Unexpected signing method: %v ", jwtToken.Header["alg"])
		}
		return []byte(secret), nil
	})
	log.Debugf("jwt parse result:%v %v",info,err)
	if info==nil {
		return nil,errors.New("token claims is nil")
	}
	if valid {
		if err!=nil {
			return nil,err
		}
		if !info.Valid {
			return nil,errors.New("token Parse not valid")
		}
	}
	return j.convertJwtToken(info)
}

func (j *JwtHandler) convertJwtToken(tk *jwtpkg.Token)(*model.TokenInfo,error){
	claims, ok := tk.Claims.(jwtpkg.MapClaims)
	if !ok {
		return nil,errors.New("token claims error")
	}
	var sid int64
	switch sidv := claims["sid"].(type) {
	case float64:
		sid = int64(sidv)
	case json.Number:
		sid,_ = sidv.Int64()
	default:
		return nil,errors.New("token claims error sid")
	}
	var exp int64
	switch expv := claims["exp"].(type) {
	case float64:
		exp = int64(expv)
	case json.Number:
		exp,_ = expv.Int64()
	default:
		return nil,errors.New("token claims error exp")
	}
	return &model.TokenInfo{
		Sid: uint64(sid),
		Exp: exp,
	},nil
}