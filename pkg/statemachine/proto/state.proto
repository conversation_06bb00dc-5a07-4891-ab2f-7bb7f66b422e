syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/statemachine";

enum State{
  UNDEFINED_STATE = 0;
  /* SingARound */
  SING_A_ROUND_PREPARE = 1; // 准备
  SING_A_ROUND_GRAB_MIC = 2; // 抢唱阶段
  SING_A_ROUND_SINGING = 3; // 接唱中
  SING_A_ROUND_REQ_HELP_1 = 4; // 请求帮唱1
  SING_A_ROUND_REQ_HELP_FAILED_1 = 5; // 请求帮唱失败1
  SING_A_ROUND_COMPARING = 6; // 接唱对比分数中
  SING_A_ROUND_RESULT = 7; // 接唱结果
  SING_A_ROUND_REQ_HELP_2 = 8; // 请求帮唱2
  SING_A_ROUND_REQ_HELP_FAILED_2 = 9; // 请求帮唱失败2
  SING_A_ROUND_HELPER_SINGING = 10; // 帮唱中
  SING_A_ROUND_HELPER_COMPARING = 11; // 帮唱对比分数中
  SING_A_ROUND_HELPER_RESULT = 12; // 帮唱结果

  SING_A_ROUND_UGC_CHANNEL_PREPARE = 13; // 准备（UGC房）
  SING_A_ROUND_UGC_CHANNEL_GRAB_MIC = 14; // 抢唱阶段（UGC房）
  SING_A_ROUND_UGC_CHANNEL_SINGING = 15; // 接唱中（UGC房）
  SING_A_ROUND_UGC_CHANNEL_REQ_HELP_1 = 16; // 请求帮唱1（UGC房）
  SING_A_ROUND_UGC_CHANNEL_REQ_HELP_FAILED_1 = 17; // 请求帮唱失败1（UGC房）
  SING_A_ROUND_UGC_CHANNEL_COMPARING = 18; // 接唱对比分数中（UGC房）
  SING_A_ROUND_UGC_CHANNEL_RESULT = 19; // 接唱结果（UGC房）
  SING_A_ROUND_UGC_CHANNEL_REQ_HELP_2 = 20; // 请求帮唱2（UGC房）
  SING_A_ROUND_UGC_CHANNEL_REQ_HELP_FAILED_2 = 21; // 请求帮唱失败2（UGC房）
  SING_A_ROUND_UGC_CHANNEL_HELPER_SINGING = 22; // 帮唱中（UGC房）
  SING_A_ROUND_UGC_CHANNEL_HELPER_COMPARING = 23; // 帮唱对比分数中（UGC房）
  SING_A_ROUND_UGC_CHANNEL_HELPER_RESULT = 24; // 帮唱结果（UGC房）

  SING_A_ROUND_PASS_THROUGH_PREPARE = 25; // 准备（闯关专区）
  SING_A_ROUND_PASS_THROUGH_GRAB_MIC = 26; // 抢唱阶段（闯关专区）
  SING_A_ROUND_PASS_THROUGH_SINGING = 27; // 接唱中（闯关专区）
  SING_A_ROUND_PASS_THROUGH_REQ_HELP_1 = 28; // 请求帮唱1（闯关专区）
  SING_A_ROUND_PASS_THROUGH_REQ_HELP_FAILED_1 = 29; // 请求帮唱失败1（闯关专区）
  SING_A_ROUND_PASS_THROUGH_COMPARING = 30; // 接唱对比分数中（闯关专区）
  SING_A_ROUND_PASS_THROUGH_RESULT = 31; // 接唱结果（闯关专区）
  SING_A_ROUND_PASS_THROUGH_REQ_HELP_2 = 32; // 请求帮唱2（闯关专区）
  SING_A_ROUND_PASS_THROUGH_REQ_HELP_FAILED_2 = 33; // 请求帮唱失败2（闯关专区）
  SING_A_ROUND_PASS_THROUGH_HELPER_SINGING = 34; // 帮唱中（闯关专区）
  SING_A_ROUND_PASS_THROUGH_HELPER_COMPARING = 35; // 帮唱对比分数中（闯关专区）
  SING_A_ROUND_PASS_THROUGH_HELPER_RESULT = 36; // 帮唱结果（闯关专区）
  SING_A_ROUND_PASS_THROUGH_PARTIAL_RESULT = 37; // 本轮结果（闯关专区）
  SING_A_ROUND_PASS_THROUGH_VOTE = 38; // 投票（闯关专区）
  SING_A_ROUND_PASS_THROUGH_PRELOAD = 39; // 预加载（闯关专区）
  
  /* Test */
  TEST_STATE_1 = 100001;
  TEST_STATE_2 = 100002;
  TEST_STATE_3 = 100003;
  TEST_STATE_4 = 100004;
  // 开始状态
  TEST_NONE = 100005;
  // 待商家处理
  TEST_SUPPLIER_PROCESSING = 100006;
  // 待控商小二处理
  TEST_SUPPLIER_MANAGER_PROCESSING = 100007;
  // 待价格管控小二处理
  TEST_PRICE_MANAGER_PROCESSING = 100008;
  // 退出
  TEST_CLOSED = 100009;
}