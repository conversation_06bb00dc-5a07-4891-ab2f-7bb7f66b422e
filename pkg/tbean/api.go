package tbean

import (
	"encoding/json"
	"errors"
	"fmt"
	imapi "golang.52tt.com/clients/im-api"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/marketid_helper"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gogo/protobuf/jsonpb"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.org/x/net/context"
	"golang.org/x/net/context/ctxhttp"
)

//go:generate mockgen -destination=./mocks/mock_tbean.go -package=mocks golang.52tt.com/pkg/tbean BalanceAPI,TradeAPI,TransferAPI,FreezeV2API,BlackUserAPI,RecordAPI,Client

const (
	PlatformIOS     = "ios"
	PlatformAndroid = "android"
)

type BalanceAPI interface {
	GetBalance(ctx context.Context, appID string, accountType AccountType, uid uint32) (balance int32, err error)
}

type TradeAPI interface {
	Consume(ctx context.Context, request *TradeRequest) (response *TradeResponseBody, err error)
	Prepare(ctx context.Context, request *TradeRequest) (response *TradeResponseBody, err error)
	Notify(ctx context.Context, request *TradeNotifyRequest, uid uint32) (status string, modifyTime string, err error)
}

type TransferAPI interface {
	TransferI2C(ctx context.Context, request *TransferI2CRequest) ([]*TransferI2CResponseBody, error)
	TransferI2CGetBalance(ctx context.Context, request *TransferI2CBalanceReq) (*TransferI2CBalanceResp, error)
	TransferI2CRefund(ctx context.Context, request *TransferI2CRefundRequest) ([]*TransferI2CRefundResponseBody, error)
	TransferI2CV2(ctx context.Context, request *TransferI2CV2Request) ([]*TransferI2CResponseBody, error)
}

type FreezeV2API interface {
	SetSecretKey(key string)
	PresetFreeze(ctx context.Context, request *PresetFreezeReqData) (*PresetFreezeResp, protocol.ServerError)
	UnFreezeAndRefund(ctx context.Context, request *UnFreezeAndRefundReqData) (*UnFreezeAndRefundResp, protocol.ServerError)
	UnfreezeAndConsume(ctx context.Context, request *UnfreezeAndConsumeReqData) (*UnfreezeAndConsumeResp, protocol.ServerError)
}

type BlackUserAPI interface {
	CheckBlackUser(ctx context.Context, uid string, blackType string) (bool, error)
	CheckBlackUserV2(ctx context.Context, uid uint32, typeList []uint32) ([]uint32, error)
}

type RecordAPI interface {
	GetFirstRechargeCheck(ctx context.Context, uid, firsMinPrice uint32, payChannel string) (bool, error)
}

type Client interface {
	BalanceAPI
	TradeAPI
	TransferAPI
	FreezeV2API
	BlackUserAPI
	RecordAPI
}

type client struct {
	contextPath string
	secretKey   string
	httpClient  *http.Client
	enc         *jsonpb.Marshaler

	imApi imapi.IClient
}

func NewClient(contextPath string) Client {
	c := &client{
		contextPath: contextPath,
		httpClient:  &http.Client{Timeout: time.Second * 5},
		enc:         &jsonpb.Marshaler{OrigName: true, EmitDefaults: true},
	}
	proxy := os.Getenv("TBEAN_API_PROXY")
	if proxy != "" {
		transport := &http.Transport{Proxy: func(_ *http.Request) (*url.URL, error) {
			return url.Parse(proxy)
		}}
		log.Debugf("TBean api proxy: %s", proxy)
		c.httpClient.Transport = transport
	}
	timeout := os.Getenv("TBEAN_API_TIMEOUT")
	if timeout != "" {
		if dur, err := time.ParseDuration(timeout); err == nil {
			log.Debugf("TBean api timeout: %v", dur)
			c.httpClient.Timeout = dur
		}
	}

	sendImCli := imapi.NewIClient()
	c.imApi = sendImCli
	return c
}

func (c *client) call(ctx context.Context, path string, request proto.MessageV1, responseBody interface{}, uid uint32) (err error) {
	if responseBody == nil {
		return errors.New("The input `responseBody` must not be nil.")
	}

	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.Errorf("Failed to get service info from context: %d", uid)
	}

	req, err := c.enc.MarshalToString(request)
	if err != nil {
		log.Errorf("Failed to marshal request: %v %v", request, err)
		return
	}

	url := fmt.Sprintf("%s%s", c.contextPath, path)

	start := time.Now()
	defer func() {
		orderInfo := OrderInfoFromContext(ctx)
		elapsed := time.Since(start)
		elapsedMillisecond := uint32(math.Ceil(float64(elapsed / time.Millisecond)))
		if elapsedMillisecond < 1 {
			elapsedMillisecond = 1
		}

		log.Infof("POST %s %s tooks %dms orderInfo: %s", url, request, elapsedMillisecond, orderInfo)
	}()

	httpReq, err := http.NewRequest("POST", url, strings.NewReader(req))
	if err != nil {
		log.Errorf("http.NewRequest %s %s post error %v", url, req, err)
		return
	}
	httpReq.Header.Set("Content-Type", "application/json")
	strUid := strconv.Itoa(int(uid))
	httpReq.Header.Set("uid", strUid)
	resp, err := ctxhttp.Do(ctx, c.httpClient, httpReq)
	//resp, err := ctxhttp.Post(ctx, c.httpClient, url, "application/json", strings.NewReader(req))
	if err != nil {
		log.Errorf("POST %s %s post error %v", url, req, err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("POST %s %s status %s", url, req, resp.Status)
		log.ErrorWithCtx(ctx, "Response code not OK: %v", err)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Errorf("POST %s %s read body failed: %+v", url, req, err)
		return
	}
	response := &ApiResponse{Body: responseBody}
	//err = json.NewDecoder(resp.Body).Decode(response)
	err = json.Unmarshal(body, response)
	if err != nil {
		log.Errorf("POST %s %s unmarshal body failed: %v", url, req, err)
		log.Errorf("body: %s", string(body))
		return
	}
	log.Debugf("POST uil:%s req:%s OK response:%+v body:%v", url, req, response, string(body))

	if response.Head.Result != resultSuccess {
		log.Errorf("POST %s %s result error (%s, %s)", url, req, response.Head.Result, response.Head.Message)
		err = toAPIError(response.Head.Result)

		// 如果是特定报错要发im

		if err == ErrorAbnormalProtect {
			content := "尊敬的用户你好，系统检测到您的账号存在异常风险，已为您启用平台消费保护机制，如需解除限制请点击【功能申诉】发起申诉解除"
			highlight := "【功能申诉】"
			jumpUrl := marketid_helper.Get("tbean_appeal_url", serviceInfo.MarketID, serviceInfo.TerminalType)
			c.SendImWithHighlight(ctx, uid, content, highlight, jumpUrl)

			content = "请根据【消费保护机制申诉材料说明】页面提示提供相关信息进行申诉解限~"
			highlight = "【消费保护机制申诉材料说明】"
			jumpUrl = marketid_helper.Get("tbean_consume_url", serviceInfo.MarketID, serviceInfo.TerminalType)
			c.SendImWithHighlight(ctx, uid, content, highlight, jumpUrl)
		}

		if err == ErrorMinorRecharge {
			content := "尊敬的用户你好，系统检测到您的账号存在异常风险，已为您启用平台充值消费保护机制，如需解除限制请点击【功能申诉】发起申诉解除。"
			highlight := "【功能申诉】"
			jumpUrl := marketid_helper.Get("tbean_appeal_url", serviceInfo.MarketID, serviceInfo.TerminalType)
			c.SendImWithHighlight(ctx, uid, content, highlight, jumpUrl)

			content = "请根据【充值消费保护机制申诉材料说明】页面提示提供相关信息进行申诉解限~"
			highlight = "【充值消费保护机制申诉材料说明】"
			jumpUrl = marketid_helper.Get("tbean_recharge_url", serviceInfo.MarketID, serviceInfo.TerminalType)
			c.SendImWithHighlight(ctx, uid, content, highlight, jumpUrl)
		}

	} else {
		log.Debugf("POST %s %s OK %+v body:%v", url, req, response, string(body))
	}

	return
}

func (c *client) callV2(ctx context.Context, path string, request interface{}, responseBody interface{}, uid uint32) (err error) {
	if responseBody == nil {
		return errors.New("The input `responseBody` must not be nil.")
	}

	reqStr := ""
	if request != nil {
		reqByte, err := json.Marshal(request)
		if err != nil {
			log.ErrorWithCtx(ctx, "Failed to marshal request: %v %v", request, err)
			return err
		}
		reqStr = string(reqByte)
	}

	reqUrl := fmt.Sprintf("%s%s", c.contextPath, path)

	start := time.Now()
	defer func() {
		orderInfo := OrderInfoFromContext(ctx)
		elapsed := time.Since(start)
		elapsedMillisecond := uint32(math.Ceil(float64(elapsed / time.Millisecond)))
		if elapsedMillisecond < 1 {
			elapsedMillisecond = 1
		}

		log.InfoWithCtx(ctx, "POST %s %s tooks %dms orderInfo: %s", reqUrl, request, elapsedMillisecond, orderInfo)
	}()

	httpReq, err := http.NewRequest("POST", reqUrl, strings.NewReader(reqStr))
	if err != nil {
		log.ErrorWithCtx(ctx, "http.NewRequest %s %s post error %v", reqUrl, reqStr, err)
		return
	}
	httpReq.Header.Set("Content-Type", "application/json")
	strUid := strconv.Itoa(int(uid))
	httpReq.Header.Set("uid", strUid)
	resp, err := ctxhttp.Do(ctx, c.httpClient, httpReq)
	//resp, err := ctxhttp.Post(ctx, c.httpClient, url, "application/json", strings.NewReader(req))
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s post error %v", reqUrl, reqStr, err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("POST %s %s status %s", reqUrl, reqStr, resp.Status)
		log.ErrorWithCtx(ctx, "Response code not OK: %v", err)
		return
	}

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s read body failed: %+v", reqUrl, reqStr, err)
		return
	}
	//err = json.NewDecoder(resp.Body).Decode(response)
	err = json.Unmarshal(body, responseBody)
	if err != nil {
		log.ErrorWithCtx(ctx, "POST %s %s unmarshal body failed: %v", reqUrl, reqStr, err)
		log.ErrorWithCtx(ctx, "body: %s", string(body))
		return
	}
	log.InfoWithCtx(ctx, "POST uil:%s req:%s OK response:%+v body:%v", reqUrl, reqStr, responseBody, string(body))

	return
}

func (c *client) SetSecretKey(secretKey string) {
	log.Debugf("SetSecretKey:%s", secretKey)

	c.secretKey = secretKey
}

func (c *client) GetSecretKey() string {
	if len(c.secretKey) == 0 {
		return "2ae000b562cba514"
	}
	return c.secretKey
}

const (
	//sandboxContextPath    = "http://120.132.68.148:18080/portal"
	sandboxContextPath    = "http://testing-tbean.ttyuyin.com/portal"
	productionContextPath = "http://10.10.4.154/portal" // "http://10.10.55.99/portal"

	resultSuccess = "SUCCESS"
)

type ApiResponse struct {
	Head ApiResponseHeader `json:"head"`
	Body interface{}       `json:"body"`
}
