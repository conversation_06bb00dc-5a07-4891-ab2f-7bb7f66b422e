package protocol

import "os"

var (
	isServerSupportGrpcError   bool
	isServerSupportTraceError  bool
	isServerSupportTrailerOnly bool

	myAppName     string
	myTrafficMark string
)

const (
	serverSupportGrpcErrorEnv   = "TT_SUPPORT_GRPC_ERROR"
	serverSupportTraceErrorEnv  = "TT_SUPPORT_TRACE_ERROR"
	serverSupportTrailerOnlyEnv = "TT_SUPPORT_TRAILER_ONLY"

	myAppNameEnv     = "MY_APP_NAME"
	myTrafficMarkEnv = "DYEING_ENVIRONMENT_MARK"
)

func init() {
	loadEnv()
}

func loadEnv() {
	isServerSupportGrpcError = os.Getenv(serverSupportGrpcErrorEnv) == "true"
	isServerSupportTraceError = os.Getenv(serverSupportTraceErrorEnv) == "true"
	isServerSupportTrailerOnly = os.Getenv(serverSupportTrailerOnlyEnv) == "true"

	myAppName = os.Getenv(myAppNameEnv)
	myTrafficMark = os.Getenv(myTrafficMarkEnv)
}

func TrafficMark() string {
	return myTrafficMark
}

func AppName() string {
	return myAppName
}

// ReloadEnv 用于测试
func ReloadEnv() {
	loadEnv()
}
