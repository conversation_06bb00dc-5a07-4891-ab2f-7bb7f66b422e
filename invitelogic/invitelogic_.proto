syntax = "proto3";

package ga.invitelogic;

import "ga_base.proto";


option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/invitelogic";

//在房间发邀请
message SendInviteFromChannelPushReq {
    ga.BaseReq base_req = 1;
    string to_account = 2;      //要邀请的人
    uint32 channel_id = 3;//房间号
    uint32 channel_type = 4;//房间类型
    string from_name = 5;//发起者名字
    bool is_online  = 6;//对方是否在线
    bool is_friend  = 7;//对方是否是玩伴
    string play_game_name  = 8;//玩法名字
    string from_account = 9;      //发出邀请的人
}

message SendInviteFromChannelPushResp {
    ga.BaseResp base_resp = 1;
}


message GetInviteFromChannelListReq{
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;//当前用户的房间id
    uint32 tab_id = 3;//房间的tab_id
    // 邀请类型
    uint32 invite_type = 4;
}

enum InviteType {
    // 原有的邀请类型（包括玩伴，最近一起玩过等）
    INVITE_TYPE_UNSPECIFIED = 0;
    // 关注的人
    INVITE_TYPE_FOLLOWING = 1;
}

message GetInviteFromChannelListResp {
    enum TypePlayer {
       none = 0;
       recent_play = 1;//最近一起玩
       friend = 2;//玩伴
    }
    message InviteItem {
        string nickname  = 1;//被邀请的昵称
        string account = 2;//被邀请的account
        bool already_invite = 3;//是否已经邀请
        string intro_text = 4;//介绍文案,最近玩过文案等
        string player_type_text = 5;//邀请人类型 最近一起玩，玩伴...
        bool is_online = 6;//现在是否在线
        TypePlayer type_player= 7;//邀请人类型
        uint32 sex  = 8;//性别
        bool can_unlock = 9;//能否解锁
        uint32 uid = 10;//能否解锁
    }
    ga.BaseResp base_resp = 1;
    repeated InviteItem invite_list = 2;
}

message ReplyInviteFromChannelReq {
    ga.BaseReq base_req = 1;
    string account = 2;//发出邀请的人
    bool accept = 3;//接受对方邀请
    string my_account = 4;//回复邀请的人 即自己
}

message ReplyInviteFromChannelResp {
    ga.BaseResp base_resp = 1;
}

