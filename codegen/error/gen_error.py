#!/usr/bin/python
# -*- coding: utf-8 -*-

import sys, getopt, re, os

import descriptor

def usage():
    print('Usage: %s [OPTION] SOURCE_FILE' % __file__)
    print("""Parse SOURCE_FILE and generate output based on the options given:
  --cpp_out=OUT_DIR           Generate C++ header and source.
  --go_out=OUT_DIR            Generate Go header and source.
  --java_out=OUT_DIR          Generate Java source file.
  --dart_out=OUT_DIR          Generate dart source file.
  --objc_out=OUT_DIR          Generate Objective C header and source.
    """)


def main(argv):
    try:
        acceptable_langs = ["cpp", "go", "java","dart","objc"]
        acceptable_opts = map(lambda lang: "%s_out=" % lang, acceptable_langs)
        opts, args = getopt.getopt(argv, "", acceptable_opts)
        if len(args) == 0:
            print("Missing source file.\n")
            usage()
            return

        src = args[0]
        workdir = os.getcwd()

        targets = {}
        acceptable_lang_opts = set(map(lambda lang: "--%s_out" % lang, acceptable_langs))
        lang_out_regex = re.compile(r"--(\S+)_out")
        for k, v in opts:
            if k in acceptable_lang_opts:
                lang = lang_out_regex.match(k).group(1)
                out_path = os.path.join(workdir, v)
                try:
                    os.mkdir(out_path)
                except:
                    pass
                targets[lang] = out_path

        r = re.compile(r"(\S+)\s+(-?[0-9]+)\s*(.*)")

        descriptors = []
        with open(src, 'r') as f:
            lines = f.readlines()
            for line in lines:
                if len(line) == 0:
                    break

                if line.startswith("//"):
                    descriptors.append(descriptor.DocumentDescriptor(line.strip()))
                else:
                    m = r.match(line.strip())
                    if m:
                        g = m.groups()
                        descriptors.append(descriptor.StatusEnumDescriptor(g[0], int(g[1]), g[2]))

        fd = descriptor.FileDescriptor(os.path.basename(src), descriptors)

        generators = []
        for (lang, out) in targets.iteritems():
            module = __import__(lang, globals(), locals(), ['CodeGenerator', ], -1)
            gen = module.CodeGenerator(out)
            generators.append(gen)

        for g in generators:
            g.generate(fd)

    except Exception as ex:
        print("Exception caught: %s" % ex)
        usage()
        return
    

if __name__ == '__main__':
    main(sys.argv[1:])
