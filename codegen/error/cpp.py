#!/usr/bin/python
# -*- coding: utf-8 -*-

import descriptor, printer, os

class CodeGenerator(descriptor.CodeGeneratorInterface):
    def __init__(self, outdir):
        self.outdir = outdir


    def generate(self, fd):
        self.generate_header(fd)
        self.generate_source(fd)

    def generate_source(self, fd):
        status_code_cpp = os.path.join(self.outdir, "%s.cc" % os.path.splitext(fd.source)[0])
        with open(status_code_cpp, "w+") as f:
            p = printer.Printer(f)
            GENERATED_FILE_HEADER = """// Generated by gen_error tool (cpp plugin).
// If you make any local change, they will be lost.
// source: %s""" % (fd.source)
            p.P(GENERATED_FILE_HEADER)
            p.P("")
            status_code_h = "%s.h" % os.path.splitext(fd.source)[0]
            p.P("#include <string>")
            p.P("#include <unordered_map>")
            p.P("#include \"%s\"" % status_code_h)
            p.P("")
            p.P("using std::string;")
            p.P("using std::unordered_map;")
            p.P("")
            p.P("namespace protocol {")
            p.P("")
            p.P("static const string EMPTY = \"\";")
            p.P("static const string DEFAULT = \"系统繁忙\";")
            p.P("static const unordered_map<int, string> codeMessageMap {")
            p.In()
            for d in fd.descriptors:
                if isinstance(d, descriptor.StatusEnumDescriptor) and len(d.msg) > 0:
                    name = d.name
                    if d.value <> 0:
                           name = "ERR_" + d.name 
                    p.P("{ %s, ERRSTR_%s }," % (name, d.name))
            p.Out()
            p.P("}; // codeMessageMap")
            p.P("")
            p.P("const string& messageFromCode(int code) {")
            p.In()
            p.P("auto it = codeMessageMap.find(code);")
            p.P("return it != codeMessageMap.end() ? it->second : DEFAULT;")
            p.Out()
            p.P("}")
            p.P("")
            p.P("} // namespace protocol")

    def generate_header(self, fd):
        status_code_h = os.path.join(self.outdir, "%s.h" % os.path.splitext(fd.source)[0])
        with open(status_code_h, "w+") as f:
            p = printer.Printer(f)
            GENERATED_FILE_HEADER = """// Generated by gen_error tool (cpp plugin).
// If you make any local change, they will be lost.
// source: %s""" % (fd.source)
            p.P(GENERATED_FILE_HEADER)
            p.P("")
            p.P("#pragma once")
            p.P("")
            p.P("#include <string>")
            p.P("")
            p.P("namespace protocol {")
            p.P("")
            p.In()
            p.P("// MessageFromCode get message associated with the code")
            p.P("const std::string& messageFromCode(int code);")
            p.P("")
            p.P("// Error code enumeration")
            p.P("")

            for d in fd.descriptors:
                if isinstance(d, descriptor.DocumentDescriptor):
                    p.P("")
                    p.P(d.doc)
                elif isinstance(d, descriptor.StatusEnumDescriptor):
                    name = d.name
                    if d.value <> 0:
                           name = "ERR_" + d.name 
                    if len(d.msg) > 0:
                        p.P("const int %s = %s;    // %s" % (name, d.value, d.msg))
                    else:
                        p.P("const int %s = %s; " % (name, d.value))
            p.P("")
            p.P("")
            p.P("// Error message definitions")
            for d in fd.descriptors:
                if isinstance(d, descriptor.StatusEnumDescriptor):
                    if len(d.msg) > 0:
                        p.P("const char * const ERRSTR_%s = \"%s\";" % (d.name, d.msg))
            p.Out()
            p.P("")
            p.P("} // namespace protocol")
