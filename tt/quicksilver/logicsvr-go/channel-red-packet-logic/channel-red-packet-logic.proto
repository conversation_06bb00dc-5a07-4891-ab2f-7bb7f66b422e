syntax = "proto3";

package logic.channel_red_packet_logic;



import "tt/quicksilver/logicsvr-go/gateway/options/options.proto";
import "channel_red_packet_logic/channel-red-packet-logic_.proto";

option go_package = "golang.52tt.com/protocol/services/logicsvr-go/channel-red-packet-logic";

service ChannelRedPacketLogic {
  option (logic.gateway.service_ext) = {
    service_name: "channel-red-packet-logic"
  };

  rpc GetRedPacketConf (ga.channel_red_packet_logic.GetRedPacketConfReq) returns (ga.channel_red_packet_logic.GetRedPacketConfResp) {
    option (logic.gateway.command) = {
      id: 30951   //改成正确的id
    };
  }

  rpc GetRedPacketList (ga.channel_red_packet_logic.GetRedPacketListReq) returns (ga.channel_red_packet_logic.GetRedPacketListResp) {
    option (logic.gateway.command) = {
      id: 30952   //改成正确的id
    };
  }

  rpc SendRedPacket (ga.channel_red_packet_logic.SendRedPacketReq) returns (ga.channel_red_packet_logic.SendRedPacketResp) {
    option (logic.gateway.command) = {
      id: 30953   //改成正确的id
    };
  }

  rpc ReportRedPacketClickCnt (ga.channel_red_packet_logic.ReportRedPacketClickCntReq) returns (ga.channel_red_packet_logic.ReportRedPacketClickCntResp) {
    option (logic.gateway.command) = {
      id: 30954   //改成正确的id
    };
  }
}