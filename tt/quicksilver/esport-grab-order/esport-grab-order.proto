syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/esport_grab_order";

package esport_grab_order;

service EsportGrabOrder {
  // 获取开关
  rpc GetSwitch (GetSwitchRequest) returns (GetSwitchResponse);
  // 电竞专区/电竞tab 心跳
  rpc EsportRegionHeartbeat (EsportRegionHeartbeatRequest) returns (EsportRegionHeartbeatResponse);

  // 获取当前正在进行中的一键找教练信息
  rpc GetGoingOneKeyFindCoach (GetGoingOneKeyFindCoachRequest) returns (GetGoingOneKeyFindCoachResponse);
  // 检查是否能发布
  rpc CheckIfCouldPublish (CheckIfCouldPublishRequest) returns (CheckIfCouldPublishResponse);
  // 发布一键找人需求
  rpc PublishOneKeyFindCoach (PublishOneKeyFindCoachRequest) returns (PublishOneKeyFindCoachResponse);
  // 取消一键找人需求
  rpc CancelOneKeyFindCoach (CancelOneKeyFindCoachRequest) returns (CancelOneKeyFindCoachResponse);
  // 置顶一键找人需求
  rpc StickOneKeyFindCoach (StickOneKeyFindCoachRequest) returns (StickOneKeyFindCoachResponse);
  // 一键找人完成选人
  rpc FinishChoseCoach (FinishChoseCoachRequest) returns (FinishChoseCoachResponse);

  // 抢单
  rpc GrabOrder (GrabOrderRequest) returns (GrabOrderResponse);
  // 获取抢单中心概要
  rpc GetGrabCenterOverview (GetGrabCenterOverviewRequest) returns (GetGrabCenterOverviewResponse);
  // 获取待抢订单列表
  rpc GetPendingGrabOrderList (GetPendingGrabOrderListRequest) returns (GetPendingGrabOrderListResponse);
  // 获取已抢订单列表
  rpc GetGrabbedOrderList (GetGrabbedOrderListRequest) returns (GetGrabbedOrderListResponse);
  //  测试推送消息
  rpc TestPushMsg (TestPushMsgRequest) returns (TestPushMsgResponse);

  // 设置抢单音频审核状态
  rpc SetGrabAudioAuditStatus (SetGrabAudioAuditStatusRequest) returns (SetGrabAudioAuditStatusResponse);
  // 获取抢单音频审核状态
  rpc GetGrabAudioAuditStatus (GetGrabAudioAuditStatusRequest) returns (GetGrabAudioAuditStatusResponse);
}

message GetSwitchRequest {
}

message GetSwitchResponse {
  bool main_switch = 1;
  uint32 max_grab_user_cnt = 2; // 最大抢单人数
  bool show_grab_order_center = 3; // 是否显示抢单中心
}


message GetGoingOneKeyFindCoachRequest {
    uint32 uid = 1;
}

message GetGoingOneKeyFindCoachResponse {
  string publish_id = 1; // 当前没有则返回空
  int64 expire_ts = 2; // 过期时间戳，单位秒。用于倒计时
  repeated GrabOrderCoachInfo grab_list = 3; // 抢单列表
  int64 publish_ts = 4; // 发布时间戳，单位秒
  uint32 game_id = 5; // 游戏id
}

message GrabOrderCoachInfo {
  uint32 coach_uid = 1;
  uint32 grab_type = 2; // 参考 EsportOneKeyGrabType
  string grab_text = 3;
  string grab_audio = 4;
  uint32 grab_audio_duration = 5;
}

// 发布一键找人需求
message PublishOneKeyFindCoachRequest {
  uint32 uid = 1; // 发布者uid
  uint32 game_id = 2; // 游戏id
  string request_text = 3; // 请求文本
  string sex = 4; // 性别
  repeated string target_tags = 5; // 目标标签
  string game_region = 6[deprecated=true]; // 大区
  string game_rank = 7[deprecated=true]; // 段位
  repeated CardInfoSection card_info_list = 8; // 卡片信息
}

message CardInfoSection {
  string title = 1;
  string content = 2;
}

// PublishOneKeyFindCoachResponse 发布一键找人需求返回
message PublishOneKeyFindCoachResponse {
  string publish_id = 1;
}


// 取消一键找人需求
message CancelOneKeyFindCoachRequest {
  uint32 uid = 1;
  string publish_id = 2;
  string cancel_reason = 3;
}

message CancelOneKeyFindCoachResponse {
}

// 置顶一键找人需求，划到最后时请求
message StickOneKeyFindCoachRequest {
  uint32 uid = 1;
  string publish_id = 2;
}

message StickOneKeyFindCoachResponse {
}

message GrabOrderRequest {
  uint32 uid = 1;
  string publish_id = 2;
  uint32 grab_type = 3; // 参考GrabType
  string grab_text = 4;
  string grab_audio = 5;
  uint32 grab_audio_duration = 6;
}

message GrabOrderResponse {
}

// 用于维护在专区中的用户，便于后续推送
// 电竞专区/电竞tab 心跳
message EsportRegionHeartbeatRequest {
  uint32 uid = 1;
  uint32 from_source = 2; // 区分 电竞专区/电竞tab see esport_logic.proto AreaCoachListFromSource
}

message EsportRegionHeartbeatResponse {
}

enum PublishRecordStatus {
  PUBLISH_RECORD_STATUS_UNSPECIFIED = 0; // 初始化
  PUBLISH_RECORD_STATUS_GRAB = 1; // 已抢单
  PUBLISH_RECORD_STATUS_CANCEL = 2; // 已取消
  PUBLISH_RECORD_STATUS_TIMEOUT = 3; // 已取消
}

message FinishChoseCoachRequest {
  uint32 uid = 1;
  uint32 target_uid = 2; // 抢单者uid
}

message FinishChoseCoachResponse {
}

message CheckIfCouldPublishRequest {
  uint32 uid = 1;
}

message CheckIfCouldPublishResponse {
  string reason = 1; // 不能发布的原因,为空则可以发布
}

message GetGrabCenterOverviewRequest {
  uint32 uid = 1;
  int64 last_refresh_ts = 2; // 上次刷新时间戳，单位秒
}

message GetGrabCenterOverviewResponse {
  uint32 update_order_count = 1; // 更新订单数量
  uint32 going_order_count = 2; // 进行中订单数量
}

message GetPendingGrabOrderListRequest {
  uint32 uid = 1;
  uint32 page = 2;
  uint32 size = 3;
  int64 refresh_ts = 4; // 本次刷新所用的时间戳，单位秒。page=1时不用传；page>1时，传page=1返回的时间戳
}

message GetPendingGrabOrderListResponse {
  repeated OneKeyFindCoachDemand order_list = 1;
  bool has_more = 2;
  int64 refresh_ts = 3; // 本次刷新所用的时间戳，单位秒
}

message GetGrabbedOrderListRequest {
  uint32 uid = 1;
  uint32 page = 2;
  uint32 size = 3;
}

message GetGrabbedOrderListResponse {
  repeated GrabOrderInfo order_list = 1;
  bool has_more = 2;
}

message OneKeyFindCoachDemand {
  string publish_id = 1;
  uint32 game_id = 2;
  uint32 player_uid = 3;
  string player_gender = 4;
  repeated string player_tag_list = 5;
  repeated string target_tag_list = 6;
  string target_custom_requirement = 7;
  uint32 grabbed_cnt = 8; // 已抢单人数
}

message GrabOrderInfo {
  OneKeyFindCoachDemand demand = 1;
  uint32 grab_type = 2; // 参考GrabType
  string grab_text = 3;
  string grab_audio = 4;
  uint32 grab_audio_duration = 5;
  uint32 grab_status = 6; // 参考GrabStatus
}

enum GrabStatus {
  GRAB_STATUS_UNSPECIFIED = 0;
  GRAB_STATUS_PENDING = 1; // 抢单中
  GRAB_STATUS_END = 2; // 已结束
  GRAB_STATUS_CHOSEN = 3; // 已抢到单
}

message TestPushMsgRequest {
  enum TestPushMsgType {
    TEST_PUSH_MSG_TYPE_UNSPECIFIED = 0;
    TEST_PUSH_MSG_TYPE_ORDER_PUBLISH_WINDOW = 1; // 新抢单发布弹窗
    TEST_PUSH_MSG_TYPE_GRAB_ORDER_FINISH = 2; // 成功下单跑马灯
  }
  repeated uint32 uid_list = 1;
  TestPushMsgType msg_type = 2;
  uint32 from_uid = 3;
  uint32 to_uid = 4;
}

message TestPushMsgResponse {
}

enum CheckStatus {
  CHECK_STATUS_UNSPECIFIED = 0;
  CHECK_STATUS_CHECKING = 1; // 正在检测
  CHECK_STATUS_VALID = 2; // 有效
  CHECK_STATUS_INVALID = 3; // 无效
}

message SetGrabAudioAuditStatusRequest {
  uint32 uid = 1;
  string grab_audio = 2;
  int64 create_at = 3;
  uint32 check_status = 4; // 参考CheckStatus
}

message SetGrabAudioAuditStatusResponse {
}

message GetGrabAudioAuditStatusRequest {
  uint32 uid = 1;
  string grab_audio = 2;
}

message GetGrabAudioAuditStatusResponse {
  uint32 check_status = 1; // 参考CheckStatus
}
