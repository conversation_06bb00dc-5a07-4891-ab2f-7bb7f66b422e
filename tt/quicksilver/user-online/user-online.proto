syntax = "proto3";

package user_online;

option go_package = "golang.52tt.com/protocol/services/user-online";

service UserOnline {
    // 获取手机当前在线信息
    rpc GetMobileOnlineInfo(GetMobileOnlineInfoReq) returns (GetMobileOnlineInfoResp) {}
    // 批量获取手机当前在线信息
    rpc BatchGetMobileOnlineInfo(BatchGetMobileOnlineInfoReq) returns (BatchGetMobileOnlineInfoResp) {}

    // 获取多端当前在线信息
    rpc GetMultiOnlineInfo(GetMultiOnlineInfoReq) returns (GetMultiOnlineInfoResp) {}
    // 批量获取多端当前在线信息
    rpc BatchGetMultiOnlineInfo(BatchGetMultiOnlineInfoReq) returns (BatchGetMultiOnlineInfoResp) {}

    // 获取最新在线信息
    rpc GetLatestOnlineInfo(GetLatestOnlineInfoReq) returns (GetLatestOnlineInfoResp) {}
    // 批量获取最新在线信息
    rpc BatchGetLatestOnlineInfo(BatchGetLatestOnlineInfoReq) returns (BatchGetLatestOnlineInfoResp) {}
    // 获取用户在线日志
    rpc GetUserOnlineLog(GetUserOnlineLogReq) returns (GetUserOnlineLogResp) {}

    // for test
    rpc HandlePres(HandlePresReq) returns (HandlePresResp) {}
    // for test
    rpc HandleLogin(HandleLoginReq) returns (HandleLoginResp) {}
}

enum OnlineType {
    // 无效
    ONLINE_TYPE_INVALID = 0;
    // 在线
    ONLINE_TYPE_ONLINE = 1;
    // 离线
    ONLINE_TYPE_OFFLINE = 2;
}

message OnlineInfo {
    uint32 uid = 1;
    // 当前是否在线
    OnlineType online_type = 2;

    // 登录时间，秒
    int64 online_at = 3;
    // 下线时间，秒
    int64 offline_at = 4;

    // 马甲包id
    uint32 market_id = 5;
    // 客户端ip
    string client_ip = 6;
    // 16进制设备id
    string device_id_hex = 7;
    // 客户端类型
    uint32 client_type = 8;
    // 设备型号
    string device_model = 9;
    // 终端类型
    uint32 terminal_type = 10;
    // 客户端版本号
    uint32 client_version = 11;
    // 操作系统版本
    string os_ver = 12;
    // 操作系统类型
    string os_type = 13;
    // 设备品牌
    string device_brand = 14;
    // 屏幕高度
    uint32 screen_height = 15;
    // 屏幕宽度
    uint32 screen_width = 16;
    // 代理ip
    string proxy_ip = 17;
    // 代理端口
    uint32 proxy_port = 18;
    // 客户端id
    uint32 client_id = 19;
}

message GetMobileOnlineInfoReq {
    uint32 uid = 1;
}

message GetMobileOnlineInfoResp {
    OnlineInfo online_info = 1;
}

message BatchGetMobileOnlineInfoReq {
    repeated uint32 uid_list = 1;
}

message BatchGetMobileOnlineInfoResp {
    repeated OnlineInfo online_info_list = 1;
}

message Option {
    // 指定客户端类型
    repeated uint32 client_type_list = 1;
    // 指定客户端类型
    repeated uint32 platform_type_list = 2;
}

message GetMultiOnlineInfoReq {
    uint32 uid = 1;
    Option opt = 2;
}

message MultiOnlineInfo {
    uint32 uid = 1;
    repeated OnlineInfo online_info_list = 2;
}

message GetMultiOnlineInfoResp {
    MultiOnlineInfo multi_online_info = 1;
}

message BatchGetMultiOnlineInfoReq {
    repeated uint32 uid_list = 1;
    Option opt = 2;
}

message BatchGetMultiOnlineInfoResp {
    repeated MultiOnlineInfo multi_online_info_list = 1;
}

message GetLatestOnlineInfoReq {
    uint32 uid = 1;
}

message GetLatestOnlineInfoResp {
    OnlineInfo online_info = 1;
}

message BatchGetLatestOnlineInfoReq {
    repeated uint32 uid_list = 1;
}

message BatchGetLatestOnlineInfoResp {
    repeated OnlineInfo online_info_list = 1;
}

message HandlePresReq {
    uint32 uid = 1;
    uint32 terminal_type = 2;
    int64 online_at = 3;
    int64 offline_at = 4;
}
    
message HandlePresResp {
}

message HandleLoginReq {
    uint32 uid = 1;
    uint32 terminal_type = 2;
    int64 online_at = 3;
    uint32 market_id = 4;
    string client_ip = 5;
    string device_id_hex = 6;
    uint32 client_type = 7;
    string device_model = 8;
    uint32 client_version = 9;
}

message HandleLoginResp {
}

message GetUserOnlineLogReq {
    uint32 uid = 1;
    uint64 begin_at_ms = 2;                    // online_at ms
    uint64 end_at_ms = 3;
    uint32 limit = 4;
}

message GetUserOnlineLogResp {
    repeated OnlineInfo online_info_list = 1;
}



