syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/recall-minigame-user";

package recall_minigame_user;

//小游戏进房服务
service RecallMinigameUser {
    // 上报minigame进入事件
    rpc ReportMinigameEnter (ReportMinigameEnterReq) returns (ReportMinigameEnterResp) {
    }
}


message ReportMinigameEnterReq {
    uint32 uid = 1;//进房的uid
    uint32 tab_id = 2;//进入的房间类型
    uint32 channel_id = 3;//进入的房间id
    int64 timestamp = 4;//上报时间
}

message ReportMinigameEnterResp { 
}
