syntax = "proto3";

// namespace
option go_package = "golang.52tt.com/protocol/services/officialcert";
package officialcert;

// 枚举值为 1 2 4 8 16 32 ... 位运算
enum ECertAttrType {
  ECertAttrTypeNone = 0;
  ECertAttrTypeImNoLimit = 1; // 达人账号对ta的非关注的粉丝 发IM消息 不限制
}

message OfficialCertInfo {

  uint32 uid = 1;
  string title = 2;
  string intro = 3;
  string style = 4;
  uint32 id = 5;
  uint64 begin_ts = 6;
  uint64 end_ts = 7;
  bool is_use = 8; // 用户是否正在佩戴
  uint32 attribute = 9; // 特殊 权限 属性，ECertAttrType 或运算后得到

  // 大v图标动效
  string certify_special_effect_title = 10; // 带有文字背景
  string certify_special_effect_icon = 11;  // 只有图标
}

message UserOfficialCertInfo {
    bool is_certified = 1;
    OfficialCertInfo cert = 2;
    repeated OfficialCertInfo cert_list = 3;
}

message GetUserOfficialCertReq {
    uint32 uid = 1;
}
message GetUserOfficialCertResp {
    UserOfficialCertInfo info = 2;
}

message ListUserOfficialCertReq {
}
message ListUserOfficialCertResp {
    repeated OfficialCertInfo cert_list = 1;
}
message SetUserOfficialCertReq {
    OfficialCertInfo cert = 1;
}
message SetUserOfficialCertResp {
}

message DelUserOfficialCertReq {
    uint32 uid = 1;
    uint32 id = 2;
}
message DelUserOfficialCertResp {
}

message DelCertInfo {
   uint32 uid = 1;
   uint32 id = 2;
}

message BatchDelOfficalCertsReq {
   repeated DelCertInfo info_list = 1;
}
message BatchDelOfficalCertsResp {
}

message BatchGetUserOfficialCertReq {
    repeated uint32 uid_list = 1;
}
message BatchGetUserOfficialCertResp {
    repeated UserOfficialCertInfo info_list = 1;
}

// 设置用户佩戴的大v认证样式
message SetUserWearCertificationReq {
   uint32 uid = 1;
   uint32 id = 2;
}
message SetUserWearCertificationResp {
}

message GetUserAllOfficialCertsReq {
    uint32 uid = 1;
}
message GetUserAllOfficialCertsResp {
    bool is_certified = 1;
    repeated OfficialCertInfo cert_list = 2;
}

// 获取用户有效的特权属性
message GetUserCertAttributeReq { uint32 uid = 1; }

// 有效的全部特权 ECertAttrType
message GetUserCertAttributeResp { uint32 attr = 1; }

// 查询是否有如下特权
message CheckUserCertAttributeReq {
  uint32 uid = 1;
  uint32 attr = 2;
}

message CheckUserCertAttributeResp { bool ok = 1; }


//新增
// 主理人信息
message DirectorCertInfo {
   uint32 uid = 1;                // uid
   string ttid = 2;               // ttid
   string nickname = 3;           // 昵称
   uint32 cooperation_type = 4;   // 合作类型 1签约 2商单 3临时
   uint32 status = 5;             // 主理人状态 0否 1是
   string introduce = 6;          // 介绍
   string manager = 7;            // 负责人
   uint64 ts = 8;                 // 创建时间
}

message UserDirectorCertInfo  {
    bool is_certified = 1;     // 是否主理人
    DirectorCertInfo cert = 2;
}

message GetUserDirectorCertReq {
    uint32 uid = 1;
}

message GetUserDirectorCertResp {
    UserDirectorCertInfo info = 1;
}

message BatchGetUserDirectorCertReq {
    repeated uint32 uid_list = 1;
}

message BatchGetUserDirectorCertResp {
    repeated UserDirectorCertInfo info_list = 1;
}

message SetUserDirectorCertReq {
    uint32 uid = 1;              // uid
    uint32 status = 2;           // 主理人状态  1通过 0失效
    uint32 cooperation_type = 3; // 合作类型 1签约 2商单 3临时
    string introduce = 4;        // 介绍
    string manager = 5;          // 负责人
}

message SetUserDirectorCertResp {
}

message AddUserDirectorCertReq {
    uint32 uid = 1;                // uid
    uint32 cooperation_type = 2;   // 合作类型 1签约 2商单 3临时
}

message AddUserDirectorCertResp {
}

message ListUserDirectorCertReq{
    uint32 offset = 1; 
    uint32 page_size = 2; 
    string ttid = 3; 
    string nickname = 4; 
    string manager = 5; 
    uint32 cooperation_type = 6; 
    uint32 status = 7; 
}
message ListUserDirectorCertResp{
   uint32 total = 1;
   repeated UserDirectorCertInfo info_list = 2;
}


service OfficialCert {

  rpc GetUserOfficialCert(GetUserOfficialCertReq) returns (GetUserOfficialCertResp) {
  }

  rpc ListUserOfficialCert(ListUserOfficialCertReq) returns (ListUserOfficialCertResp) {
  }

  // 包含主理人style
  rpc ListUserOfficialCertV2(ListUserOfficialCertReq) returns (ListUserOfficialCertResp) {
  }

  rpc SetUserOfficialCert(SetUserOfficialCertReq) returns (SetUserOfficialCertResp) {
  }

  rpc DelUserOfficialCert(DelUserOfficialCertReq) returns (DelUserOfficialCertResp) {
  }

  rpc BatchGetUserOfficialCert(BatchGetUserOfficialCertReq) returns (BatchGetUserOfficialCertResp) {
  }

  rpc SetUserWearCertification(SetUserWearCertificationReq) returns (SetUserWearCertificationResp) {
  }

  rpc GetUserAllOfficialCerts(GetUserAllOfficialCertsReq) returns (GetUserAllOfficialCertsResp) {
  }

  rpc GetUserCertAttribute(GetUserCertAttributeReq) returns (GetUserCertAttributeResp) {
  }

    rpc CheckUserCertAttribute(CheckUserCertAttributeReq) returns (CheckUserCertAttributeResp) {
  }

  rpc BatchDelOfficalCerts(BatchDelOfficalCertsReq) returns (BatchDelOfficalCertsResp) {
  }

     // 获取用户的主理人认证信息
    rpc GetUserDirectorCert(GetUserDirectorCertReq) returns (GetUserDirectorCertResp){
    }
    
    // 批量获取用户的主理人认证信息
    rpc BatchGetUserDirectorCert(BatchGetUserDirectorCertReq) returns (BatchGetUserDirectorCertResp){
    }
    
    // 后台创建主理人
    rpc AddUserDirectorCert(AddUserDirectorCertReq) returns (AddUserDirectorCertResp){
    }
    
    // 后台配置主理人相关信息
    rpc SetUserDirectorCert(SetUserDirectorCertReq) returns (SetUserDirectorCertResp){
    }
    
    // 获取主理人列表
    rpc ListUserDirectorCert(ListUserDirectorCertReq) returns (ListUserDirectorCertResp){
    }

}

//message OfficialCertCacheData {
//    bool is_certified = 1;
//    OfficialCertInfo cert = 2;
//}




