syntax = "proto3";

package livelink_svr;

option go_package = "golang.52tt.com/protocol/services/livelink-svr";

service LivelinkSvr {
    rpc GetUidByPhone(GetUidByPhoneReq) returns (GetUidByPhoneResp) {}
    rpc AddUidByPhone(AddUidByPhoneReq) returns (AddUidByPhoneResp) {}
}

message AddUidByPhoneReq {
    string phone = 1;
    uint32 uid = 2;
}
message AddUidByPhoneResp {
}

message GetUidByPhoneReq {
    string phone = 1;
}

message GetUidByPhoneResp {
    uint32 uid = 1;
}
