syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channel-red-packet";
package channel_red_packet;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
import "tt/quicksilver/you-know-who/you-know-who.proto";

service ChannelRedPacket {
  // 获取红包配置列表
  rpc GetRedPacketConf(GetRedPacketConfReq) returns (GetRedPacketConfResp) {}
  // 增加红包配置
  rpc AddRedPacketConf(AddRedPacketConfReq) returns (AddRedPacketConfResp) {}
  // 获取红包配置列表
  rpc DelRedPacketConf(DelRedPacketConfReq) returns (DelRedPacketConfResp) {}
  // 获取红包配置列表
  rpc GetRedPacketConfById(GetRedPacketConfByIdReq) returns (GetRedPacketConfByIdResp) {}

  // 检查用户能否发红包
  rpc CheckIfCanSendRedPacket(CheckIfCanSendRedPacketReq) returns (CheckIfCanSendRedPacketResp) {}
  // 发放红包
  rpc SendRedPacket(SendRedPacketReq) returns (SendRedPacketResp) {}
  // 获取房间当前红包列表
  rpc GetRedPacketList(GetRedPacketListReq) returns (GetRedPacketListResp) {}
  // 上报红包雨点击个数
  rpc ReportRedPacketClickCnt(ReportRedPacketClickCntReq) returns (ReportRedPacketClickCntResp) {}
  // 批量获取正在发红包的房间
  rpc BatGetRedPacketChannel(BatGetRedPacketChannelReq) returns (BatGetRedPacketChannelResp) {}


  // 获取订单汇总信息(对账用)
  rpc GetRedPacketOrderTotal(GetRedPacketOrderTotalReq) returns (GetRedPacketOrderTotalResp) {}
  // 获取奖励汇总信息(对账用)
  rpc GetRedPacketAwardTotal(GetRedPacketAwardTotalReq) returns (GetRedPacketAwardTotalResp) {}

  /*********对账接口V2**********/
  // 发放包裹数据对账
  rpc GetAwardTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetAwardOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
  // T豆消费数据对账
  rpc GetConsumeTotalCount(ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
  rpc GetConsumeOrderIds(ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
}


// 订单状态
enum OrderStatus {
  Paying = 0;     // 支付中
  Using = 1;      // 使用中
  Settling = 2 ;  // 结算中
  Awarding = 3;   // 发奖中
  Finish = 4;     // 订单完成
  Fail = 5;       // 订单失败
}

message RedPacketConf {
  uint32 red_packet_id = 1;
  repeated uint32 gift_id_list = 2;
  uint32 total_price = 3;         // 红包价值
  uint32 begin_time = 4;          // 配置生效时间
  uint32 end_time = 5;            // 配置过期时间
}

message RedPacketInfo {
  string order_id = 1;  // 红包订单号
  RedPacketConf red_packet_conf = 2;
  string public_msg = 3;            // 公屏文案

  uint32 begin_time = 4;            // 红包雨开始时间
  uint32 end_time = 5;              // 红包雨结束时间
  uint32 sender_uid = 6;            // 金主爸爸
  uint32 channel_id = 7;
  uint32 outside_time = 8;

  youknowwho.UKWPersonInfo ukw_info = 9;
}

// 增加红包配置
message AddRedPacketConfReq {
  repeated uint32 bg_id_list = 1; // 包裹id列表
  uint32 begin_time = 2;          // 配置生效时间
  uint32 end_time = 3;            // 配置过期时间
  uint32 total_price = 4;         // 红包价值
}

message AddRedPacketConfResp {}

// 删除红包配置
message DelRedPacketConfReq {
  uint32 red_packet_id = 1;
}

message DelRedPacketConfResp {}

// 通过id获取红包配置
message GetRedPacketConfByIdReq {
  uint32 op_uid = 1;
  uint32 red_packet_id = 2;
}

message GetRedPacketConfByIdResp {
  RedPacketConf conf = 1;
}

// 获取红包配置列表
message GetRedPacketConfReq {
  uint32 op_uid = 1;
}

message GetRedPacketConfResp {
  repeated RedPacketConf list = 1;
  repeated string default_public_msg_list = 2;
  string invalid_desc = 3;  // 无效红包描述
}

// 检查用户能否发红包
message CheckIfCanSendRedPacketReq {
  uint32 op_uid = 1;
  uint32 channel_id = 2;
}

message CheckIfCanSendRedPacketResp {
  bool ok = 1;
}

// 发红包
message SendRedPacketReq {
  uint32 op_uid = 1;
  uint32 channel_id = 2;
  uint32 red_packet_id = 3;
  string public_msg = 4;    // 公屏文案
  uint32 outside_time = 5;  // 不填则为当前时间
}

message SendRedPacketResp {}

// 获取房间排队的红包列表
message GetRedPacketListReq {
  uint32 op_uid = 1;
  uint32 channel_id = 2;
}

message GetRedPacketListResp {
  repeated RedPacketInfo list = 1;
  uint32 settle_duration_sec = 2;
  uint32 server_ts = 3;
  string rule_desc = 4;
}

// 上报用户红包雨点击次数
message ReportRedPacketClickCntReq {
  uint32 uid = 1;
  uint32 channel_id = 2;
  string order_id = 3;  // 红包订单号
  uint32 cnt = 4;
}

message ReportRedPacketClickCntResp {}


message GetRedPacketOrderTotalReq {
  uint32 begin_time = 1;
  uint32 end_time = 2;
}

message GetRedPacketOrderTotalResp {
  uint32 total_cnt = 1;
  uint32 total_price = 2;
}

message GetRedPacketAwardTotalReq {
  uint32 begin_time = 1;
  uint32 end_time = 2;
}

message GetRedPacketAwardTotalResp {
  uint32 total_cnt = 1;
  uint32 total_price = 2;
}


message BatGetRedPacketChannelReq {
  repeated uint32 channel_ids = 1;
}
message BatGetRedPacketChannelResp {
  repeated uint32 channel_ids = 1;
}