syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/guild-cooperation-http";
package guild_cooperation_http;

// 公会入驻（合作库）HTTP接口

// 合作库类型
enum CooperationType {
  // from ga:ChannelType
  CTypeInvalid = 0;
  CTypeAmuse = 4; // 娱乐房
  CTypeYuyin = 7; // 语音房
}

// 申请步骤
enum ApplicationStep {
  StepWaitSubmit = 0; // 待提交资料
  StepSubmit = 3; // 提交资料
  StepCommunicate = 5; // 微信沟通
  StepCooperation = 7; // 申请合作
}

// 申请操作
enum ApplicationOperation {
  OperationInvalid = 0;
  OperationApproval = 1; // 通过
  OperationReject = 3; // 拒绝
}

// 申请来源
enum SourcePlatform {
  PLATFORM_INVALID = 0;
  PLATFORM_APP = 1; // 端内
  PLATFORM_WEB = 2; // 官网
}

message CooperationApplication {
  string application_id = 1; // 申请编号
  string uid = 2;
  string ttid = 3;
  CooperationType cooperation_type = 4; // 合作类型
  string wechat_id = 5; // 微信号
  uint32 create_time = 6; // 申请时间
  string legal_person = 9; // 法人
  string corporation = 10; // 公司名称
  string corporation_code = 11; // 公司信息码
  string bank_account = 12; // 对公帐户
  repeated string business_license_photos = 13; // 营业执照url
  repeated string bill_history_photos = 14; // 收款记录url
  ApplicationStep application_step = 15; // 当前步骤
  ApplicationOperation application_operation = 16; // 当前操作
  string external_platform = 18; // 站外平台
  string approve_desc = 21; // 审批描述
  uint32 reapply_time = 22; // 重新申请时间
}

// 上传文件接口
message UploadFileReq {
  // binary
}
message UploadFileResp {
  string file_id = 1;
}

// 获取申请资料
message GetApplicationReq {
  string application_id = 1; // 申请ID
  string uid = 2;
}
message GetApplicationResp {
  CooperationApplication application = 1;
}

message GetCooperationInfoReq {
  string uid = 1;
}
message SubmitApplicationReq {
  string uid = 1;
  string external_platform = 2; // 站外平台
  string wechat = 3; // 微信号
  string corporation = 4; // 公司名称
  string legal_person = 5; // 法人
  string corporation_code = 6; // 公司地址
  string bank_account = 7; // 银行账号
  repeated string business_license_photos = 8; // 营业执照url
  repeated string bill_history_photos = 9; // 收款记录url
  CooperationType cooperation_type = 10; // 合作类型（二期废弃）
  repeated CooperationType cooperation_types = 11; // 合作类型
  string phone = 12; // 手机号
  SourcePlatform source_platform = 13; // 来源
}

// 创建公会
message CreateGuildReq {
  string uid = 1;
  string guild_name = 2; // 公会名称
}

message ApplyCooperationReq {
  string uid = 1;
  uint32 guild_id = 2; // 公会ID
  string application_id = 3; // 申请ID
}

// 获取公会信息
message GetGuildInfoReq {
  string uid = 1;
}
