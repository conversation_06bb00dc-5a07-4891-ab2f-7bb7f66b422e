syntax = "proto3";

// namespace
option go_package = "golang.52tt.com/protocol/services/officialcert-go";
package officialcert_go;

import "tt/quicksilver/extension/options/options.proto";

// 枚举值为 1 2 4 8 16 32 ... 位运算
enum ECertAttrType {
  ECertAttrTypeNone = 0;
  ECertAttrTypeImNoLimit = 1; // 达人账号对ta的非关注的粉丝 发IM消息 不限制
}

enum LocationType {
  Personal_Page = 0; // 个人主页
  Channel = 1;     // 房间个人资料卡
  Anchor_Card = 2;   // 主播资料卡
  PersonalityDress = 3; // 个性装扮 图标
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum STATUS_TYPE {
  STATUS_TYPE_UNUSE = 0; // 未佩戴 0,1 表示 已生效
  STATUS_TYPE_USE = 1; // 佩戴中
  STATUS_TYPE_UNEFFECT = 2; // 未生效
  STATUS_TYPE_DEL = 3; // 已删除
  STATUS_TYPE_OVERDUE = 4; // 已过期
}

message OfficialCertInfo {

  uint32 uid = 1;
  string title = 2;
  string intro = 3;
  string style = 4;
  uint32 id = 5;
  uint64 begin_ts = 6;
  uint64 end_ts = 7;
  bool is_use = 8; // 用户是否正在佩戴
  uint32 attribute = 9; // 特殊 权限 属性，ECertAttrType 或运算后得到

  // 大v图标动效
  string certify_special_effect_title = 10; // 带有文字背景
  string certify_special_effect_icon = 11;  // 只有图标

  uint32 status = 12; // STATUS_TYPE
  uint32 v_id = 13;

  string ttid = 14;
  string handler = 15;
}

message UserOfficialCertInfo {
  bool is_certified = 1;
  OfficialCertInfo cert = 2;
  repeated OfficialCertInfo cert_list = 3;
}

message GetUserOfficialCertReq {
  uint32 uid = 1;
  uint32 request_type = 2; // see CertType
}
message GetUserOfficialCertResp {
  UserOfficialCertInfo info = 1;
}

message ListUserOfficialCertReq {
}
message ListUserOfficialCertResp {
  repeated OfficialCertInfo cert_list = 1;
}
message SetUserOfficialCertReq {
  OfficialCertInfo cert = 1;
}
message SetUserOfficialCertResp {
}

message DelUserOfficialCertReq {
  uint32 uid = 1;
  uint32 id = 2;
}
message DelUserOfficialCertResp {
}

message DelCertInfo {
  uint32 uid = 1;
  uint32 id = 2;
}

message BatchDelOfficalCertsReq {
  repeated DelCertInfo info_list = 1;
}
message BatchDelOfficalCertsResp {
}

message BatchGetUserOfficialCertReq {
  repeated uint32 uid_list = 1;
}
message BatchGetUserOfficialCertResp {
  repeated UserOfficialCertInfo info_list = 1;
}

// 设置用户佩戴的大v认证样式
message SetUserWearCertificationReq {
  uint32 uid = 1;
  uint32 id = 2;
}
message SetUserWearCertificationResp {
}

message GetUserAllOfficialCertsReq {
  uint32 uid = 1;
  uint32 request_type = 2; // see CertType
}
message GetUserAllOfficialCertsResp {
  bool is_certified = 1;
  repeated OfficialCertInfo cert_list = 2;
}

// 获取用户有效的特权属性
message GetUserCertAttributeReq {uint32 uid = 1;}

// 有效的全部特权 ECertAttrType
message GetUserCertAttributeResp {uint32 attr = 1;}

// 查询是否有如下特权
message CheckUserCertAttributeReq {
  uint32 uid = 1;
  uint32 attr = 2;
}

message CheckUserCertAttributeResp {bool ok = 1;}


//新增
// 主理人信息
message DirectorCertInfo {
  uint32 uid = 1;                // uid
  string ttid = 2;               // ttid
  string nickname = 3;           // 昵称
  uint32 cooperation_type = 4;   // 合作类型 1签约 2商单 3临时
  uint32 status = 5;             // 主理人状态 0否 1是
  string introduce = 6;          // 介绍
  string manager = 7;            // 负责人
  uint64 ts = 8;                 // 创建时间
}

message UserDirectorCertInfo  {
  bool is_certified = 1;     // 是否主理人
  DirectorCertInfo cert = 2;
}

message GetUserDirectorCertReq {
  uint32 uid = 1;
}

message GetUserDirectorCertResp {
  UserDirectorCertInfo info = 1;
}

message BatchGetUserDirectorCertReq {
  repeated uint32 uid_list = 1;
}

message BatchGetUserDirectorCertResp {
  repeated UserDirectorCertInfo info_list = 1;
}

message SetUserDirectorCertReq {
  uint32 uid = 1;              // uid
  uint32 status = 2;           // 主理人状态  1通过 0失效
  uint32 cooperation_type = 3; // 合作类型 1签约 2商单 3临时
  string introduce = 4;        // 介绍
  string manager = 5;          // 负责人
}

message SetUserDirectorCertResp {
}

message AddUserDirectorCertReq {
  uint32 uid = 1;                // uid
  uint32 cooperation_type = 2;   // 合作类型 1签约 2商单 3临时
}

message AddUserDirectorCertResp {
}

message ListUserDirectorCertReq{
  uint32 offset = 1;
  uint32 page_size = 2;
  string ttid = 3;
  string nickname = 4;
  string manager = 5;
  uint32 cooperation_type = 6;
  uint32 status = 7;
}
message ListUserDirectorCertResp{
  uint32 total = 1;
  repeated UserDirectorCertInfo info_list = 2;
}


service OfficialCert {
  option (service.options.old_package_name) = "officialcert.OfficialCert";

  rpc GetUserOfficialCert(GetUserOfficialCertReq) returns (GetUserOfficialCertResp) {
  }

  rpc ListUserOfficialCert(ListUserOfficialCertReq) returns (ListUserOfficialCertResp) {
  }

  // 包含主理人style
  rpc ListUserOfficialCertV2(ListUserOfficialCertReq) returns (ListUserOfficialCertResp) {
  }

  rpc SetUserOfficialCert(SetUserOfficialCertReq) returns (SetUserOfficialCertResp) {
  }

  rpc DelUserOfficialCert(DelUserOfficialCertReq) returns (DelUserOfficialCertResp) {
  }

  rpc BatchGetUserOfficialCert(BatchGetUserOfficialCertReq) returns (BatchGetUserOfficialCertResp) {
  }

  rpc SetUserWearCertification(SetUserWearCertificationReq) returns (SetUserWearCertificationResp) {
  }

  rpc GetUserAllOfficialCerts(GetUserAllOfficialCertsReq) returns (GetUserAllOfficialCertsResp) {
  }

  rpc GetUserCertAttribute(GetUserCertAttributeReq) returns (GetUserCertAttributeResp) {
  }

  rpc CheckUserCertAttribute(CheckUserCertAttributeReq) returns (CheckUserCertAttributeResp) {
  }

  rpc BatchDelOfficalCerts(BatchDelOfficalCertsReq) returns (BatchDelOfficalCertsResp) {
  }

  // 获取用户的主理人认证信息
  rpc GetUserDirectorCert(GetUserDirectorCertReq) returns (GetUserDirectorCertResp){
  }

  // 批量获取用户的主理人认证信息
  rpc BatchGetUserDirectorCert(BatchGetUserDirectorCertReq) returns (BatchGetUserDirectorCertResp){
  }

  // 后台创建主理人
  rpc AddUserDirectorCert(AddUserDirectorCertReq) returns (AddUserDirectorCertResp){
  }

  // 后台配置主理人相关信息
  rpc SetUserDirectorCert(SetUserDirectorCertReq) returns (SetUserDirectorCertResp){
  }

  // 获取主理人列表
  rpc ListUserDirectorCert(ListUserDirectorCertReq) returns (ListUserDirectorCertResp){
  }


  /*新运营后台接口*/
  rpc BatchAddCertItem(BatchAddCertItemReq) returns (BatchAddCertItemResp) {}

  rpc ListCertItem(ListCertItemReq) returns (ListCertItemResp) {}

  rpc BatchGrantOfficialCert(BatchGrantOfficialCertReq) returns (BatchGrantOfficialCertResp) {}

  rpc DelOfficialCert(DelOfficialCertReq) returns (DelOfficialCertResp) {}

  rpc ListOfficialCert(ListOfficialCertReq) returns (ListOfficialCertResp) {}

  // 靓号原有接口
  // 靓号管理
  rpc UpdateAlias(UpdateAliasReq) returns (UpdateAliasResp) {}
  rpc ListAlias(ListAliasReq) returns (ListAliasResp) {}

  // 创建账号
  rpc CreateUserByAlias(CreateUserByAliasReq) returns (CreateUserByAliasResp) {}


  // 靓号认证相关
  rpc BatchAddAliasSign(BatchAddAliasSignReq) returns (BatchAddAliasSignResp) {}
  rpc BatchDelAliasSign(BatchDelAliasSignReq) returns (BatchDelAliasSignResp) {}
  rpc BatchCheckAliasSign (BatchCheckAliasSignReq) returns (BatchCheckAliasSignResp) {}
  rpc BatchGetAliasSign (BatchGetAliasSignReq) returns (BatchGetAliasSignResp) {}
}

/*
大v类型
ent 红v
ugc 蓝v
anchor 橙v
official 官方人员
*/

message CertifyItemInfo {
  uint32 v_id = 1;
  string style = 2; // 大v类型
  string title = 3;
  string handler = 4;
  uint32 create_ts = 5;
}

message BatchAddCertItemReq {
  repeated CertifyItemInfo info = 1;
  string handler = 2;
}
message BatchAddCertItemResp {
}

message ListCertItemReq {
  repeated uint32 v_ids = 1;
  string title = 2;
  string style = 3;
  uint32 page = 4; // 第一页从0开始
  uint32 page_num = 5;
}

message ListCertItemResp {
  uint32 total = 1;
  repeated CertifyItemInfo list = 2;
}


message GrantOfficialCertInfo {
  uint32 uid = 1;
  uint32 v_id = 2;
  string intro = 3;    // 简介
  uint64 show_day = 4; // 展示天数
}

message BatchGrantOfficialCertReq {
  repeated GrantOfficialCertInfo list = 1;
  uint64 begin_ts = 2;  // 开始时间
  uint32 attribute = 3; // 特权属性 1有特权
  string handler = 4;
}
message BatchGrantOfficialCertResp {
}

// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_TYPE {
  QUERY_TYPE_VID = 0;
  QUERY_TYPE_UID = 1;
}
message ListOfficialCertReq {
  uint32          query_type = 1;
  repeated uint32 req_ids = 2;
  uint32          page = 3; // 第一页从0开始
  uint32          page_num = 4;
  uint64          begin_ts = 5;
  uint64          end_ts = 6;
  uint32          status = 7; // 0-所有，1-已生效，2-未生效，3-已删除，4-已过期
  repeated string certify_style_list = 8; // 大v类型

  repeated uint32 vids = 9;
  repeated uint32 uids = 10;
}
message ListOfficialCertResp {
  uint32 total = 1;
  repeated OfficialCertInfo list = 2;
}

message DelOfficialCertReq {
  repeated uint32 id = 1;
  string handler = 2;
}
message DelOfficialCertResp {
}



message UpdateAliasReq {
  uint32 uid = 1;
  string alias = 2;
  string handler = 3;
  bool with_sign = 4;
  string alias_sign_level = 5;
}

message UpdateAliasResp {
}

// 更新房间的displayID
message ChangeDisplayIDReq
{
  uint32 channel_id = 1;
  string new_channel_view_id = 2;
  string handler = 3;
}

message ChangeDisplayIDResp {
}



// buf:lint:ignore ENUM_PASCAL_CASE
enum QUERY_ALIAS_TYPE {
  QUERY_ALIAS_TYPE_UID = 0; // 个人靓号
  QUERY_ALIAS_TYPE_CHANNELID = 1; // 房间靓号
}

message ListAliasReq {
  uint32 query_type = 1; // QUERY_ALIAS_TYPE

  string alias = 2;   // 个人靓号
  string ttid = 3;    // 原TTID
  string channel_view_id = 4;  // 原房间ID
  string new_channel_view_id = 5; // 房间靓号
  uint32 begin_time = 6;
  uint32 end_time = 7;

  uint32 page = 8; // 第一页从0开始
  uint32 page_num = 9;
  uint32 uid = 10;
  string alias_sign_level = 11;
}


message ListAliasResp {
  uint32 total = 1;
  repeated AliasInfo list = 2;
}

message AliasInfo {
  enum HandlerType {
    HANDLER_TYPE_UNSPECIFIED = 0;
    // 靓号更换
    HANDLER_TYPE_UPDATE = 1;
    // 创建账号
    HANDLER_TYPE_CREATE = 2;
  }

  uint32 uid = 1;
  string ttid = 2;   // 原TTID
  string alias = 3;  // 个人靓号
  string nickname = 4;

  uint32 channel_id = 5;
  string channel_view_id = 6;     // 原房间ID
  string new_channel_view_id = 7; // 房间靓号
  string channel_name = 8;
  uint32 channel_type = 9;

  string handler = 10;
  uint32 handler_time = 11;

  // 发放方式
  HandlerType handler_type = 12;
  // 初始密码
  string init_pwd = 13;

  uint32 alias_sign_status = 14;
  uint32 handler_time_sign = 15; // 标识的操作时间
  string handler_sign = 16; // 标识的操作人
  string alias_sign_level = 17; // 靓号等级
}

enum AliasSignStatus{
  AliasSignStatusNone = 0;
  AliasSignStatusActivate = 1; // 使用中
  AliasSignStatusRecycled = 2; // 已回收
}

message CreateUserByAliasReq {
  string alias = 1;
  string handler = 2;
  bool with_sign = 3;
  string alias_sign_level = 4;
}

message CreateUserByAliasResp {
}

message BatchAddAliasSignReq{
  repeated uint32 alias_sign_list = 1;
  string handler = 2;
  repeated AliasSignInfo alias_sign_info_list = 3;
}

message AliasSignInfo{
  uint32 uid = 1;
  AliasSignStatus status = 2;
  string level = 3;
  bytes alias_extend_info = 4; // 靓号额外信息 ，see 客户端协议 contact.proto AliasExtendInfo
}

message BatchAddAliasSignResp{
  string err_content = 1;
}

message BatchDelAliasSignReq{
  repeated uint32 uid = 1;
  string handler = 2;
}

message BatchDelAliasSignResp{
  string err_content = 1;
}

message BatchCheckAliasSignReq{
  enum CheckType{
    CheckTypeAdd = 0;
    CheckTypeRecycle = 1;
  }
  repeated uint32 uid = 1;
  CheckType check_type = 2;  // 0 增加 1 回收
}

message BatchCheckAliasSignResp{
  string err_content = 1;
}

message BatchGetAliasSignReq{
  repeated uint32 uid = 1;
}

message BatchGetAliasSignResp{
  repeated AliasSignInfo alias_sign_list = 1;
}