syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/super-channel";
package super_channel;

message GetChannelMemberListReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
    uint32 start = 3;
    uint32 size = 4;
}

message MemberInfo {
    uint32 uid = 1;
    uint32 rank = 2; //排名
    uint32 custom = 3; //消费
}

message GetChannelMemberListResp {
    repeated MemberInfo member_info = 1;
}

message BatchGetChannelMemberCustomReq {
    uint32 channel_id = 1;
    repeated uint32 uid_list = 2;
}
message BatchGetChannelMemberCustomResp {
    map<uint32, uint32> member_custom = 1;
}
message BatchGetChannelMemberSizeReq {
    repeated uint32 channel_list = 1;
}
message BatchGetChannelMemberSizeResp {
    map<uint32, uint32> member_size = 1;
}

service SuperChannelMember {
    rpc GetChannelMemberList (GetChannelMemberListReq) returns (GetChannelMemberListResp) {
    }

    rpc BatchGetChannelMemberCustom (BatchGetChannelMemberCustomReq) returns (BatchGetChannelMemberCustomResp) {
    }

    rpc BatchGetChannelMemberSize (BatchGetChannelMemberSizeReq) returns (BatchGetChannelMemberSizeResp) {
    }
}