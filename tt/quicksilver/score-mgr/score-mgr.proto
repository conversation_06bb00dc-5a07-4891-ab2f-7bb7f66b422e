syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/score-mgr";

package score_mgr;

service ScoreMgr {
  //上传积分回滚任务
  rpc AddScoreRollTask(AddScoreRollTaskReq) returns (AddScoreRollTaskResp) {}
  //查看积分回滚任务
  rpc GetScoreRollTask(GetScoreRollTaskReq) returns (GetScoreRollTaskResp) {}
  //查看订单积分
  rpc GetOrderScore(GetOrderScoreReq) returns (GetOrderScoreResp) {}
}

message GetOrderScoreReq {
  ScoreType score_type = 1;
  repeated string hb_order_ids = 2;
}

message OrderScoreData {
  string hb_order_id = 1;
  int64 uid = 2; //如果订单不存在，uid==-1
  uint32 roll_score = 3;  //返还积分
}

message GetOrderScoreResp {
  repeated OrderScoreData list = 1;
}

enum ScoreType {
  Unknown = 0;
  Present = 1;
  Anchor = 2;
  MaskPk = 3;
  Knight = 4;
  ESport = 5;
}

message AddScoreRollTaskReq {
  ScoreType score_type = 1;
  string hb_order_id = 2; //货币组订单号
  uint32 uid = 3;
  string reason = 4;  //原因
  string operator = 5;  //操作人
}

message AddScoreRollTaskResp {
  int32 err_code = 1; //0正常，1失败，2重复订单
  string err_msg = 2; //错误信息
}

message GetScoreRollTaskData {
  string hb_order_id = 1; //货币组订单号
  uint32 uid = 2;
  string ttid = 3;
  string nick_name = 4; //用户昵称
  uint32 roll_score = 5;  //返还积分
  string reason = 6;  //原因
  int64 create_time = 7;  //创建时间
  string operator = 8;  //操作人
}

message GetScoreRollTaskReq {
  ScoreType score_type = 1;
  repeated uint32 uid_list = 2; //uid列表
  int64 begin_time = 3; //开始时间，秒
  int64 end_time = 4; //结束时间，秒
  uint32 offset = 5;  //偏移量，从0开始
  uint32 limit = 6;   //每页个数
}

message GetScoreRollTaskResp {
  repeated GetScoreRollTaskData list = 1;
  uint32 total = 2;
}