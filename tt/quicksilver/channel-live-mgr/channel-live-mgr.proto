syntax = "proto3";

option go_package = "golang.52tt.com/protocol/services/channellivemgr";

package channellivemgr;

import "tt/quicksilver/reconcile-v2/reconcile-v2.proto";
import "tt/quicksilver/apicenter-go/channel-live-api.proto";

//开启直播房权限

//排行榜，首杀那些需要神秘人的当时状态信息，所以定义一个神秘人信息结构
message UkwInfo {
    uint32 uid = 1;  // 真uid
    string account = 2;
    string nickname = 3;
    uint32 level = 4;
    string medal = 5; // 神秘人勋章
    string head_frame = 6; // 神秘人头像框
    uint32 fake_uid = 7;  // 假uid
}

//直播权限结构
message ChannelLiveInfo {
    uint32 uid = 1; //
    uint32 channel_id = 2; //
    uint32 begin_time = 3; //直播权限开始时间
    uint32 end_time = 4; //直播权限结束时间
    uint32 create_time = 5;
    string oper_name = 6; // 开通权限运营人员
    uint32 tag_id = 7; //标签ID
    bool pk_authority = 8; //PK权限，废弃
    uint32 update_time = 9;//
    uint32 authority = 10; //各种权限位标志//
}

//给主播开通直播房权限
message SetChannelLiveInfoReq {
    ChannelLiveInfo channel_live_info = 1;
}

message SetChannelLiveInfoResp {
}

//给主播开通直播房权限，压测用精简了一些无关操作
message SetChannelLiveInfoForTestReq {
    ChannelLiveInfo channel_live_info = 1;
}

message SetChannelLiveInfoForTestResp {
    uint32 channel_id = 1;
}

//设置直播房tagID
message SetChannelLiveTagReq {
    uint32 channel_id = 1;
    uint32 tag_id = 2;
    uint32 level = 3;
    uint32 uid = 4;
    string oper_user = 5;
}

message SetChannelLiveTagResp {

}

//回收主播的主播房权限
message DelChannelLiveInfoReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    string reason = 3;
    string oper_user = 4;
}

message DelChannelLiveInfoResp {
}

//开通主播PK权限
message UpatePkAuthorityReq {
    uint32 channel_id = 1;
    uint32 authority = 2;
}

message UpatePkAuthorityResp {
}

message GetChannelLiveInfoReq {
    uint32 uid = 1;
    bool expire = 2; //是否需要过期权限
    uint32 channel_id = 3; //
}

message GetChannelLiveInfoResp {
    ChannelLiveInfo channel_live_info = 1;
}

//批量获取主播信息
message BatGetChannelLiveInfoReq {
    repeated uint32 uid_list = 1;
}
message BatGetChannelLiveInfoResp {
    repeated ChannelLiveInfo info_list = 1;
}

enum OperateType {
    InValidOper = 0;
    AddAnchor = 1; // 增加主播
    DelAnchor = 2;  // 回收直播权限
    ModifyTag = 3;  // 改标签
}

// 操作记录
message AnchorOperRecord {
    string ttid = 1;
    uint32 uid = 2;
    string nickname = 3;
    uint32 guild_id = 4;
    string guild_name = 5;
    uint32 tag_id = 6;
    uint32 operate_type = 7;  // see OperateType
    uint32 operate_ts = 8;
    string operate_name = 9;
}

message GetAnchorOperRecordReq {
    uint32 operate_type = 1;  // see  OperateType
    uint32 guild_id = 2;
    repeated uint32 uid_list = 3;
    uint32 page = 4;  // 从 1开始
    uint32 page_size = 5;
}
message GetAnchorOperRecordResp {
    repeated AnchorOperRecord record_list = 1;
    uint32 total_cnt = 2;  // 总数
    uint32 next_page = 3;  // 0表示结束
}

message GetPkAuthorityReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetPkAuthorityResp {
    bool pk_authority = 1;
}

//主播心跳
message ChannelLiveHeartbeatReq {
    uint32 my_uid = 1; //发起心跳用户UID
    uint32 uid = 2; //主播UID
    string account = 3; //主播account
    string nick = 4;
    uint32 channel_id = 5;
    uint64 channel_live_id = 6;
    string channel_client_id = 7; //发起心跳人客户端语音流ID
    uint32 mic_id = 8; //发起心跳人麦位ID
    // EnumChannelLiveStatus status = 5;
}

message ChannelLiveHeartbeatResp {
    ChannelLiveStatus channel_status = 1;
    bool is_change = 2; //是否状态有变化
}

//客户端语音流变化上报
message ReportClientIDChangeReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 mic_id = 3;
    uint64 channel_live_id = 4; //直播ID
    string client_id = 5; //语音流ID
    string channel_video_client_id = 6; // 视频流ID
}

message ReportClientIDChangeResp {
    repeated PkMicSpace mic_list = 1;
}

//设置直播状态
enum EnumChannelLiveStatus {
    CLOSE = 0;
    OPEN = 1;
    PAUSE = 2;
    CONTINUE = 3;
}

message PkMicSpace {
    uint32 uid = 1;
    string account = 2;
    string nick = 3;
    uint32 mic_id = 4; //麦位框ID
    string voice_id = 5; //麦位框语音流
    string channel_video_client_id = 6; // 视频流ID
}

enum AnchorType {
    Anchor_Type_Common = 0;  //普通主播
    Anchor_Type_Virtual = 1; //虚拟主播
}

// 屏幕类型
enum ScreenType {
    Screen_Type_Invalid = 0;
    Screen_Type_Landscape = 1;  // 横屏
    Screen_Type_Portrait = 2;  // 竖屏
}

// 虚拟开播信息
message VirtualLiveInfo {
    uint32 screen_type = 1;  //see ScreenType
}

//直播状态信息
message ChannelLiveStatus {
    uint32 uid = 1;
    string account = 2;
    string nickname = 3;
    uint32 sex = 4;
    uint32 channel_id = 5;
    uint64 channel_live_id = 6;
    //string chanenl_client_id = 7;
    string desc = 8; //开播描述
    repeated PkMicSpace channel_mic_list = 9; //流ID，deprecated
    EnumChannelLiveStatus status = 10;
    int64 fans_cnt = 11; //开播时的粉丝人数
    int64 group_fans_cnt = 12; //开播时的团粉丝人数
    uint32 begin_time = 13; //开播时间
    EnumChannelLivePKStatus pk_status = 14; //PK状态
    EnumPkMatch pk_match_state = 15; //匹配状态
    uint32 anchor_type = 16;  // 主播类型 see AnchorType
    VirtualLiveInfo virtual_live_info = 17;  // 虚拟开播信息
    uint32 pk_type = 18; // pk类型，see EChannelPkType
    uint32 pk_channel_id = 19; //PK对手的房间ID，0没有PK
}

//pk分数统计相关
message ChannelLivePkCommonInfo {
    uint32 a_score = 1;
    uint32 b_score = 2;
    EnumChannelLivePKStatus pk_status = 3;
    uint32 finish_time = 4; //当前阶段结束时间
}

message ChannelLiveStatusInfo {
    ChannelLiveStatus channel_live_status = 1;
    ChannelLiveStatus pk_channel_live_status = 2; //PK对手相关信息，如果正在PK的话
    ChannelLivePkCommonInfo pk_common_info = 3;
    bool is_challenge = 4; //当前用户是PK挑战用户
}

message SetChannelLiveStatusReq {
    uint32 uid = 1;
    string account = 2;
    string nick = 3;
    uint32 sex = 4;
    uint32 channel_id = 5;
    uint64 channel_live_id = 6; //如果是开始直播可以不用填
    string channel_client_id = 7;
    uint32 fans_cnt = 8; //保存开播是的粉丝人数，以便统计本场涨粉人数
    uint32 group_fans_cnt = 9; //粉丝团人数
    EnumChannelLiveStatus status = 10;
    EnumChannelLiveStatus orig_status = 11;
    uint32 anchor_type = 12;  //主播类型，see AnchorType
    VirtualLiveInfo virtual_live_info = 13;  // 虚拟开播信息
}

message SetChannelLiveStatusResp {
    ChannelLiveStatus channel_live_status = 1;
}

//进房取直播状态
message GetChannelLiveStatusReq {
    uint32 uid = 1; //
    uint32 channel_id = 2;
    bool ignore_mem_cache = 3; //true 不要内存缓存，默认内存缓存
}

message GetChannelLiveStatusResp {
    ChannelLiveStatusInfo channel_live_info = 1;
}

//pk申请结构
message Apply {
    uint32 apply_uid = 1;
    uint32 apply_channel_id = 2;
    uint32 apply_time = 3;
}

//连麦操作相关
message ApplyPkReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint64 channel_live_id = 3;
    uint32 target_uid = 4;
    uint32 target_channel_id = 5;
}

message ApplyPkResp {
}

enum EnumApply {
    accept = 0;
    reject = 1;
    delete = 2;
    cancel = 3; //取消PK申请
    apply = 4; //刚申请
}

message HandlerApplyReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    string username = 3;
    string nickname = 4;

    uint32 apply_uid = 5;
    uint32 apply_channel_id = 6;
    string apply_username = 7;
    string apply_nickname = 8;

    EnumApply oper = 9;
}

message HandlerApplyResp {
    uint32 pk_begin_time = 1;
}

message CancelPKApplyReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 apply_uid = 3;
    uint32 apply_channel_id = 4;
}

message CancelPKApplyResp {
}

//取申请PK列表
message GetApplyListReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message GetApplyListResp {
    repeated Apply apply_list = 1;
}

//主播直播记录
message ChannelLiveHistoryRecord {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 begin_time = 3; //开始直播时间戳
    uint32 end_time = 4; //直播持续时间，S为单位，如果还没结束根据客户端5分钟一次计时算
}

message GetChannelLiveHistoryRecordReq {
    uint32 uid = 1;

    uint32 begin_time = 2; //开播时间戳
    uint32 end_time = 3; //结束时间戳
}

message GetChannelLiveHistoryRecordResp {
    repeated ChannelLiveHistoryRecord record_list = 1;
}

//主播直播时长，有效天
message ChannelLiveRecord {
    uint32 uid = 1;
    uint32 valid_day = 2; //有效天，当天直播时间》=一小时
    uint32 live_time = 3; //直播时间
    bool first_live = 4; //是否是首次开播
    int64 first_live_time = 5; //首次开播时间
}

message BatchGetChannelLiveRecordReq {
    repeated uint32 uid_list = 1;
    uint32 begin_time = 2;
    uint32 end_time = 3;
}

message BatchGetChannelLiveRecordResp {
    repeated ChannelLiveRecord record_list = 1;
}

//查主播给定时间段内的直播记录
message BatchGetChannelLiveRecoredReq {
    uint32 begin_time = 1; //开播时间戳
    uint32 end_time = 2; //结束时间戳
}

message BatchGetChannelLiveRecoredResp {
    repeated ChannelLiveHistoryRecord record_list = 1;
}

message BatchGetChannelLiveStatusReq {
    repeated uint32 uid_list = 1;
    bool get_all = 2; //拿所有当前正在直播的房间信息
    repeated uint32 channel_list = 3; //
}

message BatchGetChannelLiveStatusResp {
    repeated ChannelLiveStatusInfo channel_live_info_list = 1;
}

message GetChannelLiveAvgReq {
    uint32 uid = 1;
}

message GetChannelLiveAvgResp {
    float avg_score7 = 1;
    float avg_score30 = 2;
    float live_day_cnt7 = 3;
    float live_score7 = 4;
    float live_day_cnt30 = 5;
    float live_score30 = 6;
}


enum EnumChannelLivePKStatus {
    IDLE = 0; //无状态
    BEGIN = 1; //开局阶段，可完成首杀
    TOOL = 2; //道具阶段
    LAST = 3; //最后一分钟
    PUNISH = 4; //惩罚阶段
    FINISH = 5; //结束阶段 双方主播依然连麦
}

enum EnumPkMatch {
    PKM_Match_Close = 0;  //因为暂停直播等原因，匹配暂停
    PKM_Matching = 1; //正在匹配
    PKM_Match_Success = 2; //匹配成功
}

//主播设置PK状态接口
message SetPkStatusReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
    EnumChannelLivePKStatus status = 4;
}

message SetPkStatusResp {
    uint32 target_channel_id = 1;
    uint32 begin_time = 2;
}

//主播中心接口
message LiveDayData {
    string date = 1; //日期
    uint32 gift_value = 2; //直播流水
    uint32 send_gift_user_cnt = 3; //付费人数
    uint32 live_time = 4; //直播时长，秒单位
    uint32 audience_cnt = 5; //直播观众
    uint32 anchor_gift_value = 6; //主播流水
    uint32 add_fans = 7; //直播时增加的粉丝数
    uint32 add_group_fans = 8; //直播时增加的团粉丝数
    uint32 begin_time = 9; //
}

message GetChannelLiveTotalDataReq {
    uint32 uid = 1; //主播UID

    uint32 begin_time = 2;
    uint32 end_time = 3;
}

message GetChannelLiveTotalDataResp {
    uint64 total_gift_value = 1; //总流水
    repeated LiveDayData day_date_list = 2;
}

message LiveTotalDayData {
    uint32 uid = 1; // 主播uid
    uint64 total_gift_value = 2; //总流水
    repeated LiveDayData day_date_list = 3;
}

message BatchGetChannelLiveTotalDataReq {
    repeated uint32 uid_list = 1; //主播UID列表

    uint32 begin_time = 2;
    uint32 end_time = 3;
}

message BatchGetChannelLiveTotalDataResp {
    repeated LiveTotalDayData data_list = 1;
}

//主播PK记录
message ChannelLivePKRecord {
    uint32 uid = 1; //
    string account = 2;
    string nick = 3;
    uint32 channel_id = 4; //对手房间ID
    uint32 target_uid = 5;
    string target_account = 6;
    uint32 target_channel_id = 7;
    uint32 create_time = 8; //开始PK时间戳
}

message GetChannelLivePKRecordReq {
    uint32 uid = 1;
}

message GetChannelLivePKRecordResp {
    repeated ChannelLivePKRecord record_list = 1;
    //repeated ChannelLiveStatus status_list = 2;
}

message SendGiftUserInfo {
    uint32 uid = 1;
    uint32 score = 2;
    bool first_kill = 3; //是否是首杀用户
    UkwInfo ukw_info = 4;  // 神秘人信息
}

message GetChanneLivePkRankUserReq {
    uint32 uid = 1; //PK双方的UID，不用区分UID和targetUID
    uint32 channel_id = 2; //
    uint32 target_uid = 3; //PK双方的UID，不用区分UID和targetUID
    uint32 off = 4;
    uint32 cnt = 5;
}

message GetChanneLivePkRankUserResp {
    repeated SendGiftUserInfo user_list = 1;
    uint32 channel_id = 2;
    uint32 anchor_uid = 3;
}

enum EChannelPkType {
    ENUM_PK_TYPE_INVALID = 0;  //无效
    ENUM_PK_TYPE_SINGLE = 1;  // 单人PK
    ENUM_PK_TYPE_MULTI_2V2 = 2;  // 多人pk 2v2
    ENUM_PK_TYPE_MULTI_1V1 = 3;  // 多人pk 1v1
}

message ChannelLiveStatusSimple {
    uint32 anchor_uid = 1;
    uint32 channel_id = 2;
    uint32 status = 3; //EnumChannelLiveStatus
    uint32 begin_time = 4;
    uint32 pk_channel_id = 5; //PK对手的房间ID，0没有PK
    uint32 pk_type = 6; // pk类型，see EChannelPkType
    repeated uint32 pk_channel_id_list= 7; // pk时，所有对手的房间id
}

message BatchGetChannelLiveStatusSimpleReq {
    repeated uint32 channel_list = 1;
}

message BatchGetChannelLiveStatusSimpleResp {
    repeated ChannelLiveStatusSimple channel_live_status_list = 2;
}

message AnchorInfo {
    uint32 uid = 1;
    uint32 channel_id = 2;
    string oper_user = 3; //操作账号
    uint32 oper_time = 4; //添加时间
    uint32 tag_id = 5; //标签
    string account = 6;
    string nickname = 7;
    uint32 channel_live_status = 8; //直播状态 EnumChannelLiveStatus
    uint32 channel_live_pk_status = 9; //PK状态 EnumChannelLivePKStatus
    uint32 authority = 10; // enum ChannelLiveAuthFlag
}

// 获取全部主播信息，数据量太多，不建议调用，可以调新的分页获取接口
message GetAllAnchorReq {
}
message GetAllAnchorResp {
    repeated AnchorInfo anchor_list = 1;
}

// 分页获取主播列表，因为主播数量越来越多，分页获取接口耗时大
// 所以需要进行更小粒度查询，需要时间范围传参,左开右闭, [begin_ts, end_ts)
// begin_ts 从 2020-01-01 00:00:00 开始，查询最大时间跨度不要超过12个月
// 指定uid查询 时间范围不用传参
message GetAnchorListReq {
    uint32 page = 1;  // 从1开始
    uint32 page_size = 2;
    repeated uint32 uid_list = 3; // 指定uid查询
    uint32 begin_ts = 4;
    uint32 end_ts = 5;  
}
message GetAnchorListResp {
    repeated AnchorInfo anchor_list = 1;
    uint32 next_page = 2;  // 为0时表示结束
    uint32 total_cnt = 3; // 总数
}

message GetAnchorByUidListReq {
    repeated uint32 uid_list = 1;
}

message GetAnchorByUidListResp {
    repeated AnchorInfo anchor_list = 1;
}

message SearchAnchorReq {
    uint32 uid = 1;
}

message SearchAnchorResp {
    AnchorInfo anchor_info = 1;
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
message ToolItem {
    string item_id = 1;
    int64 item_ns = 2; //区别不同对象
    bool beUsed = 3;
    //string active_icon = 2;
    //string used_icon = 3;
    //string desc = 4;
}

enum EnumIDType {
    INV_ID_Type = 0;
    CHANNEL_ID = 1;
    USER_ID = 2;
}

enum ItemType {
    Invalid = 0;
    Effect_Type = 1; //氛围道具
    Score_Type = 2; //增加积分道具
    Percent_Type = 3; //按百分比添加道具
    First_Kill_Type = 4; //首杀
    Deduct_Type = 5; //氛围道具到达一定数量后减对方积分
}

message EffectItemMilestone {
    uint32 count = 1;   //数量
    uint32 percent = 2; //百分比 * 100
}

message ItemConfig {
    string item_id = 1;
    string desc = 2; //道具描述
    string icon = 3; //道具icon 资源链接
    string effect_url = 4; //触发方效果资源链接
    string target_effect_url = 5; //对手方效果链接
    string msg = 6; //触发方消息
    string target_msg = 7; //对手方消息
    string gain_msg = 8; //获得时的消息
    ItemType ty = 9; //道具类型
    uint32 value = 10; //道具数值
    string name = 11; //
    uint32 version = 12; //版本号，用于标识最新版本的氛围道具
    repeated EffectItemMilestone milestone_list = 13;
}

message GetItemConfigReq {
    repeated string item_id_list = 1; //取对应itemID的配置，如果空表示全部拿。客户端保存包已经取过的配置，只有在发现有客户端没有对应的道具item时才用这个接口
    uint32 uid = 2; //操作人UID
}

//PK加时赛相关配置//
message ExtramTimeConf {
    string pk_extra_time_rule = 1; //PK防偷塔玩法规则
    bool pk_extra_switch = 2; //PK防偷塔玩法是否开启
    uint32 extra_left_time = 3; //剩余多少秒会触发加时
    uint32 extra_total_score = 4; //PK双方总分多少满足触发加时
}

message GetItemConfigResp {
    repeated ItemConfig item_conf_list = 1;
}

//取道具列表接口
message GetMyToolListReq {
    uint32 uid = 1; //自己UID
    uint32 channel_id = 2; //所在PK房间channelID
}

message GetMyToolListResp {
    repeated ToolItem items = 1;
}

//直播送礼列表
message GetChannelLiveRankUserReq {
    uint32 channel_id = 1; //直播房ID
}

message GetChannelLiveRankUserResp {
    repeated SendGiftUserInfo user_list = 1;
}
message WatchUserInfo {
    uint32 uid = 1;
    string account = 2;
    uint32 score = 3;
    //uint32 rich = 4;
    //uint32 charm = 5;
}

//直播观看时长列表
message GetChannelLiveWatchTimeRankUserReq {
    uint32 channel_id = 1;
}

message GetChannelLiveWatchTimeRankUserResp {
    repeated WatchUserInfo user_list = 1;
}

//结束直播数据统计
message ChannelLiveData {
    uint32 live_time = 1; //直播时长，秒单位
    uint32 audience_cnt = 2; //直播观众
    uint32 live_gift_value = 3; //直播流水
    uint32 anchor_gift_value = 4; //主播流水
    uint32 send_gift_audience_cnt = 5; //送礼人数
    uint32 add_fans = 6; //每场新增fans
    uint32 add_group_fans = 7; //每场新增
    // 直播场数数据需要返回直播开始结束时间
    uint32 begin_time = 8;
    uint32 end_time = 9;
    uint32 knight_value = 10;   // 骑士流水
    uint32 game_fee = 11;  //  互动有效流水
    uint32 game_ts = 12; // 互动游戏时长 秒
    uint32 virtual_fee = 13; // 虚拟直播流水
    uint32 virtual_ts = 14; // 虚拟直播时长 秒
}

//取本场直播数据统计
message GetChannelLiveDataReq {
    uint32 uid = 1;
    uint32 begin_time = 2;
    uint32 end_time = 3;
    uint32 channel_id = 4;
}

message GetChannelLiveDataResp {
    ChannelLiveData live_data = 1; //数据统计
}

message ChannelLiveEvent {
    uint32 uid = 1;
    uint32 channel_id = 2;
    EnumChannelLiveStatus status = 3;
    EnumChannelLivePKStatus pk_status = 4;
}

//批量取粉团天送礼值
message BatchGetGroupFansGiftValueReq {
    uint32 anchor_uid = 1;
    repeated uint32 uid_list = 2; //粉丝UID列表
}

message BatchGetGroupFansGiftValueResp {
    repeated uint32 gift_value_list = 1; //对应粉丝送礼值
}

// 取最近的直播数据
message GetChannelLiveTOPNReq {
    uint32 uid = 1; //主播UID
    uint32 off = 2;
    uint32 count = 3;
}

message GetChannelLiveTOPNResp {
    repeated ChannelLiveData live_data = 1; //数据统计
}

//主播积分相关
message MissionAwardDetail {
    uint32 live_time_cnt = 1;       // 时长
    uint32 live_income = 2;         // 流水
    float award_percent = 3;        // 流水奖励百分比
    uint32 award_base_score = 4;    // 基础奖励积分
    uint32 real_award_score = 5;    // 实际奖励积分
}

message AddChannelLiveAnchorScoreReq {
    uint32 uid = 1;
    int32 score = 2; //可以减
    string order_id = 3;

    enum SourceType {
        Unknown = 0;
        DayAnchorTimeMission = 1;   // 日主播时长任务奖励
        WeekAnchorTimeMission = 2;  // 周主播时长任务奖励
        DayAnchorIncomeMission = 3;   // 日主播流水任务奖励
        WeekAnchorIncomeMission = 4;  // 周主播流水任务奖励
        ScoreExchange = 5;      // 积分提现扣除积分
        ScoreExchangeReturn = 6;// 积分提现失败返还积分
        ReclaimScore = 7;// 运营回收积分
        GuildChangePrivate = 8;    //主动将个人对公转为对私扣除积分
        GuildQuit = 9;    //解约导致个人对公转为对私扣除积分
        GuildOfficalRecycle = 10;    //官方回收个人对公权扣除积分
        GuildExchange = 11;    //会长提现个人对公积分
        OfficialReward = 12;                // 官方运营发放积分
        MonthAnchorIncomeMission = 13;  // 月主播流水任务奖励
    }
    uint32 source_type = 4; // 来源 see SourceType
    MissionAwardDetail mission_award_detail = 5;

    uint32 outside_time = 6;
}

message AddChannelLiveAnchorScoreResp {
    uint32 final_score = 1;
}

message GetChannelLiveAnchorScoreReq {
    uint32 uid = 1;
}

message GetChannelLiveAnchorScoreResp {
    uint32 score = 1;
}

message AnchorScoreLog {
    string order_id = 1;
    int32 add_score = 2;    // 增加的积分，可为负数
    uint32 final_score = 3; // 增加后的积分
    uint32 source_type = 4; // 来源 see AddChannelLiveAnchorScoreReq::SourceType
    uint32 create_time = 5; // 记录的时间
    uint32 uid = 6;
}

message GetChannelLiveAnchorScoreLogReq {
    uint32 uid = 1;
    repeated uint32 source_list = 2; // 需要查询的流水的来源列表
    uint32 begin = 3;
    uint32 limit = 4;
}

message GetChannelLiveAnchorScoreLogResp {
    repeated AnchorScoreLog log_list = 1;
}

// 查询奖励积分明细列表
message GetAnchorScoreOrderListReq {
    uint32 begin_ts = 1;
    uint32 end_ts = 2; 
    uint32 page = 3;  // 从1开始
    uint32 page_size = 4; 
}
message GetAnchorScoreOrderListResp {
    repeated AnchorScoreLog list = 1;
}

// 奖励积分
message AnchorScore {
   uint32 uid = 1;
   uint32 last_month_remain_score = 2; // 上月剩余积分
   uint32 new_score = 3;  // 新产生的积分
   uint32 total_score = 4;  // 总积分
   uint32 exchange_money_score = 5;  // 提现积分
   uint32 guild_score = 6;  // 已汇总对公积分
   uint32 exchange_money_fail_return_score = 7;   //提现失败返还积分
   uint32 official_grant_score = 8;   // 官方运营发放积分
   uint32 official_reclaim_score = 9;   // 官方运营回收积分
   uint32 remain_score = 10;  // 剩余积分
   uint32 remain_exchange_money = 11;   //剩余可提现金额
}

// 查询奖励积分，
message GetAnchorScoreListReq {
   uint32 begin_ts = 1;
   uint32 end_ts = 2;
   repeated uint32 uid_list = 3; // 指定uid查询  
   bool is_has_exchage = 4;   // 提现积分或已汇总对公积分大于0
}
message GetAnchorScoreListResp {
   repeated AnchorScore list = 1;
}

message AnchorMonthScore {
   uint32 uid = 1;
   uint32 score = 2;
}

// 获取主播月积分
message GetAnchorMonthScoreListReq {
   uint32 ts = 1;
   uint32 page = 2; // 从1开始
   uint32 page_size = 3;  
}
message GetAnchorMonthScoreListResp {
   repeated AnchorMonthScore list = 1;
}


// 获取所有开播的直播间
message BatchGetAllChannelLiveReq {
}

message BatchGetAllChannelLiveResp {
    repeated uint32 channel_list = 1;
    repeated ChannelLiveStatusSimple live_channel_list = 2; // 新字段
}

message GetHeartBeatTimeOutReq {
    int64 expire = 1;
    bool del = 2;
}

message GetHeartBeatTimeOutResp {
    repeated uint32 uid_list = 1;
}

//每天推送限制
message GetUserPushCntReq {
    uint32 uid = 1;
    uint32 limit_cnt = 2;
    uint32 anchor_uid = 3;
    repeated uint32 uid_list = 4;
}

message GetUserPushCntResp {
    bool result = 1;
    repeated uint32 valid_uid_list = 2;
}

//PK一方的信息
message PkSingleInfo {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint64 channel_live_id = 3;
    uint32 pk_score = 4; //PK值
    uint32 effect_cnt = 5; //氛围道具数量
    ChannelLiveOpponentMicFlag mic_flag = 6; //麦位开关
}

//PK公共数据
message PkCommonInfo {
    uint32 begin_time = 1; //PK开始时间戳
    EnumChannelLivePKStatus pk_status = 2; //PK的阶段
    map<uint32, PkMicSpace> mic_list = 3; //PK麦位信息，通过心跳同步信息
    uint32 first_kill_uid = 4;
    uint32 first_kill_cid = 5;
    bool is_extra_time = 6; //是否在结束10秒前，两个人的PK值之和满足触发加时条件

    string pk_extra_time_rule = 7; //PK防偷塔玩法规则
    uint32 extra_left_time = 8; //剩余多少秒会触发加时
    bool is_open_extra_time = 9; //加时玩法是否开启
    UkwInfo first_kill_uwkinfo = 10;  // 首杀用户的神秘人信息
}

//用户进房的时候拿PK信息
message GetPkInfoReq {
    uint32 channel_id = 1;
}

message GetPkInfoResp {
    PkSingleInfo a_pk_info = 1;
    PkSingleInfo b_pk_info = 2;
    PkCommonInfo pk_common_info = 3;
}

//PK中，对面房间语音状态
enum ChannelLiveOpponentMicFlag {
    CPKMic_OPEN = 0; //开麦，能听到对面声音；默认是开麦状态，PK开始的时候默认开启
    CPKMic_CLOSE = 1; //闭麦，不能听到对面声音
}

//主播设置PK中是否能听到对面语音
message SetChannelLiveOpponentMicFlagReq {
    uint32 channel_id = 1;
    ChannelLiveOpponentMicFlag opt_mic_flag = 2;
}

message SetChannelLiveOpponentMicFlagResp {
    uint32 mic_flag = 1;
    uint32 target_channel_id = 2; //PK对手的channelID
}

//主播设置PK中能否听到对方房间语音房间推送，所有人根据这个推送确定能否听到对面房间声音。包括主播自己
message ChannelLiveOpponentMicFlagPushMsg {
    ChannelLiveOpponentMicFlag opt_mic_flag = 1;
}

enum ChannelLivePKMatchType {
    CPK_Match_Nornal = 0; //普通匹配
    CPK_Match_Rank = 1; //排位赛匹配
    CPK_Match_rand = 2; //随机匹配
    CPK_Match_Appoint = 3;  // 指定匹配
}

message StartPkMatchReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
    ChannelLivePKMatchType match_type = 3;
}

message StartPkMatchResp {
}

message CancelPkMatchReq {
    uint32 uid = 1;
    uint32 channel_id = 2;
}

message CancelPkMatchResp {
    uint32 channel_id = 1;
}

message PkLimitInfo {
    //需要考虑没有配置次数限制的情况，这两字段都是"",客户端不显示限制提示？
    string time_range = 1; //14:00~20:00
    string limit_cnt = 2; //2次
}

//自己PK赛的阶段信息
message PkCompetitionInfo {
    uint32 level = 1;
    string level_name = 2;
}

//取PK匹配类型和自己PK赛阶段
message GetPKMatchInfoReq {
    uint32 channel_id = 1;
    uint32 uid = 2;
}

message GetPKMatchInfoResp {
    ChannelLivePKMatchType pk_match_ty = 1;
    PkCompetitionInfo cpt_info = 2;
    PkLimitInfo pk_limit_info = 3;
}


message AnchorTotalData {
    uint32 uid = 1;
    uint32 channel_id = 2;
    uint32 total_score = 3;
    uint32 first_live_time = 4;
}

message BatchGetAnchorTotalDataReq {
    repeated uint32 uid_list = 1;
}

message BatchGetAnchorTotalDataResp {
    map<uint32, AnchorTotalData> map_total_data = 1;
}

message CheckIsAnchorInBackListReq {
    uint32 uid = 1;
}

message CheckIsAnchorInBackListResp {
    bool is_in_backlist = 1; //是否是黑名单用户
}

message AddAnchorInBackListReq {
    repeated uint32 uid_list = 1;
}

message AddAnchorInBackListResp {
}

message GetAnchorBackListReq {
    uint32 off = 1;
    uint32 count = 2;
}

message GetAnchorBackListResp {
    repeated uint32 uid_list = 1;
}

message DelAnchorBackListReq {
    repeated uint32 uid_list = 1;
}

message DelAnchorBackListResp {
}

enum ChannelLiveAuthFlag {
    Nornal = 0; //普通匹配
    MQ_Auth = 1; //排麦权限
    PK_Auth = 2; //PK权限
}

//开通操作排麦权限
message SetAuthFlagReq {
    repeated uint32 uid_list = 1; //开通权限列表
    ChannelLiveAuthFlag auth_flag = 2; //
    bool flag = 3; //开通true，关闭false
}

message SetAuthFlagResp {
    repeated uint32 fail_uid_list = 1; //设置失败UID列表
}

message PushTestReq {
    bytes pb_data = 1;
    bytes normal_data = 2;
    bytes content = 3;
    uint32 cmd = 4;
    uint32 cid = 5;
    uint32 to_uid = 6;
    uint32 from_uid = 7;
}

message PushTestResp {
}

//****** 指定pk begin *******// 
// pk对手信息
message PkRivalInfo {
    uint32 uid = 1;
    uint32 pk_begin_ts = 2;
}

// 指定pk信息
message AppointPkInfo {
    uint32 appoint_id = 1;
    uint32 uid = 2;
    uint32 begin_ts = 3;
    uint32 end_ts = 4;
    repeated PkRivalInfo rival_list = 5;
    uint32 update_ts = 6;
    string operator = 7;
}

// 增加指定pk信息
message AddAppointPkInfoReq {
    AppointPkInfo info = 1;
}
message AddAppointPkInfoResp {
}

// 更新指定pk信息
message UpdateAppointPkInfoReq {
    AppointPkInfo info = 1;
}
message UpdateAppointPkInfoResp {
}

// 删除指定pk信息
message DelAppointPkInfoReq {
    uint32 appoint_id = 1;
}
message DelAppointPkInfoResp {
}

// 获取指定pk信息列表
message GetAppointPkInfoListReq {
    enum QueryType {
        Query_All = 0;
        Query_By_Uid = 1;
        Query_By_Ts = 2;
    }
    uint32 query_type = 1; // see QueryType
    uint32 uid = 2;
    uint32 begin_ts = 3;
    uint32 end_ts = 4;
    uint32 page = 5;
    uint32 page_size = 6;
}
message GetAppointPkInfoListResp {
    repeated AppointPkInfo info_list = 1;
    uint32 next_page = 2;
    uint32 total_cnt = 3;
}

enum AppointPkEventType {
    InValid = 0;
    AppointPkInvite = 1;   // 指定pk邀约
    AppointPkAbstain = 2;  // 指定pk弃权
    AppointPkWin = 3;  // 指定pk胜利 
    AppointPkWaiting = 4;   //等待对方应战
}

message AppointPkEvent {
    uint32 event_type = 1;  //see AppointPkEventType
    uint32 my_uid = 2;
    uint32 other_uid = 3;
    string other_account = 4;
    string other_nickname = 5;
    uint32 count_down_end_time = 6; // 倒计时截止时间 秒
    int32 other_sex = 7;
    uint32 count_down_time = 8;  // 倒计时时长 秒
}

// 接受指定pk邀约
message AcceptAppointPkReq {
    uint32 my_uid = 1;
    uint32 other_uid = 2;
    uint32 channel_id = 3;
}
message AcceptAppointPkResp {
}

// 确定收到指定pk推送
message ConfirmAppointPkPushReq {
    uint32 my_uid = 1;
    uint32 other_uid = 2;
}
message ConfirmAppointPkPushResp {
}

// 主播进房获取指定pk相关信息
message GetAppointPkInfoReq {
    uint32 uid = 1;
}
message GetAppointPkInfoResp {
    AppointPkEvent push_event_info = 1;
}


//****** 指定pk end *******// 


// 虚拟主播权限信息
message VirtualAnchorPerInfo {
    uint32 id = 1; // 自增id
    uint32 cid = 2; //房间id
    uint32 uid = 3;  // 主播uid
    uint32 begin_ts = 4;  // 开始生效时间
    uint32 end_ts = 5;  // 结束时间
    uint32 update_ts = 6;  // 更新时间
}

message ErrMsg {
    uint32 uid = 1;
    string msg = 2;
}

//增加虚拟主播权限
message AddVirtualAnchorPerReq {
    repeated VirtualAnchorPerInfo info_list = 1;
}
message AddVirtualAnchorPerResp {
    repeated ErrMsg err_list = 1;
}

// 修改虚拟主播权限
message UpdateVirtualAnchorPerReq {
    VirtualAnchorPerInfo info = 1;
}
message UpdateVirtualAnchorPerResp {
    ErrMsg err_msg = 2;
}

// 删除虚拟主播权限
message DelVirtualAnchorPerReq {
    uint32 id = 1;
}
message DelVirtualAnchorPerResp {
}

// 获取虚拟主播权限列表
message GetVirtualAnchorPerListReq {
    uint32 cid = 1; // 指定房间id查询
    uint32 uid = 2;  // 指定主播id查询
    uint32 page = 3; // 从1开始
    uint32 page_size = 4;  // 页数
}
message GetVirtualAnchorPerListResp {
    repeated VirtualAnchorPerInfo info_list = 1;
    uint32 total_cnt = 2;
}

//查询是否有虚拟主播权限
message CheckHasVirtualAnchorPerReq {
    uint32 uid = 1;
}
message CheckHasVirtualAnchorPerResp {
    bool has_per = 1;
}

//获取虚拟主播房间密钥
message GetVirtualLiveChannelSecretReq {
	uint32 anchor_uid = 1;
	uint32 channel_id = 2;
	bool   refresh    = 3;      //是否刷新
}
message GetVirtualLiveChannelSecretResp {
	string channel_secret = 1;
}
//根据房间密钥获取房间信息
message GetVirtualLiveInfoBySecretReq {
	string channel_secret = 1;
}
message GetVirtualLiveInfoBySecretResp {
	uint32 anchor_uid = 1;
	uint32 channel_id = 2;
}
message ClearVirtualLiveSecretRefreshLimitReq {
	uint32 anchor_uid = 1;
	uint32 channel_id = 2;
}
message ClearVirtualLiveSecretRefreshLimitResp {
}

//****** 赛事pk begin *******//
// 赛事PK状态枚举
enum ContestPkStatus {
    CONTEST_PK_PENDING = 0;     // 待开始
    CONTEST_PK_RUNNING = 1;     // 进行中
    CONTEST_PK_FINISHED = 2;    // 已结束
    CONTEST_PK_CANCELLED = 3;   // 已取消
}

// 赛事PK配置信息
message ContestPkConfig {
    string contest_id = 1;                    // 赛事ID
    string contest_name = 2;                  // 赛事名称
    string activity_service = 3;              // 运营活动服务标识
    uint64 anchor_uid_a = 4;                  // 主播A的UID
    uint64 anchor_uid_b = 5;                  // 主播B的UID
    uint64 contest_begin_time = 6;            // 赛事开始时间戳
    uint64 contest_end_time = 7;              // 赛事结束时间戳
    uint64 pk_begin_time = 8;                 // PK开始时间戳
    uint64 pk_end_time = 9;                   // PK结束时间戳
    bool allow_extra_time = 10;               // 是否允许加时
    ContestPkStatus status = 11;              // 赛事状态
    string operator = 12;                     // 操作人
    uint64 create_time = 13;
    uint64 update_time = 14;
}

// 赛事PK结果状态枚举
enum ContestPkResultStatus {
    RESULT_NOT_STARTED = 0;     // 未开始
    RESULT_IN_PROGRESS = 1;     // 进行中
    RESULT_FINISHED = 2;        // 正常结束
    RESULT_ABNORMAL = 3;        // 异常结束
}

// 赛事PK结果信息
message ContestPkResult {
    string contest_id = 1;                    // 赛事ID
    uint64 winner_uid = 2;                    // 获胜者UID，0表示平局
    uint32 final_score_a = 3;                 // 主播A最终得分
    uint32 final_score_b = 4;                 // 主播B最终得分
    uint64 actual_start_time = 5;             // 实际开始时间戳
    uint64 actual_end_time = 6;               // 实际结束时间戳
    bool is_extra_time = 7;                   // 是否发生了加时
    uint32 pk_duration = 8;                   // PK实际时长（秒）
    ContestPkResultStatus result_status = 9;  // 结果状态
    uint64 create_time = 10;
    uint64 update_time = 11;
}

// 赛事PK完整信息（配置+结果）
message ContestPkInfo {
    ContestPkConfig config = 1;               // 配置信息
    ContestPkResult result = 2;               // 结果信息（可选）
}

// 赛事PK结果事件
message ContestPkResultEvent {
    string contest_id = 1;                    // 赛事ID
    string activity_service = 2;              // 运营活动服务标识
    uint64 anchor_uid_a = 3;                  // 主播A的UID
    uint64 anchor_uid_b = 4;                  // 主播B的UID
    uint64 winner_uid = 5;                    // 获胜者UID，0表示平局
    uint32 final_score_a = 6;                 // 主播A最终得分
    uint32 final_score_b = 7;                 // 主播B最终得分
    uint64 pk_begin_time = 8;                 // PK开始时间戳
    uint64 pk_end_time = 9;                   // PK结束时间戳
    uint64 actual_end_time = 10;              // 实际结束时间戳
    bool is_extra_time = 11;                  // 是否发生了加时
}

// 创建赛事PK
message CreateContestPkReq {
    ContestPkConfig config = 1;
}
message CreateContestPkResp {
    string contest_id = 1;
}

// 更新赛事PK配置
message UpdateContestPkReq {
    ContestPkConfig config = 1;
}
message UpdateContestPkResp {}

// 取消赛事PK
message CancelContestPkReq {
    string contest_id = 1;
    string operator = 2;
    string reason = 3;
}
message CancelContestPkResp {}

// 查询赛事PK配置
message GetContestPkConfigReq {
    string contest_id = 1;
}
message GetContestPkConfigResp {
    ContestPkConfig config = 1;
}

// 批量查询赛事PK配置
message BatchGetContestPkConfigReq {
    repeated string contest_id_list = 1;
    string activity_service = 2;           // 按活动服务筛选
    uint64 time_begin = 3;                 // 时间范围筛选
    uint64 time_end = 4;
    ContestPkStatus status = 5;            // 按状态筛选
    uint32 page = 6;
    uint32 page_size = 7;
}
message BatchGetContestPkConfigResp {
    repeated ContestPkConfig config_list = 1;
    uint32 total_count = 2;
    uint32 next_page = 3;
}

// 检查主播是否可以参与常规PK
message CheckAnchorPkAvailableReq {
    uint64 anchor_uid = 1;
    uint64 check_time = 2;                 // 检查的时间点，默认为当前时间
}
message CheckAnchorPkAvailableResp {
    bool available = 1;                    // 是否可以参与常规PK
    string reason = 2;                     // 不可用的原因
    ContestPkConfig blocking_contest = 3;   // 阻塞的赛事信息
}

// 获取赛事PK完整信息
message GetContestPkInfoReq {
    string contest_id = 1;
    bool include_result = 2;               // 是否包含结果信息
}
message GetContestPkInfoResp {
    ContestPkInfo info = 1;
}

// 批量获取赛事PK完整信息
message BatchGetContestPkInfoReq {
    repeated string contest_id_list = 1;
    string activity_service = 2;
    uint64 time_begin = 3;
    uint64 time_end = 4;
    ContestPkStatus status = 5;
    bool include_result = 6;               // 是否包含结果信息
    uint32 page = 7;
    uint32 page_size = 8;
}
message BatchGetContestPkInfoResp {
    repeated ContestPkInfo info_list = 1;
    uint32 total_count = 2;
    uint32 next_page = 3;
}

// 开始赛事PK
message StartContestPkReq {
    string contest_id = 1;
    string operator = 2;                   // 操作人
    uint64 actual_start_time = 3;          // 实际开始时间，0表示使用当前时间
}
message StartContestPkResp {
    ContestPkResult result = 1;            // 返回初始化的结果信息
}

// 更新赛事PK结果（实时更新分数）
message UpdateContestPkResultReq {
    string contest_id = 1;
    uint32 score_a = 2;                    // 主播A当前分数
    uint32 score_b = 3;                    // 主播B当前分数
    string operator = 4;                   // 操作人
    string update_reason = 5;              // 更新原因
}
message UpdateContestPkResultResp {
    ContestPkResult result = 1;            // 返回更新后的结果信息
}

// 结束赛事PK
message FinishContestPkReq {
    string contest_id = 1;
    uint64 winner_uid = 2;                 // 获胜者UID，0表示平局
    uint32 final_score_a = 3;              // 主播A最终得分
    uint32 final_score_b = 4;              // 主播B最终得分
    bool is_extra_time = 5;                // 是否发生了加时
    uint64 actual_end_time = 6;            // 实际结束时间，0表示使用当前时间
    string operator = 7;                   // 操作人
    string finish_reason = 8;              // 结束原因
}
message FinishContestPkResp {
    ContestPkResult result = 1;            // 返回最终结果信息
}

// 获取赛事PK结果
message GetContestPkResultReq {
    string contest_id = 1;
}
message GetContestPkResultResp {
    ContestPkResult result = 1;
}

//****** 赛事pk end *******//

service ChannelLiveMgr {

    rpc SetChannelLiveInfo (SetChannelLiveInfoReq) returns (SetChannelLiveInfoResp) {
    }

    rpc DelChannelLiveInfo (DelChannelLiveInfoReq) returns (DelChannelLiveInfoResp) {
    }

    rpc GetAnchorOperRecord (GetAnchorOperRecordReq) returns (GetAnchorOperRecordResp) {
    }

    rpc ChannelLiveHeartbeat (ChannelLiveHeartbeatReq) returns (ChannelLiveHeartbeatResp) {
    }

    rpc SetChannelLiveStatus (SetChannelLiveStatusReq) returns (SetChannelLiveStatusResp) {
    }

    rpc ApplyPk (ApplyPkReq) returns (ApplyPkResp) {
        option deprecated = true;
    }

    rpc HandlerApply (HandlerApplyReq) returns (HandlerApplyResp) {
        option deprecated = true;
    }

    rpc CancelPKApply (CancelPKApplyReq) returns (CancelPKApplyResp) {
        option deprecated = true;
    }

    rpc GetApplyList (GetApplyListReq) returns (GetApplyListResp) {
    }

    rpc SetPkStatus (SetPkStatusReq) returns (SetPkStatusResp) {
        option deprecated = true;
    }
    //81W  标志位移到新Redis
    rpc GetChannelLiveInfo (GetChannelLiveInfoReq) returns (GetChannelLiveInfoResp) {
    }

    rpc BatGetChannelLiveInfo (BatGetChannelLiveInfoReq) returns (BatGetChannelLiveInfoResp) {
    }


    //26W
    rpc GetChannelLiveStatus (GetChannelLiveStatusReq) returns (GetChannelLiveStatusResp) {

    }
    //25W
    rpc BatchGetChannelLiveStatus (BatchGetChannelLiveStatusReq) returns (BatchGetChannelLiveStatusResp) {

    }
    //36W
    rpc GetChannelLiveHistoryRecord (GetChannelLiveHistoryRecordReq) returns (GetChannelLiveHistoryRecordResp) {
    }

    rpc GetChannelLiveTotalData (GetChannelLiveTotalDataReq) returns (GetChannelLiveTotalDataResp) {
    }

    rpc BatchGetChannelLiveTotalData (BatchGetChannelLiveTotalDataReq) returns (BatchGetChannelLiveTotalDataResp) {
    }

    rpc GetChannelLivePKRecord (GetChannelLivePKRecordReq) returns (GetChannelLivePKRecordResp) {
    }

    rpc GetMyToolList (GetMyToolListReq) returns (GetMyToolListResp) {
        option deprecated = true;
    }

    rpc GetChanneLivePkRankUser (GetChanneLivePkRankUserReq) returns (GetChanneLivePkRankUserResp) {
        option deprecated = true;
    }

    rpc GetChannelLiveRankUser (GetChannelLiveRankUserReq) returns (GetChannelLiveRankUserResp) {
    }

    rpc GetChannelLiveWatchTimeRankUser (GetChannelLiveWatchTimeRankUserReq) returns (GetChannelLiveWatchTimeRankUserResp) {
    }

    //rpc BatchGetChannelLiveRecored (BatchGetChannelLiveRecoredReq) returns (BatchGetChannelLiveRecoredResp) {
    //}
    //10W
    rpc GetChannelLiveData (GetChannelLiveDataReq) returns (GetChannelLiveDataResp) {
    }

    rpc GetChannelLiveTOPN (GetChannelLiveTOPNReq) returns (GetChannelLiveTOPNResp) {
    }

    rpc AddChannelLiveAnchorScore (AddChannelLiveAnchorScoreReq) returns (AddChannelLiveAnchorScoreResp) {
    }

    rpc GetChannelLiveAnchorScore (GetChannelLiveAnchorScoreReq) returns (GetChannelLiveAnchorScoreResp) {
    }

    rpc GetChannelLiveAnchorScoreLog (GetChannelLiveAnchorScoreLogReq) returns (GetChannelLiveAnchorScoreLogResp) {
    }

    rpc BatchGetAllChannelLive (BatchGetAllChannelLiveReq) returns (BatchGetAllChannelLiveResp) {
    }

    rpc BatchGetChannelLiveRecord (BatchGetChannelLiveRecordReq) returns (BatchGetChannelLiveRecordResp) {
    }

    // 获取全部主播信息，数据量太多，不建议调用，可以调新的分页获取接口
    rpc GetAllAnchor (GetAllAnchorReq) returns (GetAllAnchorResp) {
    }

    // 分页获取主播信息
    rpc GetAnchorList (GetAnchorListReq) returns (GetAnchorListResp) {
    }

    rpc SetChannelLiveTag (SetChannelLiveTagReq) returns (SetChannelLiveTagResp) {
    }

    rpc BatchGetGroupFansGiftValue (BatchGetGroupFansGiftValueReq) returns (BatchGetGroupFansGiftValueResp) {
    }

    rpc GetHeartBeatTimeOut (GetHeartBeatTimeOutReq) returns (GetHeartBeatTimeOutResp) {
    }
    //90W
    rpc BatchGetChannelLiveStatusSimple (BatchGetChannelLiveStatusSimpleReq) returns (BatchGetChannelLiveStatusSimpleResp) {
    }

    rpc GetUserPushCnt (GetUserPushCntReq) returns (GetUserPushCntResp) {
    }

    rpc SearchAnchor (SearchAnchorReq) returns (SearchAnchorResp) {
        option deprecated = true;
    }

    rpc ReportClientIDChange (ReportClientIDChangeReq) returns (ReportClientIDChangeResp) {
        option deprecated = true;
    }
    //14W
    rpc GetItemConfig (GetItemConfigReq) returns (GetItemConfigResp) {
        option deprecated = true;
    }
    //9W
    rpc GetPkInfo (GetPkInfoReq) returns (GetPkInfoResp) {
        option deprecated = true;
    }

    rpc SetChannelLiveOpponentMicFlag (SetChannelLiveOpponentMicFlagReq) returns (SetChannelLiveOpponentMicFlagResp) {
    }

    rpc StartPkMatch (StartPkMatchReq) returns (StartPkMatchResp) {
        option deprecated = true;
    }

    rpc CancelPkMatch (CancelPkMatchReq) returns (CancelPkMatchResp) {
        option deprecated = true;
    }

    rpc GetPKMatchInfo (GetPKMatchInfoReq) returns (GetPKMatchInfoResp) {
        option deprecated = true;
    }

    rpc BatchGetAnchorTotalData (BatchGetAnchorTotalDataReq) returns (BatchGetAnchorTotalDataResp) {
    }

    rpc CheckIsAnchorInBackList (CheckIsAnchorInBackListReq) returns (CheckIsAnchorInBackListResp) {
    }

    rpc AddAnchorInBackList (AddAnchorInBackListReq) returns (AddAnchorInBackListResp) {
    }

    rpc GetAnchorBackList (GetAnchorBackListReq) returns (GetAnchorBackListResp) {
    }

    rpc DelAnchorBackList (DelAnchorBackListReq) returns (DelAnchorBackListResp) {
    }

    rpc SetAuthFlag (SetAuthFlagReq) returns (SetAuthFlagResp) {
    }

    rpc PushTest (PushTestReq) returns (PushTestResp) {
    }

    rpc GetAnchorByUidList (GetAnchorByUidListReq) returns (GetAnchorByUidListResp) {
    }

    rpc SetChannelLiveInfoForTest (SetChannelLiveInfoForTestReq) returns (SetChannelLiveInfoForTestResp) {
    }

    rpc AddAppointPkInfo (AddAppointPkInfoReq) returns (AddAppointPkInfoResp) {
        option deprecated = true;
    }

    rpc UpdateAppointPkInfo (UpdateAppointPkInfoReq) returns (UpdateAppointPkInfoResp) {
        option deprecated = true;
    }

    rpc DelAppointPkInfo (DelAppointPkInfoReq) returns (DelAppointPkInfoResp) {
        option deprecated = true;
    }

    rpc GetAppointPkInfoList (GetAppointPkInfoListReq) returns (GetAppointPkInfoListResp) {
        option deprecated = true;
    }

    rpc AcceptAppointPk (AcceptAppointPkReq) returns (AcceptAppointPkResp) {
        option deprecated = true;
    }

    rpc ConfirmAppointPkPush (ConfirmAppointPkPushReq) returns (ConfirmAppointPkPushResp) {
        option deprecated = true;
    }

    rpc GetAppointPkInfo (GetAppointPkInfoReq) returns (GetAppointPkInfoResp) {
        option deprecated = true;
    }

    rpc AddVirtualAnchorPer (AddVirtualAnchorPerReq) returns (AddVirtualAnchorPerResp) {
    }

    rpc UpdateVirtualAnchorPer (UpdateVirtualAnchorPerReq) returns (UpdateVirtualAnchorPerResp) {
    }

    rpc DelVirtualAnchorPer (DelVirtualAnchorPerReq) returns (DelVirtualAnchorPerResp) {
    }

    rpc GetVirtualAnchorPerList (GetVirtualAnchorPerListReq) returns (GetVirtualAnchorPerListResp) {
    }

    rpc CheckHasVirtualAnchorPer (CheckHasVirtualAnchorPerReq) returns (CheckHasVirtualAnchorPerResp) {
    }

    rpc GetChannelLiveAvg(GetChannelLiveAvgReq) returns (GetChannelLiveAvgResp) {
    }

    rpc GetVirtualLiveChannelSecret(GetVirtualLiveChannelSecretReq) returns (GetVirtualLiveChannelSecretResp){}
    rpc GetVirtualLiveInfoBySecret(GetVirtualLiveInfoBySecretReq) returns (GetVirtualLiveInfoBySecretResp){}
    rpc ClearVirtualLiveSecretRefreshLimit (ClearVirtualLiveSecretRefreshLimitReq) returns (ClearVirtualLiveSecretRefreshLimitResp){}

    /*********对账接口V2**********/
    // 发放主播奖励积分对账
    rpc GetAnchorScoreOrderCount (ReconcileV2.TimeRangeReq) returns (ReconcileV2.CountResp) {}
    rpc GetAnchorScoreOrderIds (ReconcileV2.TimeRangeReq) returns (ReconcileV2.OrderIdsResp) {}
    /*********对账接口V2**********/


    // 查询奖励积分
    rpc GetAnchorScoreList(GetAnchorScoreListReq) returns (GetAnchorScoreListResp) {}

    
    // 获取主播月积分
    rpc GetAnchorMonthScoreList(GetAnchorMonthScoreListReq) returns (GetAnchorMonthScoreListResp) {}

    // 查询奖励积分明细列表
    rpc GetAnchorScoreOrderList(GetAnchorScoreOrderListReq) returns (GetAnchorScoreOrderListResp) {}


    // apiCenter-go迁移接口
    // 黑色管理后台
    rpc GetAnchorListMgr(apicentergo.GetAnchorListReq) returns (apicentergo.GetAnchorListResp) {}
    rpc BatchAddAnchorMgr(apicentergo.BatchAddAnchorReq) returns (apicentergo.BatchAddAnchorResp) {}
    rpc GetAnchorOperRecordMgr(apicentergo.GetAnchorOperRecordReq) returns (apicentergo.GetAnchorOperRecordResp) {}
    rpc BatDelChannelLiveInfoMgr(apicentergo.BatDelChannelLiveInfoReq) returns(apicentergo.BatDelChannelLiveInfoResp) {}
    rpc SetChannelLiveTagMgr(apicentergo.SetChannelLiveTagReq) returns(apicentergo.SetChannelLiveTagResp) {}

    // 赛事PK配置管理接口
    rpc CreateContestPk(CreateContestPkReq) returns (CreateContestPkResp) {}
    rpc UpdateContestPk(UpdateContestPkReq) returns (UpdateContestPkResp) {}
    rpc CancelContestPk(CancelContestPkReq) returns (CancelContestPkResp) {}
    rpc GetContestPkConfig(GetContestPkConfigReq) returns (GetContestPkConfigResp) {}
    rpc BatchGetContestPkConfig(BatchGetContestPkConfigReq) returns (BatchGetContestPkConfigResp) {}

    // 赛事PK查询接口
    rpc GetContestPkInfo(GetContestPkInfoReq) returns (GetContestPkInfoResp) {}
    rpc BatchGetContestPkInfo(BatchGetContestPkInfoReq) returns (BatchGetContestPkInfoResp) {}

    // 赛事PK结果管理接口
    rpc StartContestPk(StartContestPkReq) returns (StartContestPkResp) {}
    rpc UpdateContestPkResult(UpdateContestPkResultReq) returns (UpdateContestPkResultResp) {}
    rpc FinishContestPk(FinishContestPkReq) returns (FinishContestPkResp) {}
    rpc GetContestPkResult(GetContestPkResultReq) returns (GetContestPkResultResp) {}

    rpc CheckAnchorPkAvailable(CheckAnchorPkAvailableReq) returns (CheckAnchorPkAvailableResp) {}
}
