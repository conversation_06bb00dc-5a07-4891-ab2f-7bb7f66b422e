syntax = "proto3";

package ga.channellisteningautoplaylogic;

import "ga_base.proto";
import "channel/channel_.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/channellisteningautoplaylogic";

//用户进房获取播放状态
message ChannelPlayStatusReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message ChannelPlayStatusResp {
  ga.BaseResp base_resp = 1;
  ChannelPlayMode mode = 2;//当前房间播放模式
  AutoPlaySwitch switch = 3;//自动播放开关
  AutoPlayInfo info = 4;//在自动播放模式下有值
}

enum ChannelPlayMode {
  chooseMode = 0;//房主可选择
  AutoMode = 1;//自动播放模式
  OrderMode = 2;//战歌点歌模式
}

enum AutoPlaySwitch {
  Close = 0;//只能点歌播放
  Open = 1;//允许自动点歌
}

message AutoPlayInfo {
  ListeningAutoPlayRcmdMenu menu = 1;//歌单
  int64 key = 2;//当前播放歌曲
  int64 play_time = 3;//当前歌曲已播放时间长度
  PlayStatus status = 4;
  uint32 Volume = 5;//房间音量
  uint32 play_mode = 6; /* 自动播放的播放模式选择 顺序、单曲、随机 */ /* PlayModeSelect */
}

enum PlayStatus {
  play = 0;//播放中
  pause = 1;//暂停
}

//与2060接口协议一致
message MusicInfo {
  string client_key = 1;  // 客户端唯一标识
  string name = 2;        // 歌曲名
  string author = 3;      // 作者
  uint32 uid = 4;         // 上传者id
  uint32 volume = 5;      // 音量
  int64 key = 6;          // 服务器标识
  string account = 7;     // 上传者account
  uint32 is_local = 8;    // 是否是客户端本地歌曲 1-是 0-否
  uint32 status = 9;      // 播放状态
  string nickname = 10;   // 上传者名字
  uint32 music_type = 11; //音乐类型 1原唱、2伴奏
}

//切换自动播放/点歌模式开关
message SwitchChannelPlayReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  AutoPlaySwitch switch = 3;
}

message SwitchChannelPlayResp {
  ga.BaseResp base_resp = 1;
}

//获取推荐歌曲歌单
message ChannelRcmdMusicMenuReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}

message ChannelRcmdMusicMenuResp {
  ga.BaseResp base_resp = 1;
  ListeningAutoPlayRcmdMenu menu = 2;
}

message ListeningAutoPlayRcmdMenu{
  string id = 1;//歌单id
  string name = 2;//歌单名字，在主动获取和歌单变化时有值
  string bg = 3;//背景图片，在主动获取和歌单变化时有值
  repeated MusicInfo musics = 4;//歌单列表，在主动获取和歌单变化时有值
}

//设置音量
message SetVolumeReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 volume = 3;
}

message SetVolumeResp {
  ga.BaseResp base_resp = 1;
}

//自动模式切歌
message CutAutoModeSongReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string music_menu_id = 3;
  int64 key = 4;//音乐服务器标识
}

message CutAutoModeSongResp {
  ga.BaseResp base_resp = 1;
}

//歌曲进度上报
message ReportChannelAutoSongProgressReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string music_menu_id = 3;
  int64 key = 4;//音乐服务器标识
  ProgressType progress = 5;
  enum ProgressType {
    start = 0;//开始
    end = 1;//结束
  }
}

message ReportChannelAutoSongProgressResp{
  ga.BaseResp base_resp = 1;
}

//切换自动播放/点歌模式
message SetChannelPlayModeReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  ChannelPlayMode mode = 3;
  string music_menu_id = 4;
  int64 key = 5;//音乐服务器标识
}

message SetChannelPlayModeResp {
  ga.BaseResp base_resp = 1;
}

//播放/暂停切换
message SetChannelPlayStatusReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string music_menu_id = 3;
  int64 key = 4;//音乐服务器标识
  PlayStatus status = 5;
}

message SetChannelPlayStatusResp {
  ga.BaseResp base_resp = 1;
}

/* 自动播放的播放模式选择 顺序、单曲、随机 */
enum PlayModeSelect{
  ORDER_MODE = 0; /* 顺序 */
  SINGLE_CYCLE = 1; /* 单曲 */
  RANDOM = 2; /* 随机 */
}
message SelectAutoPlayPlayModeRequest{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 play_mode = 5; /* PlayModeSelect */
}
message SelectAutoPlayPlayModeResponse{
  ga.BaseResp base_resp = 1;
  uint32 new_play_mode = 5; /* PlayModeSelect */
}

//自动播放歌曲暂停/播放状态通知
message AutoSongStatusNotify {
  uint32 channel_id = 1;
  AutoPlayInfo info = 2;//在自动播放模式下有值
}

//切换自动播放/点歌模式通知
message ChannelPlayModeNotify {
  uint32 channel_id = 1;
  ChannelPlayMode mode = 2;
  AutoPlayInfo info = 3;//在自动播放模式下有值
  uint32 music_list_mode = 4; // see MusicListType 战歌列表类型
}

//音量改变通知
message ChannelVolumeNotify {
  uint32 channel_id = 1;
  uint32 volume = 2;
}

/* 自动播放的播放模式选择推送 顺序、单曲、随机 */
message ChannelPlayModeSelectNotify {
  uint32 channel_id = 1;
  uint32 play_mode = 2; /* 自动播放的播放模式选择 顺序、单曲、随机 */ /* PlayModeSelect */
}

// ---------- 网易云相关接口

message WYYUserInfo {
  string id = 1; // 用户Id
  string nickname = 2; // 用户昵称
  string avatar_url = 3; // 头像
  uint32 gender = 4; // 性别, 未知： 0; 男：1; 女：2;
  string signature = 5; // 签名
  repeated WYYVipInfo vip_detail = 6; // see WYYVipInfo vip类型
  uint32 red_vip_level = 7; // 黑胶会员等级
  string red_vip_level_img = 8; // 黑胶会员等级图片
}

message WYYVipInfo {
  uint32 type = 1; // see vip类型
  int64 expire_time = 2 ;
}

enum WYYVipOpenDetailDto {
  ENUM_VIP_TYPE_NORMAL = 0; // 音乐包会员
  ENUM_VIP_TYPE_BLACK_VIP = 6; // 黑胶VIP会员
  ENUM_VIP_TYPE_BLACK_SVIP = 15; // 黑胶SVIP会员
  ENUM_VIP_TYPE_CAR = 13; // 车机端会员
  ENUM_VIP_TYPE_WATCH = 16; // 手表端会员
  ENUM_VIP_TYPE_TV = 17; // TV端会员
  ENUM_VIP_TYPE_RADIO = 18; // 音箱端会员
}

// 战歌列表类型
enum MusicListType {
  MusicListType_Default = 0;  // TT
  MusicListType_NC = 1;       // 网易云音乐
}

//message WYYSongInfo {
//  string id = 1; // 歌曲Id
//  string name = 2; // 歌曲名称
//  uint32 duration = 3; // 时长
//  repeated WYYSongArtist artists = 4; // 艺人列表
//  WYYSongAlbum album = 5; // 专辑
//  bool play_flag = 6; // 是否可以播放（true，false）
//  bool pay_play_flag = 7; // 是否需要付费才能播放（true、false）
//  bool vip_play_flag = 8; // 需要VIP才能播放
//  bool liked = 9; // 是否喜欢
//  string cover_img_url = 10; // 歌曲封面url
//  bool visible = 11; // 是否有版权（true，false）
//  string pl_level = 12; // 用户可播放歌曲最大码率对应的level
//}

message WYYSongArtist {// 艺人
  string id = 1;
  string name = 2;
}

message WYYSongAlbum {// 专辑
  string id = 1;
  string name = 2;
}

message WYYSongResource {
  string id = 1;
  string url = 2; // 歌曲播放url
  uint32 size = 3; // 歌曲大小
  string md5 = 4; // 歌曲的MD5
  string level = 5; // 当前返回的歌曲码率对应的level
}

message ListeningChangePlayerReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 music_list_mode = 3; // see MusicListType 战歌列表类型
}
message ListeningChangePlayerResp{
  ga.BaseResp base_resp = 1;
  uint32 music_list_mode = 2; // see MusicListType 战歌列表类型
}

message ListeningGetPlayerStatusReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message ListeningGetPlayerStatusResp{
  ga.BaseResp base_resp = 1;
  uint32 music_list_mode = 2;// see MusicListType 战歌列表类型
  bool close = 3;
}

message ListeningSearchSongByKeyReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string keyword = 3;
  uint32 page = 4;
}
message ListeningSearchSongByKeyResp{
  ga.BaseResp base_resp = 1;
  repeated WYYMusicInfo music_info_list = 2;
  bool has_more = 3;
  uint32 count = 4;
}

message ListeningGetSongListByTypeReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  uint32 type = 3; // SEE enum  SongListType
  uint32 page = 4;
}
message ListeningGetSongListByTypeResp{
  ga.BaseResp base_resp = 1;
  repeated WYYMusicInfo music_info_list = 2;
  uint32 count = 3;
  bool has_more = 4;
}
enum SongListType{
  ENUM_SONG_LIST_TYPE_LIKE = 0;  // 默认，我喜欢的
  ENUM_SONG_LIST_TYPE_RCMD = 1;  // 推荐
  ENUM_SONG_LIST_TYPE_TOP = 2;  // 热榜
  ENUM_SONG_LIST_TYPE_FM = 3;  // 漫游
}

message ListeningGetSongListListReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message ListeningGetSongListListResp{
  ga.BaseResp base_resp = 1;
  repeated SongListList list = 2;
}

message SongListList{
  string cover_img_url = 1; // 歌单图片
  string name = 2; // 歌单名称
  uint32 type = 3;
  string light_cover_img_url = 4; // 点亮图片
}

message WYYMusicInfo {
  ga.channel.MusicInfoV2 base_info = 1;
  string album_name = 2;
  string alg = 3;
  bool like = 4; // true: 收藏， false: 取消收藏
  string cover_img_url = 5;  // 歌曲封面url
}

message ListeningOrderSongReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  repeated WYYMusicInfo music_info_list = 3;
}
message ListeningOrderSongResp{
  ga.BaseResp base_resp = 1;
  repeated WYYMusicInfo music_added_list = 2;
  uint32 max_music_count = 3;
  uint32 current_music_count = 4;
}

message ListeningGetPlayListReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message ListeningGetPlayListResp{
  ga.BaseResp base_resp = 1;
  repeated WYYMusicInfo music_list = 2;
}

message ListeningGetPlayerUserInfoReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message ListeningGetPlayerUserInfoResp{
  ga.BaseResp base_resp = 1;
  WYYUserInfo user_info = 2;
}

message ListeningGetSongResourceReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string song_id = 3;
}
message ListeningGetSongResourceResp{
  ga.BaseResp base_resp = 1;
  WYYSongResource song_resource = 2;
}

message ListeningReportRecordReq {
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string action = 3; // 行为，startplay:开始播放，结束播放：play
  uint64 start_log_time = 4;
  string song_id = 5;
  string alg = 6;
  uint64 time = 7; // 播放时长，单位：秒， 播放完成或者中止播放时的时长，按照实际播放时长统计
  string end = 8; // 结束方式回传枚举值：playend：正常结束；interrupt：第三方APP打断： exception: 错误； ui: 用户切歌
}

message ListeningReportRecordResp {
  ga.BaseResp base_resp = 1;
}

message ListeningLogoutReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
}
message ListeningLogoutResp{
  ga.BaseResp base_resp = 1;
}

// 网易云 爱心 收藏
message ListeningLikeSongWYYReq{
  ga.BaseReq base_req = 1;
  uint32 channel_id = 2;
  string song_id = 3;
  bool like = 4; // true: 收藏， false: 取消收藏
}

message ListeningLikeSongWYYResp{
  ga.BaseResp base_resp = 1;
}
