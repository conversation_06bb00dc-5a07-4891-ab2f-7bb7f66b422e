// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.usual_device;



import "usual_device_logic/usual-device-logic_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/usual_device;usual_device";

service UsualDeviceLogic {
    option (ga.api.extension.logic_service_name) = "usual-device-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.UsualDeviceLogic/";
    rpc GetFaceAuthCheckResult(ga.usual_device_logic.GetFaceAuthCheckResultReq) returns (ga.usual_device_logic.GetFaceAuthCheckResultResp) {
        option (ga.api.extension.command) = {
             id: 30801
        };
    }
    rpc GetMessageCheckInfo(ga.usual_device_logic.GetMessageCheckInfoReq) returns (ga.usual_device_logic.GetMessageCheckInfoResp) {
        option (ga.api.extension.command) = {
             id: 30802
        };
    }
    rpc GetMessageCheckResult(ga.usual_device_logic.GetMessageCheckResultReq) returns (ga.usual_device_logic.GetMessageCheckResultResp) {
        option (ga.api.extension.command) = {
             id: 30803
        };
    }
}

