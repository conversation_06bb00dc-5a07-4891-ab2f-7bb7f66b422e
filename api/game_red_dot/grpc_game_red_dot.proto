syntax = "proto3";
package ga.api.game_red_dot;

import "game_red_dot_logic/game_red_dot_logic.proto";
import "api/extension/extension.proto";

option java_package = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/game_red_dot;game_red_dot";

service GameRedDotLogic {
  option (ga.api.extension.logic_service_name) = "game-red-dot-logic";
  option (ga.api.extension.logic_service_language) = "go";

  // 拉取红点信息
  rpc GetRedDotInfo(ga.game_red_dot_logic.GetRedDotInfoReq) returns (ga.game_red_dot_logic.GetRedDotInfoResp) {
    option (ga.api.extension.command) = {
      id: 5091;
    };
  }

  // 标记红点已读
  rpc MarkRedDotRead(ga.game_red_dot_logic.MarkRedDotReadReq) returns (ga.game_red_dot_logic.MarkRedDotReadResp) {
    option (ga.api.extension.command) = {
      id: 5092;
    };
  }
}
