// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.wish_list;

import "wishlistlogic/wish-list-logic_.proto";
import "api/extension/extension.proto";


option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/wish_list;wish_list";

service WishListLogic {
    option (ga.api.extension.logic_service_name) = "wish-list-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.WishListLogic/";
    rpc GetAnchorWishList(ga.wishlistlogic.GetAnchorWishListReq) returns (ga.wishlistlogic.GetAnchorWishListResp) {
        option (ga.api.extension.command) = {
             id: 50700
        };
    }
    rpc SetAnchorWishList(ga.wishlistlogic.SetAnchorWishListReq) returns (ga.wishlistlogic.SetAnchorWishListResp) {
        option (ga.api.extension.command) = {
             id: 50701
        };
    }
    rpc GetWishGratitudeWords(ga.wishlistlogic.GetWishGratitudeWordsReq) returns (ga.wishlistlogic.GetWishGratitudeWordsResp) {
        option (ga.api.extension.command) = {
             id: 50702
        };
    }
}

