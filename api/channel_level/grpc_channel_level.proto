// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.channel_level;



import "channel_level/channel-level_.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_level;channel_level";

service ChannelLevelLogic {
    option (ga.api.extension.logic_service_name) = "channel-level-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ChannelLevelLogic/";
    
    rpc GetChannelLevelInfo(ga.channel_level.GetChannelLevelInfoReq) returns (ga.channel_level.GetChannelLevelInfoResp) {
        option (ga.api.extension.command) = {
             id: 30370
        };
    }
    rpc GetChannelLevelSettings(ga.channel_level.GetChannelLevelSettingsReq) returns (ga.channel_level.GetChannelLevelSettingsResp) {
        option (ga.api.extension.command) = {
             id: 30371
        };
    }
}

