syntax = "proto3";
package ga.api.sync;



import "api/extension/extension.proto";
import "sync/sync.proto";

//server_name=synclogic
//language=cpp

option java_package = "com.quwan.tt.proto.api";
option go_package = "golang.52tt.com/protocol/app/api/sync;sync";
option objc_class_prefix = "RPC";

service SyncLogic {
    rpc CheckSyncKey(ga.sync.CheckSyncKeyReq) returns (ga.sync.CheckSyncKeyResp) {
        option (ga.api.extension.command) = {
            id: 74
            deprecated: true
        };
    }

    rpc ConfigV2CheckSync(ga.sync.CheckConfigSyncKeyReq) returns (ga.sync.CheckConfigSynckeyResp) {
        option (ga.api.extension.command) = {
            id: 30011
        };
    }

    rpc ConfigV2Sync(ga.sync.SyncConfigReq) returns (ga.sync.SyncConfigResp) {
        option (ga.api.extension.command) = {
            id: 30010
        };
    }

    rpc ReportSyncRead(ga.sync.ReportSyncReadReq) returns (ga.sync.ReportSyncReadResp) {
        option (ga.api.extension.command) = {
            id: 175
        };
    }

    rpc Sync(ga.sync.SyncReq) returns (ga.sync.SyncResp) {
        option (ga.api.extension.command) = {
            id: 32
        };
    }

    option (ga.api.extension.logic_service_name) = "synclogic";
    option (ga.api.extension.logic_service_language) = "cpp";
}
