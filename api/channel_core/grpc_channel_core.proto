// Code generated by protoc-gen-ga-api. DO NOT EDIT.
// 	protoc        v3.9.1

syntax = "proto3";
package ga.api.channel_core;

import "channel/channel_.proto";
import "channel_core/channel_core.proto";
import "api/extension/extension.proto";

option java_package      = "com.quwan.tt.proto.api";
option objc_class_prefix = "RPC";
option go_package = "golang.52tt.com/protocol/app/api/channel_core;channel_core";

service ChannelCoreLogic {
    option (ga.api.extension.logic_service_name) = "channel-core-logic";
    option (ga.api.extension.logic_service_language) = "go";
    option (ga.api.extension.logic_service_uri_rewrite) = "/logic.ChannelCoreLogic/";
    rpc ChannelEnter(ga.channel.ChannelEnterReq) returns (ga.channel.ChannelEnterResp) {
        option (ga.api.extension.command) = {
             id: 423;
        };
    }
    rpc ChannelQuit(ga.channel.ChannelQuitReq) returns (ga.channel.ChannelQuitResp) {
        option (ga.api.extension.command) = {
             id: 424;
        };
    }
    rpc ChannelMicHold(ga.channel.ChannelGetMicReq) returns (ga.channel.ChannelGetMicResp) {
        option (ga.api.extension.command) = {
             id: 430;
        };
    }
    rpc ChannelMicRelease(ga.channel.ChannelReleaseMicReq) returns (ga.channel.ChannelReleaseMicResp) {
        option (ga.api.extension.command) = {
             id: 431;
        };
    }
    rpc ChannelMicTakeHold(ga.channel.TakeUserToChannelMicReq) returns (ga.channel.TakeUserToChannelMicResp) {
        option (ga.api.extension.command) = {
             id: 2069;
        };
    }
    rpc ChannelEnterV2(ga.channel_core.ChannelEnterRequest) returns (ga.channel_core.ChannelEnterResponse) {
        option (ga.api.extension.command) = {
             id: 50858;
        };
    }
}


