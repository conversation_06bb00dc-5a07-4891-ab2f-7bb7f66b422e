package server

import (
	"context"
	"crypto/rand"
	"golang.52tt.com/pkg/log"
	presentPB_ "golang.52tt.com/protocol/app/userpresent"
	"math/big"

	accountPB "golang.52tt.com/protocol/services/account-go"
	pb "golang.52tt.com/protocol/services/present-middleware"
	presentPB "golang.52tt.com/protocol/services/userpresent"
	"time"
)

func (s *AllMicPresentMiddlewareMgr) BeforeSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {
	// 类型断言
	out, ok := outInter.(*pb.AllMicSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.AllMicSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "BeforeSendPresent -- para inter need *PresentSendBase type. ")
		return nil
	}
	if in.ServiceInfo == nil {
		in.ServiceInfo = &pb.ServiceCtrlInfo{}
	}
	sendExtend.nowTs = time.Now()
	out.ItemSource = in.ItemSource

	ctx = s.SetPresentGoCtx(ctx, in.GetSendUid())

	// 预处理，获得送礼要用的相关信息
	err = s.preSendPresent(ctx, sendExtend.nowTs, in.SendUid,
		in.TargetUidList, in.ItemId, in.ChannelId, uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM), sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareMgr -- preSendPresent fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}
	return err
}

func (s *AllMicPresentMiddlewareMgr) SendingPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	_, ok := outInter.(*pb.AllMicSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.AllMicSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "SendingPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//冻结红钻/t豆

	sendExtend.remainTbeans, sendExtend.remainSource, sendExtend.realItemSource, err = s.tryFreeze(ctx, sendExtend, in.Count, in.ItemSource, in.SourceId, uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM))
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- tryFreeze fail. uid:%v c:%v err:%v",
			in.SendUid, in.ChannelId, err)
		return err
	}

	sendExtend.extendInfo = &ExtendInfo{targetInfoMap: map[uint32]*TargetExtendInfo{}}

	//赠送礼物
	sendExtend.sucOrders, err = s.sendPresent(ctx, sendExtend, in.ServiceInfo, in.Count, in.AppId, in.MarketId, in.ItemSource, in.SendSource, in.BatchType, 0, in.IsOptValid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __SendPresent fail. uid:%v ret:%v req:%v",
			in.SendUid, err, in)
		return err
	}
	log.ErrorWithCtx(ctx, "after send present extend info : %v", sendExtend.extendInfo)

	//礼物周边信息处理
	err = s.ProcPresentWatch(ctx, sendExtend, in.Count, in.IsOptValid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendingPresent -- __ProcPresentWatch fail:%v. _req:%v", err, in)
	}

	//提交支付订单
	err = s.commitPayOrder(ctx, sendExtend.sucOrders, uint32(pb.PresentSendMethodType_PRESENT_TYPE_ROOM), in.ItemSource, sendExtend.presentConfig.GetItemConfig().GetPriceType())

	return err
}

func (s *AllMicPresentMiddlewareMgr) AfterSendPresent(ctx context.Context, inter interface{}, outInter interface{}, sendExtend *baseSendExtend) (err error) {

	// 类型断言
	out, ok := outInter.(*pb.AllMicSendPresentResp)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentResp type. ")
		return nil
	}
	in, ok := inter.(*pb.AllMicSendPresentReq)
	if !ok {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- para inter need *SendPresentReq type. ")
		return nil
	}

	//【防小白骚扰】24小时内单笔送出/收到1000元礼物的用户 存redis
	consumeMap := make(map[uint32]uint32, 0)
	for _, order := range sendExtend.sucOrders {
		consumeMap[order.targetUid] = sendExtend.presentConfig.GetItemConfig().GetPrice()
	}

	err = s.SetHighConsume(ctx, consumeMap, in.SendUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "AfterSendPresent -- SetHighConsume err, uid %d ,  err : %v. ", in.SendUid, err)
		return nil
	}
	//oss v2不用了 v3在 userpresent
	//s.ossReport(ctx, sendUser, targetUserMap, channelSimpleInfo, nowTs, presentConfig, in.Count, isVaild, in.ItemSource,
	//	in.BatchType, uniqOrderId, in.SendSource, in.ServiceInfo.ClientType)

	////ConsumeSdkEvent kafka
	//activateChannel, _, err := s.accountCli.GetUserActivateInfo(ctx, in.SendUid)
	//devId := make([]byte, 0)
	//hex.Encode(in.ServiceInfo.DeviceId, devId)
	//err = s.sdkKfkProduce.ProduceNewConsumeSdkEvent(sendExtend.sendUser.Uid, sendExtend.nowTs, in.Count*sendExtend.presentConfig.GetItemConfig().GetPrice(),
	//	activateChannel, sendExtend.sendUser.Username, sendExtend.uniqOrderId, in.ServiceInfo.ClientIp, in.ServiceInfo.ClientType, string(devId))
	//
	//if err != nil {
	//	log.ErrorWithCtx(ctx, "ProduceNewConsumeSdkEvent err %v info %d", err, in.SendUid)
	//}

	//送礼后处理，填充动效、推送等
	s.FillRespAndPush(ctx, sendExtend, in, out)
	return err
}

//func (s *AllMicPresentMiddlewareMgr) getStrMicroTime(time time.Time) string {
//	strMicroTime := fmt.Sprintf("batch_%s_%d", time.Format("**************"), (time.UnixNano()/100)%100000)
//	return strMicroTime
//}

func (s *AllMicPresentMiddlewareMgr) procPushEvent(ctx context.Context, sendExtend *baseSendExtend, in *pb.AllMicSendPresentReq,
	out *pb.AllMicSendPresentResp, sucUsers map[uint32]*accountPB.UserResp) {
	bIsNoPush := false
	//房间人数较多，则丢一部分推送
	channelId := sendExtend.channelSimpleInfo.GetChannelSimple().GetChannelId()
	sendUser := sendExtend.sendUser
	totalCount := in.Count * uint32(len(sucUsers))

	targetUids := []uint32{}
	for _, user := range sucUsers {
		targetUids = append(targetUids, user.GetUid())
	}

	if uint32(presentPB.PresentPriceType_PRESENT_PRICE_RED_DIAMOND) == sendExtend.presentConfig.GetItemConfig().GetPriceType() && channelId != 0 {
		// 增加短超时
		tmpCtx, cancel := context.WithTimeout(ctx, time.Millisecond*100)
		defer cancel()
		size, err := s.channelolCli.GetChannelMemberSize(tmpCtx, sendUser.GetUid(), channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentBatchSendService::procPushEvent GetChannelMemberSize failed. err:%v, uid:%v", err, sendUser.Uid)
		}
		dropRadio := (size - 1000) / 30
		if dropRadio == 0 {
			dropRadio = 1
		}
		if dropRadio > 90 {
			dropRadio = 90
		}
		roll, _ := rand.Int(rand.Reader, big.NewInt(100))
		if size > 1000 && uint32(roll.Int64()) > dropRadio {
			bIsNoPush = true
		}
	}

	tmpGaPushBatchInfo := pb.PresentBatchInfoMsg{TargetList: []*pb.PresentBatchTargetInfo{}}
	tmpGaPushBatchInfo.ItemInfo = out.ItemInfo
	tmpGaPushBatchInfo.ChannelId = channelId
	tmpGaPushBatchInfo.ItemId = sendExtend.presentConfig.GetItemConfig().ItemId
	tmpGaPushBatchInfo.BatchType = in.BatchType
	tmpGaPushBatchInfo.SendTime = uint64(sendExtend.nowTs.UnixNano() / 1000000)
	tmpGaPushBatchInfo.SendUid = sendUser.GetUid()
	tmpGaPushBatchInfo.SendAccount = sendUser.GetUsername()
	tmpGaPushBatchInfo.SendNickname = sendUser.GetNickname()
	tmpGaPushBatchInfo.TotalItemCount = totalCount
	if sendExtend.presentConfig.GetItemConfig().GetExtend().GetFlowId() > 0 {
		tmpGaPushBatchInfo.ItemInfo.DynamicTemplateId = 0
	}

	for _, user := range sucUsers {
		extend, ok := sendExtend.extendInfo.targetInfoMap[user.GetUid()]
		if ok {
			tmpGaPushBatchInfo.TargetList = append(tmpGaPushBatchInfo.TargetList, &pb.PresentBatchTargetInfo{Uid: user.GetUid(),
				Account: user.GetUsername(), Nickname: user.GetNickname(), ExtendJson: extend.userExtendJson})
		} else {
			tmpGaPushBatchInfo.TargetList = append(tmpGaPushBatchInfo.TargetList, &pb.PresentBatchTargetInfo{Uid: user.GetUid(),
				Account: user.GetUsername(), Nickname: user.GetNickname()})
		}
	}
	targetIndex := 0

	messageItemInfo := presentPB_.PresentSendItemInfo{
		ItemId:            out.ItemInfo.ItemId,
		Count:             out.ItemInfo.Count,
		ShowBatchEffect:   out.ItemInfo.ShowBatchEffect,
		ShowEffect:        out.ItemInfo.ShowEffect,
		ShowEffectV2:      out.ItemInfo.ShowEffectV2,
		FlowId:            out.ItemInfo.FlowId,
		SendType:          out.ItemInfo.SendType,
		DynamicTemplateId: out.ItemInfo.DynamicTemplateId,
		IsBatch:           true,
	}

	drawPic := &presentPB_.DrawPresentPicture{LineList: []*presentPB_.PresentLine{}}
	if out.ItemInfo.DrawPresentPic != nil {
		for _, line := range out.ItemInfo.DrawPresentPic.LineList {
			drawLine := &presentPB_.PresentLine{}
			for _, point := range line.PointList {
				drawLine.PointList = append(drawLine.PointList, &presentPB_.PresentPoint{X: point.X, Y: point.Y})
			}
			drawLine.ItemId = line.ItemId
			drawPic.LineList = append(drawPic.LineList, drawLine)
		}
	}

	messageItemInfo.DrawPresentPic = drawPic

	err := s.PushBatchInfoMsgToChannel(ctx, sendUser, sendExtend.presentConfig, totalCount, sendExtend.nowTs, channelId, &tmpGaPushBatchInfo, sendExtend)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushBatchInfoMsgToChannel -- PushMulticast. uid:%v, channel_id:%v, item_id:%v", sendUser.Uid,
			channelId, sendExtend.presentConfig.GetItemConfig().GetItemId())
	}

	log.DebugWithCtx(ctx, "procPushEvent -- targetUids. uid:%v, targetUids:[%v] , extendInfo.userExtendJson:[%v]", sendUser.Uid, sucUsers, sendExtend.extendInfo.userExtendJson)

	for _, uid := range targetUids {
		userExtendJson := ""
		user, ok := sucUsers[uid]
		if !ok {
			continue
		}
		out.TargetList = append(out.TargetList, &pb.PresentTargetUserInfo{Uid: user.GetUid(), Account: user.GetUsername(), Name: user.GetNickname()})
		out.MsgInfo.TargetList = append(out.MsgInfo.TargetList, &pb.PresentBatchTargetInfo{Uid: user.GetUid(), Account: user.GetUsername(), Nickname: user.GetNickname()})
		extend, ok := sendExtend.extendInfo.targetInfoMap[uid]
		if ok {
			userExtendJson = extend.userExtendJson
		}
		targetIndex++

		if !bIsNoPush {
			_ = s.pushNotificationToUser(ctx, sendUser, sendExtend.presentConfig, totalCount, sendExtend.nowTs, channelId, &messageItemInfo, user, userExtendJson, uid, sendExtend)
		}

		if !bIsNoPush && channelId != 0 {
			_ = s.pushNotificationToChannel(ctx, sendUser, user, sendExtend.nowTs, channelId, &messageItemInfo, sendExtend)
		}

		//如果是特定礼物全服推送
		s.PushPresentBreakingNewsToAll(ctx, sendUser, sendExtend.presentConfig, totalCount, sendExtend.nowTs, channelId, sucUsers[uid], sendExtend)
	}
}

func (s *AllMicPresentMiddlewareMgr) FillRespAndPush(ctx context.Context, sendExtend *baseSendExtend, in *pb.AllMicSendPresentReq, out *pb.AllMicSendPresentResp) {

	presentConfig := sendExtend.presentConfig
	sendUser := sendExtend.sendUser

	remainTbeans := sendExtend.remainTbeans
	remainSource := sendExtend.remainSource

	sucUsers := make(map[uint32]*accountPB.UserResp)
	for _, item := range sendExtend.sucOrders {
		sucUsers[item.targetUid] = sendExtend.targetUserMap[item.targetUid]
	}
	nowTs := sendExtend.nowTs
	extendInfo := sendExtend.extendInfo

	//送礼特效与动效
	showEffect, showEffectV2, FlowId := s.getPresentEffect(ctx, presentConfig.GetItemConfig(), in.Count)
	templateId, err := s.getPresentDynamicEffectTemplate(ctx, sendUser.GetUid(), presentConfig.GetItemConfig(), in.Count)
	if err != nil {
		return
	}

	out.CurTbeans = uint64(remainTbeans)
	out.SourceRemain = remainSource
	out.ItemInfo.ItemId = presentConfig.GetItemConfig().GetItemId()
	out.ItemInfo.Count = in.Count
	out.ItemInfo.ShowEffect = showEffect
	out.ItemInfo.ShowEffectV2 = showEffectV2
	out.ItemInfo.FlowId = FlowId
	out.ItemInfo.IsBatch = true
	out.ItemInfo.SendType = in.SendType
	out.ItemInfo.ShowBatchEffect = true

	if presentConfig.GetItemConfig().GetPriceType() == uint32(presentPB.PresentPriceType_PRESENT_PRICE_TBEAN) &&
		(presentConfig.GetItemConfig().GetExtend().GetShowEffect() == 1 || presentConfig.GetItemConfig().GetPrice() >= 10000) {
		out.ItemInfo.ShowBatchEffect = false
	}
	//涂鸦送礼
	if in.SendType == uint32(pb.PresentSendType_PRESENT_SEND_DRAW) && presentConfig.GetItemConfig().GetExtend().GetTag() == uint32(presentPB_.PresentSendSourceType_E_SEND_SOURCE_DRAW_GIFT) {
		out.ItemInfo.SendType = in.SendType
		out.ItemInfo.DrawPresentPic = in.DrawPresentPic
		out.ItemInfo.ShowBatchEffect = false
	}
	out.ItemInfo.DynamicTemplateId = templateId

	//推送
	s.procPushEvent(ctx, sendExtend, in, out, sucUsers)

	log.DebugWithCtx(ctx, "extend info : %v", extendInfo)

	// 填msgInfo
	out.MsgInfo.ItemInfo = out.ItemInfo
	out.MsgInfo.ChannelId = in.ChannelId
	out.MsgInfo.SendTime = uint64(nowTs.UnixNano() / 1000000)
	out.MsgInfo.SendUid = sendUser.GetUid()
	out.MsgInfo.SendNickname = sendUser.GetNickname()
	out.MsgInfo.SendAccount = sendUser.GetUsername()
	out.MsgInfo.ItemId = presentConfig.GetItemConfig().GetItemId()
	out.MsgInfo.TotalItemCount = in.Count * uint32(len(sucUsers))
	out.MsgInfo.BatchType = in.BatchType
	out.MsgInfo.ExtendJson = extendInfo.userExtendJson

	if presentConfig.GetItemConfig().GetExtend().FlowId > 0 {
		out.MsgInfo.ItemInfo.DynamicTemplateId = 0
	}
}
