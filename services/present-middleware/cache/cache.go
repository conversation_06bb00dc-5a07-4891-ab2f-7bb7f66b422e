package cache

import (
	"context"
	"github.com/go-redis/redis/v8"
	"golang.52tt.com/pkg/log"
	"strconv"
	"time"
)

const (
	PushContentZSet          = "push_content"
	PushContentPrefix        = "push_content_"
	PushContentChannelPrefix = "push_content_channel_"
)

var PresentMiddlewareCacheClient *PresentMiddlewareCache

type PresentMiddlewareCache struct {
	redisClient *redis.Client
}

func InitPresentMiddlewareCache(r *redis.Client) {
	PresentMiddlewareCacheClient = &PresentMiddlewareCache{redisClient: r}
}

// AddPushContentToZSet 将推送加入redis队列
func (c *PresentMiddlewareCache) AddPushContentToZSet(ctx context.Context, content string, channel uint32, pushTime, delayTime uint64,
	boxId, key string) error {
	pipe := c.redisClient.Pipeline()
	defer func(pipe redis.Pipeliner) {
		err := pipe.Close()
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareCache AddPushContentToZSet pipe.Close() fail, err %v", err)
		}
	}(pipe)

	// 盒子的实际信息
	pipe.HSet(ctx, PushContentPrefix+boxId, key, content)

	// 用于按时间过滤
	pipe.ZAdd(ctx, PushContentZSet, &redis.Z{
		Score:  float64(pushTime + delayTime),
		Member: boxId,
	})

	// 房间中有哪些箱子,给个超时，一定时间没箱子就把KEY清掉，防止堆积太多空箱子
	pipe.SAdd(ctx, PushContentChannelPrefix+strconv.Itoa(int(channel)), boxId)
	pipe.Expire(ctx, PushContentChannelPrefix+strconv.Itoa(int(channel)), time.Minute)

	_, err := pipe.Exec(ctx)

	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareCache AddPushContentToZSet fail, content %s ,err %v", content, err)
		return err
	}

	return nil
}

// GetPushContentTimeOut 取出并删除redis队列中所有到期的推送
func (c *PresentMiddlewareCache) GetPushContentTimeOut(ctx context.Context) (pushList []string, err error) {
	pipe := c.redisClient.Pipeline()
	defer func(pipe redis.Pipeliner) {
		err := pipe.Close()
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareCache  pipe.Close() fail, err %v", err)
		}
	}(pipe)

	nowTime := strconv.FormatInt(time.Now().Add(time.Second).Unix()*1000, 10)
	pushList = make([]string, 0)
	pushResp := pipe.ZRangeByScore(ctx, PushContentZSet, &redis.ZRangeBy{
		Min:    "0",
		Max:    nowTime,
		Offset: 0,
		Count:  10,
	})

	// 这里就不用一个个删PushContentPrefix的key了，会过期
	pipe.ZRemRangeByScore(ctx, PushContentZSet, strconv.Itoa(0), nowTime)

	_, err = pipe.Exec(ctx)

	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareCache pipe Exec fail, err %v", err)
		return pushList, err
	}

	if len(pushResp.Val()) == 0 {
		log.DebugWithCtx(ctx, "PresentMiddlewareCache no Expire msg")
		return pushList, nil
	}

	// 然后，mGet获取所有key对应的content,并删除。
	itemList := make([]*redis.StringStringMapCmd, 0)
	for _, item := range pushResp.Val() {
		contentResp := pipe.HGetAll(ctx, PushContentPrefix+item)
		itemList = append(itemList, contentResp)
		pipe.Del(ctx, PushContentPrefix+item)
	}

	_, err = pipe.Exec(ctx)

	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareCache pipe Exec fail, err %v", err)
		return pushList, err
	}

	for _, item := range itemList {
		for _, content := range item.Val() {
			pushList = append(pushList, content)
		}
	}

	return pushList, nil
}

// GetPushContentByKey 取出并删除特定推送
func (c *PresentMiddlewareCache) GetPushContentByKey(ctx context.Context, boxId string, channelId uint32) (pushResp map[string]string, err error) {
	pushResp = make(map[string]string)

	// pipeline确认对应推送是否存在，并删除
	pipe := c.redisClient.Pipeline()
	defer func(pipe redis.Pipeliner) {
		err := pipe.Close()
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareCache  pipe.Close() fail, err %v", err)
		}
	}(pipe)

	resp := pipe.HGetAll(ctx, PushContentPrefix+boxId)
	pipe.Del(ctx, PushContentPrefix+boxId)
	pipe.ZRem(ctx, PushContentZSet, boxId)
	pipe.SRem(ctx, PushContentChannelPrefix+strconv.Itoa(int(channelId)), boxId)

	_, err = pipe.Exec(ctx)

	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareCache pipe Exec fail, err %v", err)
		return pushResp, err
	}

	return resp.Val(), nil
}

// GetPushContentByChannelId 查询房间内的箱子
func (c *PresentMiddlewareCache) GetPushContentByChannelId(ctx context.Context, channelId uint32) (pushResp []string, err error) {
	pushResp = make([]string, 0)

	// 首先，单独拿出对应房间包含的key
	resp, err := c.redisClient.SMembers(ctx, PushContentChannelPrefix+strconv.Itoa(int(channelId))).Result()

	// 这里找不到就直接返回
	if err == redis.Nil {
		return pushResp, nil
	} else if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareCache GetPushContentByChannelId SMembers fail, key %s ,  err %v",
			PushContentChannelPrefix+strconv.Itoa(int(channelId)), err)
		return pushResp, err
	}

	pipe := c.redisClient.Pipeline()
	defer func(pipe redis.Pipeliner) {
		err := pipe.Close()
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentMiddlewareCache  pipe.Close() fail, err %v", err)
		}
	}(pipe)

	keyList := make([]*redis.StringStringMapCmd, 0)
	for _, item := range resp {
		tmpResp := pipe.HGetAll(ctx, PushContentPrefix+item)
		keyList = append(keyList, tmpResp)
	}

	_, err = pipe.Exec(ctx)

	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareCache pipe Exec fail, err %v", err)
		return pushResp, err
	}

	for _, item := range keyList {
		for _, content := range item.Val() {
			pushResp = append(pushResp, content)
		}
	}

	return pushResp, nil
}

// LockOrder 以order_id为key的订单锁
func (c *PresentMiddlewareCache) LockOrder(ctx context.Context, orderId string, expire time.Duration) (bool, error) {
	return c.redisClient.SetNX(ctx, orderId, "1", expire).Result()
}

func (c *PresentMiddlewareCache) UnlockOrder(ctx context.Context, orderId string) {
	err := c.redisClient.Del(ctx, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "PresentMiddlewareCache UnlockOrder fail, orderId %s, err %v", orderId, err)
	}
	return
}
