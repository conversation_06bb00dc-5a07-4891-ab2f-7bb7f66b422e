package cache

import(
	context "context"
	redis "github.com/go-redis/redis"
	pb "golang.52tt.com/protocol/services/headwear-go"
	time "time"
)

type IHeadwearCache interface {
	BatGetUserHeadwearInUse(ctx context.Context, uidList []uint32) (map[uint32]*pb.UserHeadwearInfo,error)
	BatchGetHeadwearConfigByIdList(ctx context.Context, headwearIdList []uint32) (map[uint32]*pb.HeadwearConfig,error)
	BatchSetHeadwearConfig(ctx context.Context, confList []*pb.HeadwearConfig) error
	ClearHeadwearConfig() error
	DelUserHeadwearInUse(uid uint32) error
	GetAllHeadwearConfigList(ctx context.Context) (map[uint32]*pb.HeadwearConfig,error)
	GetDB() *redis.Ring
	GetHeadwearConfigById(ctx context.Context, headwearId uint32) (*pb.HeadwearConfig,error)
	GetHeadwearConfigCount(ctx context.Context) (uint32,error)
	GetUserHeadwearInUse(ctx context.Context, uid uint32) (*pb.UserHeadwearInfo,bool,error)
	Lock(key string, ttl time.Duration) (bool,error)
	PopGiveHeadwearToUser() (*pb.GiveHeadwearToUserReq,error)
	PushGiveHeadwearToUser(isCheckCnt bool, giveItems ...*pb.GiveHeadwearToUserReq) error
	SetHeadwearConfig(ctx context.Context, conf *pb.HeadwearConfig) error
	SetUserHeadwearInUse(ctx context.Context, uid uint32, info *pb.UserHeadwearInfo) error
}

