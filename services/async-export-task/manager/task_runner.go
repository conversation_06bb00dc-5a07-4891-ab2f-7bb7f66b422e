package manager

import (
    "context"
    "database/sql"
    "fmt"
    "golang.52tt.com/pkg/log"
    pb "golang.52tt.com/protocol/services/async-export-task"
    "os"
)

func (mgr *AsyncExportTaskMgr) RunLastTask() {
    task, err := mgr.store.GetLastUncompletedTask(context.Background())
    if err != nil {
        if err == sql.ErrNoRows {
            log.Infof("GetLastUncompletedTask no data")
        } else {
            log.Errorf("GetLastUncompletedTask err=%v", err)
        }
        return
    }

    if task.Percent > 0 {
        log.Infof("wait task id=%d finish", task.Id)
        return
    }

    taskType := pb.TaskType(task.TaskType)
    handleTask, ok := mgr.taskMap[taskType]
    if !ok {
        log.Errorf("Cannot find taskType=%d handler", taskType)
        return
    }

    filePath, key, contentType, err := handleTask.Handle(task)
    if err != nil {
        log.Errorf("Handle taskType=%d err=%v", taskType, err)
        return
    }

    err = mgr.uploadFile(filePath, key, contentType)
    if err != nil {
        log.Errorf("mgr.uploadFile err=%v", err)
        err = mgr.store.UpdateTaskErrData(context.Background(), task.Id, fmt.Sprintf("uploadFile err=%v", err))
        if err != nil {
            log.Errorf("store.UpdateTaskErrData err=%v", err)
        }
        return
    }

    err = mgr.store.UpdateTaskObsKey(context.Background(), task.Id, key)
    if err != nil {
        log.Errorf("store.UpdateTaskObsKey err=%v", err)
        return
    }

    err = os.Remove(filePath)
    if err != nil {
        log.Errorf("os.Remove %s err=%v", filePath, err)
    }

    err = mgr.store.UpdateTaskPercent(context.Background(), task.Id, 100)
    if err != nil {
        log.Errorf("store.UpdateTaskPercent err=%v", err)
        return
    }
}
