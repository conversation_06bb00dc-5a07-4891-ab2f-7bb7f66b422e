package store

import (
	"fmt"
	"time"
	"context"
	"golang.52tt.com/pkg/log"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
)

const (
	FinishBeforeTypeFalse = 0
	FinishBeforeTypeTrue  = 1
)

var createMissionTbl = `CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    uid int(10) unsigned NOT NULL COMMENT 'uid',
    mission_type tinyint NOT NULL COMMENT '任务类型',
    status tinyint unsigned NOT NULL COMMENT '状态 0-未完成 1-已完成',
    fin_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    mtime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE INDEX idx_uid (uid, mission_type)
)engine=InnoDB default charset=utf8 COMMENT "任务状态记录表";`

func GenMissionTblName(uid uint32) string {
	return fmt.Sprintf("new_recharge_record_%d", uid%100)
}

type MissionRecord struct {
	ID          uint32    `db:"id"`
	Uid         uint32    `db:"uid"`
	MissionType uint32    `db:"mission_type"`
	Status      uint32    `db:"status"`
	FinTime     time.Time `db:"fin_time"`
}

// CreateMissionTbl 创建任务记录表
func (s *Store) CreateMissionTbl(ctx context.Context, uid uint32) error {
	_, err := s.db.ExecContext(ctx, fmt.Sprintf(createMissionTbl, GenMissionTblName(uid)))
	if err != nil {
		log.ErrorWithCtx(ctx, "create mission tbl err: %v", err)
		return err
	}
	return nil
}

// CreateMissionRecord 创建任务记录
func (s *Store) CreateMissionRecord(ctx context.Context, record *MissionRecord) error {
	query := fmt.Sprintf("INSERT INTO %s (uid, mission_type, status, fin_time) VALUES (?, ?, ?, ?)", GenMissionTblName(record.Uid))

	_, err := s.db.ExecContext(ctx, query, record.Uid, record.MissionType, record.Status, record.FinTime)
	if err != nil {
		// 表不存在则建表并重试
		if mysql.IsMySQLError(err, 1146) {
			if err := s.CreateMissionTbl(ctx, record.Uid); err != nil {
				return err
			}

			_, err = s.db.ExecContext(ctx, query, record.Uid, record.MissionType, record.Status, record.FinTime)
			if err != nil {
				log.ErrorWithCtx(ctx, "create mission record err: %v", err)
				return err
			}
		}
		// 主键重复错误
		if mysql.IsDupEntryError(err) {
			log.WarnWithCtx(ctx, "create mission record uid:%d,type:%d err: %v", record.Uid, record.MissionType, err)
			return err
		}
		log.ErrorWithCtx(ctx, "create mission record err: %v", err)
		return err
	}

	log.InfoWithCtx(ctx, "create mission record success, record:%v", record)
	return nil
}

// UpdateMissionStatus 更新任务状态
// return param1:rowsEffected
func (s *Store) UpdateMissionStatus(ctx context.Context, tx mysql.Txx, uid, missionType, status, oldStatus uint32, finTime time.Time) (uint32, error) {
	query := fmt.Sprintf("UPDATE %s SET status = ?, fin_time = ? WHERE uid = ? AND mission_type = ? AND status=?", GenMissionTblName(uid))

	changes, err := tx.ExecContext(ctx, query, status, finTime, uid, missionType, oldStatus)
	if err != nil {
		log.ErrorWithCtx(ctx, "update mission status err: %v", err)
		return 0, err
	}

	rowsEffected, _ := changes.RowsAffected()
	log.InfoWithCtx(ctx, "update mission status success, uid:%d, missionType:%d, status:%d,finTime:%v,rowsEffected:%d", uid, missionType, status, finTime, rowsEffected)
	return uint32(rowsEffected), nil
}

// GetUserMissionStatus 获取用户任务状态
func (s *Store) GetUserMissionStatus(ctx context.Context, uid uint32) ([]*MissionRecord, error) {
	list := make([]*MissionRecord, 0)
	query := fmt.Sprintf("SELECT id,uid, mission_type, status,fin_time FROM %s WHERE uid = ?", GenMissionTblName(uid))
	err := s.db.SelectContext(ctx, &list, query, uid)
	if err != nil {
		// 表不存在
		if mysql.IsMySQLError(err, 1146) {
			return list, nil
		}
		log.ErrorWithCtx(ctx, "get user mission status err: %v", err)
		return list, err
	}

	return list, nil
}

// DeleteUserMissionRecord 删除用户任务记录
func (s *Store) DeleteUserMissionRecord(ctx context.Context, uid, missionType uint32) error {
	query := fmt.Sprintf("DELETE FROM %s WHERE uid = ? AND mission_type = ?", GenMissionTblName(uid))

	_, err := s.db.ExecContext(ctx, query, uid, missionType)
	if err != nil {
		log.ErrorWithCtx(ctx, "delete user mission record err: %v", err)
		return err
	}
	return nil
}