package main

import (
	"flag"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/services/golddiamonn/cache"
	"golang.52tt.com/services/golddiamonn/initset"
	"golang.52tt.com/services/golddiamonn/mysql"
	"os"
)

type ServerFlags struct {
	*flag.FlagSet
	ol bool
}

func ParseFlags(args []string) *ServerFlags {
	fs := flag.NewFlagSet(args[0], flag.ExitOnError)

	var (
		ol = fs.Bool("ol", false, "online")
		helpFlag            = fs.Bool("h", false, "help")
	)

	if err := fs.Parse(args[1:]); err != nil {
		return nil
	}

	if *helpFlag {
		fmt.Fprintf(os.Stdout, "Usage of %s:\n", os.Args[0])
		fs.SetOutput(os.Stdout)
		fs.PrintDefaults()
		os.Exit(0)
	}

	return &ServerFlags{
		ol: *ol,
		FlagSet:             fs,
	}
}

func main() {
	flag := ParseFlags(os.Args)
	var redisClient *redis.Client
	var mysqlMasterConf *config.MysqlConfig
	var mysqlSlaveConf *config.MysqlConfig
	if !flag.ol {
		redisClient = redis.NewClient(&redis.Options{
			Addr: "*************:6379",
		})
		mysqlMasterConf = &config.MysqlConfig{
			Host:     "mysql.52tt.local",
			Port:     3306,
			Database: "appsvr",
			Charset:  "utf8mb4",
			UserName: "godman",
			Password: "thegodofman",
		}
		mysqlSlaveConf = &config.MysqlConfig{
			Host:     "mysql.52tt.local",
			Port:     3306,
			Database: "appsvr",
			Charset:  "utf8mb4",
			UserName: "godman",
			Password: "thegodofman",
		}
	} else {
		redisClient = redis.NewClient(&redis.Options{
			Addr: "*************:6379",
		})
		mysqlMasterConf = &config.MysqlConfig{
			Host:     "**********",
			Port:     3306,
			Database: "recharge_rebate",
			Charset:  "utf8mb4",
			UserName: "godman",
			Password: "thegodofman",
		}
		mysqlSlaveConf = &config.MysqlConfig{
			Host:     "************",
			Port:     3306,
			Database: "recharge_rebate",
			Charset:  "utf8mb4",
			UserName: "readonly",
			Password: "godmanreadonly",
		}
	}
	cacheClient := cache.NewGoldDiamondCache(redisClient)
	fmt.Printf("string:%s\n", mysqlMasterConf.ConnectionString())
	mysqlMasterDb, err := sqlx.Connect("mysql", mysqlMasterConf.ConnectionString())
	if err != nil {
		fmt.Errorf("Failed to Connect mysql %v", err)
		return
	}
	mysqlMasterStore := mysql.NewMysql(mysqlMasterDb)
	mysqlSlaveDb, err := sqlx.Connect("mysql", mysqlSlaveConf.ConnectionString())
	if err != nil {
		fmt.Errorf("Failed to Connect mysql %v", err)
		return
	}
	mysqlSlaveStore := mysql.NewMysql(mysqlSlaveDb)
	err = mysqlMasterStore.CreateDayGoldDiamond()
	fmt.Printf("table err:%+v", err)
	server := &initset.DataInitSet{
		CacheClient: cacheClient,
		MysqlMasterStore:  mysqlMasterStore,
		MysqlSlaveStore:  mysqlSlaveStore,
	}
	server.OptimizeTable()
}
 
 
 
