package mysql

import (
	"fmt"
	"github.com/go-sql-driver/mysql"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	"time"
)

const (
	OrderStatusPaying uint32 = iota
	OrderStatusHandling
	OrderStatusFinish
	OrderStatusFail
)

// 订单记录表
type MagicSpiritOrder struct {
	Id           uint64    `gorm:"primary_key;AUTO_INCREMENT"`
	OrderId      string    `gorm:"unique_index:order_idx"`
	Status       uint32    `gorm:"index:status_cid_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	ChannelId    uint32    `gorm:"index:status_cid_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	Uid          uint32    `gorm:"index:uid_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	MagicId      uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	Num          uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	Price        uint32    `sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
	OutsideTime  time.Time `gorm:"index:outside_ts_idx" sql:"type:TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"`
	CreateTime   time.Time `sql:"type:TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"`
	UpdateTime   time.Time `sql:"type:TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
	TBeanTimeStr string    `gorm:"index:tbean_time_idx"`
	Source       uint32    `gorm:"index:source_idx" sql:"type:INT(10) UNSIGNED NOT NULL DEFAULT 0"`
}

func (t *MagicSpiritOrder) TableName() string {
	return fmt.Sprintf("tbl_magic_spirit_order_%04d%02d", t.OutsideTime.Year(), t.OutsideTime.Month())
}

func (s *Store) CreateMagicSpiritOrder(tx *gorm.DB, order *MagicSpiritOrder) error {
	err := s.getDb(tx).Create(order).Error
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok {
			log.Errorf("CreateMagicSpiritOrder fail. order:%+v, err:%v", order, err)
			return err
		}

		// 记录已存在
		if mysqlErr.Number == 1062 {
			log.Errorf("CreateMagicSpiritOrder fail. order:%+v, err:%v", order, err)
			return protocol.NewExactServerError(nil, status.ErrMagicSpiritOrderExist)
		}

		if mysqlErr.Number != 1146 {
			log.Errorf("CreateMagicSpiritOrder fail. order:%+v, err:%v", order, err)
			return err
		}

		// 表不存在建表
		err = s.getDb(tx).CreateTable(order).Error
		if err != nil {
			log.Errorf("CreateMagicSpiritOrder fail to CreateTable.order:%+v, err:%v", order, err)
			return err
		}

		// 重新插入
		err = s.getDb(tx).Create(order).Error
		if err != nil {
			log.Errorf("CreateMagicSpiritOrder fail to Create.order:%+v, err:%v", order, err)
			return err
		}
	}

	log.Infof("CreateMagicSpiritOrder order:%+v", order)
	return nil
}

func (s *Store) UpdateMagicSpiritOrderTBeanTime(tx *gorm.DB, orderId, tBeanTime string, queryMonthTime time.Time) (bool, error) {
	r := s.getDb(tx).Model(&MagicSpiritOrder{OutsideTime: queryMonthTime}).
		Where("order_id=?", orderId).
		Update(map[string]interface{}{"t_bean_time_str": tBeanTime})

	if r.Error != nil {
		log.Errorf("UpdateMagicSpiritOrderTBeanTime fail to Update. orderId:%+v, queryMonthTime:%v, tBeanTime:%v, err:%v",
			orderId, queryMonthTime, tBeanTime, r.Error)
		return false, r.Error
	}

	if r.RowsAffected == 0 {
		log.Debugf("UpdateMagicSpiritOrderTBeanTime fail to Update. orderId:%+v, queryMonthTime:%v, tBeanTime:%v, err:RowsAffected == 0",
			orderId, queryMonthTime, tBeanTime)
		return false, nil
	}

	log.Infof("UpdateMagicSpiritOrderTBeanTime orderId:%s, queryMonthTime:%v, tBeanTime:%v",
		orderId, queryMonthTime, tBeanTime)
	return true, nil
}

func (s *Store) UpdateMagicSpiritOrderStatus(tx *gorm.DB, orderId string, queryMonthTime time.Time, sourceStatusList []uint32, targetStatus uint32) (bool, error) {
	if len(sourceStatusList) == 0 {
		return false, nil
	}

	r := s.getDb(tx).Model(&MagicSpiritOrder{OutsideTime: queryMonthTime}).
		Where("order_id=? and status in (?)", orderId, sourceStatusList).
		Update("status", targetStatus)

	if r.Error != nil {
		log.Errorf("UpdateMagicSpiritOrderStatus fail to Update. orderId:%+v, queryMonthTime:%v, sourceStatusList:%v, targetStatus:%v, err:%v",
			orderId, queryMonthTime, sourceStatusList, targetStatus, r.Error)
		return false, r.Error
	}

	if r.RowsAffected == 0 {
		log.Errorf("UpdateMagicSpiritOrderStatus fail to Update. orderId:%+v, queryMonthTime:%v, sourceStatusList:%v, targetStatus:%v, err:RowsAffected == 0",
			orderId, queryMonthTime, sourceStatusList, targetStatus)
		return false, nil
	}

	log.Infof("UpdateMagicSpiritOrderStatus orderId:%+v, queryMonthTime:%v, sourceStatusList:%v, targetStatus:%v",
		orderId, queryMonthTime, sourceStatusList, targetStatus)
	return true, nil
}

func (s *Store) GetMagicSpiritOrder(tx *gorm.DB, orderId string, queryMonthTime time.Time) (*MagicSpiritOrder, bool, error) {
	order := &MagicSpiritOrder{OutsideTime: queryMonthTime}

	err := s.getDb(tx).First(order, "order_id=?", orderId).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return order, false, nil
		}

		mysqlErr, ok := err.(*mysql.MySQLError)
		if ok && mysqlErr.Number == 1146 {
			return order, false, nil
		}

		log.Errorf("GetMagicSpiritOrder fail. orderId:%v, err:%v", orderId, err)
		return order, false, err
	}

	return order, true, nil
}

// 根据状态获取房间订单列表
func (s *Store) GetChannelOrderListByStatus(tx *gorm.DB, channelId, status uint32, queryMonthTime time.Time) ([]*MagicSpiritOrder, error) {
	list := make([]*MagicSpiritOrder, 0)

	err := s.getDb(tx).Model(&MagicSpiritOrder{OutsideTime: queryMonthTime}).Where("status = ? and channel_id = ?", status, channelId).
		Select("*").Scan(&list).Error
	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetChannelOrderListByStatus fail. status:%v, channelId:%v, err:%v", status, channelId, err)
			return list, err
		}
	}

	return list, nil
}

type TotalInfo struct {
	OrderCnt   uint32 `db:"order_cnt"`
	TotalPrice uint64 `db:"total_price"`
	TotalNum   uint64 `db:"total_num"`
}

func (s *Store) GetOrderTotalInfo(queryMonthTime, beginTime, endTime time.Time, status, source uint32) (*TotalInfo, error) {
	out := &TotalInfo{}

	err := s.readonlyDb.Model(&MagicSpiritOrder{OutsideTime: queryMonthTime}).
		Select("count(1) as order_cnt, sum(price*num) as total_price, sum(num) as total_num").
		Where("t_bean_time_str >= ? and t_bean_time_str < ? and status=? and source=?",
			beginTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"), status, source).Scan(out).Error

	if err != nil {
		mysqlErr, ok := err.(*mysql.MySQLError)
		if !ok || mysqlErr.Number != 1146 {
			log.Errorf("GetOrderTotalInfo fail. status:%v, queryMonthTime:%v, err:%v", status, queryMonthTime, err)
			return out, err
		}
	}

	return out, nil
}

func (s *Store) GetMagicConsumeOrderIdList(queryTime, begin, end time.Time, status, source uint32) ([]string, error) {
	list := make([]string, 0)

	rows, err := s.readonlyDb.Model(&MagicSpiritOrder{OutsideTime: queryTime}).
		Select("order_id").
		Where("t_bean_time_str >= ? and t_bean_time_str < ? and status=? and source=?", begin, end, status, source).Rows()
	if err != nil {
		log.Errorf("GetMagicConsumeOrderIdList fail. begin:%v, end:%v, err:%v", begin, end, err)
		return list, err
	}
	defer rows.Close()
	if rows.Err() != nil {
		log.Errorf("GetMagicConsumeOrderIdList fail. begin:%v, end:%v, err:%v", begin, end, err)
		// 为了解决异味
	}

	for rows.Next() {
		orderId := ""
		err = rows.Scan(&orderId)
		if err == nil {
			list = append(list, orderId)
		}
	}

	return list, nil
}
