package manager

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/account"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	MagicSpiritLogic "golang.52tt.com/protocol/app/magic-spirit-logic"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/magic-spirit"
	official_live_channel "golang.52tt.com/protocol/services/official-live-channel"
	pmPB "golang.52tt.com/protocol/services/present-middleware"
	present_middleware "golang.52tt.com/protocol/services/present-middleware"
	probgamecenter2 "golang.52tt.com/protocol/services/probgamecenter"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/magic-spirit/internal/mysql"
	"time"
)

func (m *MagicSpiritMgr) AddUnpackList(ctx context.Context, uid, channelId uint32, unpackPbList []*pb.UnpackGiftInfo) error {
	if len(unpackPbList) == 0 {
		return nil
	}

	cacheExpire := time.Duration(m.bc.GetUnpackGiftDelaySec()+300) * time.Second
	err := m.cache.AddUnpackOrderList(ctx, channelId, uid, unpackPbList, cacheExpire)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUnpackList fail to AddUnpackOrderList. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, unpackPbList, err)
		return err
	}

	// 房间待开箱礼物变更通知
	err = m.UnpackListChangeNotify(ctx, uid, channelId, unpackPbList)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddUnpackList fail to UnpackListChangeNotify. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, unpackPbList, err)
		return err
	}

	log.InfoWithCtx(ctx, "AddUnpackList uid:%d, channelId:%d, %+v", uid, channelId, unpackPbList)
	return nil
}

func (m *MagicSpiritMgr) GetChannelAllUnpackGift(ctx context.Context, uid, channelId uint32) (*pb.GetChannelAllUnpackGiftResp, error) {
	now := time.Now()
	limit := uint32(100) // 最多取100个
	out := &pb.GetChannelAllUnpackGiftResp{ServerTs: uint32(now.Unix())}

	// 获取房间当前待开箱列表
	orderIdList, err := m.cache.GetChannelUnpackOrderIdList(ctx, channelId, 0, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelAllUnpackGift fail to GetChannelUnpackOrderIdList. uid:%d, channelId:%d, err:%v", uid, channelId, err)
	}

	// 房间内无待开箱礼物，直接返回
	if len(orderIdList) == 0 {
		return out, nil
	}

	// 获取本房间自己的待开箱列表
	myOrderIdList, err := m.cache.GetUserUnpackOrderIdList(ctx, uid, channelId, 0, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelAllUnpackGift fail to GetUserUnpackOrderIdList. uid:%d, channelId:%d, err:%v", uid, channelId, err)
	}

	tmpList := append(orderIdList, myOrderIdList...)

	allOrderListMap, err := m.cache.BatchGetUnpackOrderInfo(ctx, tmpList)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnpackListChangeNotify fail to BatchGetUnpackOrderInfo. uid:%d, channelId:%d, err:%v", uid, channelId, err)
	}

	channelOrderPbList := make([]*pb.UnpackGiftInfo, 0, len(orderIdList))
	for _, orderId := range orderIdList {

		info, ok := allOrderListMap[orderId]
		if !ok || info.GetEndTs() < uint32(now.Unix()) {
			continue
		}

		channelOrderPbList = append(channelOrderPbList, info)
	}

	myOrderPbList := make([]*pb.UnpackGiftSimple, 0, len(orderIdList))
	for _, orderId := range myOrderIdList {

		info, ok := allOrderListMap[orderId]
		if !ok || info.GetEndTs() < uint32(now.Unix()) {
			continue
		}

		myOrderPbList = append(myOrderPbList, &pb.UnpackGiftSimple{ItemOrderId: orderId, EndTs: info.GetEndTs()})
	}

	out.List = channelOrderPbList
	out.UserUnpackOpt = &pb.UserUnpackGiftList{
		ChannelId: channelId,
		Uid:       uid,
		List:      myOrderPbList,
	}

	return out, nil
}

func (m *MagicSpiritMgr) OpenUnpackGift(ctx context.Context, uid, channelId uint32, orderId string, sysAutoOpen bool) (*present_middleware.SendMagicSpiritOpt, error) {
	now := time.Now()
	opt := &present_middleware.SendMagicSpiritOpt{}

	order, exist, err := m.mysql.GetMagicSpiritAwardLog(nil, orderId, now)
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail to GetMagicSpiritAwardLog. uid:%d, channelId:%d, orderId:%v, err:%v", uid, channelId, orderId, err)
		return opt, err
	}

	if !exist {
		lastMonthTime := getLastMonthTime(now)
		// 查上月
		order, exist, err = m.mysql.GetMagicSpiritAwardLog(nil, orderId, lastMonthTime)
		if err != nil {
			log.ErrorWithCtx(ctx, "OpenUnpackGift fail to GetMagicSpiritAwardLog. uid:%d, channelId:%d, orderId:%v, err:%v", uid, channelId, orderId, err)
			return opt, err
		}
	}

	if !exist || order.IsDone {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail. uid:%d, channelId:%d, orderId:%s, err:order not exist or has done.", uid, channelId, orderId)
		return opt, protocol.NewExactServerError(nil, status.ErrMagicSpiritOrderNotExist)
	}

	// 不是系统自动开箱的需要检查参数
	if !sysAutoOpen && (orderId != order.OrderId || uid != order.FromUid || channelId != order.ChannelId) {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail. uid:%d, channelId:%d, orderId:%s, order:%+v, err:order info err.", uid, channelId, orderId, order)
		return opt, protocol.NewExactServerError(nil, status.ErrMagicSpiritOrderNotExist, "订单信息有误")
	}

	uid = order.FromUid
	channelId = order.ChannelId

	// 移出缓存
	err = m.cache.RemoveUnpackOrder(ctx, uid, channelId, orderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail to RemoveUnpackOrder. uid:%d, channelId:%d, orderId:%v, err:%v", uid, channelId, orderId, err)
	}

	// 手动开箱的，送礼时间更新为当前时间
	if !sysAutoOpen {
		order.AwardTime = time.Unix(now.Unix(), 0)
	}

	// 更新订单完成
	ok, err := m.mysql.UpdateMagicSpiritAwardDone(nil, order.OrderId, order.OutsideTime, order.AwardTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail to UpdateMagicSpiritAwardDone. uid:%d, channelId:%d, orderId:%v, err:%v", uid, channelId, orderId, err)
		return opt, err
	}

	if !ok {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail. uid:%d, channelId:%d, orderId:%v, err:order has done", uid, channelId, orderId)
		return opt, protocol.NewExactServerError(nil, status.ErrMagicSpiritOrderNotExist, "订单已被处理")
	}

	// send present
	opt, err = m.sendUnpackGift(ctx, order, sysAutoOpen)
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail to sendUnpackGift. uid:%d, channelId:%d, orderId:%v, err:%v", uid, channelId, orderId, err)
		return opt, err
	}

	// 房间待开箱礼物变更通知
	err = m.UnpackListChangeNotify(ctx, uid, channelId, []*pb.UnpackGiftInfo{})
	if err != nil {
		log.ErrorWithCtx(ctx, "OpenUnpackGift fail to UnpackListChangeNotify. uid:%d, channelId:%d, orderId:%s, err:%v", uid, channelId, orderId, err)
		return opt, err
	}

	log.InfoWithCtx(ctx, "OpenUnpackGift uid:%d, channelId:%d, order:%+v", uid, channelId, order)
	return opt, nil
}

func (m *MagicSpiritMgr) sendUnpackGift(ctx context.Context, order *mysql.MagicSpiritAwardLog, sysAutoOpen bool) (*present_middleware.SendMagicSpiritOpt, error) {
	opt := &present_middleware.SendMagicSpiritOpt{}

	if order == nil {
		return opt, nil
	}

	magicConf, err := m.GetMagicSpiritById(ctx, order.MagicId)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendUnpackGift fail to GetMagicSpiritById. orderId:%+v, err:%v", order.OrderId, err)
		return opt, err
	}

	pondList, err := m.GetMagicSpiritPondWithCache(ctx, order.MagicId)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendUnpackGift fail to GetMagicSpiritById. orderId:%+v, err:%v", order.OrderId, err)
		return opt, err
	}

	pondMap := make(map[uint32]*mysql.MagicSpiritPond)
	for _, pond := range pondList {
		pondMap[pond.PresentId] = pond
	}

	bindCid, err := m.getOfficialBindCid(ctx, order.FromUid, order.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendUnpackGift fail to getOfficialBindCid. orderId:%s, err:%v", order.OrderId, err)
		//return opt, err
	}

	// 概率玩法风控中心检查
	// 需将成本和出奖都置为0， 即不需要计算亏损，亏损在消费时已上报，此处仅获取token
	probTokenMap, err := m.checkProbGameCenter(ctx, []*probgamecenter2.CheckFuseItem{{
		ConsumeValue: 0,
		PrizeValue:   0,
		OrderId:      order.OrderId,
		Uid:          order.FromUid,
	}})
	if err != nil {
		log.ErrorWithCtx(ctx, "sendUnpackGift fail to checkProbGameCenter. orderId:%s, err:%v", order.OrderId, err)
		return opt, err
	}

	dealToken, err := GenMagicDealToken(order.FromUid, order.GiftTotalPrice, order.TBeanDealToken, probTokenMap[order.OrderId], order.MagicOrderId, order.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendUnpackGift fail to GenMagicDealToken. order:%+v, err:%v", order, err)
		//return out, err
	}

	pmReq := &pmPB.MagicSendPresentReq{
		SendUid: order.FromUid,
		AppId:   1,
		//CombConfig:      combByte,
		ChannelId:      order.ChannelId,
		ConsumeOrderId: order.MagicOrderId,
		//IsBatch:         req.GetIsBatch(),
		MagicSpiritId:   order.MagicId,
		MagicSpiritCnt:  order.Num,
		MagicSpiritName: magicConf.GetName(),
		MagicSpiritIcon: magicConf.GetIconUrl(),
		SendTime:        uint32(order.AwardTime.Unix()),

		BindChannelId: bindCid,
		SysAutoSend:   sysAutoOpen,
	}

	item := &pmPB.GiftItem{
		Uid:            order.ToUid,
		ItemId:         order.GiftId,
		OrderId:        order.OrderId,
		Count:          order.Num,
		AwardEffect:    pondMap[order.GiftId].PrizeLevel,
		DealToken:      dealToken,
		BreakingNewsId: pondMap[order.GiftId].BreakingNewsId,
	}
	pmReq.GiftItemList = append(pmReq.GiftItemList, item)

	resp, err := m.presentMWCli.MagicSendPresent(context.Background(), pmReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "sendUnpackGift fail to MagicSendPresent. orderId:%+v, err:%v", order.OrderId, err)
		return opt, err
	}

	log.InfoWithCtx(ctx, "sendUnpackGift order:%+v, pmReq:%+v", order, pmReq)
	return resp.GetSpiritOpt(), nil
}

// 获取官频转播房间id
func (m *MagicSpiritMgr) getOfficialBindCid(ctx context.Context, uid, channelId uint32) (uint32, error) {
	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOfficialBindCid GetChannelSimpleInfo fail. uid:%d, channelId:%d, err:%v", uid, channelId, err)
		return 0, err
	}

	// 非官频
	if channelInfo.GetChannelType() != uint32(channelPB.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		return 0, nil
	}

	resp, err := m.officialChannelCli.GetRelay(ctx, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "getOfficialBindCid GetRelay fail. uid:%d, channelId:%d, err:%v", uid, channelId, err)
		return 0, err
	}

	if resp.GetRelayStatus() == official_live_channel.RelayStatus_RUNNING {
		return resp.GetChannelId(), nil
	}

	return 0, nil
}

func (m *MagicSpiritMgr) UnpackListChangeNotify(ctx context.Context, uid, channelId uint32, newUnpackPbList []*pb.UnpackGiftInfo) error {
	now := time.Now()
	limit := uint32(100) // 最多取100个
	// 获取房间当前待开箱列表
	orderIdList, err := m.cache.GetChannelUnpackOrderIdList(ctx, channelId, 0, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnpackListChangeNotify fail to GetChannelUnpackOrderIdList. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, newUnpackPbList, err)
	}

	allOrderList, err := m.cache.BatchGetUnpackOrderInfo(ctx, orderIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnpackListChangeNotify fail to BatchGetUnpackOrderInfo. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, newUnpackPbList, err)
	}

	uidList := make([]uint32, 0)
	if uid != 0 {
		uidList = append(uidList, uid)
	}

	for _, order := range allOrderList {
		uidList = append(uidList, order.GetSendUid())
	}

	userMap, err := m.accountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnpackListChangeNotify fail to GetUsersMap. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, newUnpackPbList, err)
	}

	channelInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, uid, channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnpackListChangeNotify fail to GetUsersMap. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, newUnpackPbList, err)
	}

	ukwMap := make(map[uint32]*app.UserProfile)
	if channelInfo.GetChannelType() == uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) ||
		channelInfo.GetChannelType() == uint32(channelPB.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) ||
		channelInfo.GetChannelType() == uint32(channelPB.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		ukwMap, err = m.userProfileCli.BatchGetUserProfile(ctx, uidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "UnpackListChangeNotify fail to BatchGetUserProfile. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, newUnpackPbList, err)
		}
	}

	opt := &MagicSpiritLogic.ChannelUnpackGiftListChangeOpt{
		ChannelId: channelId,
		AddList:   make([]*MagicSpiritLogic.UnpackGiftInfo, 0, len(newUnpackPbList)),
		List:      make([]*MagicSpiritLogic.UnpackGiftInfo, 0, len(allOrderList)),
		ServerTs:  uint32(time.Now().Unix()),
	}

	for _, order := range newUnpackPbList {
		opt.AddList = append(opt.AddList, genUnpackGiftLogicPb(order, userMap[order.GetSendUid()], ukwMap[order.GetSendUid()]))
	}

	for _, order := range allOrderList {
		if order.GetEndTs() < uint32(now.Unix()) {
			continue
		}
		opt.List = append(opt.List, genUnpackGiftLogicPb(order, userMap[order.GetSendUid()], ukwMap[order.GetSendUid()]))
	}

	err = m.pushChannelUnpackOpt(ctx, channelId, opt)
	if err != nil {
		log.ErrorWithCtx(ctx, "UnpackListChangeNotify fail to pushChannelUnpackOpt. uid:%d, channelId:%d, %+v, err:%v", uid, channelId, newUnpackPbList, err)
		return err
	}

	return nil
}

func genUnpackGiftLogicPb(info *pb.UnpackGiftInfo, user *account.User, ukwInfo *app.UserProfile) *MagicSpiritLogic.UnpackGiftInfo {
	out := &MagicSpiritLogic.UnpackGiftInfo{
		ItemOrderId:   info.GetItemOrderId(),
		MagicSpiritId: info.GetMagicSpiritId(),
		ItemId:        info.GetItemId(),
		ChannelId:     info.GetChannelId(),
		SendUid:       info.GetSendUid(),
		SendAccount:   user.GetUsername(),
		SendNickname:  user.GetNickname(),
		SendSex:       uint32(user.GetSex()),
		TargetUid:     info.GetTargetUid(),
		EndTs:         info.GetEndTs(),
	}

	if ukwInfo != nil {
		if ukwInfo.GetPrivilege() != nil {
			out.SendAccount = ukwInfo.GetPrivilege().GetAccount()
			out.SendNickname = ukwInfo.GetPrivilege().GetNickname()
			out.SendUid = ukwInfo.GetUid()
		}
		out.FromUserProfile = ukwInfo
	}

	return out
}

func (m *MagicSpiritMgr) pushChannelUnpackOpt(ctx context.Context, channelId uint32, opt *MagicSpiritLogic.ChannelUnpackGiftListChangeOpt) error {
	b, _ := proto.Marshal(opt)
	err := m.pushChannelBroadcastMsg(ctx, channelId, uint32(channelPB.ChannelMsgType_MAGIC_SPIRIT_CHANNEL_UNPACK_PRESENT_CHANGE), "", b)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelUnpackOpt fail to pushMsgToChannel. info:%+v, err:%v", opt, err)
		return err
	}

	log.Debugf("pushChannelUnpackOpt channelId:%v, opt:%+v", channelId, opt)
	return nil
}

func (m *MagicSpiritMgr) pushChannelBroadcastMsg(ctx context.Context, channelId, msgType uint32, content string, data []byte) error {
	msg := &channelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		ToChannelId:  channelId,
		Type:         msgType,
		Content:      []byte(content),
		PbOptContent: data,
	}

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroadcastMsg marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	err = m.pushChannelBroMsgToChannels(ctx, []uint32{channelId}, channelMsgBin)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroadcastMsg fail to SendChannelBroadcastMsg. channelId:%d err:%v", channelId, err)
		return err
	}

	log.DebugWithCtx(ctx, "pushChannelBroadcastMsg msgType:%v, channelId:%d, content:%s", msgType, channelId, content)
	return nil
}

// 房间广播消息
func (m *MagicSpiritMgr) pushChannelBroMsgToChannels(ctx context.Context, channelIds []uint32, channelMsgBin []byte) error {
	pushMessage := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_MSG_BRO),
		Content: channelMsgBin,
		SeqId:   uint32(time.Now().Unix()),
	}
	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels Marshal channelIds:%v, err: %v", channelIds, e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: uint32(time.Now().Unix()),
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
		},
	}

	multicastMap := map[uint64]string{}
	for _, channelId := range channelIds {
		if channelId == 0 {
			continue
		}
		multicastMap[uint64(channelId)] = fmt.Sprintf("%d@channel", channelId)
	}

	if len(multicastMap) == 0 {
		return nil
	}

	err := m.pushCli.PushMulticasts(ctx, multicastMap, []uint32{}, notification)
	if err != nil {
		log.ErrorWithCtx(ctx, "pushChannelBroMsgToChannels fail to PushMulticasts channelIds:%v, err: %s", channelIds, err.Error())
		return err
	}

	return nil
}

func (m *MagicSpiritMgr) handleTimeoutUnpackGift() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	orderId, exist, err := m.cache.PopExpireUnpackOrderId(ctx)
	if err != nil {
		log.Errorf("handleTimeoutUnpackGift fail to PopExpireUnpackOrderId err: %s", err.Error())
		return
	}

	if !exist || orderId == "" {
		return
	}

	// 开箱
	_, err = m.OpenUnpackGift(ctx, 0, 0, orderId, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleTimeoutUnpackGift fail to OpenUnpackGift orderId:%s, err: %s", orderId, err.Error())
		return
	}
}
