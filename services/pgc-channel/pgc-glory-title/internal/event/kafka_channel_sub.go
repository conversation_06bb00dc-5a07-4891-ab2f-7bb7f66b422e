package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"golang.52tt.com/pkg/log"
	channelGA "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannalevent"
	"golang.52tt.com/services/pgc-channel/pgc-glory-title/internal/manager"
	"time"
)

const (
	topicTypeChannel = "simple_channel_ev"
)

type ChannelKafkaSub struct {
	subscriber.Subscriber
	mgr *manager.PgcGloryTitleManager
}

func (s *ChannelKafkaSub) Close() {
	s.Subscriber.Stop()
}

func NewChannelKafkaSub(clientId, groupId string, topics, brokers []string, mgr_ *manager.PgcGloryTitleManager) (*ChannelKafkaSub, error) {

	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := kafka.NewSubscriber(brokers, conf)
	if err != nil {
		return nil, err
	}

	sub := &ChannelKafkaSub{
		Subscriber: kafkaSub,
		mgr:        mgr_,
	}

	err = sub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		return nil, err
	}

	return sub, nil
}

func (s *ChannelKafkaSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicTypeChannel:
		return s.handlerChannelEvent(msg)
	}
	return nil, false
}

func (s *ChannelKafkaSub) handlerChannelEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	channelEvent := &kafkachannalevent.ChSimpleEvent{}
	err := proto.Unmarshal(msg.Value, channelEvent)
	if err != nil {
		log.Errorf("handlerChannelEvent handlerEvent Unmarshal err:%v", err)
		return nil, false
	}

	log.Debugf("handlerChannelEvent begin %v", channelEvent)

	eventType := channelEvent.GetEventType()
	if eventType != uint32(kafkachannalevent.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER) ||
		channelEvent.GetChannelType() != uint32(channelGA.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE) {
		log.Debugf("handlerChannelEvent no handler uid %d cid %d type %d", channelEvent.GetUid(), channelEvent.GetChId(), channelEvent.GetEventType())
		return nil, false
	}

	enterOpt := &kafkachannalevent.ChSimpleEnterOpt{}
	err = proto.Unmarshal(channelEvent.GetOptPbInfo(), enterOpt)
	if err != nil {
		log.Errorf("handlerChannelEvent enterOpt Unmarshal err:%v", err)
		return nil, false
	}

	if !enterOpt.GetInvisible() {
		s.mgr.ProcPrivilegeUserEnterChannel(ctx, channelEvent.GetUid(), channelEvent.GetChId())
	}

	log.Debugf("handlerChannelEvent end %v", channelEvent)
	return nil, false
}
