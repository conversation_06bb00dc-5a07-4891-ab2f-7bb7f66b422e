package hanlders

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/scylladb/go-set"
	"golang.52tt.com/pkg/datahouse"
	"golang.52tt.com/pkg/foundation/utils"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/web"
	"golang.52tt.com/protocol/common/status"
	anchorcontract_go "golang.52tt.com/protocol/services/anchorcontract-go"
	"golang.52tt.com/protocol/services/guild-management-svr"
	"golang.52tt.com/services/guild-management/guild-management-http-logic/models"
	api "golang.52tt.com/services/guild-management/guild-management-http-logic/models/gen-go"
)

func GetGuildViolationRecord(authInfo *web.AuthInfo, w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(r.Context(), time.Second*5)
	defer cancel()

	uid := authInfo.UserID

	req := &api.GetGuildViolationRecordReq{}
	err := json.Unmarshal(authInfo.Body, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildViolationRecord Failed to parse request body [%s], err %+v", string(authInfo.Body), err)
		web.ServeBadReq(w)
		return
	}
	guildId := req.GuildId

	log.DebugWithCtx(ctx, "GetGuildViolationRecord uid %d begin %s req=%+v", uid, string(authInfo.Body), req)

	_, hasPer, respCode, respMsg, _ := checkUserHasPer(ctx, uid, guildId, api.AnchorContractMenuType_AnchorContractVio.String())
	if !hasPer {
		web.ServeAPICodeJson(w, respCode, respMsg, nil)
		return
	}

	resp := &api.GetGuildViolationRecordResp{}

	uids := []uint32{}
	if len(req.TtidList) > 0 {
		uids, _, _, err = Tid2UidV2(ctx, req.TtidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "failed to Tid2UidV2 err %+v, uid:%d guild:%d", err, uid, req.GuildId)
			_ = web.ServeAPICodeJson(w, status.ErrSys, "系统错误", nil)
			return
		}
		log.DebugWithCtx(ctx, "GetGuildViolationRecord guildId %d, reqUids %v", guildId, uids)
		if len(uids) == 0 {
			log.InfoWithCtx(ctx, "GetGuildViolationRecord no valid uids, uid %d, req %v", uid, req)
			web.ServeAPIJson(w, resp)
			return
		}
	}

	dayCntLimit := models.GetModelServer().GetDyConfig().GetVioDayCntLimit()
	nowTm := time.Now()
	beginTs := req.BeginTs
	endTs := req.EndTs
	if beginTs == 0 {
		endTs = uint32(nowTm.Unix())
		beginTs = uint32(nowTm.AddDate(0, 0, -7).Unix())
	} else {
		minBeginTm := nowTm.AddDate(0, 0, -int(dayCntLimit-1))
		minBeginTm = time.Date(minBeginTm.Year(), minBeginTm.Month(), minBeginTm.Day(), 0, 0, 0, 0, minBeginTm.Location())
		if beginTs < uint32(minBeginTm.Unix()) {
			log.ErrorWithCtx(ctx, "GetGuildViolationRecord beginTs %d is too old, minBeginTm %d req:%v", req.BeginTs, minBeginTm.Unix(), req)
			_ = web.ServeAPICodeJson(w, status.ErrSys, "超出时间查询范围", nil)
			return
		}
	}

	startTm := time.Unix(int64(beginTs), 0)
	endTm := time.Unix(int64(endTs), 0)
	page := req.Page + 1
	pageNum := req.PageNum

	total, list, err := datahouse.QueryGuildViolationRecord(ctx, guildId, uids, startTm, endTm, page, pageNum)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildViolationRecord failed QueryGuildViolationRecord err %+v, guildId %d", err, req.GuildId)
		_ = web.ServeAPICodeJson(w, status.ErrSys, "系统错误", nil)
		return
	}
	if guildId == 100154 {
		total += 1
		list = append(list, &datahouse.GuildViolationRecord{
			Uid:                2412355,
			OptTime:            "2023-12-21",
			GuildId:            guildId,
			SanctionActionDesc: "1",
			SourceType:         "1",
			ViolationReason:    "1",
			PubViolationReason: "1",
		})
	}
	log.DebugWithCtx(ctx, "GetGuildViolationRecord QueryGuildViolationRecord=%+v", list)

	fetchUids := set.NewUint32Set()
	for _, info := range list {
		fetchUids.Add(info.Uid)
	}
	resp.Total = total
	contractUids := fetchUids.List()

	uid2Contractinfo, err := BatchGetContract(ctx, contractUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildViolationRecord failed .BatchGetContract err %+v, guildId %d", err, req.GuildId)
		_ = web.ServeAPICodeJson(w, status.ErrSys, "系统错误", nil)
		return
	}
	log.DebugWithCtx(ctx, "GetGuildViolationRecord guildId %d contractUids=%v uid2Contractinfo=%+v", req.GuildId, contractUids, uid2Contractinfo)

	agentUidMap, err := BatchGetAgentUid(ctx, contractUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildViolationRecord failed .BatchGetAgentUid err %+v, guildId %d", err, req.GuildId)
		_ = web.ServeAPICodeJson(w, status.ErrSys, "系统错误", nil)
		return
	}
	log.DebugWithCtx(ctx, "GetGuildViolationRecord guildId %d BatchGetAgentUid=%+v", req.GuildId, agentUidMap)

	if req.GetIsExport() {
		_, err = models.GetModelServer().GuildManagementCli.AddVioExportRecord(ctx, &guild_management_svr.AddVioExportRecordReq{
			GuildId: req.GuildId,
			Uid:     uid,
			BeginTs: uint64(req.GetBeginTs()),
			EndTs:   uint64(req.GetEndTs()),
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "AddVioExportRecord failed err %+v, guildId %d", err, req.GuildId)
			_ = web.ServeAPICodeJson(w, status.ErrSys, "系统错误", nil)
			return
		}
	}

	for _, agentUid := range agentUidMap {
		fetchUids.Add(agentUid)
	}

	allUids := fetchUids.List()
	log.DebugWithCtx(ctx, "GetGuildViolationRecord guildId %d allUids=%v", req.GuildId, allUids)
	userMap, err := batGetUser(ctx, allUids)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildViolationRecord failed .GetUsersMap err %+v, guildId %d", err, req.GuildId)
		_ = web.ServeAPICodeJson(w, status.ErrSys, "系统错误", nil)
		return
	}

	for _, info := range list {
		userInfo := userMap[info.Uid]
		contractInfo := uid2Contractinfo[info.Uid]
		agentUid := agentUidMap[info.Uid]

		resp.List = append(resp.List, &api.ViolationRecord{
			AnchorUid:        info.Uid,
			AnchorTtid:       userInfo.GetAlias(),
			AnchorAccount:    userInfo.GetUsername(),
			AnchorNickname:   userInfo.GetNickname(),
			AnchorSex:        uint32(userInfo.GetSex()),
			GuildId:          info.GuildId,
			IdentityInfoList: contractInfo.GetAnchorIdentityList(),
			AgentUid:         agentUid,
			AgentNickname:    userMap[agentUid].GetNickname(),
			AgentTtid:        userMap[agentUid].GetAlias(),
			//ViolationType:      info.SourceType,
			ViolationReason:    info.PubViolationReason,
			SanctionActionDesc: info.SanctionActionDesc,
			OptTime:            info.OptTime,
			ViolationLevel:     info.LabelLevelKey,
			ViolationSource:    info.SourceType,
		})
	}

	resp.DayCntLimit = dayCntLimit
	web.ServeAPIJson(w, resp)
	log.DebugWithCtx(ctx, "GetGuildViolationRecord end guild %d, ttid %v, page %d pagenum %d begin %d end %d out %s",
		req.GuildId, req.TtidList, req.Page, req.PageNum, req.BeginTs, req.EndTs, utils.ToJson(resp))
}

func BatchGetContract(ctx context.Context, uids []uint32) (map[uint32]*anchorcontract_go.ContractCacheInfo, error) {
	uid2contract := map[uint32]*anchorcontract_go.ContractCacheInfo{}
	for _, uid := range uids {
		contract, err := models.GetModelServer().AnchorcontractCli.GetUserContractCacheInfo(ctx, 0, uid)
		if err != nil {
			log.Errorf("BatchGetContract fail %v", err)
			return nil, err
		}
		uid2contract[uid] = contract
	}
	return uid2contract, nil
}

func BatchGetAgentUid(ctx context.Context, uidList []uint32) (map[uint32]uint32, error) {
	uid2AgentUid := map[uint32]uint32{}
	limit := 50
	sz := len(uidList)
	for i := 0; i < sz; i += limit {
		end := i + limit
		if end > sz {
			end = sz
		}

		uidsx := uidList[i:end]
		log.Debugf("BatchGetAgentUid %d-%d uids=%v", i, end, uidsx)
		if len(uidsx) == 0 {
			break
		}

		resp, err := models.GetModelServer().AnchorcontractCli.GetAnchorAgentUid(ctx, uidsx)
		if nil != err {
			log.ErrorWithCtx(ctx, "BatGetUserConsumeInfo failed   err:%v", err)
			return nil, err
		}
		for uid, user := range resp.GetUid2AgentUid() {
			uid2AgentUid[uid] = user
		}
	}
	return uid2AgentUid, nil
}
