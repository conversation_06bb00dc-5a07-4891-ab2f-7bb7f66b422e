package store

import (
	"context"
	"testing"

	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
)

func newTestStore() (*Store, error) {
	return NewStore(context.Background(), &mysqlConnect.MysqlConfig{
		Host:     "*************",
		Port:     3306,
		Protocol: "tcp",
		Database: "appsvr",
		UserName: "godman",
		Password: "thegodofman",
		Charset:  "utf8",
	},
	)
}

var testStore *Store

func init() {
	var err error
	testStore, err = newTestStore()
	if err != nil {
		panic(err)
	}
}

func TestStore_UpdateChannelPKSwitch(t *testing.T) {
	err := testStore.UpdateChannelPKSwitch(context.Background(), 123, ChannelPKSwitchOFF)
	if err != nil {
		t.Error(err)
	}
}

func TestStore_GetChannelPKSwitch(t *testing.T) {
	_, err := testStore.GetChannelPKSwitch(context.Background(), 2255334)
	if err != nil {
		t.<PERSON>rror(err)
	}
}
