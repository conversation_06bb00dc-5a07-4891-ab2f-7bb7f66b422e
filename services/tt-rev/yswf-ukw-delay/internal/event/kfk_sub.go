package event

import (
	"context"

	comKafka "golang.52tt.com/services/common/kafka"
	"golang.52tt.com/services/tt-rev/yswf-ukw-delay/internal/cache"
	"golang.52tt.com/services/tt-rev/yswf-ukw-delay/internal/conf"
	"golang.52tt.com/services/tt-rev/yswf-ukw-delay/internal/mgr"
	"golang.52tt.com/services/ugc/common/event"
)

type KafkaSub struct {
	simpleChannelEvSub *event.KafkaSub
	ukwEvSub           *event.KafkaSub
	cache              *cache.Cache
	mgr                *mgr.Mgr
}

func NewKafkaSub(ctx context.Context, sc *conf.StartConfig) (*KafkaSub, error) {
	var err error
	sub := &KafkaSub{}

	sub.cache, err = cache.NewCache(ctx, sc.RedisConfig)
	if err != nil {
		return sub, err
	}

	sub.mgr, err = mgr.NewMgr(sc)
	if err != nil {
		return sub, err
	}

	sub.simpleChannelEvSub, err = comKafka.NewKFKSub(sc.SimpleChannelEvKafkaConfig, sub.handleSimpleChannelEv)
	if err != nil {
		return sub, err
	}

	sub.ukwEvSub, err = comKafka.NewKFKSub(sc.UkwEvKafkaConfig, sub.handleUKWEvent)
	if err != nil {
		return sub, err
	}
	return sub, nil
}

func (sub *KafkaSub) Shutdown() {
	sub.simpleChannelEvSub.Stop()
}
