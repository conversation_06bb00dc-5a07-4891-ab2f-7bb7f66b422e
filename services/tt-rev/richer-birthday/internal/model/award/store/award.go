package store

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/mysql"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    "time"
)

const (
    AwardStatusInit = 0
    AwardStatusDone = 1
)

var createAwardTableSql = `
CREATE TABLE IF NOT EXISTS %s (
    id int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id 自增',
    order_id varchar(128) DEFAULT NULL COMMENT '奖励订单号',
    uid int(10) unsigned NOT NULL COMMENT 'uid',
    birthday varchar(128) NOT NULL default '' COMMENT '生日日期',
    gift_id varchar(128) NOT NULL default '0' COMMENT '奖励礼物id',
    gift_type int(10) unsigned NOT NULL default '0' COMMENT '礼物类型 麦位框/坐骑/...',
    num int(10) unsigned NOT NULL default '0' COMMENT '发放数量/时长',
    status tinyint unsigned NOT NULL COMMENT '状态 0-待发奖 1-发奖成功',
    award_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ctime timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    mtime timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id),
    UNIQUE KEY idx_order_id (order_id),
    INDEX idx_uid (uid),
    INDEX idx_status (status),
    INDEX idx_award_time (award_time),
    INDEX idx_create_time (ctime)
)engine=InnoDB default charset=utf8mb4 COMMENT "奖励发放记录表";
`

func genAwardTblName(t time.Time) string {
    return fmt.Sprintf("richer_birthday_award_%04d%02d", t.Year(), t.Month())
}

// 建表
func (s *Store) createAwardTable(ctx context.Context, tblTime time.Time) error {
    _, err := s.db.ExecContext(ctx, fmt.Sprintf(createAwardTableSql, genAwardTblName(tblTime)))
    if err != nil {
        log.ErrorWithCtx(nil, "createAwardTable failed. err:%v", err)
        return err
    }
    return nil
}

type Award struct {
    ID        uint64    `db:"id"`
    OrderId   string    `db:"order_id"`
    Uid       uint32    `db:"uid"`
    Birthday  string    `db:"birthday"`
    GiftId    string    `db:"gift_id"`
    GiftType  uint32    `db:"gift_type"`
    Num       uint32    `db:"num"`
    Status    uint32    `db:"status"`
    AwardTime time.Time `db:"award_time"`
    Ctime     time.Time `db:"ctime"`
    Mtime     time.Time `db:"mtime"`
}

// BatchInsertAward 批量插入奖励发放记录
func (s *Store) BatchInsertAward(ctx context.Context, tblTime time.Time, awards []*Award) error {
    if len(awards) == 0 {
        return nil
    }

    sql := fmt.Sprintf("INSERT INTO %s (order_id, uid, birthday, gift_id, gift_type, num, status, award_time) VALUES ", genAwardTblName(tblTime))
    var values []interface{}
    for i, award := range awards {
        if i > 0 {
            sql += ","
        }
        sql += "(?, ?, ?, ?, ?, ?, ?, ?)"
        values = append(values, award.OrderId, award.Uid, award.Birthday, award.GiftId, award.GiftType, award.Num, award.Status, award.AwardTime)
    }

    _, err := s.db.ExecContext(ctx, sql, values...)
    // 表不存在则创建表
    if mysql.IsMySQLError(err, 1146) {
        if err = s.createAwardTable(ctx, tblTime); err != nil {
            log.ErrorWithCtx(ctx, "createAwardTable failed. err:%v", err)
            return err
        }

        // 重新插入
        _, err = s.db.ExecContext(ctx, sql, values...)
    }

    if err != nil {
        log.ErrorWithCtx(ctx, "BatchInsertAward failed. err:%v", err)
        return err
    }
    return nil
}

// GetAwardByStatus 根据状态查询奖励发放记录
func (s *Store) GetAwardByStatus(ctx context.Context, tblTime time.Time, status uint32) ([]*Award, error) {
    var awards []*Award
    sql := fmt.Sprintf("SELECT id, order_id, uid, birthday, gift_id, gift_type, num, status, award_time, ctime, mtime FROM %s WHERE status = ?", genAwardTblName(tblTime))
    err := s.db.SelectContext(ctx, &awards, sql, status)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) {
            return awards, nil
        }
        log.ErrorWithCtx(ctx, "GetAwardByStatus failed. err:%v", err)
        return nil, err
    }
    return awards, nil
}

// UpdateAwardStatus 更新奖励发放记录状态
func (s *Store) UpdateAwardStatus(ctx context.Context, tblTime time.Time, orderId string, status uint32) error {
    sql := fmt.Sprintf("UPDATE %s SET status = ? WHERE order_id = ?", genAwardTblName(tblTime))
    _, err := s.db.ExecContext(ctx, sql, status, orderId)
    if err != nil {
        log.ErrorWithCtx(ctx, "UpdateAwardStatus failed. err:%v", err)
        return err
    }
    return nil
}

// GetAwardByBirthday 根据生日日期查询用户奖励发放记录
func (s *Store) GetAwardByBirthday(ctx context.Context, tblTime time.Time, uid uint32, birthday string) ([]*Award, error) {
    var awards []*Award
    sql := fmt.Sprintf("SELECT id, order_id, uid, birthday, gift_id, gift_type, num, status, award_time, ctime, mtime FROM %s WHERE uid = ? AND birthday = ?",
        genAwardTblName(tblTime))
    err := s.db.SelectContext(ctx, &awards, sql, uid, birthday)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) {
            return awards, nil
        }
        log.ErrorWithCtx(ctx, "GetAwardByBirthday failed. err:%v", err)
        return nil, err
    }
    return awards, nil
}

func (s *Store) GetAwardByOrderId(ctx context.Context, tblTime time.Time, orderId string) (*Award, error) {
    var award Award
    sql := fmt.Sprintf("SELECT id, order_id, uid, birthday, gift_id, gift_type, num, status, award_time, ctime, mtime FROM %s WHERE order_id = ?",
        genAwardTblName(tblTime))
    err := s.db.GetContext(ctx, &award, sql, orderId)
    if err != nil {
        if mysql.IsMySQLError(err, 1146) || mysql.IsNoRowsError(err) {
            return &award, nil
        }
        log.ErrorWithCtx(ctx, "GetAwardByOrderId failed. err:%v", err)
        return nil, err
    }
    return &award, nil
}
