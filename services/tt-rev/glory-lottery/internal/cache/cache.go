//go:generate mockgen -destination=../mocks/mock_cache.go -package=mocks golang.52tt.com/services/tt-rev/glory-lottery/internal/cache ICache
package cache

import (
    "context"
    "fmt"
    "time"

    redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
    "gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
)

const ()

// Cache .
type Cache struct {
    cmder redis.Cmdable
}

// NewCache .
func NewCache(ctx context.Context, cfg *redisConnect.RedisConfig) (*Cache, error) {
    client, err := redisConnect.NewClient(ctx, cfg)
    if err != nil {
        return nil, err
    }

    c := &Cache{
        cmder: client,
    }
    return c, nil
}

func (c *Cache) Close(ctx context.Context) {
    c.cmder.Shutdown(ctx)
}

const (
    lockKeyPrefix = "glory:lottery:lock:%s"
)

func genLockKey(key string) string {
    return fmt.Sprintf(lockKeyPrefix, key)
}

func (c *Cache) Lock(ctx context.Context, key string, ttl time.Duration) bool {
    ok, err := c.cmder.SetNX(ctx, genLockKey(key), "1", ttl).Result()
    if err != nil {
        return false
    }
    return ok
}

func (c *Cache) Unlock(ctx context.Context, key string) {
    c.cmder.Del(ctx, genLockKey(key))
}

func (c *Cache) LockHandle(ctx context.Context, key string, fn func() error) error {
    defer c.Unlock(ctx, key)

    if c.Lock(ctx, key, 5*time.Second) {
        return fn()
    }
    return fmt.Errorf("get lock fail")
}

func getTodayEndingDuration() time.Duration {
    tomorrow := time.Now().Add(24 * time.Hour)
    tomorrowZero := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, time.Local)
    return time.Duration(tomorrowZero.UnixNano() - time.Now().UnixNano())
}


func GetTodayEndingDuration() time.Duration {
    return getTodayEndingDuration()
}

func (c *Cache) GetRedisClient() redis.Cmdable {
    return c.cmder
}
