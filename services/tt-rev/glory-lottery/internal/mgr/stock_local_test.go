package mgr

import (
	"context"
	"testing"

	mysqlConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/mysql/connect"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"golang.52tt.com/services/tt-rev/glory-lottery/internal/cache"
	"golang.52tt.com/services/tt-rev/glory-lottery/internal/conf"
	"golang.52tt.com/services/tt-rev/glory-lottery/internal/rpc"
	"golang.52tt.com/services/tt-rev/glory-lottery/internal/store"
)

var testMgr *Mgr
var ctx = context.Background()

func init() {
	//sc := &conf.StartConfig{
	//    RedisConfig: &redisConnect.RedisConfig{
	//        Host: "*************",
	//        Port: 6379,
	//    },
	//    Mysql: &mysqlConnect.MysqlConfig{
	//        Host:     "*************",
	//        Port:     3306,
	//        Database: "appsvr",
	//        UserName: "godman",
	//        Password: "thegodofman",
	//    },
	//}

	// 云测
	sc := &conf.StartConfig{
		RedisConfig: &redisConnect.RedisConfig{
			Host: "************",
			Port: 6379,
		},
		Mysql: &mysqlConnect.MysqlConfig{
			Host:     "************",
			Port:     3306,
			Database: "appsvr",
			UserName: "godman",
			Password: "thegodofman",
		},
	}

	ca, err := cache.NewCache(ctx, sc.GetRedisConfig())
	if err != nil {
		panic(err)
	}
	st, err := store.NewStore(ctx, sc.GetMysqlConfig())
	if err != nil {
		panic(err)
	}

	rpcCli := rpc.NewClient()

	testMgr = NewMgr(sc, nil, ca, st, rpcCli)
	if testMgr == nil {
		panic("test mgr init fail")
	}
}

func TestMgr_ResetStockCyclely(t *testing.T) {
	testMgr.resetStockCyclely(1)
}
