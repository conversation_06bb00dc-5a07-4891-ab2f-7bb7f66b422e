// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/channel-gift-pk/internal/conf (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	conf "golang.52tt.com/services/tt-rev/channel-gift-pk/internal/conf"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetCommonRes mocks base method.
func (m *MockIBusinessConfManager) GetCommonRes() *conf.ResourceCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCommonRes")
	ret0, _ := ret[0].(*conf.ResourceCfg)
	return ret0
}

// GetCommonRes indicates an expected call of GetCommonRes.
func (mr *MockIBusinessConfManagerMockRecorder) GetCommonRes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCommonRes", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetCommonRes))
}

// GetCustomerPopUpVisible mocks base method.
func (m *MockIBusinessConfManager) GetCustomerPopUpVisible() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerPopUpVisible")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetCustomerPopUpVisible indicates an expected call of GetCustomerPopUpVisible.
func (mr *MockIBusinessConfManagerMockRecorder) GetCustomerPopUpVisible() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerPopUpVisible", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetCustomerPopUpVisible))
}

// GetEntryCfg mocks base method.
func (m *MockIBusinessConfManager) GetEntryCfg() *conf.EntryCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntryCfg")
	ret0, _ := ret[0].(*conf.EntryCfg)
	return ret0
}

// GetEntryCfg indicates an expected call of GetEntryCfg.
func (mr *MockIBusinessConfManagerMockRecorder) GetEntryCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntryCfg", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetEntryCfg))
}

// GetLevelGiftCfgByGiftId mocks base method.
func (m *MockIBusinessConfManager) GetLevelGiftCfgByGiftId(arg0 uint32) *conf.LevelGiftCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelGiftCfgByGiftId", arg0)
	ret0, _ := ret[0].(*conf.LevelGiftCfg)
	return ret0
}

// GetLevelGiftCfgByGiftId indicates an expected call of GetLevelGiftCfgByGiftId.
func (mr *MockIBusinessConfManagerMockRecorder) GetLevelGiftCfgByGiftId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelGiftCfgByGiftId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetLevelGiftCfgByGiftId), arg0)
}

// GetLevelGiftCfgList mocks base method.
func (m *MockIBusinessConfManager) GetLevelGiftCfgList() []*conf.LevelGiftCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelGiftCfgList")
	ret0, _ := ret[0].([]*conf.LevelGiftCfg)
	return ret0
}

// GetLevelGiftCfgList indicates an expected call of GetLevelGiftCfgList.
func (mr *MockIBusinessConfManagerMockRecorder) GetLevelGiftCfgList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelGiftCfgList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetLevelGiftCfgList))
}

// GetMatchMaxSec mocks base method.
func (m *MockIBusinessConfManager) GetMatchMaxSec() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMatchMaxSec")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetMatchMaxSec indicates an expected call of GetMatchMaxSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetMatchMaxSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMatchMaxSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMatchMaxSec))
}

// GetMatchTimeRangeList mocks base method.
func (m *MockIBusinessConfManager) GetMatchTimeRangeList() []*conf.TimeRange {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMatchTimeRangeList")
	ret0, _ := ret[0].([]*conf.TimeRange)
	return ret0
}

// GetMatchTimeRangeList indicates an expected call of GetMatchTimeRangeList.
func (mr *MockIBusinessConfManagerMockRecorder) GetMatchTimeRangeList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMatchTimeRangeList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetMatchTimeRangeList))
}

// GetPayAppId mocks base method.
func (m *MockIBusinessConfManager) GetPayAppId() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPayAppId")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetPayAppId indicates an expected call of GetPayAppId.
func (mr *MockIBusinessConfManagerMockRecorder) GetPayAppId() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPayAppId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPayAppId))
}

// GetPkRecordDurationDay mocks base method.
func (m *MockIBusinessConfManager) GetPkRecordDurationDay() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPkRecordDurationDay")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetPkRecordDurationDay indicates an expected call of GetPkRecordDurationDay.
func (mr *MockIBusinessConfManagerMockRecorder) GetPkRecordDurationDay() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPkRecordDurationDay", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPkRecordDurationDay))
}

// GetPkSec mocks base method.
func (m *MockIBusinessConfManager) GetPkSec() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPkSec")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetPkSec indicates an expected call of GetPkSec.
func (mr *MockIBusinessConfManagerMockRecorder) GetPkSec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPkSec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPkSec))
}

// GetRecentWinningDurationDay mocks base method.
func (m *MockIBusinessConfManager) GetRecentWinningDurationDay() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecentWinningDurationDay")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetRecentWinningDurationDay indicates an expected call of GetRecentWinningDurationDay.
func (mr *MockIBusinessConfManagerMockRecorder) GetRecentWinningDurationDay() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecentWinningDurationDay", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRecentWinningDurationDay))
}

// GetRecentlyWinningLimit mocks base method.
func (m *MockIBusinessConfManager) GetRecentlyWinningLimit() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecentlyWinningLimit")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetRecentlyWinningLimit indicates an expected call of GetRecentlyWinningLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetRecentlyWinningLimit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecentlyWinningLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRecentlyWinningLimit))
}

// GetResultAwardDelaySec mocks base method.
func (m *MockIBusinessConfManager) GetResultAwardDelaySec() int64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResultAwardDelaySec")
	ret0, _ := ret[0].(int64)
	return ret0
}

// GetResultAwardDelaySec indicates an expected call of GetResultAwardDelaySec.
func (mr *MockIBusinessConfManagerMockRecorder) GetResultAwardDelaySec() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResultAwardDelaySec", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetResultAwardDelaySec))
}

// GetSpriteCfgBySpriteId mocks base method.
func (m *MockIBusinessConfManager) GetSpriteCfgBySpriteId(arg0 uint32) *conf.SpriteCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpriteCfgBySpriteId", arg0)
	ret0, _ := ret[0].(*conf.SpriteCfg)
	return ret0
}

// GetSpriteCfgBySpriteId indicates an expected call of GetSpriteCfgBySpriteId.
func (mr *MockIBusinessConfManagerMockRecorder) GetSpriteCfgBySpriteId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpriteCfgBySpriteId", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetSpriteCfgBySpriteId), arg0)
}

// GetSpriteCfgList mocks base method.
func (m *MockIBusinessConfManager) GetSpriteCfgList() []*conf.SpriteCfg {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSpriteCfgList")
	ret0, _ := ret[0].([]*conf.SpriteCfg)
	return ret0
}

// GetSpriteCfgList indicates an expected call of GetSpriteCfgList.
func (mr *MockIBusinessConfManagerMockRecorder) GetSpriteCfgList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSpriteCfgList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetSpriteCfgList))
}

// GetSysSelectSpriteToast mocks base method.
func (m *MockIBusinessConfManager) GetSysSelectSpriteToast() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSysSelectSpriteToast")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetSysSelectSpriteToast indicates an expected call of GetSysSelectSpriteToast.
func (mr *MockIBusinessConfManagerMockRecorder) GetSysSelectSpriteToast() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSysSelectSpriteToast", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetSysSelectSpriteToast))
}

// GetUserDailyMatchLimit mocks base method.
func (m *MockIBusinessConfManager) GetUserDailyMatchLimit() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDailyMatchLimit")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUserDailyMatchLimit indicates an expected call of GetUserDailyMatchLimit.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserDailyMatchLimit() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDailyMatchLimit", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserDailyMatchLimit))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
