package resource_cfg

import(
	context "context"
	pb "golang.52tt.com/protocol/services/virtual-avatar"
)

type IMgr interface {
	BatchGetVirtualAvatarConfig(ctx context.Context, ids []uint32) (map[uint32]*pb.VirtualAvatarConfig,error)
	DelVirtualAvatarConfig(ctx context.Context, id uint32) error
	GetAllVirtualAvatarConfig(ctx context.Context) ([]*pb.VirtualAvatarConfig,error)
	GetVirtualAvatarConfByVaId(ctx context.Context, id uint32) (*pb.VirtualAvatarConfig,bool,error)
	SetVirtualAvatarConfig(ctx context.Context, cfg *pb.VirtualAvatarConfig) error
	Stop() 
}

