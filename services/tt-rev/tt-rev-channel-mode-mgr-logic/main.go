package main

import (
	"context"
	"golang.52tt.com/pkg/log"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"golang.52tt.com/protocol/services/demo/echo"

	pb "golang.52tt.com/protocol/services/logicsvr-go/tt-rev-channel-mode-mgr-logic"

	"golang.52tt.com/services/tt-rev/tt-rev-channel-mode-mgr-logic/internal"

	// use logic startup
	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/logic"
	

	_ "golang.52tt.com/pkg/hub/tyr/compatible/logic" // 兼容tyr公共库
)

func main() {
	var (
		server *internal.Server
		cfg    = &internal.StartConfig{}
		err    error
	)

	// config file support yaml & json, default tt-rev-channel-mode-mgr-logic.json/yaml
	if err := startup.New("tt-rev-channel-mode-mgr-logic", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				if server, err = internal.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterChannelModeMgrLogicServer(s, server)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, server)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			server.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
