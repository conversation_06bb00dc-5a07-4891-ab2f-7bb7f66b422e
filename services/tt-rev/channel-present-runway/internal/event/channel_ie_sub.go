package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/channelol"
	protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
	kafkaSimpleChPb "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkachannelsimple"
	"golang.52tt.com/services/tt-rev/channel-present-runway/internal/manager"
	"time"
)

const topicSimpleChEvent = "simple_channel_ev"

type SimpleChEventSub struct {
	kafkaSub     subscriber.Subscriber
	channelOLCli channelol.IClient
	mgr          manager.IChannelPresentRunwayMgr
}

func NewSimpleChEventSub(clientId, groupId string, topics, brokers []string, mgr manager.IChannelPresentRunwayMgr) (*SimpleChEventSub, error) {
	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	kafkaSub, err := kafka.NewSubscriber(brokers, conf, subscriber.WithMaxRetryTimes(3))
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber groupID:%s, topics:%v, brokers:%v, conf:%+v, err:%v", groupId, topics, brokers, conf, err)
		return nil, err
	}

	channelOLCli := channelol.NewClient()

	sub := &SimpleChEventSub{
		kafkaSub:     kafkaSub,
		channelOLCli: channelOLCli,
		mgr:          mgr,
	}

	err = kafkaSub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber groupID:%s, topics:%v, brokers:%v, conf:%+v, err:%v", groupId, topics, brokers, conf, err)
		return nil, err
	}

	log.Infof("NewSimpleChEventSub success. %+v", conf)
	return sub, nil
}

func (s *SimpleChEventSub) Close() error {
	return s.kafkaSub.Stop()
}

func (s *SimpleChEventSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)

	if msg.Topic == topicSimpleChEvent {
		return s.handlerSimpleChEvent(ctx, msg)
	}
	return nil, false
}

func (s *SimpleChEventSub) handlerSimpleChEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(ctx, 3*time.Minute)
	defer cancel()

	simpleChEvent := &kafkaSimpleChPb.ChSimpleEvent{}
	err := proto.Unmarshal(msg.Value, simpleChEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handlerPresentEvent Fail at proto.Unmarshal,Obj:%v err(%v)", msg.Value, err)
		return err, false
	}

	if simpleChEvent.EventType != uint32(kafkaSimpleChPb.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER) &&
		simpleChEvent.EventType != uint32(kafkaSimpleChPb.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_LEAVE) &&
		simpleChEvent.EventType != uint32(kafkaSimpleChPb.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_QUIT) {
		log.DebugWithCtx(ctx, "do not consumer, simpleChEvent.EventType: %d", simpleChEvent.EventType)
		return nil, false
	}

	// 大房检测
	isNeedGoAhead := s.mgr.CheckSuperChannelUser(simpleChEvent.GetUid(), simpleChEvent.GetChId())
	if !isNeedGoAhead {
		log.InfoWithCtx(ctx, "uid:%d, enter supperChannel without a runway, end the handler.", simpleChEvent.GetUid())
		return nil, false
	}

	// 进房事件
	if simpleChEvent.EventType == uint32(kafkaSimpleChPb.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_ENTER) {
		channelEnterOpt := &kafkaSimpleChPb.ChSimpleEnterOpt{}

		err := proto.Unmarshal(simpleChEvent.GetOptPbInfo(), channelEnterOpt)
		if err != nil {
			log.ErrorWithCtx(ctx, " handlerSimpleChEvent Failed to proto.Unmarshal err(%v)", err)
			return err, false
		}

		var overTime int64
		tsMs := channelEnterOpt.GetTsMs()
		curTS := time.Now().Unix()

		if curTS > (int64(tsMs / 1000)) {
			overTime = curTS - (int64(tsMs / 1000))
		} else {
			overTime = 0
		}
		if overTime > 12 {
			log.InfoWithCtx(ctx, "handlerSimpleChEvent: this enter event's overTime is too long,uid:%d, chId:%d, overTime: %v", simpleChEvent.GetUid(), simpleChEvent.GetChId(), overTime)
			return nil, false
		}

		// 进房事件消费
		err = s.mgr.OnUserEnterChannelEvent(ctx, simpleChEvent.GetUid(), simpleChEvent.GetChId())
		if err != nil {
			log.ErrorWithCtx(ctx, "OnUserEnterChannel mgr_Func，uid: %d,chId: %d, err:%v", simpleChEvent.GetUid(), simpleChEvent.GetChId(), err)
			return err, false
		}
	}
	// 退房事件
	if simpleChEvent.EventType == uint32(kafkaSimpleChPb.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_LEAVE) ||
		simpleChEvent.EventType == uint32(kafkaSimpleChPb.ESIMPLE_EVENT_TYPE_ENUM_SIMPLE_EXPIRE_QUIT) {

		err = s.mgr.OnUserLeaveChannelEvent(ctx, simpleChEvent.Uid, simpleChEvent.ChId)
		if err != nil {
			log.ErrorWithCtx(ctx, "OnUserLeaveChannel mgr_Func，uid: %d,chId: %d, err:%v", simpleChEvent.Uid, simpleChEvent.ChId, err)
			return err, false
		}
	}
	return nil, false
}
