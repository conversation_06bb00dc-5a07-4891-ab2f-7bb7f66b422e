package timer

import (
	"context"
	"errors"
	"time"

	channelMsgClient "golang.52tt.com/pkg/channel-msg"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app/channel"
	channelapi_go_pb "golang.52tt.com/protocol/services/channelapi-go"
	"golang.52tt.com/services/tt-rev/offering-room/internal/mgr"
)

func init() {
	registerTask(func(timerD *Timer) error {
		p := timerD.processor.(*processor)
		timerD.registeIntervalTask(
			"定期结束主持麦10分钟没人的房间的任务", // 任务名称，自己手动改
			time.Second*5, // 任务执行间隔，自己手动改
			p.ExpiredGameCleanerTimer,
		)
		return nil
	})
}

// ExpiredGameCleanerTimer 定期结束主持麦10分钟没人的房间的任务
func (t *processor) ExpiredGameCleanerTimer(ctx context.Context, in interface{}) (out interface{}, err error) {
	log.Debugf("ExpiredGameCleanerTimer start")
	now := time.Now()
	list, err := t.recordHostLeaveMic.GetExpiredChannels(ctx, now.Unix())
	if err != nil {
		log.ErrorWithCtx(ctx, "ExpiredGameCleanerTimer failed to GetExpiredChannels, now:%v, err:%v", now, err)
		return out, err
	}
	log.DebugWithCtx(ctx, "ExpiredGameCleanerTimer GetExpiredChannels success, now:%v, list:%v", now, list)

	// 遍历房间列表， 将嘉宾踢下麦 （嘉宾下麦事件 ==》 触发游戏结束逻辑）
	successChannelId := make([]uint32, 0, len(list))
	for _, channelId := range list {
		err := t.kickoutChannelMic(ctx, channelId)
		if err != nil {
			log.ErrorWithCtx(ctx, "ExpiredGameCleanerTimer failed to kickoutChannelMic, channelId:%d, err:%v", channelId, err)
			continue
		}
		successChannelId = append(successChannelId, channelId)
	}

	// 只移除处理成功的channelId
	err = t.recordHostLeaveMic.Remove(ctx, successChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ExpiredGameCleanerTimer failed to Remove, channelIdList:%v, err:%v", successChannelId, err)
		return out, err
	}
	log.InfoWithCtx(ctx, "kickoutChannelMic success, channelIdList:%v", successChannelId)
	return out, err
}

func (t *processor) kickoutChannelMic(ctx context.Context, channelId uint32) error {

	// 获取当前的游戏信息
	game, err := t.gameMgr.GetCurGame(ctx, channelId)

	if err != nil {
		if errors.Is(err, mgr.ErrNotFound) {
			log.InfoWithCtx(ctx, "ExpiredGameCleanerTimer 游戏未开始 channelId:%d", channelId)
			return nil
		}
		log.ErrorWithCtx(ctx, "ExpiredGameCleanerTimer 获取当前游戏信息失败: %v, cid: %d", err, channelId)
		return err
	}
	// 如果游戏在进行中，抱嘉宾下麦 == 》 嘉宾下麦，触发后续逻辑

	if game != nil && game.BaseInfo != nil && game.BaseInfo.Phase != mgr.GamePhaseUnspecified {
		// 把嘉宾踢下麦
		uid := game.BaseInfo.HonoredGuestUid
		_, serverError := t.channelApiCli.KickOutMicSpace(ctx, &channelapi_go_pb.KickOutMicReq{
			OpUid:         0,
			ChannelId:     channelId,
			TargetUidList: []uint32{uid},
		})
		if serverError != nil {
			log.ErrorWithCtx(ctx, "ExpiredGameCleanerTimer failed to KickoutChannelMic, channelId:%d, err:%v", channelId, err)
			return nil
		}
	}

	// 发送公屏
	sErr := t.channelMsgSender.MultiCast(ctx, &channelMsgClient.PushMessage{
		ChannelId: channelId,
		MsgType:   uint32(channel.ChannelMsgType_CHANNEL_TEXT_SYS_MSG),
		Content:   "因10分钟内无人主持，本轮拍拍取消",
	}, true, nil)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "ExpiredGameCleanerTimer failed to MultiCast, channelId:%d, err:%v", channelId, err)
		return sErr
	}

	return nil
}
