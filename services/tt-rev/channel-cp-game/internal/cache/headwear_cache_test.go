package cache

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
)

func TestChannelCpGameCache_AddLoseHeadWear(t *testing.T) {
	type fields struct {
		redisCli *redis.Client
		tracer   opentracing.Tracer
	}
	type args struct {
		ctx       context.Context
		uid       uint32
		channelId uint32
		newId     uint32
		expireSec uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "AddLoseHeadWear", fields: fields{redisCli: redisClient},
			args: args{
				ctx:       context.Background(),
				uid:       uid,
				channelId: channelId,
				newId:     1,
				expireSec: 100,
			},
			want:    100,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChannelCpGameCache{
				redisCli: tt.fields.redisCli,
				tracer:   tt.fields.tracer,
			}
			got, err := r.AddLose<PERSON>ead<PERSON>ear(tt.args.ctx, tt.args.uid, tt.args.channelId, tt.args.newId, tt.args.expireSec)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddLoseHeadWear() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AddLoseHeadWear() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelCpGameCache_GetLoseHeadWear(t *testing.T) {
	type fields struct {
		redisCli *redis.Client
		tracer   opentracing.Tracer
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int64
		want1   uint32
		wantErr bool
	}{
		//// TODO: Add test cases.
		//{name:"GetLoseHeadWear",fields: fields{redisCli: redisClient},
		//	args: args{ctx:context.Background(),  uid:uid},
		//want: 1,
		//want1: 100,
		//wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChannelCpGameCache{
				redisCli: tt.fields.redisCli,
				tracer:   tt.fields.tracer,
			}
			got, got1, err := r.GetLoseHeadWear(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLoseHeadWear() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetLoseHeadWear() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("GetLoseHeadWear() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestChannelCpGameCache_GetExpiredLoseHeadWear(t *testing.T) {
	type fields struct {
		redisCli *redis.Client
		tracer   opentracing.Tracer
	}
	tests := []struct {
		name    string
		fields  fields
		want    []string
		wantErr bool
	}{
		// TODO: Add test cases.
		//{name: "GetLoseHeadWear", fields: fields{redisCli: redisClient},
		//	want:    []string{fmt.Sprintf("%d", uid)},
		//	wantErr: false,
		//},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChannelCpGameCache{
				redisCli: tt.fields.redisCli,
				tracer:   tt.fields.tracer,
			}
			got, err := r.GetExpiredLoseHeadWear()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExpiredLoseHeadWear() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExpiredLoseHeadWear() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelCpGameCache_SetCurrentChannelId(t *testing.T) {
	type fields struct {
		redisCli *redis.Client
		tracer   opentracing.Tracer
	}
	type args struct {
		uid       uint32
		channelId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "SetCurrentChannelId", fields: fields{redisCli: redisClient}, args: args{uid: uid, channelId: channelId}, wantErr: false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChannelCpGameCache{
				redisCli: tt.fields.redisCli,
				tracer:   tt.fields.tracer,
			}
			if err := r.SetCurrentChannelId(tt.args.uid, tt.args.channelId); (err != nil) != tt.wantErr {
				t.Errorf("SetCurrentChannelId() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestChannelCpGameCache_GetCurrentChannelId(t *testing.T) {
	type fields struct {
		redisCli *redis.Client
		tracer   opentracing.Tracer
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    uint32
		wantErr bool
	}{
		// TODO: Add test cases.
		{name: "GetCurrentChannelId", fields: fields{redisCli: redisClient}, args: args{uid: uid}, wantErr: false, want: channelId},
		{name: "GetCurrentChannelId_nil", fields: fields{redisCli: redisClient}, args: args{uid: 111}, wantErr: false, want: 0},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChannelCpGameCache{
				redisCli: tt.fields.redisCli,
				tracer:   tt.fields.tracer,
			}
			got, err := r.GetCurrentChannelId(tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCurrentChannelId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetCurrentChannelId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestChannelCpGameCache_genLoseHeadWearKey(t *testing.T) {
	type fields struct {
		redisCli *redis.Client
		tracer   opentracing.Tracer
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		// TODO: Add test cases.
		{name: "genLoseHeadWearKey", fields: fields{redisCli: redisClient}, args: args{uid: uid}, want: fmt.Sprintf("set_lose_head_wear_%d", uid)},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ChannelCpGameCache{
				redisCli: tt.fields.redisCli,
				tracer:   tt.fields.tracer,
			}
			if got := r.genLoseHeadWearKey(tt.args.uid); got != tt.want {
				t.Errorf("genLoseHeadWearKey() = %v, want %v", got, tt.want)
			}
		})
	}
}
