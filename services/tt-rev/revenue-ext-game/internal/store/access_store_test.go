package store

/*
func GetMockMysql() (mock sqlxmock.Sqlmock, db *mysql.DBx) {
	var err error
	db, mock, err = sqlxmock.Newx()
	if err != nil {
		panic(err)
	}

	return
}

var (
	uid         = uint32(2266893)
	cid         = uint32(100085)
	uid2        = uint32(2266894)
	cid2        = uint32(100086)
	now         = time.Now()
	beginTime   = uint32(now.Unix())
	endTime     = uint32(now.Add(24 * time.Hour).Unix())
	channelType = uint32(7)
	opUser      = "11111"
	recordA     = &ChannelAccessConf{
		Id:          1,
		ChannelId:   cid,
		ChannelType: channelType,
		Uid:         uid,
		BeginTime:   beginTime,
		EndTime:     endTime,
		//PullFlowType: 0,
		IsDel:  false,
		MTime:  now,
		OpUser: opUser,
	}

	recordB = &ChannelAccessConf{
		Id:          2,
		ChannelId:   cid2,
		ChannelType: channelType,
		Uid:         uid2,
		BeginTime:   beginTime,
		EndTime:     endTime,
		//PullFlowType: 0,
		IsDel:  false,
		MTime:  now,
		OpUser: opUser,
	}
)

func TestStore_BatDelChannelAccessRecords(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	testList := make([]uint32, 0)
	testList = append(testList, cid)

	type fields struct {
		db *mysql.DBx
	}
	type args struct {
		ctx        context.Context
		confIdList []uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name:   "TestStore_BatDelChannelAccessRecords success",
			fields: fields{db: db},
			initFunc: func() {
				sql := fmt.Sprintf("update %s set is_del=1 where id in (%s)", RevenueExtGameChannelTbl, genParamJoinStr(testList))
				t.Logf("sql:%s", sql)
				mock.ExpectExec(regexp.QuoteMeta(sql)).WillReturnResult(sqlxmock.NewResult(0, 1))
			},
			args: args{
				ctx:        context.Background(),
				confIdList: testList,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := s.BatDelChannelAccessRecords(tt.args.ctx, tt.args.confIdList); (err != nil) != tt.wantErr {
				t.Errorf("BatDelChannelAccessRecords() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_BatInsertChannelAccessConf(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	testList := make([]*ChannelAccessConf, 0)
	testList = append(testList, recordA, recordB)

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx      context.Context
		confList []*ChannelAccessConf
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		wantErr  bool
	}{
		{
			name:   "TestStore_BatInsertChannelAccessConf success",
			fields: fields{db: db},
			initFunc: func() {
				query := fmt.Sprintf("insert into %s (channel_id, channel_type,uid,begin_time,end_time,stream_type,op_user) VALUES ", RevenueExtGameChannelTbl)
				param1 := fmt.Sprintf("(%d,%d,%d,%d,%d,%d,'%s')", cid, channelType, uid, beginTime, endTime, 0, opUser)
				param2 := fmt.Sprintf("(%d,%d,%d,%d,%d,%d,'%s')", cid2, channelType, uid2, beginTime, endTime, 0, opUser)
				sql := query + param1 + "," + param2

				t.Logf("sql:%s", sql)
				mock.ExpectExec(regexp.QuoteMeta(sql)).WillReturnResult(sqlxmock.NewResult(2, 2))
			},
			args: args{
				ctx:      context.Background(),
				confList: testList,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			if err := s.BatInsertChannelAccessConf(tt.args.ctx, tt.args.confList); (err != nil) != tt.wantErr {
				t.Errorf("BatInsertChannelAccessConf() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_GetAllChannelPendingTimeScope(t *testing.T) {
	//mock, db := GetMockMysql()
	//defer func() {
	//	_ = db.Close()
	//}()

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx         context.Context
		channelId   uint32
		nowTimeUnix uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*ChannelTimeScope
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			got, err := s.GetAllChannelPendingTimeScope(tt.args.ctx, tt.args.channelId, tt.args.nowTimeUnix)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllChannelPendingTimeScope() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllChannelPendingTimeScope() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetAllRecordCnt(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     uint32
		wantErr  bool
	}{
		{
			name:   "TestStore_GetAllRecordCnt success",
			fields: fields{db: db},
			initFunc: func() {
				mock.NewRows([]string{"id", "channel_id", "channel_type", "uid", "begin_time", "end_time", "stream_type", "is_del", "mtime", "op_user"}).
					AddRow(recordA.Id, recordA.ChannelId, recordA.ChannelType, recordA.Uid, recordA.BeginTime, recordA.EndTime, recordA.StreamType, recordA.IsDel, uint32(recordA.MTime.Unix()), recordA.OpUser).
					AddRow(recordB.Id, recordB.ChannelId, recordB.ChannelType, recordB.Uid, recordB.BeginTime, recordB.EndTime, recordB.StreamType, recordB.IsDel, uint32(recordB.MTime.Unix()), recordB.OpUser)

				sql := fmt.Sprintf("select count(*) from %s where is_del=0", RevenueExtGameChannelTbl)
				mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(sqlxmock.NewRows([]string{"count(*)"}).AddRow(2))
			},
			args:    args{ctx: context.Background()},
			want:    2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := s.GetAllRecordCnt(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllRecordCnt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetAllRecordCnt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_GetChannelAccessRecords(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	limit1 := uint32(10)
	offset1 := uint32(0)

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx    context.Context
		limit  uint32
		offset uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     []*ChannelAccessConf
		wantErr  bool
	}{
		{
			name:   "TestStore_GetChannelAccessRecords success",
			fields: fields{db: db},
			initFunc: func() {
				mock.NewRows([]string{"id", "channel_id", "channel_type", "uid", "begin_time", "end_time", "stream_type", "is_del", "mtime", "op_user"}).
					AddRow(recordA.Id, recordA.ChannelId, recordA.ChannelType, recordA.Uid, recordA.BeginTime, recordA.EndTime, recordA.StreamType, recordA.IsDel, recordA.MTime, recordA.OpUser).
					AddRow(recordB.Id, recordB.ChannelId, recordB.ChannelType, recordB.Uid, recordB.BeginTime, recordB.EndTime, recordB.StreamType, recordB.IsDel, recordB.MTime, recordB.OpUser)

				rows := sqlxmock.NewRows([]string{"id", "channel_id", "channel_type", "uid", "begin_time", "end_time", "stream_type", "is_del", "mtime", "op_user"}).
					AddRow(recordA.Id, recordA.ChannelId, recordA.ChannelType, recordA.Uid, recordA.BeginTime, recordA.EndTime, recordA.StreamType, recordA.IsDel, recordA.MTime, recordA.OpUser).
					AddRow(recordB.Id, recordB.ChannelId, recordB.ChannelType, recordB.Uid, recordB.BeginTime, recordB.EndTime, recordB.StreamType, recordB.IsDel, recordB.MTime, recordB.OpUser)

				sql := fmt.Sprintf("select %s from %s where is_del=0 order by mtime desc limit %d,%d", queryAllFiled, RevenueExtGameChannelTbl, offset1, limit1)
				mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(rows)
			},
			args: args{
				ctx:    context.Background(),
				limit:  limit1,
				offset: offset1,
			},
			want:    []*ChannelAccessConf{recordA, recordB},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := s.GetChannelAccessRecords(tt.args.ctx, tt.args.limit, tt.args.offset)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChannelAccessRecords() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChannelAccessRecords() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_SearchChannelAccessRecordByCid(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx       context.Context
		channelId uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     []*ChannelAccessConf
		wantErr  bool
	}{
		{
			name:   "TestStore_SearchChannelAccessRecordByCid success",
			fields: fields{db: db},
			initFunc: func() {
				mock.NewRows([]string{"id", "channel_id", "channel_type", "uid", "begin_time", "end_time", "stream_type", "is_del", "mtime", "op_user"}).
					AddRow(recordA.Id, recordA.ChannelId, recordA.ChannelType, recordA.Uid, recordA.BeginTime, recordA.EndTime, recordA.StreamType, recordA.IsDel, recordA.MTime, recordA.OpUser).
					AddRow(recordB.Id, recordB.ChannelId, recordB.ChannelType, recordB.Uid, recordB.BeginTime, recordB.EndTime, recordB.StreamType, recordB.IsDel, recordB.MTime, recordB.OpUser)
				rows := sqlxmock.NewRows([]string{"id", "channel_id", "channel_type", "uid", "begin_time", "end_time", "stream_type", "is_del", "mtime", "op_user"}).
					AddRow(recordA.Id, recordA.ChannelId, recordA.ChannelType, recordA.Uid, recordA.BeginTime, recordA.EndTime, recordA.StreamType, recordA.IsDel, recordA.MTime, recordA.OpUser)

				sql := fmt.Sprintf("select %s from %s where channel_id=%d and is_del=0", queryAllFiled, RevenueExtGameChannelTbl, cid)
				mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(rows)
			},
			args: args{
				ctx:       context.Background(),
				channelId: cid,
			},
			want:    []*ChannelAccessConf{recordA},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := s.SearchChannelAccessRecordByCid(tt.args.ctx, tt.args.channelId)
			if (err != nil) != tt.wantErr {
				t.Errorf("SearchChannelAccessRecordByCid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SearchChannelAccessRecordByCid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_SearchChannelAccessRecordByUid(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
		Uid uint32
	}
	tests := []struct {
		name     string
		fields   fields
		initFunc func()
		args     args
		want     []*ChannelAccessConf
		wantErr  bool
	}{
		{
			name:   "TestStore_SearchChannelAccessRecordByUid success",
			fields: fields{db: db},
			initFunc: func() {
				mock.NewRows([]string{"id", "channel_id", "channel_type", "uid", "begin_time", "end_time", "stream_type", "is_del", "mtime", "op_user"}).
					AddRow(recordA.Id, recordA.ChannelId, recordA.ChannelType, recordA.Uid, recordA.BeginTime, recordA.EndTime, recordA.StreamType, recordA.IsDel, recordA.MTime, recordA.OpUser).
					AddRow(recordB.Id, recordB.ChannelId, recordB.ChannelType, recordB.Uid, recordB.BeginTime, recordB.EndTime, recordB.StreamType, recordB.IsDel, recordB.MTime, recordB.OpUser)
				rows := sqlxmock.NewRows([]string{"id", "channel_id", "channel_type", "uid", "begin_time", "end_time", "stream_type", "is_del", "mtime", "op_user"}).
					AddRow(recordB.Id, recordB.ChannelId, recordB.ChannelType, recordB.Uid, recordB.BeginTime, recordB.EndTime, recordB.StreamType, recordB.IsDel, recordB.MTime, recordB.OpUser)

				sql := fmt.Sprintf("select %s from %s where uid=%d and is_del=0", queryAllFiled, RevenueExtGameChannelTbl, uid2)
				mock.ExpectQuery(regexp.QuoteMeta(sql)).WillReturnRows(rows)
			},
			args: args{
				ctx: context.Background(),
				Uid: uid2,
			},
			want:    []*ChannelAccessConf{recordB},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if tt.initFunc != nil {
				tt.initFunc()
			}
			got, err := s.SearchChannelAccessRecordByUid(tt.args.ctx, tt.args.Uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("SearchChannelAccessRecordByUid() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SearchChannelAccessRecordByUid() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_UpdateChannelAccessConf(t *testing.T) {
	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx  context.Context
		conf *ChannelAccessConf
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.UpdateChannelAccessConf(tt.args.ctx, tt.args.conf); (err != nil) != tt.wantErr {
				t.Errorf("UpdateChannelAccessConf() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_createRevenueExtGameChannelTbl(t *testing.T) {
	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.createRevenueExtGameChannelTbl(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("createRevenueExtGameChannelTbl() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
*/
