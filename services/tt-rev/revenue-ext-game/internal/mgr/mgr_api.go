package mgr

import (
	context "context"
	kafkapresent "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	pb "golang.52tt.com/protocol/services/revenue-ext-game"
	youknowwhopb "golang.52tt.com/protocol/services/youknowwho"
	entity "golang.52tt.com/services/tt-rev/revenue-ext-game/internal/entity"
)

type IMgr interface {
	BatDelChannelExtGameAccess(ctx context.Context, confIdList []uint32) error
	BatSetChannelExtGameAccessConf(ctx context.Context, in *pb.BatSetChannelExtGameAccessConfReq) ([]uint32, error)
	BatchGetChannelMountGame(ctx context.Context, channelIdList []uint32) (map[uint32]uint32, error)
	ChannelLiveEventHandle(ctx context.Context, channelId, uid uint32) error
	ChannelMsgEventHandle(ctx context.Context, channelId, uid uint32, text string) error
	ChannelUserWantPlayDataClean(channelId uint32) error
	ChatPkHeartbeat(ctx context.Context, gameType uint32, pkId string) error
	ChatPkHeartbeatTimeout()
	CheckChannelExtGameAccess(ctx context.Context, channelId uint32) (bool, error)
	CheckMountGame(ctx context.Context, channelId, gameType uint32) error
	ClearZombiePkHeartbeat()
	Close()
	DataReportComment(ctx context.Context, cid uint32, gameType uint32, list []*entity.Comment) error
	DataReportGift(ctx context.Context, cid uint32, gameType uint32, list []*entity.GiftData) error
	DoChannelMountGameHandle(ctx context.Context, gameType, channelId, uid uint32) (string, error)
	DoChannelUnmountGameHandle(ctx context.Context, channelId, gameType, uid uint32) error
	DoStartDataReportTask(ctx context.Context, channelId, gameType uint32, taskTypeList []string) error
	DoStopDataReportTask(ctx context.Context, channelId, gameType uint32, taskTypeList []string) error
	GenUserAvatarUrl(userName string) string
	GetChannelBySerial(ctx context.Context, gameType uint32, serial string) (uint32, error)
	GetChannelExtGameAccessRecords(ctx context.Context, in *pb.GetChannelExtGameAccessConfReq) (*pb.GetChannelExtGameAccessConfResp, error)
	GetChannelMountGame(ctx context.Context, channelId uint32) (uint32, error)
	GetDataReportTaskStatus(ctx context.Context, channelId, queryGameTy uint32, taskType string) (pb.GetDataReportTaskStatusResp_Status, error)
	GetExtGameCfgList(ctx context.Context, cid, uid uint32) ([]*pb.ExtGameCfg, error)
	GetGameRank(ctx context.Context, req *pb.GetExtGameScoreRankReq) (*pb.GetExtGameScoreRankResp, error)
	GetGameRankHonorInfo(ctx context.Context, req *pb.GetExtGameRankHonorInfoReq) (*pb.GetExtGameRankHonorInfoResp, error)
	GetUserExtGameInfo(ctx context.Context, channelId, uid uint32) (*pb.GetUserExtGameInfoResp, error)
	GetUserRankNameplate(ctx context.Context, req *pb.GetExtGameRankNameplateReq) (*pb.GetExtGameRankNameplateResp, error)
	PresentEventHandle(ctx context.Context, eventMsg *kafkapresent.PresentEvent) error
	RecordUserWantPlay(ctx context.Context, in *pb.ReportUserWantPlayReq) error
	ReportGameEnd(ctx context.Context, in *pb.ReportGameEndReq) error
	SearchChannelAccessByChannelId(ctx context.Context, channelId uint32) (*pb.GetChannelExtGameAccessConfResp, error)
	SearchChannelAccessByUid(ctx context.Context, uid uint32) (*pb.GetChannelExtGameAccessConfResp, error)
	SendSystemIMMsg(ctx context.Context, uid uint32, content string) error
	SetExtGameOpCfg(ctx context.Context, in *pb.SetExtGameOpCfgReq) error
	SetGameScoresRank(ctx context.Context, req *pb.SetGameScoresRankReq) error
	SetUserCamp(ctx context.Context, in *pb.SetUserCampReq) error
	StartChatPk(ctx context.Context, req *pb.StartChatPkReq) error
	StopChatPk(ctx context.Context, gameType uint32, pkId string) error
	TaskDataReport(ctx context.Context, cid uint32, msgType string, gameType uint32, payload interface{}) error
	UkwEventHandle(ctx context.Context, eventMsg *youknowwhopb.UKWChangeStatusMsg) error
	UpdateChannelExtGameAccessConf(ctx context.Context, in *pb.UpdateChannelExtGameAccessConfReq) error
}
