package transform

import (
    "github.com/stretchr/testify/assert"
    "testing"
)

func TestFlatMap(t *testing.T) {
    // Test case 1: General scenario
    items1 := []int{1, 2, 3}
    transformFn1 := func(i int) []int {
        return []int{i, i}
    }
    expected1 := []int{1, 1, 2, 2, 3, 3}
    result1 := FlatMap(items1, transformFn1)
    assert.ElementsMatch(t, expected1, result1)

    // Test case 2: Transformation with filtering
    items2 := []int{1, 2, 3, 4, 5}
    transformFn2 := func(s int) []int {
        if s > 3 {
            return []int{s}
        }
        return []int{}
    }
    expected2 := []int{4, 5}
    result2 := FlatMap(items2, transformFn2)
    assert.ElementsMatch(t, expected2, result2)
}
