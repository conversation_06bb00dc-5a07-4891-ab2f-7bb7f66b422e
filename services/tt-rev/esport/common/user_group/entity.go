package user_group

type EntityListReq struct {
    GroupId      string `json:"groupId"`
    GroupVersion string `json:"groupVersion"`
    PageNo       int    `json:"pageNo"`
    PageSize     int    `json:"pageSize"`
}

type EntityListRespData struct {
    GroupId      int      `json:"groupId"`
    GroupVersion string   `json:"groupVersion"`
    EntityIdList []string `json:"entityIdList"`
    TotalCount   int      `json:"totalCount"`
    PageNo       int      `json:"pageNo"`
    PageSize     int      `json:"pageSize"`
}

type EntityListResp struct {
    Code    int                 `json:"code"`
    Success bool                `json:"success"`
    Data    *EntityListRespData `json:"data"`
    Msg     string              `json:"msg"`
}

type GroupInfoReq struct {
    GroupId string `json:"groupId"`
}

// GroupInfoRespData 用户群信息,只选取了部分字段, 后续有需要再添加
type GroupInfoRespData struct {
    Id               int    `json:"id"`
    ProductId        string `json:"productId"`
    EntityType       string `json:"entityType"`
    GroupName        string `json:"groupName"`
    GroupContent     string `json:"groupContent"`
    EntityCount      int    `json:"entityCount"`
    LastGroupVersion string `json:"lastGroupVersion"`
    IsDeleted        int    `json:"isDeleted"`
    RealtimeType     string `json:"realtimeType"`
}

func (d *GroupInfoRespData) IsRealtime() bool {
    if d == nil {
        return false
    }
    return d.RealtimeType == "realtime"
}

type GroupInfoResp struct {
    Code    int                `json:"code"`
    Success bool               `json:"success"`
    Data    *GroupInfoRespData `json:"data"`
    Msg     string             `json:"msg"`
}

type MatchBatchReq struct {
    Id     int64                `json:"id"`
    Client *MatchBatchReqClient `json:"client"`
    Data   *MatchBatchReqData   `json:"data"`
}

type MatchBatchReqClient struct {
    Caller string `json:"caller"`
    Ex     string `json:"ex"`
}

type MatchBatchReqData struct {
    AppId      string   `json:"appId"`
    GroupList  []string `json:"groupList"`
    EntityList []string `json:"entityList"`
}

type MatchBatchResp struct {
    Id      int64               `json:"id"`
    Status  int                 `json:"status"`
    Message string              `json:"message"`
    Data    *MatchBatchRespData `json:"data"`
}

type MatchBatchRespData struct {
    Result []map[string]interface{} `json:"result"`
}
