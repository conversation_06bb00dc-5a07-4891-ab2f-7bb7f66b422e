package mgr

import (
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/mocks/account"
	"golang.52tt.com/clients/mocks/guild"
	Account "golang.52tt.com/protocol/services/accountsvr"
	esport_skill "golang.52tt.com/protocol/services/esport-skill"
	"golang.52tt.com/protocol/services/mocks"
	innerMocks "golang.52tt.com/services/tt-rev/esport/esport-admin-logic/internal/mocks"
	"reflect"
	"testing"
)

type helperForTest struct {
	*Manager
}

//go:generate mockgen -destination=../mocks/mocks.go -package=mocks golang.52tt.com/protocol/services/esport-skill EsportSkillClient

func newHelperForTest(t *testing.T) *helperForTest {
	ctrl := gomock.NewController(t)

	return &helperForTest{
		Manager: &Manager{
			skillCLi:    innerMocks.NewMockEsportSkillClient(ctrl),
			accountCli:  account.NewMockIClient(ctrl),
			guildCli:    guild.NewMockIClient(ctrl),
			customerCli: mocks.NewMockCustomerServiceClient(ctrl),
			roleCli:     mocks.NewMockESportRoleClient(ctrl),
		},
	}
}

func (receiver *helperForTest) getSkillCLi() *innerMocks.MockEsportSkillClient {
	return receiver.skillCLi.(*innerMocks.MockEsportSkillClient)
}

func (receiver *helperForTest) getAccountCli() *account.MockIClient {
	return receiver.accountCli.(*account.MockIClient)
}

func (receiver *helperForTest) getGuildCli() *guild.MockIClient {
	return receiver.guildCli.(*guild.MockIClient)
}

func (receiver *helperForTest) getCustomerCli() *mocks.MockCustomerServiceClient {
	return receiver.customerCli.(*mocks.MockCustomerServiceClient)
}

func (receiver *helperForTest) getRoleCli() *mocks.MockESportRoleClient {
	return receiver.roleCli.(*mocks.MockESportRoleClient)
}

func TestManager_IssueLabelPreCheck(t *testing.T) {
	type args struct {
		ctx                context.Context
		labelId            uint32
		coachIds           []uint32
		effectiveStartTime int64
		effectiveEndTime   int64
	}
	tests := []struct {
		name     string
		args     args
		want     []uint32
		wantErr  bool
		initFunc func(s *helperForTest)
	}{
		{
			name: "正常-无异常教练",
			args: args{
				ctx:                context.Background(),
				labelId:            1,
				coachIds:           []uint32{101, 102, 103},
				effectiveStartTime: 1000,
				effectiveEndTime:   2000,
			},
			want:    []uint32{},
			wantErr: false,
			initFunc: func(s *helperForTest) {
				queryIssuanceRecordsRes := &esport_skill.QueryIssuanceRecordsResponse{
					IssuanceRecordList: []*esport_skill.IssuanceRecord{},
					TotalCnt:           0,
				}
				s.getSkillCLi().EXPECT().QueryIssuanceRecords(gomock.Any(), gomock.Any()).Return(queryIssuanceRecordsRes, nil)
				s.getSkillCLi().EXPECT().ListLabels(gomock.Any(), gomock.Any()).Return(&esport_skill.ListLabelsResponse{
					LabelInfoList: []*esport_skill.LabelInfo{
						{
							Id:        1,
							GameId:    119,
							LabelType: esport_skill.LabelType_LABEL_TYPE_SKILL,
						},
					},
					TotalCnt: 1,
				}, nil)
				s.getSkillCLi().EXPECT().BatchCheckCoachHasGame(gomock.Any(), gomock.Any()).Return(&esport_skill.BatchCheckCoachHasGameResponse{
					CoachHasGameMap: map[uint32]bool{
						101: true,
						102: true,
						103: true,
					},
				}, nil)
			},
		},
		{
			name: "正常-存在异常教练",
			args: args{
				ctx:                context.Background(),
				labelId:            2,
				coachIds:           []uint32{201, 202, 203},
				effectiveStartTime: 3000,
				effectiveEndTime:   4000,
			},
			want:    nil,
			wantErr: true,
			initFunc: func(s *helperForTest) {
				queryIssuanceRecordsRes := &esport_skill.QueryIssuanceRecordsResponse{
					IssuanceRecordList: []*esport_skill.IssuanceRecord{
						{
							Uid: 203,
						},
					},
					TotalCnt: 1,
				}
				s.getSkillCLi().EXPECT().QueryIssuanceRecords(gomock.Any(), gomock.Any()).Return(queryIssuanceRecordsRes, nil)
				s.getSkillCLi().EXPECT().ListLabels(gomock.Any(), gomock.Any()).Return(&esport_skill.ListLabelsResponse{
					LabelInfoList: []*esport_skill.LabelInfo{
						{
							Id:        1,
							GameId:    119,
							LabelType: esport_skill.LabelType_LABEL_TYPE_SKILL,
						},
					},
					TotalCnt: 1,
				}, nil)
				s.getSkillCLi().EXPECT().BatchCheckCoachHasGame(gomock.Any(), gomock.Any()).Return(&esport_skill.BatchCheckCoachHasGameResponse{
					CoachHasGameMap: map[uint32]bool{
						101: true,
						102: true,
						103: true,
					},
				}, nil)
				s.getAccountCli().EXPECT().GetUserByUid(gomock.Any(), gomock.Any()).Return(&Account.UserResp{
					Uid:                 0,
					Phone:               "",
					Username:            "",
					Alias:               "",
					Nickname:            "",
					Sex:                 0,
					Signature:           "",
					Password:            "",
					Verify:              false,
					CurrentGuildId:      0,
					LastQuitGuildType:   0,
					RegisteredAt:        0,
					KickClientDeviceId:  nil,
					Question:            0,
					LastQuitGuildId:     0,
					LastQuitGuildTime:   0,
					WhoInviteUid:        0,
					InviteCode:          "",
					Status:              0,
					UserType:            0,
					Fromid:              "",
					Source:              0,
					LastLoginAt:         0,
					PasswordSet:         false,
					PrefixValid:         0,
					OriginalNickname:    "",
					GuildPrefix:         "",
					IsUnregister:        false,
					IsClearLocalAccount: false,
					IsNoble:             false,
				}, nil)
			},
		},
		{
			name: "RPC调用失败",
			args: args{
				ctx:                context.Background(),
				labelId:            3,
				coachIds:           []uint32{301, 302, 303},
				effectiveStartTime: 5000,
				effectiveEndTime:   6000,
			},
			want:    nil,
			wantErr: true,
			initFunc: func(s *helperForTest) {
				s.getSkillCLi().EXPECT().QueryIssuanceRecords(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("rpc error"))
				s.getSkillCLi().EXPECT().ListLabels(gomock.Any(), gomock.Any()).Return(&esport_skill.ListLabelsResponse{
					LabelInfoList: []*esport_skill.LabelInfo{
						{
							Id:        1,
							GameId:    119,
							LabelType: esport_skill.LabelType_LABEL_TYPE_SKILL,
						},
					},
					TotalCnt: 1,
				}, nil)
				s.getSkillCLi().EXPECT().BatchCheckCoachHasGame(gomock.Any(), gomock.Any()).Return(&esport_skill.BatchCheckCoachHasGameResponse{
					CoachHasGameMap: map[uint32]bool{
						101: true,
						102: true,
						103: true,
					},
				}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := newHelperForTest(t)
			if tt.initFunc != nil {
				tt.initFunc(m)
			}
			got, err := m.IssueLabelPreCheck(tt.args.ctx, tt.args.labelId, tt.args.coachIds, tt.args.effectiveStartTime, tt.args.effectiveEndTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("Manager.IssueLabelPreCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Manager.IssueLabelPreCheck() = %v, want %v", got, tt.want)
			}
		})
	}
}
