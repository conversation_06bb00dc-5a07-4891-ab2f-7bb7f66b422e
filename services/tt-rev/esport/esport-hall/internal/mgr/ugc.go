package mgr

import (
    "context"
    "fmt"
    "time"
)

// AddIgnoreRecommendCoach 添加忽略推荐的教练记录
// 1. 更新 Redis 缓存
// 2. 将记录持久化到数据库
func (m *Mgr) AddIgnoreRecommendCoach(ctx context.Context, uid, coachID uint32) error {
    err := m.cache.AddIgnoreRecommendCoach(ctx, uid, coachID)
    if err != nil {
        return fmt.Errorf("failed to add ignore recommend coach to cache: %w", err)
    }
    return nil
}

// GetIgnoreRecommendCoaches 获取用户不喜欢的教练列表
func (m *Mgr) GetIgnoreRecommendCoaches(ctx context.Context, uid uint32) ([]uint32, error) {

    // 获取动态配置的过期时间
    days := m.bc.GetUgcRecommendConfig().UnlikeExpireTime
    // 往前算过期时间
    expirationTime := time.Now().AddDate(0, 0, int(days)*-1)

    // 从缓存中获取
    coachIDs, err := m.cache.GetIgnoreRecommendCoaches(ctx, uid, expirationTime)
    if err != nil {
        return nil, fmt.Errorf("failed to get ignore recommend coaches from cache: %w", err)
    }
    return coachIDs, nil
}
