package conf

import (
	"gitlab.ttyuyin.com/avengers/tyr/core/config/ttconfig"
	"golang.52tt.com/protocol/services/esport_rcmd"
	"sync/atomic"
)

type BusinessConf struct {
	ExposeGradingConf       *ExposeGradingConf                   `json:"expose_grading_conf"`
	Scenes                  []*esport_rcmd.Scene                 `json:"scenes"`
	FilterRules             []*esport_rcmd.FilterRule            `json:"filter_rules"`
	RecallSourceQueueRules  []*esport_rcmd.RecallSourceQueueRule `json:"recall_source_queue_rules"`
	RecallSourceRules       []*esport_rcmd.RecallSourceRule      `json:"recall_source_rules"`
	ABTestApi               string                               `json:"ab_test_api"`
	UserGroupApi            UserGroupApi                         `json:"user_group_api"`
	SkillProductSearchPrice map[string][]string                  `json:"skill_product_search_price"`
	NewRcmdConfig           *NewRcmdConfig                       `json:"new_rcmd_config"`
	CouponConf              *CouponConf                          `json:"coupon_conf"`
	GroupCacheUpdateSec     int                                  `json:"group_cache_update_sec"`
}

type NewRcmdConfig struct {
	IsOpen    bool     `json:"is_open"`
	WhiteList []uint32 `json:"white_list"`
	UserGroup map[string]string `json:"user_group"`
}

type BusinessDyConf struct {
	atomicConfig *atomic.Value
}

func NewBusinessDyConf() (*BusinessDyConf, error) {
	out := &BusinessDyConf{}
	cfg := &BusinessConf{}
	atomCfg, err := ttconfig.AtomLoad("esport-rcmd", cfg)
	if nil != err {
		return out, err
	}

	out.atomicConfig = atomCfg
	return out, nil
}

func (c *BusinessDyConf) GetConfig() *BusinessConf {
	if c == nil || c.atomicConfig == nil {
		return &BusinessConf{}
	}

	if cfg, ok := c.atomicConfig.Load().(*BusinessConf); ok {
		return cfg
	}

	return &BusinessConf{}
}

// ================================= 业务动态配置 =================================
// ExposeGradingConf 曝光分级策略配置
type ExposeGradingConf struct {
	GodLevelConf     []*ExposeGradingGodLevelConf   `json:"god_level_conf"`
	FamousPlayerConf *ExposeGradingFamousPlayerConf `json:"famous_player_conf"`
}

type ExposeGradingGodLevelConf struct {
	LV                    uint32  `json:"lv"`
	HourExposeLimit       uint32  `json:"hour_expose_limit"`
	DayExposeLimit        uint32  `json:"day_expose_limit"`
	ConversionRateRequire float64 `json:"conversion_rate_require"`
}

type ExposeGradingFamousPlayerConf struct {
	HourExposeLimit uint32 `json:"hour_expose_limit"`
	DayExposeLimit  uint32 `json:"day_expose_limit"`
}

type UserGroupApi struct {
	DspLpmAdminHost        string `json:"dsp_lpm_admin_host"`
	DspLpmOfflineGroupHost string `json:"dsp_lpm_offline_group_host"`
	DspLpmApiserverHost    string `json:"dsp_lpm_apiserver_host"`
}

type CouponConf struct {
	CanUseCouponSwitch bool   `json:"can_use_coupon_switch"`
	CoachEveryDayCount uint32 `json:"coach_everyday_count"`
}

func (c *BusinessDyConf) IsNewRcmdOpen(uid uint32) bool {
	if c == nil || c.atomicConfig == nil {
		return false
	}

	if cfg, ok := c.atomicConfig.Load().(*BusinessConf); ok && cfg != nil {
		if cfg.NewRcmdConfig.IsOpen {
			return true
		} else {
			for _, v := range cfg.NewRcmdConfig.WhiteList {
				if uid == v {
					return true
				}
			}
		}
	}
	return false
}
