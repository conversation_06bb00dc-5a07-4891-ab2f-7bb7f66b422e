package mysql

import (
	context "context"
	sql "database/sql"
	sqlx "github.com/jmoiron/sqlx"
	time "time"
)

type IStore interface {
	AddCatCanteenLightEffects(ctx context.Context, infos []*LightEffect) ([]uint32, error)
	BatDelLightEffectByConfId(ctx context.Context, confIdList []uint32) error
	BatchGetCostLogByLottery(ctx context.Context, uid uint32, lotteryIdList []string, outsideTime time.Time) ([]*GiftCostLog, error)
	BatchGetLotteryLog(ctx context.Context, uid uint32, lotteryIdList []string, outsideTime time.Time) ([]*LotteryLog, error)
	ChangeAwardMountRecordStatus(ctx context.Context, t time.Time, orderId string, status uint32) error
	ChangeAwardPackRecordStatus(ctx context.Context, t time.Time, orderId string, uid, status uint32) error
	ChangeConsumeRecordPayInfo(ctx context.Context, t time.Time, uid uint32, oldStatus []uint32, newStatus uint32, payOrderId, tBeanTimeStr string) (bool, error)
	ChangeConsumeRecordStatus(ctx context.Context, tx *sql.Tx, t time.Time, uid uint32, oldStatus []uint32, newStatus uint32, payOrderId string) (bool, error)
	CheckUserHasBigAward(ctx context.Context, uid uint32, levelId uint32, beginTime, endTime time.Time) (bool, error)
	CreateAwardMountTbl(ctx context.Context, tx *sql.Tx, t time.Time) error
	CreateAwardPackTbl(ctx context.Context, tx *sql.Tx, t time.Time, idx uint32) error
	CreateCatCanteenLightEffectTbl(ctx context.Context) error
	CreateConsumeLogTbl(ctx context.Context, t time.Time) error
	CreateDailyAwardLogTbl(ctx context.Context) error
	CreateDailyFinancialLogTbl(ctx context.Context) error
	CreateGiftCostLogTbl(ctx context.Context, outsideTime time.Time) error
	CreateLevelConfTbl(ctx context.Context) error
	CreateLimitConfTbl(ctx context.Context) error
	CreateLotteryLogTbl(ctx context.Context, tx *sql.Tx, t time.Time, idx uint32) error
	CreatePrizeTbl(ctx context.Context) error
	CreateRemainChanceTbl(ctx context.Context) error
	CreateTable()
	CreateUserGuaranteedTbl(ctx context.Context) error
	DelPendingLevelInfo(ctx context.Context, tx *sql.Tx, nowTime time.Time) error
	DelPendingPrizeInfo(ctx context.Context, tx *sql.Tx, nowTime time.Time) error
	GetAllCatCanteenLight(ctx context.Context) ([]*LightEffect, error)
	GetAllLevelInfoList(ctx context.Context) ([]*LevelConf, error)
	GetAllLevelInfoPending(ctx context.Context, nowTime time.Time, orderAsc bool) ([]*LevelConf, error)
	GetAllPrizeList(ctx context.Context) ([]*Prize, error)
	GetAwardMountRecordByLottery(ctx context.Context, uid uint32, lotteryIdList []string, t time.Time) ([]*AwardMountRecord, error)
	GetAwardMountRecordByStatus(ctx context.Context, status, limit uint32, beginTime, endTime time.Time) ([]*AwardMountRecord, error)
	GetAwardOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error)
	GetAwardPackRecordByLottery(ctx context.Context, uid uint32, lotteryIdList []string, t time.Time) ([]*AwardPackRecord, error)
	GetAwardPackRecordByStatus(ctx context.Context, status, limit, idx uint32, beginTime, endTime time.Time) ([]*AwardPackRecord, error)
	GetAwardStatistics(ctx context.Context, beginTime, endTime time.Time) (map[uint32]map[uint32]*AwardStatistics, error)
	GetAwardStats(ctx context.Context, begin, end time.Time) ([]*AwardStats, error)
	GetAwardTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error)
	GetChanceBuyStats(ctx context.Context, begin, end time.Time) (*Stats, error)
	GetChanceBuyStatsUseTBeanTime(ctx context.Context, begin, end time.Time) (*Stats, error)
	GetConsumeOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error)
	GetConsumeRecordByPayId(ctx context.Context, uid uint32, t time.Time, payOrderId string) (*ConsumeRecord, bool, error)
	GetConsumeTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error)
	GetCostLogByOrder(ctx context.Context, outsideTime time.Time, orderId string) (*GiftCostLog, bool, error)
	GetCostLogsByStatus(ctx context.Context, outsideTime time.Time, status, offset, limit uint32) ([]*GiftCostLog, error)
	GetCostOrderIds(ctx context.Context, beginTime, endTime time.Time) ([]string, error)
	GetCostStats(ctx context.Context, beginTime, endTime time.Time) (map[uint32]*CostStats, error)
	GetCostTotalCountInfo(ctx context.Context, beginTime, endTime time.Time) (*StCount, error)
	GetDailyCostStats(ctx context.Context, begin, end time.Time) (*Stats, error)
	GetDateRemainTotal(ctx context.Context, date time.Time) (uint64, error)
	GetDb() *sqlx.DB
	GetEffectPrizeList(ctx context.Context, nowTime time.Time) ([]*Prize, error)
	GetEffectPrizeListByLevelId(ctx context.Context, levelId uint32, nowTime time.Time) ([]*Prize, error)
	GetLevelInfoByLevelId(ctx context.Context, levelId uint32) (*LevelConf, error)
	GetLevelInfoEffectiveInOrder(ctx context.Context, nowTime time.Time, orderAsc bool) ([]*LevelConf, error)
	GetLevelInfoUpdateVersion(ctx context.Context, nowTime time.Time) (int64, error)
	GetLimitConfList(ctx context.Context) ([]*UserLimitCfg, error)
	GetLotteryStatistics(ctx context.Context, beginTime, endTime time.Time) (map[uint32]*LotteryStatistics, error)
	GetLotteryStats(ctx context.Context, begin, end time.Time) (*LotteryStats, error)
	GetLotteryTotalPeopleCnt(ctx context.Context, beginTime, endTime time.Time) (int64, error)
	GetMaxMtimeOfLightEffTbl(ctx context.Context) (int64, error)
	GetPendingPrizeList(ctx context.Context, nowTime time.Time) ([]*Prize, error)
	GetPeopleCntByPackId(ctx context.Context, begin, end time.Time, queryCondition string, packIdList ...uint32) (uint64, error)
	GetPrizeUpdateVersion(ctx context.Context, nowTime time.Time) (int64, error)
	GetUserAwardPackRecords(ctx context.Context, uid, levelId, limit uint32, offset string, ascSort bool) ([]*AwardPackRecord, error)
	GetUserGuaranteed(ctx context.Context, uid uint32) (map[uint32]*UserGuaranteed, error)
	GetUserGuaranteedByLv(ctx context.Context, uid, levelId uint32) (*UserGuaranteed, bool, error)
	GetUserLotteryLogs(ctx context.Context, uid, levelId, limit uint32, offset string, ascSort bool) ([]*LotteryLog, error)
	InsertAwardMountRecords(ctx context.Context, tx *sql.Tx, outsideTime time.Time, uid uint32, records []*AwardMountRecord) error
	InsertAwardPackRecords(ctx context.Context, tx *sql.Tx, outsideTime time.Time, uid uint32, records []*AwardPackRecord) error
	InsertConsumeRecord(ctx context.Context, r *ConsumeRecord) error
	InsertDailyAwardLog(ctx context.Context, info *DailyAwardLog) error
	InsertDailyConsumeLog(ctx context.Context, info *DailyConsumeLog) error
	InsertGiftCostLogs(ctx context.Context, outsideTime time.Time, uid uint32, logs []*GiftCostLog) error
	InsertLotteryLog(ctx context.Context, tx *sql.Tx, outsideTime time.Time, uid uint32, info *LotteryLog) error
	SetLevelInfo(ctx context.Context, tx *sql.Tx, levelId uint32, conf *LevelConf, beginTime, versionTime time.Time) error
	SetLimitConfList(ctx context.Context, list []*UserLimitCfg) error
	SetPrizeList(ctx context.Context, tx *sql.Tx, levelId uint32, list []*Prize, beginTime, versionTime time.Time) error
	Transaction(ctx context.Context, f func(tx *sql.Tx) error) error
	UpdateCostLogStatus(ctx context.Context, tx *sql.Tx, outsideTime time.Time, orderIds []string, toStatus uint32, fromStatusList []uint32) (bool, error)
	UpdateLightEffectByConfId(ctx context.Context, confId uint32, info *LightEffect) error
	UpsertUserGuaranteed(ctx context.Context, tx *sql.Tx, info *UserGuaranteed) error
}

type IDailyAwardLog interface {
	Incr(chanceCost, awardAmount, awardPrice uint64)
}

type IStats interface {
	Incr(amount, price uint64)
}
