// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/tt-rev/cat-canteen/manager/lottery (interfaces: ILottery)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	lottery "golang.52tt.com/services/tt-rev/cat-canteen/internal/manager/lottery"
)

// MockILottery is a mock of ILottery interface.
type MockILottery struct {
	ctrl     *gomock.Controller
	recorder *MockILotteryMockRecorder
}

// MockILotteryMockRecorder is the mock recorder for MockILottery.
type MockILotteryMockRecorder struct {
	mock *MockILottery
}

// NewMockILottery creates a new mock instance.
func NewMockILottery(ctrl *gomock.Controller) *MockILottery {
	mock := &MockILottery{ctrl: ctrl}
	mock.recorder = &MockILotteryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILottery) EXPECT() *MockILotteryMockRecorder {
	return m.recorder
}

// CheckLotteryUpdate mocks base method.
func (m *MockILottery) CheckLotteryUpdate() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckLotteryUpdate")
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckLotteryUpdate indicates an expected call of CheckLotteryUpdate.
func (mr *MockILotteryMockRecorder) CheckLotteryUpdate() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckLotteryUpdate", reflect.TypeOf((*MockILottery)(nil).CheckLotteryUpdate))
}

// GetLevelCfgList mocks base method.
func (m *MockILottery) GetLevelCfgList() []*lottery.LevelConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelCfgList")
	ret0, _ := ret[0].([]*lottery.LevelConf)
	return ret0
}

// GetLevelCfgList indicates an expected call of GetLevelCfgList.
func (mr *MockILotteryMockRecorder) GetLevelCfgList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelCfgList", reflect.TypeOf((*MockILottery)(nil).GetLevelCfgList))
}

// GetNextPrizePool mocks base method.
func (m *MockILottery) GetNextPrizePool(arg0, arg1 uint32) (*lottery.Pool, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextPrizePool", arg0, arg1)
	ret0, _ := ret[0].(*lottery.Pool)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetNextPrizePool indicates an expected call of GetNextPrizePool.
func (mr *MockILotteryMockRecorder) GetNextPrizePool(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextPrizePool", reflect.TypeOf((*MockILottery)(nil).GetNextPrizePool), arg0, arg1)
}

// GetPrizeInfo mocks base method.
func (m *MockILottery) GetPrizeInfo(arg0 uint32) (*lottery.Prize, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrizeInfo", arg0)
	ret0, _ := ret[0].(*lottery.Prize)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetPrizeInfo indicates an expected call of GetPrizeInfo.
func (mr *MockILotteryMockRecorder) GetPrizeInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrizeInfo", reflect.TypeOf((*MockILottery)(nil).GetPrizeInfo), arg0)
}

// GetPrizePool mocks base method.
func (m *MockILottery) GetPrizePool(arg0 uint32) (*lottery.Pool, bool) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrizePool", arg0)
	ret0, _ := ret[0].(*lottery.Pool)
	ret1, _ := ret[1].(bool)
	return ret0, ret1
}

// GetPrizePool indicates an expected call of GetPrizePool.
func (mr *MockILotteryMockRecorder) GetPrizePool(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrizePool", reflect.TypeOf((*MockILottery)(nil).GetPrizePool), arg0)
}

// PrizeDraw mocks base method.
func (m *MockILottery) PrizeDraw(arg0 context.Context, arg1 lottery.PrizeDrawReq, arg2 uint32) ([]*lottery.AwardInfo, uint32, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PrizeDraw", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*lottery.AwardInfo)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(uint32)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// PrizeDraw indicates an expected call of PrizeDraw.
func (mr *MockILotteryMockRecorder) PrizeDraw(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PrizeDraw", reflect.TypeOf((*MockILottery)(nil).PrizeDraw), arg0, arg1, arg2)
}
