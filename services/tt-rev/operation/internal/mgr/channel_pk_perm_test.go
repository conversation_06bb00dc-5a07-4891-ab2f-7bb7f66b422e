package mgr

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	channelpb "golang.52tt.com/protocol/services/channelsvr"
	guildpb "golang.52tt.com/protocol/services/guildsvr"
	pb "golang.52tt.com/protocol/services/tt-rev-operation"
	"golang.52tt.com/services/tt-rev/operation/internal/store"

	_ "golang.52tt.com/services/recommend-dialog/tools/grpc_proxy/enable"
	"time"
)

func TestMgr_BatchGetChannelPKPerm(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testMgr := newMockTestMgr(ctrl)

	type args struct {
		ctx        context.Context
		viewIdList []string
	}
	tests := []struct {
		name    string
		args    args
		want    []*pb.ChannelPKPermInfo
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx:        context.Background(),
				viewIdList: []string{"2193307"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockChannelCli.EXPECT().BatchGetChannelSimpleInfoByViewId(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*channelpb.ChannelSimpleInfo{2255334: {}}, nil)
			mockStore.EXPECT().BatchGetChannelPKPerm(gomock.Any(), gomock.Any()).Return([]*store.ChannelPKPerm{{}}, nil)
			mockGuildCli.EXPECT().GetGuildBat(gomock.Any(), gomock.Any(), gomock.Any()).Return(&guildpb.GetGuildBatResp{}, nil)
			_, err := testMgr.BatchGetChannelPKPerm(tt.args.ctx, tt.args.viewIdList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchGetChannelPKPerm() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestMgr_BatchOperationChannelPKPerm(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testMgr := newMockTestMgr(ctrl)
	type args struct {
		ctx             context.Context
		viewIdList      []string
		op              uint32
		effectTimeStart uint32
		effectTimeEnd   uint32
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "",
			args: args{
				ctx:             context.Background(),
				viewIdList:      []string{"2181945"},
				op:              1,
				effectTimeStart: 0,
				effectTimeEnd:   0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			channelId := uint32(2255334)
			channelType := uint32(4)
			mockChannelCli.EXPECT().BatchGetChannelSimpleInfoByViewId(gomock.Any(), gomock.Any(), gomock.Any()).Return(map[uint32]*channelpb.ChannelSimpleInfo{
				2255334: {ChannelViewId: &tt.args.viewIdList[0], ChannelType: &channelType, ChannelId: &channelId},
			}, nil)
			mockStore.EXPECT().BatchDelChannelPKPerm(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mockStore.EXPECT().BatchGrantGetChannelPKPerm(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			if err := testMgr.BatchOperationChannelPKPerm(tt.args.ctx, tt.args.viewIdList, tt.args.op, tt.args.effectTimeStart, tt.args.effectTimeEnd); (err != nil) != tt.wantErr {
				t.Errorf("BatchOperationChannelPKPerm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMgr_UpdateChannelPkPermCache(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testMgr := newMockTestMgr(ctrl)

	beginTime := time.Now()
	endTime := beginTime.AddDate(0, 0, 20)
	tests := []struct {
		name     string
		initFunc func()
	}{
		{
			name: "common",
			initFunc: func() {
				mockStore.EXPECT().BatchGetChannelPKPerm(gomock.Any(), gomock.Any()).Return([]*store.ChannelPKPerm{
					{
						ID:              1,
						ChannelId:       1,
						PermType:        1,
						EffectTimeStart: uint32(beginTime.Unix()),
						EffectTimeEnd:   uint32(endTime.Unix()),
					},
					{
						ID:              2,
						ChannelId:       2,
						PermType:        1,
						EffectTimeStart: uint32(beginTime.Unix()),
						EffectTimeEnd:   uint32(endTime.Unix()),
					},
				}, nil)
				mockCache.EXPECT().UpdateChannelPKPerm(gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mgr := testMgr
			if tt.initFunc != nil {
				tt.initFunc()
			}
			mgr.UpdateChannelPkPermCache()
		})
	}
}
