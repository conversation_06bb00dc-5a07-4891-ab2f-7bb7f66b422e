package main

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/core/service/grpc"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/protocol/services/demo/echo"

	pb "golang.52tt.com/protocol/services/knight-group-mission"

	"golang.52tt.com/services/knight-group/knight-group-mission/internal/conf"
	"golang.52tt.com/services/knight-group/knight-group-mission/internal/server"

	// use server startup
	startup "gitlab.ttyuyin.com/avengers/tyr/core/service/startup/suit/grpc/server"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {
	var (
		svr *server.Server
		cfg = &conf.StartConfig{}
		err error
	)

	// config file support yaml & json, default knight-group-mission.json/yaml
	if err := startup.NewServer("knight-group-mission", cfg).
		AddGrpcServer(grpc.NewBuildOption().
			WithInitializeFunc(func(ctx context.Context, s *grpc.Server) error {
				kafka.InitEventLinkSubWithGrpcSvr(s)
				if svr, err = server.NewServer(ctx, cfg); err != nil {
					return err
				}

				// register custom grpc server
				pb.RegisterKnightGroupMissionServer(s, svr)

				// grpcurl -plaintext -d '{"value":"hello"}' 127.0.0.1:80 demo.echo.EchoService.Echo
				// grpcurl -plaintext 127.0.0.1:80 grpc.health.v1.Health.Check
				echo.RegisterEchoServiceServer(s, svr)
				return nil
			}),
		).
		WithCloseFunc(func(ctx context.Context) {
			// do something when server terminating
			svr.ShutDown()
		}).
		Start(); err != nil {
		log.Errorf("server start fail, err: %v", err)
	}
}
