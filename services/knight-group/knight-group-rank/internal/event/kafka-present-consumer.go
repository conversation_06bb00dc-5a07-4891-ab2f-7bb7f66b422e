package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	event_link_wrap "golang.52tt.com/pkg/event-link-wrap"
	"golang.52tt.com/pkg/log"
	kfkPB "golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"time"
)

const (
	topicTypePresent = "present_event_v2"
)

type PresentSubscriber struct {
	KafkaSub *event_link_wrap.SEventLinkAsyncSub
	handler  EventHandler
}

func NewPresentSubscriberSubscriber(clientId, groupId string, topics, brokers []string, handler EventHandler) (*PresentSubscriber, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	sub := &PresentSubscriber{
		handler: handler,
	}
	kafkaSub, err := event_link_wrap.NewEventLinkAsyncSub(brokers, topics, groupId, clientId,
		32, sub.handlerEvent, event_link_wrap.WithMaxRetryTimes(5))
	if err != nil {
		log.ErrorWithCtx(ctx, "NewPresentSubscriberSubscriber Failed to create kafka-subscriber kfk conf:(%v, %v, %v, %v) err:%v ", brokers, topics, groupId, clientId, err)
		return nil, err
	}
	sub.KafkaSub = kafkaSub
	sub.KafkaSub.Start()
	log.InfoWithCtx(ctx, "NewPresentSubscriberSubscriber success... kfk conf:(%v, %v, %v, %v)", brokers, topics, groupId, clientId)
	return sub, nil
}

func (s *PresentSubscriber) Close() {
	s.KafkaSub.Stop()
}

func (s *PresentSubscriber) handlerEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	if msg.Topic == topicTypePresent {
		return s.handlerPresentEvent(msg)
	}
	return nil, false
}

func (s *PresentSubscriber) handlerPresentEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	presentEvent := &kfkPB.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to proto.Unmarshal %+v", err)
		return err, false
	}
	if presentEvent.GetPriceType() != 2 {
		return nil, false
	}

	_ = s.handler.HandlerPresentEvent(ctx, presentEvent)
	log.InfoWithCtx(ctx, "handlerPresentEvent success: %+v", presentEvent)
	return nil, false
}
