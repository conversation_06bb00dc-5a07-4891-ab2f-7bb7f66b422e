package world

import (
	"context"

	"github.com/jmoiron/sqlx"
	"github.com/vingarcia/ksql"
	"github.com/vingarcia/ksql/adapters/kmysql"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/rev-mudgame/internal/player"
)

type WolrdMgr struct {
	ksqlDb   ksql.Provider
	townInfo *townInfoMgr
	olInfo   *userOnlineMgr
}

func initMysql(cfg *config.MysqlConfig) (db *sqlx.DB, err error) {
	db, err = sqlx.Connect("mysql", cfg.ConnectionString())
	if err != nil {
		log.Errorf("initMysql failed to connect backpack mysql %v", err)
		return nil, err
	}
	db.SetMaxOpenConns(cfg.MaxOpenConns)
	db.SetMaxIdleConns(cfg.MaxIdleConns)
	log.Infof("connect mysql %s", cfg.ConnectionString())
	return db, nil
}

func NewWorldMgr(ac *config.MysqlConfig) *WolrdMgr {
	db, err := initMysql(ac)
	if err != nil {
		log.Errorf("failed to connect backpack mysql %v", err)
		return nil
	}
	ksqld, _ := kmysql.NewFromSQLDB(db.DB)
	st := NewWorldTownMgr(ksqld)
	ol := NewUserOnlineMgr()
	return &WolrdMgr{
		ksqlDb:   ksqld,
		townInfo: st,
		olInfo:   ol,
	}
}

func (m *WolrdMgr) UserJoin(user *player.Player, townId uint32, locationName string) bool {

	// 检查 town 和 location 是否存在

	log.Infof("user %d join world", user.ID)

	olRes, olLocation := m.olInfo.UserOnline(user, townId, locationName)
	if olRes == UserOnlineResult_Success || olRes == UserOnlineResult_AlreadyExist {
		if olLocation != locationName {
			log.Errorf("user %d town %d location %s world, already join other location %d",
				user.ID, townId, locationName, olLocation)
			return false
		}

	} else {
		log.Errorf("user %d town %d location %s join world failed, res %d",
			user.ID, townId, locationName, olRes)
		return false
	}

	return true
}

func (m *WolrdMgr) UserLeave(uid uint32, townId uint32) {
	log.Infof("user %d leave world", uid)

	res := m.olInfo.UserOffline(uid, townId)
	if res != UserOnlineResult_Success {
		log.Errorf("user %d leave world failed, res %d", uid, res)
	}
	return
}

func (m *WolrdMgr) UserMoving(user *player.Player, townId uint32, locationName string) {

	res := m.olInfo.UserMoving(user, townId, locationName)
	if res != UserOnlineResult_Success {
		log.Errorf("user %d town %d location %s world Moving failed, res %d",
			user.ID, townId, locationName, res)
	}
	return
}

func (m *WolrdMgr) GetUser(uid, townId uint32) (user *player.Player, locationName string) {

	user, locationName = m.olInfo.GetUser(uid, townId)
	return user, locationName
}
func (m *WolrdMgr) GetRandomUser(townId uint32) (user *player.Player, locationName string) {

	user, locationName = m.olInfo.GetRandomUser(townId)
	return user, locationName
}

func (m *WolrdMgr) GetTownByCID(ctx context.Context, cid uint32) *Town {
	townV := m.townInfo.GetTownByCID(ctx, cid)
	return townV
}

func (m *WolrdMgr) GetTown(ctx context.Context, townID uint32) *Town {
	townV := m.townInfo.GetTown(ctx, townID)
	return townV
}
func (m *WolrdMgr) GetRandomTown(ctx context.Context) *Town {
	townV := m.townInfo.GetRandomTown(ctx)
	return townV
}

func (m *WolrdMgr) AddConfigurationTown(ctx context.Context, cid uint32, name, desc string) error {
	err := m.townInfo.AddConfigurationTown(ctx, cid, name, desc)
	return err
}
