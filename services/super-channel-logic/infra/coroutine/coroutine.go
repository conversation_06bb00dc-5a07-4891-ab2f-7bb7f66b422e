package coroutine

import (
	"math/rand"
	"sync"
	"time"
)

var (
	closing chan bool

	sw sync.WaitGroup
)

func init() {
	// 用于多实例随机开始时间
	rand.Seed(time.Now().UnixNano())

	closing = make(chan bool)
}

func Exec(f func(closing chan bool)) {
	sw.Add(1)

	go func() {
		f(closing)

		sw.Done()
	}()
}

func FixIntervalExec(f func(), interval time.Duration) {
	sw.Add(1)

	go func() {
		for {
			select {
			case <-closing:
				sw.Done()
				return
			case <-time.After(interval):
				f()
			}
		}
	}()
}

func FixIntervalExecWithDelay(f func(), interval time.Duration, delay time.Duration) {
	if 0 == delay {
		FixIntervalExec(f, interval)
		return
	}

	sw.Add(1)

	go func() {

		select {
		case <-closing:
			sw.Done()
			return
		case <-time.After(delay):
		}

		for {
			select {
			case <-closing:
				sw.Done()
				return
			case <-time.After(interval):
				f()
			}
		}
	}()
}

func DynamicIntervalExec(f func() time.Duration, interval time.Duration) {
	sw.Add(1)

	go func() {
		for {
			select {
			case <-closing:
				sw.Done()
				return
			case <-time.After(interval):
				interval = f()
			}
		}
	}()
}

func StrictIntervalExec(f func() time.Duration, interval time.Duration) {
	sw.Add(1)

	go func() {
		for {
			select {
			case <-closing:
				sw.Done()
				return
			case <-time.After(interval):
				start := time.Now()
				_ = f()
				takes := time.Since(start)

				if takes > interval {
					interval = 0
				} else {
					interval -= takes
				}
			}
		}
	}()
}

func StopAll() {
	close(closing)
	sw.Wait()
}
