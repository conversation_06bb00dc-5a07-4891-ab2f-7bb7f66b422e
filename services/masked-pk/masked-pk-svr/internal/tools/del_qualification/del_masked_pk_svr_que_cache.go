package main

import (
	"context"
	"fmt"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
)


func main() () {
	rdb, err := redisConnect.NewClient(context.Background(), &redisConnect.RedisConfig{
		Host: "**********",
		Port: 6379,
	})
	if err != nil {
		fmt.Println("ping redis failed err:", err)
		return
	}


	ctx := context.Background()
	fmt.Println(  "ClearAll start")
	cursor := uint64(0)
	num := 0
	for {
		keys, c, err := rdb.Scan(ctx, cursor, "masked_game_channel_*", 1000).Result()
		if err != nil {
			fmt.Println( "ClearAll cursor %d, err:%v", cursor, err)
			break
		}

		rdb.Del(ctx, keys...)
		fmt.Printf("Del:%v c:%d, cursor:%d\n", keys, c, cursor)
		num += len(keys)
		cursor = c
		if c == 0 {
			break
		}
	}

}