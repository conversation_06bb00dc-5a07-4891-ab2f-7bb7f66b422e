package server

import (
	"context"
	"errors"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/yuyingold"
	"golang.52tt.com/services/yuyin-gold/common"
	"time"
)

// 公会日流水收益对比
func (s *YuyinGoldServer) GuildConsumeDayQoq(ctx context.Context, in *pb.GuildConsumeDayQoqReq) (out *pb.GuildConsumeDayQoqRsp, err error) {
	log.Infof("GuildConsumeDayQoq in:%+v", in)
	out = &pb.GuildConsumeDayQoqRsp{}
	guildId := in.GetGuildId()
	queryDay := in.GetDay()
	nowTime := time.Now()
	if nowTime.Day() == 1 {
		out.CompareStatTime = time.Date(nowTime.Year(), nowTime.Month()-1, 1, 0, 0, 0, 0, time.Local).Unix()
		out.StatTime = time.Date(nowTime.Year(), nowTime.Month(), 0, 0, 0, 0, 0, time.Local).Unix()
	} else {
		out.CompareStatTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
		out.StatTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day()-1, 0, 0, 0, 0, time.Local).Unix()
	}
	beginTime := time.Unix(queryDay, 0)
	beginTime = time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day(), 0, 0, 0, 0, time.Local)
	compareTime := time.Date(beginTime.Year(), beginTime.Month()-1, beginTime.Day(), 0, 0, 0, 0, time.Local).Unix()
	realCompareTime := time.Unix(compareTime, 0)
	if realCompareTime.Month() != beginTime.Month()-1 && (realCompareTime.Month() != 12 && beginTime.Month() != 1) {
		return out, nil
	}

	dayInfo, err := s.MysqlStore.GetDayIncome(ctx, guildId, beginTime.Unix(), beginTime.Unix()+86400)
	if err == nil {
		if common.TimeValid(beginTime.Unix()) {
			out.Consume = dayInfo.TotalFee / 20
		} else {
			out.Consume = dayInfo.TotalIncome
		}
	}
	compareDayInfo, err := s.MysqlStore.GetDayIncome(ctx, guildId, compareTime, compareTime+86400)
	if err == nil {
		if common.TimeValid(compareTime) {
			out.CompareConsume = compareDayInfo.TotalFee / 20
		} else {
			out.CompareConsume = compareDayInfo.TotalIncome
		}
	}
	if out.CompareConsume != 0 && out.Consume != 0 {
		out.Qoq = float32(FloatRound(float64(out.Consume-out.CompareConsume)/float64(out.CompareConsume), 4))
	}
	log.Infof("GuildConsumeDayQoq in:%+v,out:%+v", in, out)
	return out, nil
}

// 今日收益
func (s *YuyinGoldServer) GetGuildTodayIncomeInfo(ctx context.Context, in *pb.GuildTodayIncomeReq) (out *pb.GuildTodayIncomeRsp, err error) {
	log.Infof("GetGuildTodayIncomeInfo in:%+v", in)
	out = &pb.GuildTodayIncomeRsp{}
	nowTime := time.Now()
	beginTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local).Unix()
	endTime := nowTime.Unix()
	infos, err := s.MysqlStore.GetGuildAnchorSummaryFromDetail(ctx, in.GetGuildId(), pb.ReqSettleStatus_ReqSettleStatusALL, beginTime, endTime, 0, 500)
	if err != nil {
		return
	}
	for _, info := range infos {
		roomInfo := &pb.StatTodayRoomIncomeInfo{
			Fees:     info.Fee,
			Members:  info.PaidUidCnt,
			AnchorId: info.AnchorId,
		}
		if common.TimeValid(beginTime) {
			roomInfo.Incomes = info.Fee / 20
		} else {
			roomInfo.Incomes = info.Income
		}
		out.StatRoomInfo = append(out.StatRoomInfo, roomInfo)
	}
	log.Infof("GetGuildTodayIncomeInfo in:%+v,out:%+v", in, out)
	// fmt.Printf("GetGuildTodayIncomeInfo in:%+v,out:%+v\n", in, out)
	return out, nil
}

func (s *YuyinGoldServer) GetGuildsBaseIncomeInfo(ctx context.Context, in *pb.GetGuildsBaseIncomeInfoReq) (out *pb.GetGuildsBaseIncomeInfoRsp, err error) {
	log.Infof("GetGuildsBaseIncomeInfo in:%+v", in)
	out = &pb.GetGuildsBaseIncomeInfoRsp{}
	nowTime := time.Now()
	var beginTime, endTime int64
	if in.BeginTime > 0 && in.EndTime > 0 {
		beginTime, endTime = int64(in.BeginTime), int64(in.EndTime)
	} else if in.Unit == pb.TimeFilterUnit_BY_DAY {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local).Unix()
		endTime = nowTime.Unix()
	} else if in.Unit == pb.TimeFilterUnit_BY_MONTH {
		beginTime = time.Date(nowTime.Year(), nowTime.Month(), 1, 0, 0, 0, 0, time.Local).Unix()
		endTime = nowTime.Unix()
	} else {
		err = errors.New("type err")
		log.ErrorWithCtx(ctx, "GetGuildsBaseIncomeInfo args.type error. in %v err %v", in, err)
		return out, err
	}

	records, err := s.MysqlStore.GetGuildsAnchorStat(ctx, in.Unit, in.GetGuildIds(), beginTime, endTime, in.Offset, in.Limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGuildsBaseIncomeInfo GetGuildsAnchorStat err:%v", err)
		return out, err
	}
	for _, r := range records {
		out.Anchors = append(out.Anchors, &pb.BaseAnchorInfo{
			Date:     uint32(r.Date),
			Fee:      r.Fee,
			Income:   r.Fee / 20,
			AnchorId: r.AnchorID,
			GuildId:  r.GuildID,
		})
	}

	log.Infof("GetGuildsBaseIncomeInfo in:%+v,out:%+v", in, out)
	return out, nil
}

// 本月收益
func (s *YuyinGoldServer) GetGuildMonthTrendInfo(ctx context.Context, in *pb.GuildMonthIncomeReq) (out *pb.GuildMonthIncomeRsp, err error) {
	log.Infof("GetGuildMonthTrendInfo in:%+v", in)
	out = &pb.GuildMonthIncomeRsp{}

	infos, err := s.MysqlStore.GetMonthTrend(ctx, in.GetGuildId(), in.GetMonth())
	if err != nil {
		return out, err
	}

	for _, info := range infos {
		getStatTime := time.Unix(info.StatTime/1000, 0)
		statTrendInfo := &pb.DayTrendMoreInfo{
			Fee:     info.Fee,
			Members: info.PaidUidCnt,
			Day:     int64(getStatTime.Day()),
		}
		if common.TimeValid(in.GetMonth()) {
			statTrendInfo.Income = info.Fee / 20
		} else {
			statTrendInfo.Income = info.Income
		}
		out.StatTrendInfo = append(out.StatTrendInfo, statTrendInfo)
	}
	log.Infof("GetGuildMonthTrendInfo in:%+v,out:%+v", in, out)
	return out, nil
}
