package server

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/datacenter"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channellivelogic "golang.52tt.com/protocol/app/channel-live-logic"
	"golang.52tt.com/protocol/app/sync"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	bpb "golang.52tt.com/protocol/services/backpacksender"
	"golang.52tt.com/protocol/services/channellivefans"
	"golang.52tt.com/services/channel-live-mission/conf"
	"golang.52tt.com/services/notify"
	butils "golang.52tt.com/services/risk-control/backpack-sender/utils"
	"time"
)

var (
/*mapUserMissionConf map[uint32]*conf.MissionConf
  mapFansMissionConf map[uint32]*conf.MissionConf
  actorMissionConf   *conf.ActorMissionConf*/
)

const (
	/*** 用户任务 100~199 ***/
	UMissionWatchThreeLiveChannel = conf.UMissionWatchThreeLiveChannel // 浏览三个不同的直播间
	UMissionWatchActorThreeMin    = conf.UMissionWatchActorThreeMin    // 观看单个主播满3分钟
	UMissionWatchThreeDays        = conf.UMissionWatchThreeDays        // 单个主播看满3分钟/天，持续3天
	UMissionFollowActor           = conf.UMissionFollowActor           // 关注主播
	UMissionAddFanClub            = conf.UMissionAddFanClub            // 加入粉丝团
	UMissionWatchActorTwentyMin   = conf.UMissionWatchActorTwentyMin   // 观看单场直播满20分钟
	UMissionSendAnyGift           = conf.UMissionSendAnyGift           // 向主播送任意T豆礼物（赠送入团礼物除外）
	UMissionSendBigGift           = conf.UMissionSendBigGift           // 向主播单笔送1万T豆（或以上）礼物

	/*** 粉丝任务 200~299 ***/
	FMissionWatchLive           = conf.FMissionWatchLive           // 观看直播
	FMissionSendGift            = conf.FMissionSendGift            // 赠送礼物
	FMissionShareLiveChannel    = conf.FMissionShareLiveChannel    // 每日分享直播间
	FMissionWatchActorSevenDays = conf.FMissionWatchActorSevenDays // 连续观看此主播：每日5分钟，连续7天

)

type UMissionAwardInfo struct {
	ImMsg string
	//pushMsg         string
	ArgCnt          uint32
	ProgressDescFmt string
}

type FMissionAwardInfo struct {
	PushMsg string
}

/*
var mapUserMissionMsgInfo = map[uint32]UMissionAwardInfo{
	UMissionWatchThreeLiveChannel: {
		"你已完成语音直播房[观看3个直播间]任务，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已浏览3个直播间，奖励放在[礼物架-背包]啦",
		1, "再看%d个直播间"},

	UMissionWatchActorThreeMin: {
		"你已完成语音直播房[观看3分钟]任务，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已观看3分钟，奖励放在[礼物架-背包]啦",
		1, "再看%d分钟"},

	UMissionWatchActorTwentyMin: {
		"你已完成语音直播房[观看20分钟]任务，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已观看20分钟，奖励放在[礼物架-背包]啦",
		1, "再看%d分钟"},

	UMissionWatchThreeDays: {
		"你已完成语音直播房[持续观看3天]任务，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已观看3天，奖励放在[礼物架-背包]啦",
		1, "再看%d天直播"},

	UMissionFollowActor: {
		"你已成功关注1名主播，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已关注主播，奖励放在[礼物架-背包]啦",
		2, "关注主播 %d/%d"},

	UMissionAddFanClub: {
		"你已成功加入1个粉丝团，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已加入粉丝团，奖励放在[礼物架-背包]啦",
		2, "加入粉丝团 %d/%d"},

	UMissionSendAnyGift: {
		"你已完成语音直播房[送主播任意T豆]任务，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已赠送T豆礼物，奖励放在[礼物架-背包]啦",
		0, "送任意T豆礼物"},

	UMissionSendBigGift: {
		"你已完成语音直播房[单笔送主播1万T豆（或以上）礼物]任务，获得礼物 心意连连x%d，已发放到背包中，请注意查收~", "你已送单笔>=1万T豆礼物，奖励放在[礼物架-背包]啦",
		2, "单笔>=1万T豆礼物 %d/%d"},
}

var mapFansMissionAwardInfo = map[uint32]FMissionAwardInfo{
	FMissionWatchLive:           {"[粉丝团任务]观看5分钟，已+%d亲密值"},
	FMissionSendGift:            {"[粉丝团任务]赠送T豆礼物，已+%d亲密值"},
	FMissionShareLiveChannel:    {"[粉丝团任务]分享直播间，已+%d亲密值"},
	FMissionWatchActorSevenDays: {"[粉丝团任务]连续7天看主播，已+%d亲密值"},
}*/

func (s *ChannelLiveMissionServer) AwardUserMission(ctx context.Context, uid, channelId uint32, missionConf *conf.UserMissionConf) {
	missionId := missionConf.MissionId
	now := time.Now()
	orderId := fmt.Sprintf("CLUM-%d-%d-%d", uid, missionId, now.Unix())
	/*req := &backpack.GiveUserPackageReq{
		Uid:     uid,
		BgId:    MissionConf.AwardGiftId,
		Num:     MissionConf.AwardNum,
		Source:  uint32(backpack.PackageSourceType_PACKAGE_SOURCE_AWARD_YUYIN_LIVE_MISSION),
		OrderId: orderId,
	}

	_, serr := s.BackpackCli.GiveUserPackage(ctx, uid, req)
	if serr != nil {
		log.Errorf("AwardUserMission GiveUserPackage fail. uid:%d mission_id:%d err:%s", uid, missionId, serr.Error())
		return
	}*/

	backpackSenderConf := s.Sc.GetBackpackSenderConfig()
	orderId = fmt.Sprintf("%d_%s", backpackSenderConf.BusinessID, orderId)
	cipherText := butils.AESEncrypt([]byte(orderId), []byte(backpackSenderConf.SecretKey))

	// 接入风控发奖
	_, serr := s.BackpackSenderClient.SendBackpackWithRiskControl(ctx, &bpb.SendBackpackWithRiskControlReq{
		BusinessId:  backpackSenderConf.BusinessID,
		BackpackId:  missionConf.AwardGiftId,
		BackpackCnt: missionConf.AwardNum,
		ReceiveUid:  uid,
		ServerTime:  now.Unix(),
		OrderId:     orderId,
		Ciphertext:  cipherText,
	})
	if serr != nil {
		log.Errorf("AwardUserMission SendBackpackWithRiskControl fail. uid:%d mission_id:%d err:%v", uid, missionId, serr)
		return
	}

	log.Infof("AwardUserMission SendBackpackWithRiskControl success. uid:%d mission_id:%d giftId:%d num:%d orderId:%s", uid, missionId, missionConf.AwardGiftId, missionConf.AwardNum, orderId)

	// 推任务奖励弹窗
	//pushMsg := fmt.Sprintf("你已完成【%s】任务，奖励【涂鸦礼物】%d个，礼物可在【礼物架-我的背包】查看", confInfo.MissionName, awardInfo.Num)
	opt := &channellivelogic.MissionAwardOptMsg{
		Type:     uint32(channellivelogic.MissionAwardOptMsg_UserMission),
		Text:     missionConf.PushMsg,
		IconUrl:  missionConf.IconUrl,
		AwardNum: missionConf.AwardNum,
	}

	msg, err := proto.Marshal(opt)
	if err != nil {
		log.Errorf("AwardUserMission Marshal fail. uid:%d mission_id:%d err:%s", uid, missionId, err.Error())
	} else {
		err = s.PushMissionAwardMsg(ctx, uid, channelId, msg)
		if err != nil {
			log.Errorf("AwardUserMission PushMissionAwardMsg fail. uid:%d mission_id:%d err:%s", uid, missionId, err.Error())
		}
	}

	// 发tt助手消息
	err = s.SendIMMsg(ctx, uid, fmt.Sprintf(missionConf.ImMsg, missionConf.AwardNum))
	if err != nil {
		log.Errorf("AwardUserMission SendIMMsg fail. uid:%d mission_id:%d err:%s", uid, missionId, err.Error())
	}

	// 数据上报
	UserMissionDataCenterReport(ctx, uid, missionId, missionConf)
}

func (s *ChannelLiveMissionServer) AwardFansMission(ctx context.Context, uid, actorUid, incrLoveVal, channelId uint32, missionConf *conf.FansMissionConf) {
	if incrLoveVal == 0 {
		return
	}
	missionName := missionConf.MissionName
	missionId := missionConf.MissionId

	resp, serr := s.FansCli.AddFansLoveValue(ctx, uid, actorUid, incrLoveVal, channelId, missionId)
	if serr != nil {
		log.Errorf("AwardFansMission fail. uid:%d actorUid:%d mission_id:%d mission_name:%s err:%s", uid, actorUid, missionId, missionName, serr.Error())
		return
	}

	log.Infof("AwardFansMission done. uid:%d actorUid:%d mission_id:%d mission_name:%s incrLoveVal:%d newLoveVal:%d",
		uid, actorUid, missionId, missionName, incrLoveVal, resp.GetNewLoveValue())

	/*Sc, ok := protogrpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.Debugf("AwardFansMission ServiceInfoFromContext no service info context")
	}

	log.DebugfWithCtx(ctx, "AwardFansMission ServiceInfoFromContext %+v", Sc)*/

	// 推奖励公屏文案
	opt := &channellivelogic.MissionAwardOptMsg{
		Type: uint32(channellivelogic.MissionAwardOptMsg_FansMission),
		Text: fmt.Sprintf(missionConf.PushMsg, incrLoveVal),
	}

	msg, err := proto.Marshal(opt)
	if err != nil {
		log.Errorf("AwardFansMission Marshal fail. uid:%d mission_id:%d err:%s", uid, missionId, err.Error())
	} else {
		err = s.PushMissionAwardMsg(ctx, uid, channelId, msg)
		if err != nil {
			log.Errorf("AwardFansMission PushMissionAwardMsg fail. uid:%d mission_id:%d err:%s", uid, missionId, err.Error())
		}
	}

	// 数据上报
	FansMissionDataCenterReport(ctx, uid, actorUid, missionId, incrLoveVal, missionName, resp)
}

// 发送TT助手消息
func (s *ChannelLiveMissionServer) SendIMMsg(ctx context.Context, uid uint32, content string) error {
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextNormal: &apiPB.ImTextNormal{
				Content: content,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: "all",
	}

	err := s.ApiCli.SendImMsg(ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.Errorf("SendImMsg fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, sync.SyncReq_IM_MSG)

	log.Debugf("SendIMMsg done. uid:%d, content:%s, Msg:%+v", uid, content, msg)
	return nil
}

func (s *ChannelLiveMissionServer) SendIMMsgWithJumpUrl(uid uint32, content, highlight, jumpUrl, appName, appPlatform string) error {
	msg := &apiPB.ImMsg{
		ImType: &apiPB.ImType{
			SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
			ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
			ContentType:  uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL),
		},
		FromUid: uint32(10000),
		ToIdList: []uint32{
			uid,
		},
		ImContent: &apiPB.ImContent{
			TextHlUrl: &apiPB.ImTextWithHighlightUrl{
				Content:    content,
				Hightlight: highlight,
				Url:        jumpUrl,
			},
		},
		Platform:    apiPB.Platform_UNSPECIFIED,
		AppPlatform: appPlatform,
		AppName:     appName,
	}

	err := s.ApiCli.SendImMsg(context.Background(), uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.Errorf("SendIMMsgWithJumpUrl fail. uid:%d err: %s", uid, err.Error())
		return err
	}

	notify.NotifySync(uid, sync.SyncReq_IM_MSG)

	log.Debugf("SendIMMsgWithJumpUrl done. uid:%d, content:%s, highlight:%s, jumpUrl:%s, Msg:%+v", uid, content, highlight, jumpUrl, msg)
	return nil
}

func UserMissionDataCenterReport(ctx context.Context, uid, missionId uint32, confInfo *conf.UserMissionConf) {
	datacenter.StdReportKV(ctx, "************", map[string]interface{}{
		"uid":        uid,
		"taskId":     missionId,
		"taskName":   confInfo.MissionName,
		"itemId":     confInfo.AwardGiftId,
		"itemCount":  confInfo.AwardNum,
		"createTime": time.Now().Format("2006-01-02 15:04:05"),
	})
}

func FansMissionDataCenterReport(ctx context.Context, uid, actorUid, missionId, incrLoveVal uint32, missionName string, resp *channellivefans.AddFansLoveValueResp) {
	datacenter.StdReportKV(ctx, "************", map[string]interface{}{
		"uid":           uid,
		"anchorId":      actorUid,
		"taskId":        missionId,
		"taskName":      missionName,
		"intimacy":      incrLoveVal,
		"totalIntimacy": resp.GetNewLoveValue(),
		"fansTeamId":    resp.GetGroupId(),
		"level":         resp.GetFansLevel(),
		"createTime":    time.Now().Format("2006-01-02 15:04:05"),
	})
}
