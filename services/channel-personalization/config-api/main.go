package main

import (
	"fmt"
	"os"

	"github.com/astaxie/beego"
	"github.com/urfave/cli"
	"golang.52tt.com/pkg/log"

	"golang.52tt.com/pkg/versioning/svn" // code revision

	_ "golang.52tt.com/services/channel-personalization/config-api/routers"
)

func main() {

	app := cli.NewApp()
	app.Version = svn.CodeRevision
	app.Flags = []cli.Flag{
		cli.StringFlag{
			Name:  "config",
			Value: "conf/app.conf",
			Usage: "config path",
		},
	}

	cli.VersionPrinter = func(c *cli.Context) {
		fmt.Fprintf(os.Stdout, "%s\n%s\n", c.App.Name, c.App.Version)
	}

	app.Action = func(c *cli.Context) error {
		configPath := c.String("config")

		err := beego.LoadAppConfig("ini", configPath)
		if err != nil {
			log.Fatalln("Failed to LoadAppConfig:", err)
			return err
		}

		beego.Run()

		return nil
	}

	_ = app.Run(os.Args)

}
