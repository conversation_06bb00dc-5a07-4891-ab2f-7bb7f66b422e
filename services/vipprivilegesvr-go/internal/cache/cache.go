package cache

import (
	"context"
	"errors"
	userpresent "golang.52tt.com/protocol/services/userpresent-go"
	"golang.52tt.com/services/vipprivilegesvr-go/internal/cache/localcache"
	"strconv"

	"github.com/google/wire"
	redisConnect "gitlab.ttyuyin.com/avengers/tyr/core/middleware/redis/connect"
	"gitlab.ttyuyin.com/tt-infra/tyr/core/middleware/redis"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/services/vipprivilegesvr-go/internal/conf"
)

var ProviderSetForCache = wire.NewSet(NewCache)

const KefuKey = "KEFU_KEY"

type VipPrivilegeCache struct {
	redisCli      redis.Cmdable
	presentCache  localcache.IPresentLocalCache
	userListCache localcache.IUserListLocalCache
}

func NewCache(ctx context.Context, cfg *conf.StartConfig) (ICache, func(), error) {
	client, err := redisConnect.NewClient(ctx, cfg.RedisConfig)
	if err != nil {
		return nil, func() {}, err
	}

	c := &VipPrivilegeCache{
		redisCli: client,
	}
	return c, func() {
		client.Close()
	}, nil
}

func (c *VipPrivilegeCache) GetRedisCli() redis.Cmdable {
	return c.redisCli
}

func (c *VipPrivilegeCache) InitPresentCache(ctx context.Context, f func(ctx context.Context) ([]*userpresent.StPresentItemConfig, error)) error {
	presentCache, err := localcache.NewPresentCache(f)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitPresentCache err:%v", err)
		return err
	}
	c.presentCache = presentCache
	return nil
}

func (c *VipPrivilegeCache) InitUserListCache(ctx context.Context, f func(ctx context.Context) (b []uint32, w []uint32, err error)) error {
	userListCache, err := localcache.NewUserListLocalCache(f)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitUserListCache err:%v", err)
		return err
	}
	c.userListCache = userListCache
	return nil
}

func (c *VipPrivilegeCache) AddVipKefu(ctx context.Context, uid uint32) error {
	err := c.redisCli.SAdd(ctx, KefuKey, uid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "Cache AddVipKefu uid:%d, err %v", uid, err)
	}
	return err
}

func (c *VipPrivilegeCache) AddVipKefuList(ctx context.Context, uidList []uint32) {
	uids := make([]interface{}, 0, len(uidList))
	for _, v := range uidList {
		uids = append(uids, v)
	}
	err := c.redisCli.SAdd(ctx, KefuKey, uids...).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "Cache AddVipKefuList uids:%v, err %v", uidList, err)
	}
	log.DebugWithCtx(ctx, "uids:%v", uids)
}

func (c *VipPrivilegeCache) DeleteVipKefu(ctx context.Context, uid uint32) error {
	err := c.redisCli.SRem(ctx, KefuKey, uid).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "Cache DeleteVipKefu uid:%d, err %v", uid, err)
	}
	return err
}

func (c *VipPrivilegeCache) GetVipKefus(ctx context.Context) ([]uint32, error) {
	members, err := c.redisCli.SMembers(ctx, KefuKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, nil
		}
		log.ErrorWithCtx(ctx, "Cache GetVipKefus, err %v", err)
		return nil, err
	}
	uidList := make([]uint32, len(members))
	for i := 0; i < len(members); i++ {
		t, _ := strconv.Atoi(members[i])
		uidList[i] = uint32(t)
	}
	return uidList, nil
}

func (c *VipPrivilegeCache) GetPresentCfg(ctx context.Context, id uint32) *userpresent.StPresentItemConfig {
	return c.presentCache.GetPresent(ctx, id)
}

func (c *VipPrivilegeCache) GetUserListCache() localcache.IUserListLocalCache {
	return c.userListCache
}
