package main

import (
	"context"
	"golang.52tt.com/services/vipprivilegesvr-go/internal/conf"
	"golang.52tt.com/services/vipprivilegesvr-go/internal/mgr"
)

func main() {
	ctx := context.Background()
	uid := uint32(2412015)
	cnt := "测试给TT和马甲包推送消息123"
	sc := &conf.StartConfig{}
	vipConfig, err := conf.NewVipConfig(sc)
	if err != nil {
		panic(err)
	}
	vipPrivilegeMgr := mgr.NewVipPrivilegeMgr(vipConfig, nil, nil, nil)
	_ = vipPrivilegeMgr.SendTTAssistMsg(ctx, uid, cnt)
}
