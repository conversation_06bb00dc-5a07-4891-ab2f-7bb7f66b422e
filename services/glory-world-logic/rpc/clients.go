package rpc

import (
	"github.com/google/wire"
	glory_reward "golang.52tt.com/clients/glory-reward"
	"google.golang.org/grpc"
)

type Client struct {
}

var ProviderSetForDownStreamClient = wire.NewSet(
	NewGrpcOption,
	NewClient,
	glory_reward.NewClientOrg,
)

func NewClient() *Client {
	return &Client{}
}

func NewGrpcOption() []grpc.DialOption {
	return []grpc.DialOption{
		grpc.WithBlock(),
	}
}
