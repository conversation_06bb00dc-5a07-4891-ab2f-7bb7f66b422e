package main

import (
	"os"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"

	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	"golang.52tt.com/pkg/tracing/jaeger"
	pb "golang.52tt.com/protocol/services/logicsvr-go/gnobilitylogic"
	"golang.52tt.com/services/gnobility-logic/server"
	"golang.52tt.com/services/runtime"
)

func main() {
	flags := grpcEx.ParseServerFlags(os.Args)

	tracer := jaeger.Init("gnobility-logic")

	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.<PERSON>, os.<PERSON>dout, os.Stdout))

	var (
		svr *server.GnobilityLogic_
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		svr, err = server.NewGnobilityLogic_(ctx, sc.Configer, tracer)
		if err != nil {
			return err
		}
		pb.RegisterGnobilityLogicServer(s, svr)
		return nil
	}

	closer := func(ctx context.Context, s *grpc.Server) error {
		if svr != nil {
			svr.ShutDown()
		}
		return nil
	}

	unaryInt := grpc_middleware.ChainUnaryServer(
		runtime.LogicServerUnaryInterceptor(flags.LogRequests, flags.LogResponses),
		traceGRPC.TracedUnaryServerInterceptor(
			tracing.UsingTracer(tracer), tracing.LogPayloads(true),
		),
	)

	s := grpcEx.NewServer(
		flags,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(unaryInt)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("gnobility-logic.json", grpcEx.AdapterJSON),
	)
	s.Serve()
}
 
 
 
