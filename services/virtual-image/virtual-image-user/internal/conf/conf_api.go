package conf

import(
)

type IBusinessConfManager interface {
	Close() 
	GetAutoPlayDurationSec() uint32
	GetBindInviteExpireSec() uint32
	GetBindInviteMsgPic() string
	GetDisplayReminder() *DisplayReminder
	GetFirstUseReminder() string
	GetInviteCheckBeforeHours() int64
	GetRelationCfg(relationId string) (bool,*RelationCfg)
	GetUserBindCntCacheExpSec() uint32
	GetUserBindMaxCount() uint32
	Reload(file string) error
	Watch(file string) 
}


type IBusinessConf interface {
	CheckConf() error
	Parse(configFile string) (isChange bool,err error)
}

