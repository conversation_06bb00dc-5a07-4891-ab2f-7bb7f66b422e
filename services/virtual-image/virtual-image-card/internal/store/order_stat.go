package store

import (
	"context"
	"fmt"
	mysql2 "github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	"strings"
	"time"
)

type OrderStat struct {
	OrderId             string    `db:"order_id" json:"order_id"`                           // 订单ID
	Uid                 uint32    `db:"uid" json:"uid"`                                     // 用户ID
	PackageId           uint32    `db:"package_id" json:"package_id"`                       // 套餐ID
	PackageName         string    `db:"package_name" json:"package_name"`                   // 套餐名称
	PayChannel          string    `db:"pay_channel" json:"pay_channel"`                     // 支付渠道
	Price               float64   `db:"price" json:"price"`                                 // 价格
	Days                uint32    `db:"days" json:"days"`                                   // 天数
	DailyPrice          float64   `db:"daily_price" json:"daily_price"`                     // 日均价格
	PayTime             time.Time `db:"pay_time" json:"pay_time"`                           // 购买时间
	SettleStartTime     time.Time `db:"settle_start_time" json:"settle_start_time"`         // 结算开始时间
	SettleEndTime       time.Time `db:"settle_end_time" json:"settle_end_time"`             // 结算结束时间
	BeforeLeftDays      uint32    `db:"before_left_days" json:"before_left_days"`           // 上月剩余天数
	BeforeLeftPrice     float64   `db:"before_left_price" json:"before_left_price"`         // 上月剩余金额
	BuyDays             uint32    `db:"buy_days" json:"buy_days"`                           // 本月购买天数
	BuyPrice            float64   `db:"buy_price" json:"buy_price"`                         // 本月购买金额
	RedemptionDays      uint32    `db:"redemption_days" json:"redemption_days"`             // 本月核销天数
	RedemptionPrice     float64   `db:"redemption_price" json:"redemption_price"`           // 本月核销金额
	RevokePrice         float64   `db:"revoke_price" json:"revoke_price"`                   // 本月撤销金额
	RevokeRollbackDays  uint32    `db:"revoke_rollback_days" json:"revoke_rollback_days"`   // 本月撤销回滚天数
	RevokeRollbackPrice float64   `db:"revoke_rollback_price" json:"revoke_rollback_price"` // 本月撤销回滚金额
	LeftDays            uint32    `db:"left_days" json:"left_days"`                         // 本月剩余天数
	LeftPrice           float64   `db:"left_price" json:"left_price"`                       // 本月剩余金额
	DiffCheck           float64   `db:"diff_check" json:"diff_check"`                       // 误差结果
	CreateTime          time.Time `db:"create_time" json:"create_time"`                     // 创建时间
	UpdateTime          time.Time `db:"update_time" json:"update_time"`                     // 更新时间
}

const createOrderStatTblSQL = `CREATE TABLE IF NOT EXISTS %s (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  order_id varchar(128) NOT NULL DEFAULT '' COMMENT '订单ID',
  uid int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户UID',
  package_id int(10) unsigned NOT NULL DEFAULT '0' COMMENT '套餐ID',
  package_name varchar(128) NOT NULL DEFAULT '' COMMENT '套餐名称',
  pay_channel varchar(128) NOT NULL DEFAULT '' COMMENT '支付渠道',
  price double NOT NULL DEFAULT '0' COMMENT '支付价格',
  days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单天数',
  daily_price double NOT NULL DEFAULT '0' COMMENT '日均价格',
  pay_time datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '购买时间',
  settle_start_time datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结算开始时间',
  settle_end_time datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结算结束时间',
  before_left_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上月剩余天数',
  before_left_price double NOT NULL DEFAULT '0' COMMENT '上月剩余金额',
  buy_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月购买天数',
  buy_price double NOT NULL DEFAULT '0' COMMENT '本月购买金额',
  redemption_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月核销天数',
  redemption_price double NOT NULL DEFAULT '0' COMMENT '本月核销金额',
  revoke_price double NOT NULL DEFAULT '0' COMMENT '本月撤销金额',
  revoke_rollback_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月撤销回滚天数',
  revoke_rollback_price double NOT NULL DEFAULT '0' COMMENT '本月撤销回滚金额',
  left_days int(10) unsigned NOT NULL DEFAULT '0' COMMENT '本月剩余天数',
  left_price double NOT NULL DEFAULT '0' COMMENT '本月剩余金额',
  diff_check double NOT NULL DEFAULT '0' COMMENT '误差结果',
  create_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uniq_order (order_id),
  KEY idx_uid (uid)
) engine=InnoDB default charset=utf8 COMMENT "无限换装卡订单统计表";`

const (
	queryOrderStatFields = `order_id, uid, package_id, package_name, pay_channel, price, days, daily_price, pay_time, settle_start_time, settle_end_time, before_left_days, before_left_price, buy_days, buy_price, redemption_days, redemption_price, revoke_price, revoke_rollback_days, revoke_rollback_price, left_days, left_price, diff_check`
)

func getOrderStatTblName(periodTime time.Time) string {
	return fmt.Sprintf("tbl_virtual_image_card_order_stat_%s", periodTime.Format("200601"))
}

func (s *Store) GetPrePeriodLeftOrders(ctx context.Context, currentPeriodTime time.Time) ([]*OrderStat, error) {
	prePeriodTime := time.Date(currentPeriodTime.Year(), currentPeriodTime.Month()-1, 1, 0, 0, 0, 0, time.Local)
	query := fmt.Sprintf("SELECT %s FROM %s WHERE left_days > 0", queryOrderStatFields, getOrderStatTblName(prePeriodTime))

	orders := make([]*OrderStat, 0)
	err := s.readonlyDb.SelectContext(ctx, &orders, query)
	if err != nil {
		if driverErr, ok := err.(*mysql2.MySQLError); ok {
			if driverErr.Number == 1146 { // 忽略表不存在错误
				err = nil
			}
		}
		if err != nil {
			return nil, err
		}
	}
	return orders, nil
}

func (s *Store) SetPeriodStatOrders(ctx context.Context, periodTime time.Time, orders []*OrderStat) error {
	tblName := getOrderStatTblName(periodTime)

	// 先完全清空表的数据
	query := fmt.Sprintf("DELETE FROM %s", tblName)
	_, err := s.db.ExecContext(ctx, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetPeriodStatOrders TRUNCATE TABLE err: %v", err)
		return err
	}

	// 分批添加数据
	batchSize := 100
	for start := 0; start < len(orders); start += batchSize {
		end := start + batchSize
		if end > len(orders) {
			end = len(orders)
		}
		batch := orders[start:end]

		sqlStr := fmt.Sprintf("INSERT INTO %s (order_id, uid, package_id, package_name, pay_channel, price, days, daily_price, pay_time, settle_start_time, settle_end_time, before_left_days, before_left_price, buy_days, buy_price, redemption_days, redemption_price, revoke_price, revoke_rollback_days, revoke_rollback_price, left_days, left_price, diff_check) VALUES ", tblName)
		var values []interface{}
		var placeholders []string
		for _, order := range batch {
			values = append(values, order.OrderId, order.Uid, order.PackageId, order.PackageName, order.PayChannel, order.Price, order.Days, order.DailyPrice, order.PayTime, order.SettleStartTime, order.SettleEndTime, order.BeforeLeftDays, order.BeforeLeftPrice, order.BuyDays, order.BuyPrice, order.RedemptionDays, order.RedemptionPrice, order.RevokePrice, order.RevokeRollbackDays, order.RevokeRollbackPrice, order.LeftDays, order.LeftPrice, order.DiffCheck)
			placeholders = append(placeholders, "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
		}
		sqlStr += strings.Join(placeholders, ", ")

		_, err = s.db.ExecContext(ctx, sqlStr, values...)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetPeriodStatOrders INSERT err: %v", err)
			return err
		}
	}

	return nil
}
