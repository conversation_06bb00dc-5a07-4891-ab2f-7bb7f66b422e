// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/gold/gold-settlement/mysql (interfaces: IGoldCommissionMysql)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	commission "golang.52tt.com/pkg/commission"
	gold_commission "golang.52tt.com/protocol/services/gold-commission"
	model "golang.52tt.com/services/gold/common/model"
	mysql "golang.52tt.com/services/gold/gold-settlement/mysql"
	gorm "gorm.io/gorm"
)

// MockIGoldCommissionMysql is a mock of IGoldCommissionMysql interface.
type MockIGoldCommissionMysql struct {
	ctrl     *gomock.Controller
	recorder *MockIGoldCommissionMysqlMockRecorder
}

// MockIGoldCommissionMysqlMockRecorder is the mock recorder for MockIGoldCommissionMysql.
type MockIGoldCommissionMysqlMockRecorder struct {
	mock *MockIGoldCommissionMysql
}

// NewMockIGoldCommissionMysql creates a new mock instance.
func NewMockIGoldCommissionMysql(ctrl *gomock.Controller) *MockIGoldCommissionMysql {
	mock := &MockIGoldCommissionMysql{ctrl: ctrl}
	mock.recorder = &MockIGoldCommissionMysqlMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIGoldCommissionMysql) EXPECT() *MockIGoldCommissionMysqlMockRecorder {
	return m.recorder
}

// Aggregation mocks base method.
func (m *MockIGoldCommissionMysql) Aggregation(arg0 context.Context, arg1 *model.GoldSettleInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Aggregation", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Aggregation indicates an expected call of Aggregation.
func (mr *MockIGoldCommissionMysqlMockRecorder) Aggregation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Aggregation", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).Aggregation), arg0, arg1)
}

// AggregationInteractGameExtraIncome mocks base method.
func (m *MockIGoldCommissionMysql) AggregationInteractGameExtraIncome(arg0 context.Context, arg1 *model.InteractGameExtraSettleInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AggregationInteractGameExtraIncome", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AggregationInteractGameExtraIncome indicates an expected call of AggregationInteractGameExtraIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) AggregationInteractGameExtraIncome(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AggregationInteractGameExtraIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).AggregationInteractGameExtraIncome), arg0, arg1)
}

// AmuseExtraSettlement mocks base method.
func (m *MockIGoldCommissionMysql) AmuseExtraSettlement(arg0 context.Context, arg1 *model.AmuseGoldExtraSettleInfo, arg2 func() error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AmuseExtraSettlement", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AmuseExtraSettlement indicates an expected call of AmuseExtraSettlement.
func (mr *MockIGoldCommissionMysqlMockRecorder) AmuseExtraSettlement(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AmuseExtraSettlement", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).AmuseExtraSettlement), arg0, arg1, arg2)
}

// GetAmuseAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetAmuseAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.GoldSettleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GoldSettleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseAggIncome indicates an expected call of GetAmuseAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetAmuseAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetAmuseAggIncome), arg0, arg1, arg2)
}

// GetAmuseExtraAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetAmuseExtraAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.AmuseExtraIncome, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseExtraAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.AmuseExtraIncome)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseExtraAggIncome indicates an expected call of GetAmuseExtraAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetAmuseExtraAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseExtraAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetAmuseExtraAggIncome), arg0, arg1, arg2)
}

// GetAmuseExtraSettleIncomeCntSum mocks base method.
func (m *MockIGoldCommissionMysql) GetAmuseExtraSettleIncomeCntSum(arg0 context.Context, arg1, arg2 time.Time) (uint64, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseExtraSettleIncomeCntSum", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAmuseExtraSettleIncomeCntSum indicates an expected call of GetAmuseExtraSettleIncomeCntSum.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetAmuseExtraSettleIncomeCntSum(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseExtraSettleIncomeCntSum", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetAmuseExtraSettleIncomeCntSum), arg0, arg1, arg2)
}

// GetAmuseExtraSettleIncomeOrderList mocks base method.
func (m *MockIGoldCommissionMysql) GetAmuseExtraSettleIncomeOrderList(arg0 context.Context, arg1, arg2 time.Time) ([]*model.AmuseExtraIncome, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseExtraSettleIncomeOrderList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.AmuseExtraIncome)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseExtraSettleIncomeOrderList indicates an expected call of GetAmuseExtraSettleIncomeOrderList.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetAmuseExtraSettleIncomeOrderList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseExtraSettleIncomeOrderList", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetAmuseExtraSettleIncomeOrderList), arg0, arg1, arg2)
}

// GetAmuseSpecialAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetAmuseSpecialAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.GoldSettleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAmuseSpecialAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GoldSettleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAmuseSpecialAggIncome indicates an expected call of GetAmuseSpecialAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetAmuseSpecialAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAmuseSpecialAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetAmuseSpecialAggIncome), arg0, arg1, arg2)
}

// GetDb mocks base method.
func (m *MockIGoldCommissionMysql) GetDb() *gorm.DB {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDb")
	ret0, _ := ret[0].(*gorm.DB)
	return ret0
}

// GetDb indicates an expected call of GetDb.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetDb() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDb", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetDb))
}

// GetDistinctGuildIds mocks base method.
func (m *MockIGoldCommissionMysql) GetDistinctGuildIds(arg0 context.Context, arg1, arg2 time.Time, arg3 gold_commission.ReqSettleStatus) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDistinctGuildIds", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDistinctGuildIds indicates an expected call of GetDistinctGuildIds.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetDistinctGuildIds(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDistinctGuildIds", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetDistinctGuildIds), arg0, arg1, arg2, arg3)
}

// GetDistinctGuildIdsFromDayStat mocks base method.
func (m *MockIGoldCommissionMysql) GetDistinctGuildIdsFromDayStat(arg0 context.Context, arg1, arg2 time.Time) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDistinctGuildIdsFromDayStat", arg0, arg1, arg2)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDistinctGuildIdsFromDayStat indicates an expected call of GetDistinctGuildIdsFromDayStat.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetDistinctGuildIdsFromDayStat(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDistinctGuildIdsFromDayStat", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetDistinctGuildIdsFromDayStat), arg0, arg1, arg2)
}

// GetESportAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetESportAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.GoldSettleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetESportAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GoldSettleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetESportAggIncome indicates an expected call of GetESportAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetESportAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetESportAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetESportAggIncome), arg0, arg1, arg2)
}

// GetGuildGoldTotal mocks base method.
func (m *MockIGoldCommissionMysql) GetGuildGoldTotal(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time, arg4 gold_commission.SourceType, arg5 gold_commission.ReqSettleStatus) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGuildGoldTotal", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGuildGoldTotal indicates an expected call of GetGuildGoldTotal.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetGuildGoldTotal(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGuildGoldTotal", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetGuildGoldTotal), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetInteractGameAggExtraIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetInteractGameAggExtraIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.InteractGameGoldExtraIncome, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractGameAggExtraIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.InteractGameGoldExtraIncome)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractGameAggExtraIncome indicates an expected call of GetInteractGameAggExtraIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetInteractGameAggExtraIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractGameAggExtraIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetInteractGameAggExtraIncome), arg0, arg1, arg2)
}

// GetInteractGameAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetInteractGameAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.GoldSettleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractGameAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GoldSettleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractGameAggIncome indicates an expected call of GetInteractGameAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetInteractGameAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractGameAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetInteractGameAggIncome), arg0, arg1, arg2)
}

// GetInteractGameAnchorFee mocks base method.
func (m *MockIGoldCommissionMysql) GetInteractGameAnchorFee(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time, arg4 gold_commission.SourceType) (map[uint32]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractGameAnchorFee", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(map[uint32]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractGameAnchorFee indicates an expected call of GetInteractGameAnchorFee.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetInteractGameAnchorFee(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractGameAnchorFee", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetInteractGameAnchorFee), arg0, arg1, arg2, arg3, arg4)
}

// GetInteractGameChannelFee mocks base method.
func (m *MockIGoldCommissionMysql) GetInteractGameChannelFee(arg0 context.Context, arg1 uint32, arg2, arg3 time.Time, arg4 gold_commission.SourceType) ([]*mysql.InteractGameChannelFee, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractGameChannelFee", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*mysql.InteractGameChannelFee)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractGameChannelFee indicates an expected call of GetInteractGameChannelFee.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetInteractGameChannelFee(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractGameChannelFee", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetInteractGameChannelFee), arg0, arg1, arg2, arg3, arg4)
}

// GetInteractGameExtraChannelIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetInteractGameExtraChannelIncome(arg0 context.Context, arg1 time.Time, arg2 uint32) ([]*model.InteractGameExtraChannelIncome, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInteractGameExtraChannelIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.InteractGameExtraChannelIncome)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInteractGameExtraChannelIncome indicates an expected call of GetInteractGameExtraChannelIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetInteractGameExtraChannelIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInteractGameExtraChannelIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetInteractGameExtraChannelIncome), arg0, arg1, arg2)
}

// GetSettleIncomeCntSum mocks base method.
func (m *MockIGoldCommissionMysql) GetSettleIncomeCntSum(arg0 context.Context, arg1, arg2 time.Time) (uint64, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettleIncomeCntSum", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetSettleIncomeCntSum indicates an expected call of GetSettleIncomeCntSum.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetSettleIncomeCntSum(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettleIncomeCntSum", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetSettleIncomeCntSum), arg0, arg1, arg2)
}

// GetSettleIncomeOrderList mocks base method.
func (m *MockIGoldCommissionMysql) GetSettleIncomeOrderList(arg0 context.Context, arg1, arg2 time.Time) ([]*model.GoldIncome, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettleIncomeOrderList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GoldIncome)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettleIncomeOrderList indicates an expected call of GetSettleIncomeOrderList.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetSettleIncomeOrderList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettleIncomeOrderList", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetSettleIncomeOrderList), arg0, arg1, arg2)
}

// GetTable mocks base method.
func (m *MockIGoldCommissionMysql) GetTable() model.Table {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTable")
	ret0, _ := ret[0].(model.Table)
	return ret0
}

// GetTable indicates an expected call of GetTable.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetTable() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTable", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetTable))
}

// GetYuyinAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetYuyinAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.GoldSettleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GoldSettleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinAggIncome indicates an expected call of GetYuyinAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetYuyinAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetYuyinAggIncome), arg0, arg1, arg2)
}

// GetYuyinExtraAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetYuyinExtraAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.YuyinGoldExtraIncome, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinExtraAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.YuyinGoldExtraIncome)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinExtraAggIncome indicates an expected call of GetYuyinExtraAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetYuyinExtraAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinExtraAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetYuyinExtraAggIncome), arg0, arg1, arg2)
}

// GetYuyinExtraSettleIncomeCntSum mocks base method.
func (m *MockIGoldCommissionMysql) GetYuyinExtraSettleIncomeCntSum(arg0 context.Context, arg1, arg2 time.Time) (uint64, uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinExtraSettleIncomeCntSum", arg0, arg1, arg2)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetYuyinExtraSettleIncomeCntSum indicates an expected call of GetYuyinExtraSettleIncomeCntSum.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetYuyinExtraSettleIncomeCntSum(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinExtraSettleIncomeCntSum", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetYuyinExtraSettleIncomeCntSum), arg0, arg1, arg2)
}

// GetYuyinExtraSettleIncomeOrderList mocks base method.
func (m *MockIGoldCommissionMysql) GetYuyinExtraSettleIncomeOrderList(arg0 context.Context, arg1, arg2 time.Time) ([]*model.YuyinGoldExtraIncome, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinExtraSettleIncomeOrderList", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.YuyinGoldExtraIncome)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinExtraSettleIncomeOrderList indicates an expected call of GetYuyinExtraSettleIncomeOrderList.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetYuyinExtraSettleIncomeOrderList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinExtraSettleIncomeOrderList", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetYuyinExtraSettleIncomeOrderList), arg0, arg1, arg2)
}

// GetYuyinSpecialAggIncome mocks base method.
func (m *MockIGoldCommissionMysql) GetYuyinSpecialAggIncome(arg0 context.Context, arg1 time.Time, arg2 gold_commission.ReqSettleStatus) ([]*model.GoldSettleInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetYuyinSpecialAggIncome", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*model.GoldSettleInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetYuyinSpecialAggIncome indicates an expected call of GetYuyinSpecialAggIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) GetYuyinSpecialAggIncome(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetYuyinSpecialAggIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).GetYuyinSpecialAggIncome), arg0, arg1, arg2)
}

// InteractGameExtraIncomeSettlement mocks base method.
func (m *MockIGoldCommissionMysql) InteractGameExtraIncomeSettlement(arg0 context.Context, arg1 *model.InteractGameExtraSettleInfo, arg2 func() error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InteractGameExtraIncomeSettlement", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// InteractGameExtraIncomeSettlement indicates an expected call of InteractGameExtraIncomeSettlement.
func (mr *MockIGoldCommissionMysqlMockRecorder) InteractGameExtraIncomeSettlement(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InteractGameExtraIncomeSettlement", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).InteractGameExtraIncomeSettlement), arg0, arg1, arg2)
}

// SetExtraSettleReconcileInfo mocks base method.
func (m *MockIGoldCommissionMysql) SetExtraSettleReconcileInfo(arg0 context.Context, arg1 uint32, arg2 time.Time, arg3 *commission.SettlementDataResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetExtraSettleReconcileInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetExtraSettleReconcileInfo indicates an expected call of SetExtraSettleReconcileInfo.
func (mr *MockIGoldCommissionMysqlMockRecorder) SetExtraSettleReconcileInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetExtraSettleReconcileInfo", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).SetExtraSettleReconcileInfo), arg0, arg1, arg2, arg3)
}

// SetInteractGameExtraChannelIncome mocks base method.
func (m *MockIGoldCommissionMysql) SetInteractGameExtraChannelIncome(arg0 context.Context, arg1 time.Time, arg2, arg3, arg4 uint32, arg5, arg6, arg7 uint64, arg8, arg9 float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetInteractGameExtraChannelIncome", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetInteractGameExtraChannelIncome indicates an expected call of SetInteractGameExtraChannelIncome.
func (mr *MockIGoldCommissionMysqlMockRecorder) SetInteractGameExtraChannelIncome(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetInteractGameExtraChannelIncome", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).SetInteractGameExtraChannelIncome), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
}

// SetSettleReconcileInfo mocks base method.
func (m *MockIGoldCommissionMysql) SetSettleReconcileInfo(arg0 context.Context, arg1 uint32, arg2 time.Time, arg3 *commission.SettlementDataResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSettleReconcileInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetSettleReconcileInfo indicates an expected call of SetSettleReconcileInfo.
func (mr *MockIGoldCommissionMysqlMockRecorder) SetSettleReconcileInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSettleReconcileInfo", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).SetSettleReconcileInfo), arg0, arg1, arg2, arg3)
}

// SetSpecialSettleReconcileInfo mocks base method.
func (m *MockIGoldCommissionMysql) SetSpecialSettleReconcileInfo(arg0 context.Context, arg1 uint32, arg2 time.Time, arg3 *commission.SettlementDataResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSpecialSettleReconcileInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetSpecialSettleReconcileInfo indicates an expected call of SetSpecialSettleReconcileInfo.
func (mr *MockIGoldCommissionMysqlMockRecorder) SetSpecialSettleReconcileInfo(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSpecialSettleReconcileInfo", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).SetSpecialSettleReconcileInfo), arg0, arg1, arg2, arg3)
}

// Settlement mocks base method.
func (m *MockIGoldCommissionMysql) Settlement(arg0 context.Context, arg1 *model.GoldSettleInfo, arg2 func() error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Settlement", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Settlement indicates an expected call of Settlement.
func (mr *MockIGoldCommissionMysqlMockRecorder) Settlement(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Settlement", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).Settlement), arg0, arg1, arg2)
}

// SpecialAggregation mocks base method.
func (m *MockIGoldCommissionMysql) SpecialAggregation(arg0 context.Context, arg1 *model.GoldSettleInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SpecialAggregation", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SpecialAggregation indicates an expected call of SpecialAggregation.
func (mr *MockIGoldCommissionMysqlMockRecorder) SpecialAggregation(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SpecialAggregation", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).SpecialAggregation), arg0, arg1)
}

// SpecialSettlement mocks base method.
func (m *MockIGoldCommissionMysql) SpecialSettlement(arg0 context.Context, arg1 *model.GoldSettleInfo, arg2 func() error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SpecialSettlement", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SpecialSettlement indicates an expected call of SpecialSettlement.
func (mr *MockIGoldCommissionMysqlMockRecorder) SpecialSettlement(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SpecialSettlement", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).SpecialSettlement), arg0, arg1, arg2)
}

// Transaction mocks base method.
func (m *MockIGoldCommissionMysql) Transaction(arg0 context.Context, arg1 func(*gorm.DB) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transaction indicates an expected call of Transaction.
func (mr *MockIGoldCommissionMysqlMockRecorder) Transaction(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).Transaction), arg0, arg1)
}

// YuyinExtraSettlement mocks base method.
func (m *MockIGoldCommissionMysql) YuyinExtraSettlement(arg0 context.Context, arg1 *model.YuyinGoldExtraSettleInfo, arg2 func() error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "YuyinExtraSettlement", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// YuyinExtraSettlement indicates an expected call of YuyinExtraSettlement.
func (mr *MockIGoldCommissionMysqlMockRecorder) YuyinExtraSettlement(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "YuyinExtraSettlement", reflect.TypeOf((*MockIGoldCommissionMysql)(nil).YuyinExtraSettlement), arg0, arg1, arg2)
}
