package cache

import (
	"context"
	"fmt"
	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"github.com/opentracing/opentracing-go"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channelstats"
	"golang.52tt.com/services/channel-stats/conf"
	"golang.52tt.com/services/channel-stats/util"
	"crypto/rand"
	"math/big"
	"strconv"
	"time"
)

type ChannelStatsCache struct {
	redisClient *redis.Client
	tracer      opentracing.Tracer
}

func channelHotValuePushList() string {
	return "channel_hot_value_push"
}

func channelPresentValueKey(channelId uint32) string {
	return fmt.Sprintf("channel_present_value_%v", channelId)
}

func channelPresentRecordKey(channelId uint32) string {
	return fmt.Sprintf("channel_present_record_%v", channelId)
}

func NewChannelStatsCache(r *redis.Client, tracer opentracing.Tracer) *ChannelStatsCache {
	return &ChannelStatsCache{redisClient: r, tracer: tracer}
}

func (cache *ChannelStatsCache) RecordPresent(ctx context.Context,orderId string, channelId, channelType, sendTs, value uint32) error {

	maxSec := time.Now().Unix()
	minSec := maxSec - conf.GetRecordSec()
	recordSec := conf.GetRecordSec()

	results, err := cache.redisClient.ZRangeByScore(channelPresentRecordKey(channelId), redis.ZRangeBy{
		Min: fmt.Sprintf("%v", minSec),
		Max: fmt.Sprintf("%v", maxSec+1),
	}).Result()

	if nil != err {
		log.ErrorWithCtx(ctx,"GetPresentTotalValue channelId:%v err:%v", channelId, err)
	}

	var totalVal int64 = 0
	for _, res := range results {
		hotValItem := &pb.HotValueItem{}
		err := proto.Unmarshal([]byte(res), hotValItem)
		if nil != err {
			continue
		}
		totalVal = totalVal + hotValItem.Value
	}

	//加时当前这次的
	totalVal = totalVal + int64(value)
	hotValItem := &pb.HotValueItem{
		OrderId: orderId,
		Value:   int64(value),
	}
	item, err := proto.Marshal(hotValItem)
	if nil != err {
		return err
	}

	pipe := cache.redisClient.Pipeline()
	err = pipe.ZAdd(channelPresentRecordKey(channelId), redis.Z{
		Score:  float64(sendTs),
		Member: item,
	}).Err()

	if nil != err {
		log.ErrorWithCtx(ctx,"RecordPresent ZAdd channelId:%v sendTs:%v value:%v err:%v", channelId, sendTs, value, err)
	}

	err = pipe.Set(channelPresentValueKey(channelId), totalVal, time.Second*time.Duration(recordSec)).Err()
	if nil != err {
		log.ErrorWithCtx(ctx,"GetPresentValue Set channelPresentValue fail channelId:%v err:%v", channelId, err)
	}

	//用于推送列表
	isPush := util.IsPushChannelType(channelType)
	if isPush {
		err = pipe.ZAdd(channelHotValuePushList(), redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: channelId,
		}).Err()

		if nil != err {
			log.ErrorWithCtx(ctx,"GetPresentValue ZAdd channelHotValuePushList fail channelId:%v err:%v", channelId, err)
		}
	}
	b := new(big.Int).SetInt64(int64(10))
	n, err := rand.Int(rand.Reader,b)
	if err == nil && n.Int64() == 8 {
		pipe.ZRemRangeByScore(channelPresentRecordKey(channelId), "0", fmt.Sprintf("%v", minSec-1))
	}

	pipe.Exec()

	return nil
}

func (cache *ChannelStatsCache) Lock(key string, expire int64) bool {
	result, err := cache.redisClient.SetNX(key, "lock", time.Duration(expire)*time.Second).Result()
	if nil != err {
		return false
	}
	return result
}

func pushTaskListKey() string {
	return "channel_stats_push_task_list"
}

func (cache *ChannelStatsCache) PushTaskList(channelIdList []uint32) {
	cache.redisClient.LPush(pushTaskListKey(), channelIdList)
}

func (cache *ChannelStatsCache) PopTask() (int64, error) {
	cid, err := cache.redisClient.RPop(pushTaskListKey()).Int64()
	return cid, err
}

// 重复推
func (cache *ChannelStatsCache) GetPushList(ctx context.Context) []uint32 {
	channelIdList := make([]uint32, 0)
	nowTs := time.Now().Unix()
	max := nowTs
	min := max - 180
	results, err := cache.redisClient.ZRangeByScore(channelHotValuePushList(), redis.ZRangeBy{
		Min:    fmt.Sprintf("%v", min),
		Max:    fmt.Sprintf("%v", max),
		Offset: 0,
		Count:  1024*2,
	}).Result()

	if nil != err {
		log.ErrorWithCtx(ctx,"GetPushList ZRangeByScore err:%v", err)
		return channelIdList
	}

	for _, res := range results {
		cid, err := strconv.ParseUint(res, 10, 32)
		if nil != err {
			continue
		}
		channelIdList = append(channelIdList, uint32(cid))
	}

	log.DebugfWithCtx(ctx,"GetPushList channelIdList:%v", channelIdList)

	cache.redisClient.ZRemRangeByScore(channelHotValuePushList(), "0", fmt.Sprintf("%v", min))

	return channelIdList
}

func (cache *ChannelStatsCache) GetPresentHotValue(ctx context.Context,channelId uint32) (int64, error) {
	val, err := cache.redisClient.Get(channelPresentValueKey(channelId)).Int64()
	if nil == err {
		return val, nil
	} else {
		if err != redis.Nil {
			log.ErrorWithCtx(ctx,"GetPresentHotValue get ChannelPresentValueKey channelId:%v err:%v", channelId, err)
		}
	}

	return val, nil
}

func (cache *ChannelStatsCache) BatchGetPresentHotValue(ctx context.Context,channelIdList []uint32) (map[uint32]int64,error) {
	valMap := make(map[uint32]int64)
	keys := make([]string, 0)
	for _, cid := range channelIdList {
		keys = append(keys, channelPresentValueKey(cid))
	}

	results, err := cache.redisClient.MGet(keys...).Result()
	if nil != err {
		log.ErrorWithCtx(ctx,"BatchGetPresentHotValue MGet err:%v", err)
		return valMap,err
	}

	for idx, res := range results {
		cid := channelIdList[idx]
		var val int64 = 0
		if nil != res {
			str, ok := res.(string)
			if ok {
				tval, err := strconv.ParseUint(str, 10, 32)
				if nil == err {
					val = int64(tval)
				}
			}
		}
		valMap[cid] = val
	}

	return valMap,nil
}
