package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
)

/*
CREATE TABLE IF NOT EXISTS fans_week_rank_settle_record_202312 (
  anchor_uid int(10) unsigned NOT NULL COMMENT '主播uid',
  fans_uid int(10) unsigned NOT NULL COMMENT '粉丝uid',
  week_begin int unsigned NOT NULL COMMENT '周一0点时间戳',
  rank int(10) unsigned NOT NULL COMMENT '排名',
  rank_val int(10) unsigned NOT NULL COMMENT '榜单值',
  create_time int(10) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (anchor_uid, fans_uid, week_begin),
  KEY idx_fans_uid (fans_uid),
  KEY idx_week_begin (week_begin)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '粉丝团周榜结算记录表';


CREATE TABLE `tbl_fans_week_rank_order_202312` (
  `order_id` varchar(128) NOT NULL COMMENT '订单号',
  `uid` int(10) unsigned NOT NULL COMMENT '送礼人uid',
  `to_uid` int(10) unsigned NOT NULL COMMENT '收礼人uid',
  `channel_id` int(10) unsigned NOT NULL COMMENT '房间id',
  `source_type` tinyint(3) unsigned NOT NULL COMMENT '来源',
  `status` tinyint(3) unsigned NOT NULL COMMENT '状态',
  `total_price` int(10) unsigned NOT NULL COMMENT '总价值',
  `outside_time` datetime NOT NULL COMMENT '外部系统时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_to_uid` (`to_uid`),
  KEY `idx_uid` (`uid`),
  KEY `idx_status` (`status`),
  KEY `idx_outside_time` (`outside_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='粉丝团周榜送礼流水表';

*/

type FansWeekRankSettleRecord struct {
	AnchorUid  uint32 `db:"anchor_uid"`
	FansUid    uint32 `db:"fans_uid"`
	WeekBegin  uint32 `db:"week_begin"`
	Rank       uint32 `db:"rank"`
	RankVal    uint32 `db:"rank_val"`
	CreateTime uint32 `db:"create_time"`
}

type FansWeekRankOrderLog struct {
	OrderId     string    `db:"order_id"`
	Uid         uint32    `db:"uid"`
	ToUid       uint32    `db:"to_uid"`
	ChannelId   uint32    `db:"channel_id"`
	SourceType  uint32    `db:"source_type"`
	Status      uint32    `db:"status"`
	TotalPrice  uint32    `db:"total_price"`
	OutsideTime time.Time `db:"outside_time"`
	UpdateTime  time.Time `db:"update_time"`
}

func (s *FansWeekRankSettleRecord) TableName() string {
	tm := time.Unix(int64(s.WeekBegin), 0)
	return fmt.Sprintf("fans_week_rank_settle_record_%d%02d", tm.Year(), tm.Month())
}

func (s *FansWeekRankOrderLog) TableName() string {
	return fmt.Sprintf("tbl_fans_week_rank_order_%d%02d", s.OutsideTime.Year(), s.OutsideTime.Month())
}

func (s *Store) RecordFansWeekRankOrderLog(info *FansWeekRankOrderLog) error {
	execSql := fmt.Sprintf("INSERT INTO %s(order_id,uid,to_uid,channel_id,source_type,status,total_price,outside_time)"+
		"VALUES(?,?,?,?,?,?,?,?)",
		info.TableName())
	_, err := s.db.Exec(execSql,
		info.OrderId,
		info.Uid,
		info.ToUid,
		info.ChannelId,
		info.SourceType,
		info.Status,
		info.TotalPrice,
		info.OutsideTime.Format("2006-01-02 15:04:05"))
	if err != nil {
		log.Errorf("RecordFansWeekRankOrderLog fail %v, info=%+v", err, info)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			_, err = s.db.Exec(fmt.Sprintf(CreateTblFanasWeekRankOrderSQL, info.TableName()))
			if err != nil {
				return err
			}
			_, err = s.db.Exec(execSql,
				info.OrderId,
				info.Uid,
				info.ToUid,
				info.ChannelId,
				info.SourceType,
				info.Status,
				info.TotalPrice,
				info.OutsideTime.Format("2006-01-02 15:04:05"))
		}
	}
	return err
}

func (s *Store) GettFansWeekRankOrderCount(ctx context.Context, beginTs, endTs, source uint32) (uint32, uint32, error) {

	tm := time.Unix(int64(beginTs), 0)
	tableName := (&FansWeekRankOrderLog{OutsideTime: tm}).TableName()

	query := fmt.Sprintf("select count(order_id) as order_count, sum(total_price) as total_price "+
		"from %s where outside_time>=FROM_UNIXTIME(%d) and outside_time < FROM_UNIXTIME(%d) ",
		tableName, beginTs, endTs)
	if source > 0 {
		query = fmt.Sprintf("%s and source_type = %d", query, source)
	}
	row := s.db.QueryRowxContext(ctx, query)
	if err := row.Err(); err != nil {
		log.Errorf("GettFansWeekRankOrderCount query:%s, err:%v", query, err)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			//return 0, 0, nil
			return 00, 0, nil
		}
		return 0, 0, err
	}

	var order, price sql.NullInt32
	if err := row.Scan(&order, &price); err != nil {
		log.Errorf("GettFansWeekRankOrderCount Scan query:%s, err:%v \n", query, err)
		return 0, 0, err
	}
	log.DebugWithCtx(ctx, "GettFansWeekRankOrderCount query=%s order=%d, price=%d", query, order.Int32, price.Int32)
	return uint32(order.Int32), uint32(price.Int32), nil
}

func (s *Store) GetFansWeekRankOrderList(ctx context.Context, beginTs, endTs, uid, toUid, source uint32, status int32) ([]*FansWeekRankOrderLog, error) {
	list := make([]*FansWeekRankOrderLog, 0)
	tm := time.Unix(int64(beginTs), 0)

	tableName := (&FansWeekRankOrderLog{OutsideTime: tm}).TableName()
	query := fmt.Sprintf("select order_id,uid,to_uid,channel_id,source_type,status,total_price,outside_time,update_time "+
		"from %s where outside_time>=FROM_UNIXTIME(%d) and outside_time < FROM_UNIXTIME(%d) ", tableName, beginTs, endTs)

	if uid > 0 {
		query = fmt.Sprintf("%s and uid = %d", query, uid)
	}
	if toUid > 0 {
		query = fmt.Sprintf("%s and to_uid = %d", query, toUid)
	}
	if source > 0 {
		query = fmt.Sprintf("%s and source_type = %d", query, source)
	}
	if status >= 0 {
		query = fmt.Sprintf("%s and status = %d", query, status)
	}

	err := s.db.SelectContext(ctx, &list, query)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetFansWeekRankOrderList fail %v ", err)
		return nil, err
	}
	log.DebugWithCtx(ctx, "GetFansWeekRankOrderList query=[%s]", query)
	return list, nil
}

func (s *Store) UpdateFansWeekRankOrderStatus(orderId string, ts, status uint32) error {
	tm := time.Unix(int64(ts), 0)
	tableName := (&FansWeekRankOrderLog{OutsideTime: tm}).TableName()
	execSql := fmt.Sprintf("update %s set status=%d where order_id='%s'", tableName, status, orderId)
	res, err := s.db.Exec(execSql)
	if err != nil {
		return err
	}
	RowsAffected, _ := res.RowsAffected()
	log.Infof("UpdateFansWeekRankOrderStatus orderId=%s, RowsAffected=%d", orderId, RowsAffected)
	return err
}

func (s *Store) GetFansWeekRankSettleRecord(ctx context.Context, anchorUid, weekBegin uint32) ([]*FansWeekRankSettleRecord, error) {
	list := make([]*FansWeekRankSettleRecord, 0)
	tblName := (&FansWeekRankSettleRecord{WeekBegin: weekBegin}).TableName()
	sqlStr := "select anchor_uid,fans_uid,week_begin,rank,rank_val,create_time from " +
		tblName + " where anchor_uid=? and week_begin=?"
	err := s.db.SelectContext(ctx, &list, sqlStr, anchorUid, weekBegin)
	if err != nil {
		return list, err
	}
	return list, nil
}

func (s *Store) AddFansWeekRankSettleRecord(ctx context.Context, info *FansWeekRankSettleRecord) error {
	sqlStr := "insert into " + info.TableName() +
		"(anchor_uid,fans_uid,week_begin,rank,rank_val,create_time) values(?,?,?,?,?,?)"
	_, err := s.db.ExecContext(ctx, sqlStr, info.AnchorUid, info.FansUid, info.WeekBegin, info.Rank, info.RankVal, time.Now().Unix())
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1146 {
			_, err = s.db.Exec(fmt.Sprintf(CreateTblFanasWeekRankSettleSQL, info.TableName()))
			if err != nil {
				return err
			}
			_, err = s.db.ExecContext(ctx, sqlStr, info.AnchorUid, info.FansUid, info.WeekBegin, info.Rank, info.RankVal, time.Now().Unix())
		}
	}
	return err
}

func (s *Store) DelFansWeekRankSettleRecord(ctx context.Context, anchorUid, weekBegin uint32) error {
	tblName := (&FansWeekRankSettleRecord{WeekBegin: weekBegin}).TableName()
	sqlStr := "delete from " + tblName + " where anchor_uid=? and week_begin=?"
	_, err := s.db.ExecContext(ctx, sqlStr, anchorUid, weekBegin)
	return err
}

func (s *Store) CreateFansWeekRankTable(ts time.Time) error {
	_, err := s.db.Exec(fmt.Sprintf(CreateTblFanasWeekRankOrderSQL, (&FansWeekRankOrderLog{OutsideTime: ts}).TableName()))
	if err != nil {
		return err
	}
	_, err = s.db.Exec(fmt.Sprintf(CreateTblFanasWeekRankSettleSQL, (&FansWeekRankSettleRecord{WeekBegin: uint32(ts.Unix())}).TableName()))
	if err != nil {
		return err
	}
	return nil
}

const (
	CreateTblFanasWeekRankOrderSQL = `
	CREATE TABLE IF NOT EXISTS %s (
		order_id varchar(128) NOT NULL COMMENT '订单号',
		uid int(10) unsigned NOT NULL COMMENT '送礼人uid',
		to_uid int(10) unsigned NOT NULL COMMENT '收礼人uid',
		channel_id int(10) unsigned NOT NULL COMMENT '房间id',
		source_type tinyint(3) unsigned NOT NULL COMMENT '来源',
		status tinyint(3) unsigned NOT NULL COMMENT '状态',
		total_price int(10) unsigned NOT NULL COMMENT '总价值',
		outside_time datetime NOT NULL COMMENT '外部系统时间',
		update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
		PRIMARY KEY (order_id),
		KEY idx_to_uid (to_uid),
		KEY idx_uid (uid),
		KEY idx_status (status),
		KEY idx_outside_time (outside_time)
	  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='粉丝团周榜送礼流水表';
	`
	CreateTblFanasWeekRankSettleSQL = `
	CREATE TABLE IF NOT EXISTS %s (
		anchor_uid int(10) unsigned NOT NULL COMMENT '主播uid',
		fans_uid int(10) unsigned NOT NULL COMMENT '粉丝uid',
		week_begin int unsigned NOT NULL COMMENT '周一0点时间戳',
		rank int(10) unsigned NOT NULL COMMENT '排名',
		rank_val int(10) unsigned NOT NULL COMMENT '榜单值',
		create_time int(10) unsigned NOT NULL COMMENT '创建时间',
		PRIMARY KEY (anchor_uid, fans_uid, week_begin),
		KEY idx_fans_uid (fans_uid),
		KEY idx_week_begin (week_begin)
	  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '粉丝团周榜结算记录表';
	`
)
