package manager

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	appChannelPB "golang.52tt.com/protocol/app/channel"
	channellivelogic "golang.52tt.com/protocol/app/channel-live-logic"
	pb "golang.52tt.com/protocol/services/channel-live-ranking"
	"golang.52tt.com/services/channel-live-ranking/cache"
	"golang.52tt.com/services/channel-live-ranking/mysql"
	"time"
)

func (m *ChannelLiveRankingManager) UpdateHonorNameplateCache(ctx context.Context) error {
	dataList, err := m.MysqlStore.GetAllAnchorHonorNameplate(ctx)
	if err != nil {
		log.Errorf("UpdateHonorNameplateCache fail to GetAllAnchorHonorNameplate. err:%v", err)
		return err
	}

	cacheList := make([]*cache.AnchorHonorNameplate, 0, len(dataList))
	for _, info := range dataList {
		cacheList = append(cacheList, &cache.AnchorHonorNameplate{
			Uid:       info.Uid,
			HonorId:   info.HonorId,
			ExpiredTs: info.ExpiredTs,
		})
	}

	if len(cacheList) == 0 {
		// 加入哨兵，防止缓存击穿
		cacheList = append(cacheList, &cache.AnchorHonorNameplate{})
	}

	weekNum := m.GetWeekNum(uint32(pb.QueryTimeType_QueryTimeType_ThisWeek))
	err = m.CacheClient.BatchUpdateAnchorHonorNameplate(weekNum, cacheList, 12*time.Hour)
	if err != nil {
		log.Errorf("UpdateHonorNameplateCache fail to BatchUpdateAnchorHonorNameplate. err:%v", err)
		return err
	}

	log.Debugf("UpdateHonorNameplateCache len(list):%d", len(cacheList))
	return nil
}

func (m *ChannelLiveRankingManager) GetAnchorHonorNameplate(ctx context.Context, uid uint32) (*pb.HonorNameplate, error) {
	weekNum := m.GetWeekNum(uint32(pb.QueryTimeType_QueryTimeType_ThisWeek))
	out := &pb.HonorNameplate{}

	cacheInfo, exists, err := m.CacheClient.GetAnchorHonorNameplate(weekNum, uid)
	if err != nil {
		log.Errorf("GetAnchorHonorNameplate fail to GetAnchorHonorNameplate. uid:%d, err:%v", uid, err)
		return out, err
	}

	if !exists {
		// 缓存不存在，更新缓存
		err = m.UpdateHonorNameplateCache(ctx)
		if err != nil {
			log.Errorf("GetAnchorHonorNameplate fail to UpdateHonorNameplateCache. uid:%d, err:%v", uid, err)
			return out, err
		} else {
			cacheInfo, _, err = m.CacheClient.GetAnchorHonorNameplate(weekNum, uid)
			if err != nil {
				log.Errorf("GetAnchorHonorNameplate fail to GetAnchorHonorNameplate. uid:%d, err:%v", uid, err)
				return out, err
			}
		}
	}

	log.Debugf("GetAnchorHonorNameplate uid:%d, info:%v", uid, cacheInfo)
	return m.GenHonorNameplatePbFromCache(cacheInfo), nil
}

func (m *ChannelLiveRankingManager) BatchGetAnchorHonorNameplate(ctx context.Context, uidList []uint32) (map[uint32]*pb.HonorNameplate, error) {
	out := make(map[uint32]*pb.HonorNameplate)
	weekNum := m.GetWeekNum(uint32(pb.QueryTimeType_QueryTimeType_ThisWeek))

	cacheList, exists, err := m.CacheClient.BatchGetAnchorHonorNameplate(weekNum, uidList)
	if err != nil {
		log.Errorf("BatchGetAnchorHonorNameplate fail to BatchGetAnchorHonorNameplate. uidList:%v, err:%v", uidList, err)
		return out, err
	}

	if !exists {
		// 缓存不存在，更新缓存
		err = m.UpdateHonorNameplateCache(ctx)
		if err != nil {
			log.Errorf("BatchGetAnchorHonorNameplate fail to UpdateHonorNameplateCache. uidList:%v, err:%v", uidList, err)
			return out, err
		} else {
			cacheList, _, err = m.CacheClient.BatchGetAnchorHonorNameplate(weekNum, uidList)
			if err != nil {
				log.Errorf("BatchGetAnchorHonorNameplate fail to BatchGetAnchorHonorNameplate. uidList:%v, err:%v", uidList, err)
				return out, err
			}
		}
	}

	for _, info := range cacheList {
		out[info.Uid] = m.GenHonorNameplatePbFromCache(info)
	}

	log.Debugf("BatchGetAnchorHonorNameplate uidList:%v, list:%+v", uidList, out)
	return out, nil
}

func (m *ChannelLiveRankingManager) GiveAnchorHonorNameplate(ctx context.Context, uid, honorId, expiredTime uint32) error {
	err := m.MysqlStore.GiveAnchorHonorNameplate(ctx, &mysql.AnchorHonorNameplate{
		Uid:       uid,
		HonorId:   honorId,
		ExpiredTs: expiredTime,
	})
	if err != nil {
		log.Errorf("GiveAnchorHonorNameplate fail to GiveAnchorHonorNameplate. uid:%d, honorId:%d, expiredTime:%d, err:%v",
			uid, honorId, expiredTime, err)
		return err
	}

	weekNum := m.GetWeekNum(uint32(pb.QueryTimeType_QueryTimeType_ThisWeek))
	// 清除本周缓存荣誉铭牌
	err = m.CacheClient.ClearAnchorHonorNameplate(weekNum)
	if err != nil {
		log.Errorf("GiveAnchorHonorNameplate fail to ClearAnchorHonorNameplate. uid:%d, weekNum:%d, err:%v",
			uid, weekNum, err)
	}

	// 推送铭牌信息
	err = m.PushHonorNameplateToChannel(ctx, uid, honorId, expiredTime)
	if err != nil {
		log.Errorf("GiveAnchorHonorNameplate fail to PushHonorNameplateToChannel. uid:%d, weekNum:%d, err:%v",
			uid, weekNum, err)
	}

	log.Debugf("GiveAnchorHonorNameplate uid:%d, honorId:%d, expiredTime:%d", uid, honorId, expiredTime)
	return nil
}

func (m *ChannelLiveRankingManager) PushHonorNameplateToChannel(ctx context.Context, uid, honorId, expiredTime uint32) error {
	channelId, err := m.CacheClient.GetLivingAnchorChannel(uid)
	if err != nil {
		log.Errorf("PushHonorNameplateToChannel fail to GetLivingAnchorChannel. uid:%d, err:%v", uid, err)
		return err
	}

	if channelId == 0 {
		return nil
	}

	// 获取铭牌配置
	conf := m.BusinessConfMgr.GetHonorNameplateById(honorId)

	pbInfo := &channellivelogic.HonorNameplate{
		Uid:                  uid,
		ExpiredTime:          expiredTime,
		HonorUrl:             conf.Url,
		RankingEnterHonorUrl: conf.RankingEnterUrl,
	}

	pbContent, err := proto.Marshal(pbInfo)
	if err != nil {
		log.Errorf("PushHonorNameplateToChannel fail to proto.Marshal. pbOpt:%+v, err:%v", pbInfo, err)
		return err
	}

	channelMsg := &appChannelPB.ChannelBroadcastMsg{
		Time:         uint64(time.Now().Unix()),
		TargetUid:    uid,
		ToChannelId:  channelId,
		Type:         uint32(appChannelPB.ChannelMsgType_CHANNEL_LIVE_HONOR_NAMEPLATE_CHANGE_MSG),
		PbOptContent: pbContent,
	}

	channelMsgBin, err := channelMsg.Marshal()
	if err != nil {
		log.Errorf("PushHonorNameplateToChannel marshal err: %s, %+v", err.Error(), channelMsg)
		return err
	}

	err = m.PushChannelBroMsgToChannel(ctx, channelId, channelMsgBin)
	if err != nil {
		log.Errorf("PushHonorNameplateToChannel PushChannelBroMsgToChannel err: %s, %+v", err.Error(), channelMsg)
		return err
	}

	log.Debugf("PushHonorNameplateToChannel success. uid:%d, honorId:%d, expiredTime:%d", uid, honorId, expiredTime)
	return nil
}

func (m *ChannelLiveRankingManager) GenHonorNameplatePbFromCache(cache *cache.AnchorHonorNameplate) *pb.HonorNameplate {
	conf := m.BusinessConfMgr.GetHonorNameplateById(cache.HonorId)

	return &pb.HonorNameplate{
		Uid:                  cache.Uid,
		HonorId:              cache.HonorId,
		ExpiredTime:          cache.ExpiredTs,
		HonorName:            conf.Name,
		HonorUrl:             conf.Url,
		RankingEnterHonorUrl: conf.RankingEnterUrl,
	}
}
