package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app"
	channel_live_logic "golang.52tt.com/protocol/app/channel-live-logic"
	pb "golang.52tt.com/protocol/services/logicsvr-go/channel-live-logic"
	grpc "google.golang.org/grpc"
	// protocol\services\logicsvr-go\channel-live-logic\channel-live-logic.pb.go
)

var (
	addr      = flag.String("addr", "", "addr")
	anchoruid = flag.Int("anchoruid", 0, "anchoruid")
)

func main() {

	flag.Parse()

	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()

	opts := []grpc.DialOption{
		//grpc.WithAuthority(Authority),
		grpc.WithInsecure(),
		grpc.WithBlock(),
		grpc.WithUserAgent("test"),
	}
	cc, err := grpc.DialContext(ctx, *addr, opts...)
	if err != nil {
		log.Errorf("NewGrpcClient fail %v ", err)
		return //nil, err
	}

	c := pb.NewChannelLiveLogicClient(cc)
	resp, err := c.GetAnchorFansInfo(ctx, &channel_live_logic.GetAnchorFansInfoReq{
		BaseReq:   &app.BaseReq{},
		AnchorUid: uint32(*anchoruid),
	})
	if err != nil {
		log.Errorln(err)
		return
	}

	v, _ := json.MarshalIndent(resp, "", "\t")
	fmt.Printf("%s\n", v)
}
