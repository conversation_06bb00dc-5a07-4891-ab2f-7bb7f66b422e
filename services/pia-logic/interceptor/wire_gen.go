// Code generated by Wire. DO NOT EDIT.

//go:generate go run github.com/google/wire/cmd/wire
//+build !wireinject

package interceptor

import (
	"golang.52tt.com/services/pia-logic/conf"
)

// Injectors from wire.go:

func InitInterceptor() (Interceptor, func(), error) {
	managerManager := conf.NewApolloManager()
	businessConfig := conf.NewBusinessConfig(managerManager)
	interceptor := NewManager(businessConfig)
	return interceptor, func() {
	}, nil
}
