package main

import (
	"context"
	"fmt"
	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	pb "golang.52tt.com/protocol/services/levelup-present"
	"golang.52tt.com/services/levelup-present/server"
	"google.golang.org/grpc"
	"os"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {
	flag := grpcEx.ParseServerFlags(os.Args)
	fmt.Println(os.Args[0])

	var (
		svr *server.LevelupPresentServer
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		svr, err = server.NewLevelupPresentServer(ctx, sc)
		if err != nil {
			return err
		}
		pb.RegisterLevelupPresentServer(s, svr)
		pb.RegisterLevelupPresentServerCompat(s, svr)
		return nil
	}

	closer := func(ctx context.Context, s *grpc.Server) error {
		if svr != nil {
			svr.ShutDown()
		}
		return nil
	}

	s := grpcEx.NewServer(
		flag,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(grpcEx.StatusCodeUnaryInterceptor)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("levelup-present.json", grpcEx.AdapterJSON),
	)

	s.Serve()
}
