package mysql

// 神秘人变更状态
const (
	UKW_CHANGE_NEWORDER    = "购买神秘人订单"
	UKW_CHANGE_SETTLEMENT  = "结算神秘人时间"
	UKW_CHANGE_FREEZE      = "冻结神秘人"
	UKW_CHANGE_UNFREEZE    = "解冻神秘人"
	UKW_CHANGE_CLOSE       = "神秘人到期关闭"
	UKW_OPEN_SWITCH        = "神秘人开启开关"
	UKW_CLOSE_SWITCH       = "神秘人关闭开关"
	UKW_OPEN_RANK_SWITCH   = "神秘人开启排行开关"
	UKW_CLOSE_RANK_SWITCH  = "神秘人关闭排行开关"
	UKW_NOTIC_OPEN_SWITCH  = "神秘人进房提示开启"
	UKW_NOTIC_CLOSE_SWITCH = "神秘人进房提示关闭"
)

// 神秘人开通状态
const (
	UKW_NO_OPEN = 0 // 未开通/已过期
	UKW_OPEN    = 1 // 已开通
	UKW_FREEZE  = 2 // 已冻结
	UKW_BAN     = 3 // 强制封禁
)

// 神秘人开关状态
const (
	UKW_SWITCH_OFF = 0 // 神秘人关闭
	UKW_SWITCH_ON  = 1 // 神秘人开启
)

const (
	UKW_RANK_SWITCH_OFF = 0 // 神秘人排行榜开关关闭
	UKW_RANK_SWITCH_ON  = 1 // 神秘人排行榜开关开启
)

// 数据表删除状态
const (
	TABLE_DELETE_FLAG_NO_DELETE  = 0
	TABLE_DELETE_FLAG_HAS_DELETE = 1
)

// 订单包裹发放状态
const (
	UKW_ORDER_PKG_NOT_SENT = 0
	UKW_ORDER_PKG_HAS_SENT = 1
)
