package conf

import (
	"encoding/json"
	"golang.52tt.com/pkg/files"
	"golang.52tt.com/pkg/log"
	"io/ioutil"
	"sync"
	"time"
)

const BusinessConfFile = "/data/oss/conf-center/tt/auth_award.json"

type AuthAwardConfig struct {
	Id        string `json:"id"`
	Begin     string `json:"begin"`
	End       string `json:"end"`
	AwardType uint32 `json:"award_type"`
	Value     uint32 `json:"value"`
}

type AwardConfig struct {
	Id        string `json:"id"`
	AwardType uint32 `json:"award_type"`
	Value     uint32 `json:"value"`
	Begin     time.Time
	End       time.Time
}

type BusinessConf struct {
	AuthConfigList []*AwardConfig `json:"auth_award"`
}

type BusinessConfManager struct {
	mutex sync.RWMutex
	conf  *BusinessConf
}

func NewBusinessConfManager() *BusinessConfManager {
	businessConf := &BusinessConf{}

	confMgr := &BusinessConfManager{
		conf:  businessConf,
		mutex: sync.RWMutex{},
	}

	confMgr.Load()
	confMgr.maintain()

	return confMgr
}

func (sc *BusinessConfManager) Load() {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	data, err := ioutil.ReadFile(BusinessConfFile)
	if err != nil {
		return
	}

	cfg := make([]*AuthAwardConfig, 0)
	err = json.Unmarshal(data, &cfg)

	sc.conf.AuthConfigList = make([]*AwardConfig, 0)
	for _, item := range cfg {
		begin, _ := time.ParseInLocation("2006-01-02 15:04:05", item.Begin, time.Local)
		end, _ := time.ParseInLocation("2006-01-02 15:04:05", item.End, time.Local)
		sc.conf.AuthConfigList = append(sc.conf.AuthConfigList, &AwardConfig{
			Id:        item.Id,
			AwardType: item.AwardType,
			Value:     item.Value,
			Begin:     begin,
			End:       end,
		})
	}

	log.Infof("load conf :%+v", sc.conf.AuthConfigList)
}

func (sc *BusinessConfManager) maintain() {
	watch := files.NewFileModifyWatch(BusinessConfFile, time.Second)
	go watch.Start(func() {
		sc.Load()
	})
}

// GetAuthAwardConfig 只会取出当前生效的第一个配置
func (sc *BusinessConfManager) GetAuthAwardConfig() *AwardConfig {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()

	for _, config := range sc.conf.AuthConfigList {
		if config.Begin.Before(time.Now()) && config.End.After(time.Now()) {
			return config
		}
	}

	return nil
}
