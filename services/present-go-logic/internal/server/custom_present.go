package server

import (
	"context"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/protocol/grpc"
	pb "golang.52tt.com/protocol/app/present-go-logic"
	"golang.52tt.com/protocol/common/status"
	"golang.52tt.com/protocol/services/presentextraconf"
	"golang.52tt.com/services/present-go-logic/internal/rpc"
	"time"
)

func (s *PresentGoLogic_) GetCustomizedPresentList(ctx context.Context, req *pb.GetCustomizedPresentListReq) (*pb.GetCustomizedPresentListResp, error) {
	resp := &pb.GetCustomizedPresentListResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetCustomizedPresentList ServiceInfoFromContext fail. ctx:%+v", ctx)
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	customResp, err := client.PresentExtraConfigCli.GetUserCustomizedInfo(ctx, &presentextraconf.GetUserCustomizedInfoReq{Uid: serviceInfo.UserID})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCustomizedPresentList GetUserCustomizedInfo fail. ctx:%+v", ctx)
		return resp, protocol.ToServerError(err)
	}

	resp.PresentInfo = make([]*pb.CustomizedPresentInfo, 0)
	for _, item := range customResp.GetPresentInfo() {
		resp.PresentInfo = append(resp.PresentInfo, &pb.CustomizedPresentInfo{
			Id:           item.GetId(),
			CustomOption: item.GetCustomOption(),
			PresentId:    item.GetPresentId(),
			CmsUrl:       item.GetCmsUrl(),
			HasAuthority: item.GetHasAuthority(),
			HasNewCustom: item.GetHasNewCustom(),
			EffectBegin:  item.GetEffectBegin(),
			EffectEnd:    item.GetEffectEnd(),
		})
	}

	for _, item := range customResp.GetPresentEffectAppend() {
		nowTs := uint32(time.Now().Unix())
		// 当前时间生效才返回，不生效不返回
		if nowTs > item.GetEffectBegin() && nowTs < item.GetEffectEnd() {
			resp.PresentEffectAppend = append(resp.PresentEffectAppend, &pb.PresentEffectAppend{
				GiftId:      item.GetGiftId(),
				EffectBegin: item.GetEffectBegin(),
				EffectEnd:   item.GetEffectEnd(),
			})
		}
	}

	resp.LastUpdateTs = customResp.GetLastUpdateTs()

	return resp, nil
}

func (s *PresentGoLogic_) GetCustomizedPresentDetail(ctx context.Context, req *pb.GetCustomizedPresentDetailReq) (*pb.GetCustomizedPresentDetailResp, error) {
	resp := &pb.GetCustomizedPresentDetailResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "GetCustomizedPresentDetail ServiceInfoFromContext fail. ctx:%+v", ctx)
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	customResp, err := client.PresentExtraConfigCli.GetUserCustomizedInfoByGiftId(ctx,
		&presentextraconf.GetUserCustomizedInfoByGiftIdReq{Uid: serviceInfo.UserID, Id: req.GetId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetCustomizedPresentDetail GetUserCustomizedInfoByGiftIdReq fail. ctx:%+v", ctx)
		return resp, protocol.ToServerError(err)
	}

	resp.PresentDetail = &pb.CustomizedPresentDetail{
		Id:           customResp.GetPresentDetail().GetId(),
		CustomOption: []*pb.CustomOption{},
		CustomMethod: customResp.GetPresentDetail().GetCustomMethod(),
		UserLevel:    customResp.GetPresentDetail().GetUserLevel(),
		LevelText:    customResp.GetPresentDetail().GetLevelText(),
		ColorfulText: customResp.GetPresentDetail().GetColorfulText(),
	}

	for _, item := range customResp.GetPresentDetail().GetCustomOption() {
		tmpCustom := &pb.CustomOption{
			CustomId:   item.GetCustomId(),
			CustomName: item.GetCustomName(),
			OptionInfo: []*pb.OptionInfo{},
			CustomText: item.GetCustomText(),
		}

		for _, option := range item.GetOptionInfo() {
			tmpCustom.OptionInfo = append(tmpCustom.OptionInfo, &pb.OptionInfo{
				OptionId:    option.GetOptionId(),
				OptionName:  option.GetOptionName(),
				OptionLevel: option.GetOptionLevel(),
				IsNew:       option.GetIsNew(),
				IsActive:    option.GetIsActive(),
			})
		}

		resp.GetPresentDetail().CustomOption = append(resp.GetPresentDetail().CustomOption, tmpCustom)
	}

	resp.GetPresentDetail().PreviewMap = make(map[uint32]*pb.CustomPresentPreview)
	for itemId, preview := range customResp.GetPresentDetail().GetPreviewMap() {
		resp.GetPresentDetail().PreviewMap[itemId] = &pb.CustomPresentPreview{
			PreviewType: preview.GetPreviewType(),
			PreviewUrl:  preview.GetPreviewUrl(),
			PreviewMd5:  preview.GetPreviewMd5(),
		}
	}

	return resp, nil
}

func (s *PresentGoLogic_) ReportCustomOptionChoose(ctx context.Context, req *pb.ReportCustomOptionChooseReq) (*pb.ReportCustomOptionChooseResp, error) {
	resp := &pb.ReportCustomOptionChooseResp{}
	serviceInfo, ok := grpc.ServiceInfoFromContext(ctx)
	if !ok {
		log.ErrorWithCtx(ctx, "ReportCustomOptionChoose ServiceInfoFromContext fail. ctx:%+v", ctx)
		return resp, protocol.NewExactServerError(nil, status.ErrRevenueSvrErr)
	}

	customReq := &presentextraconf.ReportCustomOptionChooseReq{
		Uid:        serviceInfo.UserID,
		Id:         req.GetId(),
		CustomPair: []*presentextraconf.CustomPair{},
	}

	for _, item := range req.GetCustomPair() {
		customReq.CustomPair = append(customReq.CustomPair, &presentextraconf.CustomPair{
			CustomId: item.GetCustomId(),
			OptionId: item.GetOptionId(),
		})
	}

	log.DebugWithCtx(ctx, "ReportCustomOptionChooseReq : %v", customReq)
	_, err := client.PresentExtraConfigCli.ReportCustomOptionChoose(ctx, customReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "ReportCustomOptionChoose ReportCustomOptionChoose fail. ctx:%+v", ctx)
		return resp, protocol.ToServerError(err)
	}

	return resp, nil
}
