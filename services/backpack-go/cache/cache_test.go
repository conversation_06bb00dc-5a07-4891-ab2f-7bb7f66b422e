package cache

import (
	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
	"testing"
)

func TestGetUserBackpackItemListKey(t *testing.T) {
	type args struct {
		uid uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "TestGetUserBackpackItemListKey",
			args: args{uid:1024},
			want: "user_backpack_1024",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetUserBackpackItemListKey(tt.args.uid); got != tt.want {
				t.Errorf("GetUserBackpackItemListKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewCache(t *testing.T) {
	type args struct {
		r      *redis.Client
		tracer opentracing.Tracer
	}
	tests := []struct {
		name string
		args args
		want *Cache
	}{
		// TODO: Add test cases.
		{
			name: "TestNewCache",
			args: args{
				r:      nil,
				tracer: nil,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			NewCache(tt.args.r, tt.args.tracer)
/*			if got := NewCache(tt.args.r, tt.args.tracer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewCache() = %v, want %v", got, tt.want)
			}*/
		})
	}
}