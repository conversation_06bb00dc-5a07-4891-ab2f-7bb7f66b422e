package event

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
	eventlink "golang.52tt.com/pkg/event-link-wrap"
	"golang.52tt.com/pkg/log"
	pbLogic "golang.52tt.com/protocol/app/channel-live-logic"
	"golang.52tt.com/services/anchor-contract/anchorcontract-go/manager"
)

const (
	topicChLiveStatusName = "channel_live_kafka_topic"
)

type ChLiveStatusKafkaSub struct {
	kafkaSub *eventlink.EventLinkAsyncSub
	mgr      *manager.AnchorContractMgr
}

func NewChLiveStatusKafkaSub(clientId, groupId string, topics, brokers []string, mgr *manager.AnchorContractMgr) (*ChLiveStatusKafkaSub, error) {
	sub := &ChLiveStatusKafkaSub{
		mgr: mgr,
	}

	log.Infof("NewChLiveStatusKafkaSub kafka_config Topics:%s, Brokers:%s", topics, brokers)
	//kafkaSub, err := event.NewKafkaSubV2(kafkaConfig, sub.handlerEvent)

	option := []eventlink.Option{
		eventlink.WithMaxRetryTimes(5),
		eventlink.WithProcessWorkerNum(10),
	}

	kafkaSub, err := eventlink.NewEventLinkAsyncSub2(brokers, topics, groupId,
		clientId, sub.handlerEvent, option...)
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber %+v", err)
		return nil, err
	}
	sub.kafkaSub = kafkaSub
	log.Infof("NewChLiveStatusKafkaSub clientId %s, groupId %s, topics %v, brokers%v", clientId, groupId, topics, brokers)
	return sub, nil
}

func (s *ChLiveStatusKafkaSub) Close() {
	s.kafkaSub.Stop()
}

func (s *ChLiveStatusKafkaSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	switch msg.Topic {
	case topicChLiveStatusName:
		return s.handleLiveStatusEvent(ctx, msg)
	default:
	}
	return nil, false
}

func (s *ChLiveStatusKafkaSub) handleLiveStatusEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	statusEvent := &pbLogic.ChannelLiveKafkaEvent{}
	err := proto.Unmarshal(msg.Value, statusEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "handleLiveStatusEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	log.DebugWithCtx(ctx, "handleLiveStatusEvent %+v", statusEvent)

	// 只处理直播状态变化事件
	if statusEvent.GetTy() != pbLogic.ChannelLiveKafkaEventType_ChannelLiveType {
		return nil, false
	}

	/*ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()*/
	// x fix anchor identity

	// 记录今日开播
	if statusEvent.GetChannelLiveStatus() == pbLogic.EnumChannelLiveStatus_OPEN {
		err = s.mgr.HandleAnchorLiveEvent(statusEvent)
		if err != nil {
			log.Errorf("handleLiveStatusEvent Failed to HandleAnchorLiveEvent err(%v) Event:%+v", err, statusEvent)
		}
	}

	log.Debugf("handleLiveStatusEvent done. Event:%+v", statusEvent)
	return nil, true
}
