package mysql

import (
	"fmt"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/config"
	"testing"
	"time"
)

var mysqlStore *Store

func init() {
	mysqlConf := &config.MysqlConfig{
		Host:     "mysql.52tt.local",
		Port:     3306,
		Database: "appsvr",
		Charset:  "utf8mb4",
		UserName: "godman",
		Password: "thegodofman",
	}
	fmt.Printf("string:%s\n", mysqlConf.ConnectionString())
	mysqlDb, err := sqlx.Connect("mysql", mysqlConf.ConnectionString())
	if err != nil {
		fmt.Errorf("Failed to Connect mysql %v", err)
		return
	}
	mysqlStore = NewMysql(mysqlDb)
	err = mysqlStore.CreateMysqlTable()
	fmt.Printf("table err:%+v", err)
}

func TestCreateTable(t *testing.T) {
	mysqlStore.CreateMysqlTable()
}

func TestTimeRight(t *testing.T) {
	nowTime := time.Now()
	beginTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 0, 0, 0, 0, time.Local).Unix()
	endTime := nowTime.Unix()
	fmt.Printf("time:%d,%d,%d,%d,%d\n", beginTime, endTime, nowTime.Month(), nowTime.Day(), nowTime.Hour())
}
