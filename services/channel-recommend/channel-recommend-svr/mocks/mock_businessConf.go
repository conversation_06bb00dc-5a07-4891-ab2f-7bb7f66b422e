// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/channel-recommend/channel-recommend-svr/model (interfaces: IBusinessConfManager)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_recommend_svr "golang.52tt.com/protocol/services/channel-recommend-svr"
	model "golang.52tt.com/services/channel-recommend/channel-recommend-svr/model"
)

// MockIBusinessConfManager is a mock of IBusinessConfManager interface.
type MockIBusinessConfManager struct {
	ctrl     *gomock.Controller
	recorder *MockIBusinessConfManagerMockRecorder
}

// MockIBusinessConfManagerMockRecorder is the mock recorder for MockIBusinessConfManager.
type MockIBusinessConfManagerMockRecorder struct {
	mock *MockIBusinessConfManager
}

// NewMockIBusinessConfManager creates a new mock instance.
func NewMockIBusinessConfManager(ctrl *gomock.Controller) *MockIBusinessConfManager {
	mock := &MockIBusinessConfManager{ctrl: ctrl}
	mock.recorder = &MockIBusinessConfManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBusinessConfManager) EXPECT() *MockIBusinessConfManagerMockRecorder {
	return m.recorder
}

// CheckIsRoiCpId mocks base method.
func (m *MockIBusinessConfManager) CheckIsRoiCpId(arg0 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsRoiCpId", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckIsRoiCpId indicates an expected call of CheckIsRoiCpId.
func (mr *MockIBusinessConfManagerMockRecorder) CheckIsRoiCpId(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsRoiCpId", reflect.TypeOf((*MockIBusinessConfManager)(nil).CheckIsRoiCpId), arg0)
}

// CheckIsRoiTagContent mocks base method.
func (m *MockIBusinessConfManager) CheckIsRoiTagContent(arg0 []string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsRoiTagContent", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckIsRoiTagContent indicates an expected call of CheckIsRoiTagContent.
func (mr *MockIBusinessConfManagerMockRecorder) CheckIsRoiTagContent(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsRoiTagContent", reflect.TypeOf((*MockIBusinessConfManager)(nil).CheckIsRoiTagContent), arg0)
}

// Close mocks base method.
func (m *MockIBusinessConfManager) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIBusinessConfManagerMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIBusinessConfManager)(nil).Close))
}

// GetBanTagMap mocks base method.
func (m *MockIBusinessConfManager) GetBanTagMap(arg0 uint32) map[uint32]bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanTagMap", arg0)
	ret0, _ := ret[0].(map[uint32]bool)
	return ret0
}

// GetBanTagMap indicates an expected call of GetBanTagMap.
func (mr *MockIBusinessConfManagerMockRecorder) GetBanTagMap(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanTagMap", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetBanTagMap), arg0)
}

// GetDeviceStatusHttp mocks base method.
func (m *MockIBusinessConfManager) GetDeviceStatusHttp() model.DeviceStatusHttp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeviceStatusHttp")
	ret0, _ := ret[0].(model.DeviceStatusHttp)
	return ret0
}

// GetDeviceStatusHttp indicates an expected call of GetDeviceStatusHttp.
func (mr *MockIBusinessConfManagerMockRecorder) GetDeviceStatusHttp() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceStatusHttp", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetDeviceStatusHttp))
}

// GetImPushConf mocks base method.
func (m *MockIBusinessConfManager) GetImPushConf(arg0, arg1 uint32) model.ImPushConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImPushConf", arg0, arg1)
	ret0, _ := ret[0].(model.ImPushConf)
	return ret0
}

// GetImPushConf indicates an expected call of GetImPushConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetImPushConf(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImPushConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetImPushConf), arg0, arg1)
}

// GetIsTesting mocks base method.
func (m *MockIBusinessConfManager) GetIsTesting() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIsTesting")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetIsTesting indicates an expected call of GetIsTesting.
func (mr *MockIBusinessConfManagerMockRecorder) GetIsTesting() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIsTesting", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetIsTesting))
}

// GetLevelRatioMap mocks base method.
func (m *MockIBusinessConfManager) GetLevelRatioMap() map[uint32]float32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLevelRatioMap")
	ret0, _ := ret[0].(map[uint32]float32)
	return ret0
}

// GetLevelRatioMap indicates an expected call of GetLevelRatioMap.
func (mr *MockIBusinessConfManagerMockRecorder) GetLevelRatioMap() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLevelRatioMap", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetLevelRatioMap))
}

// GetLotteryRecConf mocks base method.
func (m *MockIBusinessConfManager) GetLotteryRecConf() model.LotteryRec {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLotteryRecConf")
	ret0, _ := ret[0].(model.LotteryRec)
	return ret0
}

// GetLotteryRecConf indicates an expected call of GetLotteryRecConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetLotteryRecConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLotteryRecConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetLotteryRecConf))
}

// GetNewRoiDeviceStatusList mocks base method.
func (m *MockIBusinessConfManager) GetNewRoiDeviceStatusList() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNewRoiDeviceStatusList")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetNewRoiDeviceStatusList indicates an expected call of GetNewRoiDeviceStatusList.
func (mr *MockIBusinessConfManagerMockRecorder) GetNewRoiDeviceStatusList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewRoiDeviceStatusList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetNewRoiDeviceStatusList))
}

// GetOldRoiDeviceStatusList mocks base method.
func (m *MockIBusinessConfManager) GetOldRoiDeviceStatusList() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOldRoiDeviceStatusList")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetOldRoiDeviceStatusList indicates an expected call of GetOldRoiDeviceStatusList.
func (mr *MockIBusinessConfManagerMockRecorder) GetOldRoiDeviceStatusList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOldRoiDeviceStatusList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetOldRoiDeviceStatusList))
}

// GetPrepareLevelByRank mocks base method.
func (m *MockIBusinessConfManager) GetPrepareLevelByRank(arg0, arg1, arg2 uint32) channel_recommend_svr.ChannelLevel {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrepareLevelByRank", arg0, arg1, arg2)
	ret0, _ := ret[0].(channel_recommend_svr.ChannelLevel)
	return ret0
}

// GetPrepareLevelByRank indicates an expected call of GetPrepareLevelByRank.
func (mr *MockIBusinessConfManagerMockRecorder) GetPrepareLevelByRank(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrepareLevelByRank", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetPrepareLevelByRank), arg0, arg1, arg2)
}

// GetRoiChannelList mocks base method.
func (m *MockIBusinessConfManager) GetRoiChannelList() ([]uint32, []uint32) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoiChannelList")
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].([]uint32)
	return ret0, ret1
}

// GetRoiChannelList indicates an expected call of GetRoiChannelList.
func (mr *MockIBusinessConfManagerMockRecorder) GetRoiChannelList() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoiChannelList", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRoiChannelList))
}

// GetRoiDeviceStatusQueryConf mocks base method.
func (m *MockIBusinessConfManager) GetRoiDeviceStatusQueryConf(arg0 uint32) model.DeviceStatusConf {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoiDeviceStatusQueryConf", arg0)
	ret0, _ := ret[0].(model.DeviceStatusConf)
	return ret0
}

// GetRoiDeviceStatusQueryConf indicates an expected call of GetRoiDeviceStatusQueryConf.
func (mr *MockIBusinessConfManagerMockRecorder) GetRoiDeviceStatusQueryConf(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoiDeviceStatusQueryConf", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRoiDeviceStatusQueryConf), arg0)
}

// GetRoiHighCertUrl mocks base method.
func (m *MockIBusinessConfManager) GetRoiHighCertUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoiHighCertUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetRoiHighCertUrl indicates an expected call of GetRoiHighCertUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetRoiHighCertUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoiHighCertUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRoiHighCertUrl))
}

// GetRoiHighPopUrl mocks base method.
func (m *MockIBusinessConfManager) GetRoiHighPopUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoiHighPopUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetRoiHighPopUrl indicates an expected call of GetRoiHighPopUrl.
func (mr *MockIBusinessConfManagerMockRecorder) GetRoiHighPopUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoiHighPopUrl", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRoiHighPopUrl))
}

// GetRoiStateHttp mocks base method.
func (m *MockIBusinessConfManager) GetRoiStateHttp() model.RoiStateHttp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoiStateHttp")
	ret0, _ := ret[0].(model.RoiStateHttp)
	return ret0
}

// GetRoiStateHttp indicates an expected call of GetRoiStateHttp.
func (mr *MockIBusinessConfManagerMockRecorder) GetRoiStateHttp() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoiStateHttp", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetRoiStateHttp))
}

// GetUserLabelHttp mocks base method.
func (m *MockIBusinessConfManager) GetUserLabelHttp() model.UserLabelHttp {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLabelHttp")
	ret0, _ := ret[0].(model.UserLabelHttp)
	return ret0
}

// GetUserLabelHttp indicates an expected call of GetUserLabelHttp.
func (mr *MockIBusinessConfManagerMockRecorder) GetUserLabelHttp() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLabelHttp", reflect.TypeOf((*MockIBusinessConfManager)(nil).GetUserLabelHttp))
}

// Reload mocks base method.
func (m *MockIBusinessConfManager) Reload(arg0 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Reload", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Reload indicates an expected call of Reload.
func (mr *MockIBusinessConfManagerMockRecorder) Reload(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Reload", reflect.TypeOf((*MockIBusinessConfManager)(nil).Reload), arg0)
}

// SetIsTesting mocks base method.
func (m *MockIBusinessConfManager) SetIsTesting(arg0 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetIsTesting", arg0)
}

// SetIsTesting indicates an expected call of SetIsTesting.
func (mr *MockIBusinessConfManagerMockRecorder) SetIsTesting(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIsTesting", reflect.TypeOf((*MockIBusinessConfManager)(nil).SetIsTesting), arg0)
}

// Watch mocks base method.
func (m *MockIBusinessConfManager) Watch(arg0 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Watch", arg0)
}

// Watch indicates an expected call of Watch.
func (mr *MockIBusinessConfManagerMockRecorder) Watch(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Watch", reflect.TypeOf((*MockIBusinessConfManager)(nil).Watch), arg0)
}
