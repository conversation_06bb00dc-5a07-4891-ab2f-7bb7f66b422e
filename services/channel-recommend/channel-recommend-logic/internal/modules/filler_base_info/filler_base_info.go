package filler_base_info

import (
	"context"
	grpc "gitlab.ttyuyin.com/avengers/tyr/core/service/basepb/info"
	account "golang.52tt.com/clients/account-go"
	"golang.52tt.com/pkg/log"
	protogrpc "golang.52tt.com/pkg/protocol/grpc"
	"golang.52tt.com/pkg/ttversion"
	"golang.52tt.com/protocol/app"
	"golang.52tt.com/protocol/app/channel"
	channel_recommend_svr "golang.52tt.com/protocol/services/channel-recommend-svr"
	ChannelMic "golang.52tt.com/protocol/services/channelmicsvr"
	channelPb "golang.52tt.com/protocol/services/channelsvr"
	official_live_channel "golang.52tt.com/protocol/services/official-live-channel"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/conf"
	"golang.52tt.com/services/channel-recommend/channel-recommend-logic/internal/rpc"
	"golang.org/x/sync/errgroup"
	"sort"
	"sync"
	"time"
)

const NewUserSec = 7 * 24 * 3600 // 新注册用户时间

type FillBaseInfo struct {
	Ctx                    context.Context
	tmpCtx                 context.Context
	Uid                    uint32
	ServiceInfo            *grpc.ServiceInfo
	UserInfo               *account.User
	CidList                []uint32
	UidList                []uint32
	MapCid2ChannelInfo     map[uint32]*channelPb.ChannelSimpleInfo
	MapCid2MicInfo         map[uint32]*ChannelMic.MicrData
	MapUid2UserInfo        map[uint32]*app.UserProfile
	MapCid2Recommend       map[uint32]*channel_recommend_svr.ChannelRecommendSimpleInfo
	MapCid2OfficialChannel map[uint32]*official_live_channel.GetRelayByResp
	DyConfigMgr            *conf.BusinessConfManager
	MapCid2HourRank        map[uint32]string
	MapUid2Level           map[uint32]uint32
}

func (f *FillBaseInfo) InitChannelInfo() (err error) {
	wg := sync.WaitGroup{}

	f.MapCid2ChannelInfo = make(map[uint32]*channelPb.ChannelSimpleInfo)
	mutex := sync.Mutex{}

	for i := 0; i < len(f.CidList); i += 50 {
		wg.Add(1)
		i := i
		go func() {
			defer wg.Done()
			subCtx, cancel := protogrpc.InheritContextWithInfoTimeout(f.tmpCtx, time.Millisecond*100)
			defer cancel()

			resp, sErr := rpc.ChannelCli.BatchGetChannelSimpleInfo(subCtx, f.Uid, f.CidList[i:min(i+50, len(f.CidList))])

			if sErr != nil {
				log.ErrorWithCtx(f.tmpCtx, "BatchGetChannelSimpleInfo failed Uid:%v cid:%d err:%v", f.Uid, f.CidList, err)
				err = sErr
				return
			}

			mutex.Lock()
			for _, v := range resp {
				f.MapCid2ChannelInfo[v.GetChannelId()] = v
			}
			mutex.Unlock()
		}()
	}

	wg.Wait()

	return err
}

func (f *FillBaseInfo) InitMicInfo() (err error) {

	f.MapCid2MicInfo = make(map[uint32]*ChannelMic.MicrData)
	mutex := sync.Mutex{}

	// 分批处理，每20个一批
	// 异步处理
	wg := sync.WaitGroup{}
	for i := 0; i < len(f.CidList); i += 20 {
		wg.Add(1)
		i := i
		go func() {
			defer wg.Done()
			subCtx, cancel := protogrpc.InheritContextWithInfoTimeout(f.tmpCtx, time.Millisecond*100)
			defer cancel()

			resp, sErr := rpc.ChannelMicCli.BatGetMicrList(subCtx, f.Uid, &ChannelMic.BatGetMicrListReq{ChannelIdList: f.CidList[i:min(i+20, len(f.CidList))]})
			if sErr != nil {
				log.ErrorWithCtx(f.tmpCtx, "BatchGetMicrData failed Uid:%v cid:%d err:%v", f.Uid, f.CidList, err)
				err = sErr
				return
			}

			mutex.Lock()
			for _, v := range resp.GetMicDataList() {
				f.MapCid2MicInfo[v.GetChannelId()] = v
			}
			mutex.Unlock()
		}()
	}

	wg.Wait()

	return err
}

func min(i int, i2 int) int {
	if i < i2 {
		return i
	}
	return i2
}

// InitOfficialChannelInfo 官频这里，获取不到也要继续，所以不返回错误了
func (f *FillBaseInfo) InitOfficialChannelInfo() error {
	subCtx, cancel := protogrpc.InheritContextWithInfoTimeout(f.tmpCtx, time.Millisecond*100)
	defer cancel()

	resp, err := rpc.OfficialLiveCli.GetAllRelayBy(subCtx, &official_live_channel.GetAllRelayByReq{})
	if err != nil {
		log.ErrorWithCtx(f.tmpCtx, "GetRelay failed err:%v", err)
		return nil
	}

	f.MapCid2OfficialChannel = make(map[uint32]*official_live_channel.GetRelayByResp)
	for _, item := range resp.GetRelayByList() {
		if item.GetRelayStatus() == official_live_channel.RelayStatus_RUNNING || item.GetRelayStatus() == official_live_channel.RelayStatus_PAUSE {
			f.MapCid2OfficialChannel[item.GetOfficialChannelId()] = item
		}
	}

	return nil
}

func (f *FillBaseInfo) InitUserInfo() error {
	subCtx, cancel := protogrpc.InheritContextWithInfoTimeout(f.tmpCtx, time.Millisecond*100)
	defer cancel()

	info, err := rpc.AccountCli.GetUserByUid(subCtx, f.Uid)
	if err != nil {
		log.ErrorWithCtx(f.tmpCtx, "GetUserByUid failed Uid:%v err:%v", f.Uid, err)
		return err
	}

	f.UserInfo = info
	return nil
}

func (f *FillBaseInfo) InitMicUserInfo(ctx context.Context, mapCid2MicInfo map[uint32]*ChannelMic.MicrData) error {

	f.UidList = make([]uint32, 0)
	for _, item := range mapCid2MicInfo {
		// 先按MicId升序排序,前提是麦位上有人
		sort.Slice(item.GetAllMicList(), func(i, j int) bool {
			if item.GetAllMicList()[i].GetMicUid() == 0 {
				return false
			}

			if item.GetAllMicList()[j].GetMicUid() == 0 {
				return true
			}

			return item.GetAllMicList()[i].GetMicId() < item.GetAllMicList()[j].GetMicId()
		})

		uidLen := 9
		if len(item.GetAllMicList()) < 9 {
			uidLen = len(item.GetAllMicList())
		}

		// 取前9个用户的uid
		for _, mic := range item.GetAllMicList()[:uidLen] {
			if mic.GetMicUid() == 0 {
				continue
			}
			f.UidList = append(f.UidList, mic.GetMicUid())
		}
	}

	err := f.FillUserInfo(ctx, f.UidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "InitUserInfo failed Uid:%v err:%v", f.UidList, err)
		return err
	}

	return nil
}

func (f *FillBaseInfo) ChannelValidFilter() {
	tmpCidList := make([]uint32, 0)
	for _, cid := range f.CidList {
		if f.checkIsValidRecChannel(f.Ctx, cid) {
			tmpCidList = append(tmpCidList, cid)
		}
	}

	f.CidList = tmpCidList
}

func getUserCategory(userInfo *account.User) channel_recommend_svr.UserCategory {
	userCategory := channel_recommend_svr.UserCategory_OldUser_Type

	nowTs := uint32(time.Now().Unix())

	if (nowTs - userInfo.GetRegisteredAt()) <= NewUserSec {
		userCategory = channel_recommend_svr.UserCategory_NewUser_Type
	}

	return userCategory
}

func (f *FillBaseInfo) checkIsValidRecChannel(ctx context.Context, cid uint32) bool {
	channelInfo := f.MapCid2ChannelInfo[cid]
	recInfo := f.MapCid2Recommend[cid]
	officialInfo := f.MapCid2OfficialChannel[cid]

	// 判断房间有效性，官频不需要判断
	if channelInfo.GetChannelType() != uint32(channel.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		if channelInfo.GetHasPwd() || (channelInfo.GetSwitchFlag()&0x2 == 0) ||
			(f.Uid == channelInfo.GetCreaterUid() && recInfo.GetCategory() == uint32(channel.ChannelCategory_Used_Enter)) {
			log.ErrorWithCtx(ctx, "checkIsValidRecChannel invalid reqParam:%v channelInfo:%v recInfo:%v", f.Uid, channelInfo, recInfo)
			return false
		}
	}

	// 官频
	if channelInfo.GetChannelType() == uint32(channel.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		if !ttversion.OfficialLiveChannelFeature.Atleast(f.ServiceInfo.ClientType, f.ServiceInfo.ClientVersion) {
			// 不是官频版本
			log.ErrorWithCtx(ctx, "checkIsValidRecChannel invalid ver reqParam:%v channelInfo:%v recInfo:%v", f.Uid, channelInfo, recInfo)
			return false
		}

		if officialInfo.GetOfficialChannelId() == 0 {
			// 官频不在开播
			log.ErrorWithCtx(ctx, "checkIsValidRecChannel invalid official reqParam:%v channelInfo:%v officialInfo:%v", f.Uid, channelInfo, officialInfo)
			return false
		}
	}

	if len(f.MapCid2MicInfo[cid].GetAllMicList()) == 0 {
		log.ErrorWithCtx(ctx, "checkIsValidRecChannel invalid mic reqParam:%v channelInfo:%v recInfo:%v", f.Uid, channelInfo, recInfo)
		return false
	}

	// 除了官频，0号麦没人就不行

	if channelInfo.GetChannelType() != uint32(channel.ChannelType_OFFICIAL_LIVE_CHANNEL_TYPE) {
		for _, item := range f.MapCid2MicInfo[cid].GetAllMicList() {
			if item.GetMicId() == 1 {
				if item.GetMicUid() == 0 {
					log.ErrorWithCtx(ctx, "checkIsValidRecChannel invalid mic reqParam:%v channelInfo:%v recInfo:%v", f.Uid, channelInfo, recInfo)
					return false
				}
			}
		}
	}

	log.DebugWithCtx(ctx, "checkIsValidRecChannel end reqParam:%+v", f.Uid)
	return true
}

func (f *FillBaseInfo) FillUserInfo(ctx context.Context, uidList []uint32) error {
	// 每50个一组获取用户信息和头像

	// 异步获取一些后续可能会用到的基本信息
	g, tmpCtx := errgroup.WithContext(ctx)
	mutex := sync.Mutex{}

	userInfoMap := make(map[uint32]*app.UserProfile)
	for i := 0; i < len(uidList); i += 50 {
		end := i + 50
		if end > len(uidList) {
			end = len(uidList)
		}
		uList := uidList[i:end]

		g.Go(func() error {
			subCtx, cancel := protogrpc.InheritContextWithInfoTimeout(tmpCtx, time.Millisecond*500)
			defer cancel()

			resp, err := rpc.UserProfileCli.BatchGetUserProfileV2(subCtx, uList, true)
			if err != nil {
				log.ErrorWithCtx(tmpCtx, "GetUsersMap failed Uid:%v err:%v", uList, err)
				return err
			}

			mutex.Lock()
			defer mutex.Unlock()
			for uid, item := range resp {
				userInfoMap[uid] = item
			}

			return nil
		})
	}

	if err := g.Wait(); err != nil {
		log.ErrorWithCtx(tmpCtx, "GetUsersMap failed err:%v", err)
		return err
	}

	f.MapUid2UserInfo = make(map[uint32]*app.UserProfile)
	for uid, item := range userInfoMap {
		f.MapUid2UserInfo[uid] = item
	}

	return nil
}
