package mgr

import (
	"context"
	"encoding/json"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	backpack_base_mock "golang.52tt.com/clients/mocks/backpack-base"
	"golang.52tt.com/protocol/app/backpack"
	backpackBasePb "golang.52tt.com/protocol/services/backpack-base"
	"golang.52tt.com/services/backpack/backpack-logic-go/conf"
)

type backpackItemForTest struct {
	*backpackItem
}

func newBackpackItemTest(t *testing.T) *backpackItemForTest {
	controller := gomock.NewController(t)
	return &backpackItemForTest{
		&backpackItem{
			backpackCli: backpack_base_mock.NewMockIClient(controller),
			dyConf:      conf.NewMockDynamicConf(controller),
			funcCard:    NewMockFuncCardMgr(controller),
			cfgCache:    NewMockConfigCacheMgr(controller),
		},
	}
}

func (receiver *backpackItemForTest) GetBackpackCli() *backpack_base_mock.MockIClient {
	return receiver.backpackCli.(*backpack_base_mock.MockIClient)
}

func (receiver *backpackItemForTest) GetDyConf() *conf.MockDynamicConf {
	return receiver.dyConf.(*conf.MockDynamicConf)
}

func (receiver *backpackItemForTest) GetFuncCard() *MockFuncCardMgr {
	return receiver.funcCard.(*MockFuncCardMgr)
}

func (receiver *backpackItemForTest) GetCfgCache() *MockConfigCacheMgr {
	return receiver.cfgCache.(*MockConfigCacheMgr)
}

func Test_backpackItem_GetUserBackpack(t *testing.T) {
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name                       string
		args                       args
		wantUserBackpackList       []*backpack.UserPackageItem
		wantLastGainItemTs         int64
		wantUserBackpackDetailList []*backpack.UserPackageItem
		wantErr                    bool
		initFunc                   func(b *backpackItemForTest)
	}{
		{
			name: "测试获取用户背包信息",
			args: args{
				ctx: context.Background(),
				uid: 123456,
			},
			wantUserBackpackList: []*backpack.UserPackageItem{
				{
					ItemType:   2,
					UserItemId: 25,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Card: &backpack.FuncCardCfg{
						CardId:    1,
						CardType:  2,
						CardName:  "测试1",
						CardDesc:  "测试加速卡",
						CardUrl:   "http://test1",
						CardTimes: 1,
						ValidTime: 86401,
						IsDel:     0,
						CardValue: 23,
					},
					BackupText: "测试文案",
					ObtainTime: 1692770516,
				},
				{
					ItemType:   5,
					UserItemId: 26,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Card: &backpack.FuncCardCfg{
						CardId:    2,
						CardType:  5,
						CardName:  "测试2",
						CardUrl:   "http://test2",
						CardTimes: 2,
						ValidTime: 144000,
						CardDesc:  "你已锁定财富值，无法使用财富卡哦~",
						IsDel:     0,
						CardValue: 233,
					},
					BackupText: "测试文案",
					ObtainTime: 1692770516,
				},
				{
					ItemType:   1,
					UserItemId: 24,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  2347,
					BackupText: "测试文案",
					ObtainTime: 1692770516,
				},
				{
					ItemType:   1,
					UserItemId: 23,
					ItemCount:  2,
					FinTime:    1692770515,
					PresentId:  2345,
					BackupText: "测试文案",
					ObtainTime: 1692770515,
				},
			},
			wantLastGainItemTs: 23333,
			wantUserBackpackDetailList: []*backpack.UserPackageItem{
				{
					ItemType:   1,
					UserItemId: 22,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  2345,
					BackupText: "测试文案",
					ObtainTime: 1692770516,
				},
				{
					ItemType:   1,
					UserItemId: 23,
					ItemCount:  1,
					FinTime:    1692770515,
					PresentId:  2345,
					BackupText: "测试文案",
					ObtainTime: 1692770515,
				},
				{
					ItemType:   1,
					UserItemId: 24,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  2347,
					BackupText: "测试文案",
					ObtainTime: 1692770516,
				},
			},
			wantErr: false,
			initFunc: func(b *backpackItemForTest) {
				b.GetBackpackCli().EXPECT().GetUserBackpack(gomock.Any(), uint32(123456)).Return(&backpackBasePb.GetUserBackpackResp{
					UserItemList: []*backpackBasePb.UserBackpackItem{
						{
							ItemType:       1,
							UserItemId:     22,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       2345,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       1,
							UserItemId:     23,
							ItemCount:      1,
							FinTime:        1692770515,
							SourceId:       2345,
							Weight:         0,
							ObtainTime:     1692770515,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       1,
							UserItemId:     24,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       2347,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       2,
							UserItemId:     25,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       1,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       5,
							UserItemId:     26,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       2,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
					},
					LastObtainTs: 23333,
				}, nil)
				b.GetDyConf().EXPECT().GetBackupText().Return("测试文案")
				b.GetFuncCard().EXPECT().GetUserFuncCardCfgMapWithList(gomock.Any(), uint32(123456), []uint32{1, 2}, true).Return(map[uint32]*backpack.FuncCardCfg{
					1: {
						CardId:    1,
						CardType:  2,
						CardName:  "测试1",
						CardUrl:   "http://test1",
						CardTimes: 1,
						ValidTime: 86401,
						CardDesc:  "测试加速卡",
						IsDel:     0,
						CardValue: 23,
					},
					2: {
						CardId:    2,
						CardType:  5,
						CardName:  "测试2",
						CardUrl:   "http://test2",
						CardTimes: 2,
						ValidTime: 144000,
						CardDesc:  "你已锁定财富值，无法使用财富卡哦~",
						IsDel:     0,
						CardValue: 233,
					},
				}, nil)
				b.GetCfgCache().EXPECT().GetItemWeightCacheMap().Return(map[uint32]map[uint32]uint32{
					1: {
						2347: 2,
						2345: 1,
					},
				})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := newBackpackItemTest(t)
			if tt.initFunc != nil {
				tt.initFunc(b)
			}
			gotUserBackpackList, gotLastGainItemTs, gotUserBackpackDetailList, err := b.GetUserBackpack(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserBackpack() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotUserBackpackList, tt.wantUserBackpackList) {
				t.Errorf("GetUserBackpack() gotUserBackpackList = %v, want %v", gotUserBackpackList, tt.wantUserBackpackList)
			}
			if gotLastGainItemTs != tt.wantLastGainItemTs {
				t.Errorf("GetUserBackpack() gotLastGainItemTs = %v, want %v", gotLastGainItemTs, tt.wantLastGainItemTs)
			}
			if !reflect.DeepEqual(gotUserBackpackDetailList, tt.wantUserBackpackDetailList) {
				t.Errorf("GetUserBackpack() gotUserBackpackDetailList = %v, want %v", gotUserBackpackDetailList, tt.wantUserBackpackDetailList)
			}
		})
	}
}

func Test_backpackItem_GetImBackpack(t *testing.T) {
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name               string
		args               args
		wantItemList       []*backpack.UserPackageItem
		wantLastGainItemTs int64
		wantItemDetailList []*backpack.UserPackageItem
		wantErr            bool
		initFunc           func(b *backpackItemForTest)
	}{
		{
			name: "测试获取IM礼物信息",
			args: args{
				ctx: context.Background(),
				uid: 123456,
			},
			wantItemList: []*backpack.UserPackageItem{
				{
					ItemType:   1,
					UserItemId: 23,
					ItemCount:  2,
					FinTime:    1692770515,
					PresentId:  2345,
				},
			},
			wantLastGainItemTs: 23333,
			wantItemDetailList: []*backpack.UserPackageItem{
				{
					ItemType:   1,
					UserItemId: 22,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  2345,
				},
				{
					ItemType:   1,
					UserItemId: 23,
					ItemCount:  1,
					FinTime:    1692770515,
					PresentId:  2345,
				},
			},
			wantErr: false,
			initFunc: func(b *backpackItemForTest) {
				b.GetBackpackCli().EXPECT().GetUserBackpackItemsAndLastObtainTs(gomock.Any(), uint32(123456), uint32(backpackBasePb.PackageItemType_BACKPACK_PRESENT), uint32(0)).
					Return(&backpackBasePb.GetUserBackpackByItemResp{
						UserItemList: []*backpackBasePb.UserBackpackItem{
							{
								ItemType:       1,
								UserItemId:     22,
								ItemCount:      1,
								FinTime:        1692770516,
								SourceId:       2345,
								Weight:         0,
								ObtainTime:     1692770516,
								SourceType:     1,
								FinalItemCount: 0,
							},
							{
								ItemType:       1,
								UserItemId:     23,
								ItemCount:      1,
								FinTime:        1692770515,
								SourceId:       2345,
								Weight:         0,
								ObtainTime:     1692770515,
								SourceType:     1,
								FinalItemCount: 0,
							},
							{
								ItemType:       1,
								UserItemId:     24,
								ItemCount:      1,
								FinTime:        1692770516,
								SourceId:       2347,
								Weight:         0,
								ObtainTime:     1692770516,
								SourceType:     1,
								FinalItemCount: 0,
							},
							{
								ItemType:       2,
								UserItemId:     25,
								ItemCount:      1,
								FinTime:        1692770516,
								SourceId:       1,
								Weight:         0,
								ObtainTime:     1692770516,
								SourceType:     1,
								FinalItemCount: 0,
							},
							{
								ItemType:       5,
								UserItemId:     26,
								ItemCount:      1,
								FinTime:        1692770516,
								SourceId:       2,
								Weight:         0,
								ObtainTime:     1692770516,
								SourceType:     1,
								FinalItemCount: 0,
							},
						},
						LastObtainTs: 23333,
					}, nil)
				b.GetDyConf().EXPECT().GetImBackpackItemMap().Return(map[uint32]bool{2345: true})
				b.GetDyConf().EXPECT().GetImPresentMap().Return(map[uint32]*conf.ImPresentConfig{352: {
					ItemId:        352,
					ItemIdStr:     "352",
					BiggerPicUrl:  "https://ga-album-cdnqn.52tt.com/channel/interaction_intimacy/20201013/img_im_intimacy_present_precious_A_big.png",
					SmallerPicUrl: "https://ga-album-cdnqn.52tt.com/channel/interaction_intimacy/20201013/img_im_intimacy_present_precious_A.png",
				}})
				b.GetCfgCache().EXPECT().GetItemWeightCacheMap().Return(map[uint32]map[uint32]uint32{1: {2333: 6}})
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := newBackpackItemTest(t)
			if tt.initFunc != nil {
				tt.initFunc(b)
			}
			gotItemList, gotLastGainItemTs, gotItemDetailList, err := b.GetImBackpack(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetImBackpack() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotItemList, tt.wantItemList) {
				t.Errorf("GetImBackpack() gotItemList = %v, want %v", gotItemList, tt.wantItemList)
			}
			if gotLastGainItemTs != tt.wantLastGainItemTs {
				t.Errorf("GetImBackpack() gotLastGainItemTs = %v, want %v", gotLastGainItemTs, tt.wantLastGainItemTs)
			}
			if !reflect.DeepEqual(gotItemDetailList, tt.wantItemDetailList) {
				t.Errorf("GetImBackpack() gotItemDetailList = %v, want %v", gotItemDetailList, tt.wantItemDetailList)
			}
		})
	}
}

func Test_backpackItem_GetUserFragment(t *testing.T) {
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name               string
		args               args
		wantItemList       []*backpack.UserPackageItem
		wantLastGainItemTs int64
		wantItemDetailList []*backpack.UserPackageItem
		wantErr            bool
		initFunc           func(b *backpackItemForTest)
	}{
		{
			name: "测试获取用户带有碎片的背包信息",
			args: args{
				ctx: context.Background(),
				uid: 123456,
			},
			wantItemList: []*backpack.UserPackageItem{
				{
					ItemType:   5,
					UserItemId: 5,
					ItemCount:  2,
					FinTime:    1692770516,
					PresentId:  0,
					Card: &backpack.FuncCardCfg{
						CardId:    2,
						CardType:  5,
						CardName:  "财富卡",
						CardUrl:   "http://caifuka",
						CardTimes: 1,
						ValidTime: 86400,
						CardDesc:  "测试财富卡",
						IsDel:     0,
						CardValue: 23333,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   2,
					UserItemId: 2,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Card: &backpack.FuncCardCfg{
						CardId:    6,
						CardType:  2,
						CardName:  "财富经验加速卡2",
						CardUrl:   "http://caifujingyanjiasuka2",
						CardTimes: 2,
						ValidTime: 678000,
						CardDesc:  "测试财富经验加速卡2",
						IsDel:     0,
						CardValue: 777,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   2,
					UserItemId: 25,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Card: &backpack.FuncCardCfg{
						CardId:    1,
						CardType:  2,
						CardName:  "财富经验加速卡",
						CardUrl:   "http://caifujingyanjiasuka",
						CardTimes: 1,
						ValidTime: 1440000,
						CardDesc:  "测试财富经验加速卡",
						IsDel:     0,
						CardValue: 666,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   3,
					UserItemId: 3,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Card: &backpack.FuncCardCfg{
						CardId:    7,
						CardType:  3,
						CardName:  "魅力经验加速卡",
						CardUrl:   "http://meilijingyanjiasuka",
						CardTimes: 3,
						ValidTime: 998800,
						CardDesc:  "测试魅力经验加速卡",
						IsDel:     0,
						CardValue: 888,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 6,
					ItemCount:  12,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        7,
						FragmentType:      3,
						FragmentName:      "声望星钻",
						FragmentDesc:      "测试声望星钻碎片",
						FragmentUrl:       "http://shengwangxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 7,
					ItemCount:  13,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        8,
						FragmentType:      3,
						FragmentName:      "荣耀星钻",
						FragmentDesc:      "测试荣耀星钻碎片",
						FragmentUrl:       "http://rongyaoxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 4,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        1,
						FragmentType:      1,
						FragmentName:      "碎片",
						FragmentDesc:      "测试碎片",
						FragmentUrl:       "http://suipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   1,
					UserItemId: 24,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  2347,
					BackupText: "测试文案",
				},
				{
					ItemType:   1,
					UserItemId: 1,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  233,
					BackupText: "测试文案",
				},
				{
					ItemType:   1,
					UserItemId: 23,
					ItemCount:  2,
					FinTime:    1692770515,
					PresentId:  2345,
					BackupText: "测试文案",
				},
			},
			wantLastGainItemTs: 23333,
			wantItemDetailList: []*backpack.UserPackageItem{
				{
					ItemType:   1,
					UserItemId: 1,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  233,
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 4,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        1,
						FragmentType:      1,
						FragmentName:      "碎片",
						FragmentDesc:      "测试碎片",
						FragmentUrl:       "http://suipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 6,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        7,
						FragmentType:      3,
						FragmentName:      "声望星钻",
						FragmentDesc:      "测试声望星钻碎片",
						FragmentUrl:       "http://shengwangxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 7,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        8,
						FragmentType:      3,
						FragmentName:      "荣耀星钻",
						FragmentDesc:      "测试荣耀星钻碎片",
						FragmentUrl:       "http://rongyaoxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 8,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        7,
						FragmentType:      3,
						FragmentName:      "声望星钻",
						FragmentDesc:      "测试声望星钻碎片",
						FragmentUrl:       "http://shengwangxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 9,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        8,
						FragmentType:      3,
						FragmentName:      "荣耀星钻",
						FragmentDesc:      "测试荣耀星钻碎片",
						FragmentUrl:       "http://rongyaoxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 10,
					ItemCount:  10,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        7,
						FragmentType:      3,
						FragmentName:      "声望星钻",
						FragmentDesc:      "测试声望星钻碎片",
						FragmentUrl:       "http://shengwangxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   4,
					UserItemId: 11,
					ItemCount:  11,
					FinTime:    1692770516,
					PresentId:  0,
					Fragment: &backpack.FragmentCfg{
						FragmentId:        8,
						FragmentType:      3,
						FragmentName:      "荣耀星钻",
						FragmentDesc:      "测试荣耀星钻碎片",
						FragmentUrl:       "http://rongyaoxingzuansuipian",
						IsDel:             0,
						FragmentPrice:     0,
						FragmentPriceType: 2,
						IsShowExpireHint:  10,
					},
					BackupText: "测试文案",
				},
				{
					ItemType:   1,
					UserItemId: 22,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  2345,
					BackupText: "测试文案",
				},
				{
					ItemType:   1,
					UserItemId: 23,
					ItemCount:  1,
					FinTime:    1692770515,
					PresentId:  2345,
					BackupText: "测试文案",
				},
				{
					ItemType:   1,
					UserItemId: 24,
					ItemCount:  1,
					FinTime:    1692770516,
					PresentId:  2347,
					BackupText: "测试文案",
				},
			},
			wantErr: false,
			initFunc: func(b *backpackItemForTest) {
				b.GetBackpackCli().EXPECT().GetUserBackpack(gomock.Any(), uint32(123456)).Return(&backpackBasePb.GetUserBackpackResp{
					UserItemList: []*backpackBasePb.UserBackpackItem{
						{
							ItemType:       1,
							UserItemId:     1,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       233,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       2,
							UserItemId:     2,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       6,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       3,
							UserItemId:     3,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       7,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       4,
							UserItemId:     4,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       1,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       5,
							UserItemId:     5,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       2,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       4,
							UserItemId:     6,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       7,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     3,
							FinalItemCount: 0,
						},
						{
							ItemType:       4,
							UserItemId:     7,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       8,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     3,
							FinalItemCount: 0,
						},
						{
							ItemType:       4,
							UserItemId:     8,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       7,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     3,
							FinalItemCount: 0,
						},
						{
							ItemType:       4,
							UserItemId:     9,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       8,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     3,
							FinalItemCount: 0,
						},
						{
							ItemType:       4,
							UserItemId:     10,
							ItemCount:      10,
							FinTime:        1692770516,
							SourceId:       7,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     3,
							FinalItemCount: 0,
						},
						{
							ItemType:       4,
							UserItemId:     11,
							ItemCount:      11,
							FinTime:        1692770516,
							SourceId:       8,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     3,
							FinalItemCount: 0,
						},
						{
							ItemType:       1,
							UserItemId:     22,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       2345,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       1,
							UserItemId:     23,
							ItemCount:      1,
							FinTime:        1692770515,
							SourceId:       2345,
							Weight:         0,
							ObtainTime:     1692770515,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       1,
							UserItemId:     24,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       2347,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       2,
							UserItemId:     25,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       1,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
						{
							ItemType:       5,
							UserItemId:     26,
							ItemCount:      1,
							FinTime:        1692770516,
							SourceId:       2,
							Weight:         0,
							ObtainTime:     1692770516,
							SourceType:     1,
							FinalItemCount: 0,
						},
					},
					LastObtainTs: 23333,
				}, nil)
				b.GetDyConf().EXPECT().GetBackupText().Return("测试文案")
				b.GetCfgCache().EXPECT().GetFragmentCfgCacheMap().Return(map[uint32]*backpackBasePb.LotteryFragmentCfg{
					1: {
						FragmentId:       1,
						FragmentType:     1,
						FragmentName:     "碎片",
						FragmentDesc:     "测试碎片",
						FragmentUrl:      "http://suipian",
						IsDel:            0,
						FragmentPrice:    2,
						IsShowExpireHint: 10,
					},
					7: {
						FragmentId:       7,
						FragmentType:     3,
						FragmentName:     "声望星钻",
						FragmentDesc:     "测试声望星钻碎片",
						FragmentUrl:      "http://shengwangxingzuansuipian",
						IsDel:            0,
						FragmentPrice:    0,
						IsShowExpireHint: 10,
					},
					8: {
						FragmentId:       8,
						FragmentType:     3,
						FragmentName:     "荣耀星钻",
						FragmentDesc:     "测试荣耀星钻碎片",
						FragmentUrl:      "http://rongyaoxingzuansuipian",
						IsDel:            0,
						FragmentPrice:    1,
						IsShowExpireHint: 10,
					},
				}).AnyTimes()
				b.GetDyConf().EXPECT().GetDisplayPriceFragmentIdMap().Return(map[uint32]bool{}).AnyTimes()
				b.GetFuncCard().EXPECT().GetUserFuncCardCfgMapWithList(gomock.Any(), uint32(123456), gomock.Any(), true).Return(map[uint32]*backpack.FuncCardCfg{
					1: {
						CardId:    1,
						CardType:  2,
						CardName:  "财富经验加速卡",
						CardUrl:   "http://caifujingyanjiasuka",
						CardTimes: 1,
						ValidTime: 1440000,
						CardDesc:  "测试财富经验加速卡",
						IsDel:     0,
						CardValue: 666,
					},
					2: {
						CardId:    2,
						CardType:  5,
						CardName:  "财富卡",
						CardUrl:   "http://caifuka",
						CardTimes: 1,
						ValidTime: 86400,
						CardDesc:  "测试财富卡",
						IsDel:     0,
						CardValue: 23333,
					},
					6: {
						CardId:    6,
						CardType:  2,
						CardName:  "财富经验加速卡2",
						CardUrl:   "http://caifujingyanjiasuka2",
						CardTimes: 2,
						ValidTime: 678000,
						CardDesc:  "测试财富经验加速卡2",
						IsDel:     0,
						CardValue: 777,
					},
					7: {
						CardId:    7,
						CardType:  3,
						CardName:  "魅力经验加速卡",
						CardUrl:   "http://meilijingyanjiasuka",
						CardTimes: 3,
						ValidTime: 998800,
						CardDesc:  "测试魅力经验加速卡",
						IsDel:     0,
						CardValue: 888,
					},
				}, nil)
				b.GetCfgCache().EXPECT().GetItemWeightCacheMap().Return(map[uint32]map[uint32]uint32{
					1: {
						2347: 6,
						233:  1,
					},
					2: {
						1: 3,
						6: 4,
					},
					3: {
						7: 2,
					},
					4: {
						7: 2,
						8: 1,
					},
					5: {
						66: 6,
					},
				}).AnyTimes()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			b := newBackpackItemTest(t)
			if tt.initFunc != nil {
				tt.initFunc(b)
			}
			gotItemList, gotLastGainItemTs, gotItemDetailList, err := b.GetUserFragment(tt.args.ctx, tt.args.uid)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUserFragment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotItemList, tt.wantItemList) {
				gotItemListJson, _ := json.Marshal(gotItemList)
				wantItemListJson, _ := json.Marshal(tt.wantItemList)
				t.Errorf("GetUserFragment() gotItemList = %v, want %v", string(gotItemListJson), string(wantItemListJson))
			}
			if gotLastGainItemTs != tt.wantLastGainItemTs {
				t.Errorf("GetUserFragment() gotLastGainItemTs = %v, want %v", gotLastGainItemTs, tt.wantLastGainItemTs)
			}
			if !reflect.DeepEqual(gotItemDetailList, tt.wantItemDetailList) {
				gotItemDetailListJson, _ := json.Marshal(gotItemDetailList)
				wantItemDetailListJson, _ := json.Marshal(tt.wantItemDetailList)
				t.Errorf("GetUserFragment() gotItemDetailList = %v, want %v", string(gotItemDetailListJson), string(wantItemDetailListJson))
			}
		})
	}
}
