package server

import (
	"context"
	"golang.52tt.com/services/anchor-level/conf"
	event "golang.52tt.com/services/anchor-level/event"
	"golang.52tt.com/services/anchor-level/report"
	"golang.52tt.com/services/anchor-level/timer"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/anchor-level/manager"
)

type AnchorLevelServer struct {
	mgr      manager.IAnchorLevelMgr
	sub      event.AnchorLevelMonthSub
	timerMgr *timer.TimerManger
}

func NewAnchorLevelServer(ctx context.Context, cfg *conf.StartConfig) (*AnchorLevelServer, error) {
	log.ParseLevel("debug")
	log.Infof("xxx :%v", cfg)
	parseLevel, err := log.ParseLevel(cfg.SConf.LogLevel)
	if err != nil {
		return nil, err
	}
	log.SetLevel(parseLevel)

	if err != nil {
		log.Fatalln(err)
		return nil, err
	}

	reporter := report.NewFeiShuReporterV2(cfg.WarnFeishuUrl, cfg.Env, cfg.Push)

	mgr, err := manager.NewAnchorLevelMgr(cfg, reporter)
	if err != nil {
		log.Fatalln(err)
		return nil, err
	}
	sub, err := event.NewAnchorLevelMonthSub(cfg, mgr)
	timerMgr := timer.NewTimerManger(ctx, mgr, reporter)

	svr := &AnchorLevelServer{mgr: mgr, sub: *sub, timerMgr: timerMgr}

	return svr, nil
}

func (s *AnchorLevelServer) ShutDown() {
	s.mgr.ShutDown()
	s.sub.Close()
	s.timerMgr.ShutDown()
}
