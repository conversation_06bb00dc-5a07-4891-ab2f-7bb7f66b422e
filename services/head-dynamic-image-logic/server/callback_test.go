package server

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/clients/avatar"
	censoringproxyCli "golang.52tt.com/clients/censoring-proxy"
	mockaccount "golang.52tt.com/clients/mocks/account"
	mockPushV2 "golang.52tt.com/clients/mocks/push-notification/v2"
	mockseq "golang.52tt.com/clients/mocks/seqgen/v2"
	mockUgcfriendship "golang.52tt.com/clients/mocks/ugc/friendship"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/clients/ugc/friendship"
	user_privilege "golang.52tt.com/clients/user-privilege"
	pb "golang.52tt.com/protocol/app/headdynamicimagelogic"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	friendshippb "golang.52tt.com/protocol/services/ugc/friendship"
	"golang.52tt.com/services/head-dynamic-image-logic/cache"
	"golang.52tt.com/services/head-dynamic-image-logic/conf"
	"golang.52tt.com/services/head-dynamic-image-logic/event"
	"golang.52tt.com/services/head-dynamic-image-logic/model"
)

func TestHeadDynamicImageLogic_handleAuditResult(t *testing.T) {

	type fields struct {
		sc                *conf.ServiceConfigT
		kafkasub          *event.SCybrosArbiterCallbackSub
		store             model.IStore
		cacheCli          cache.ICache
		dyconfig          conf.ISDyConfigHandler
		avatatHost        string
		CensoringProxyCli censoringproxyCli.IClient
		AvatarCli         avatar.IClient
		UserPrivilegeCli  user_privilege.IClient
		PushV2Cli         PushNotification.IClient
		UgcfriendshipCli  friendship.IClient
		AccountCli        account.IClient
		SeqClient         seqgen.IClient
	}
	type args struct {
		uid         uint32
		account     string
		version     string
		auditStatus pb.HeadImageSuggestion
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HeadDynamicImageLogic{
				sc:                tt.fields.sc,
				kafkasub:          tt.fields.kafkasub,
				store:             tt.fields.store,
				cacheCli:          tt.fields.cacheCli,
				dyconfig:          tt.fields.dyconfig,
				avatatHost:        tt.fields.avatatHost,
				CensoringProxyCli: tt.fields.CensoringProxyCli,
				AvatarCli:         tt.fields.AvatarCli,
				UserPrivilegeCli:  tt.fields.UserPrivilegeCli,
				PushV2Cli:         tt.fields.PushV2Cli,
				UgcfriendshipCli:  tt.fields.UgcfriendshipCli,
				AccountCli:        tt.fields.AccountCli,
				SeqClient:         tt.fields.SeqClient,
			}
			s.handleAuditResult(tt.args.uid, tt.args.account, tt.args.version, tt.args.auditStatus)
		})
	}
}

func TestHeadDynamicImageLogic_notifyHeadImageUpdated(t *testing.T) {
	type fields struct {
		sc                *conf.ServiceConfigT
		kafkasub          *event.SCybrosArbiterCallbackSub
		store             model.IStore
		cacheCli          cache.ICache
		dyconfig          conf.ISDyConfigHandler
		avatatHost        string
		CensoringProxyCli censoringproxyCli.IClient
		AvatarCli         avatar.IClient
		UserPrivilegeCli  user_privilege.IClient
		PushV2Cli         PushNotification.IClient
		UgcfriendshipCli  friendship.IClient
		AccountCli        account.IClient
		SeqClient         seqgen.IClient
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HeadDynamicImageLogic{
				sc:                tt.fields.sc,
				kafkasub:          tt.fields.kafkasub,
				store:             tt.fields.store,
				cacheCli:          tt.fields.cacheCli,
				dyconfig:          tt.fields.dyconfig,
				avatatHost:        tt.fields.avatatHost,
				CensoringProxyCli: tt.fields.CensoringProxyCli,
				AvatarCli:         tt.fields.AvatarCli,
				UserPrivilegeCli:  tt.fields.UserPrivilegeCli,
				PushV2Cli:         tt.fields.PushV2Cli,
				UgcfriendshipCli:  tt.fields.UgcfriendshipCli,
				AccountCli:        tt.fields.AccountCli,
				SeqClient:         tt.fields.SeqClient,
			}
			s.notifyHeadImageUpdated(tt.args.uid)
		})
	}
}

// go test -timeout 30s -run ^TestHeadDynamicImageLogic_notifyAllMyFriend$ golang.52tt.com/services/head-dynamic-image-logic/server -v -count=1
func TestHeadDynamicImageLogic_notifyAllMyFriend(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	mockseqcli := mockseq.NewMockIClient(ctl)
	mockUgcfriendshipCli := mockUgcfriendship.NewMockIClient(ctl)
	mockPushV2Cli := mockPushV2.NewMockIClient(ctl)
	mockacccli := mockaccount.NewMockIClient(ctl)
	seq := uint64(1)
	friendsResp := []*friendshippb.FriendInfo{{FriendUid: 1}, {FriendUid: 2}}
	usermp := map[uint32]*accountPB.UserResp{1: {}, 2: {}}
	targetFriendInfo := &friendshippb.FriendInfo{FriendUid: 1}
	gomock.InOrder(
		//
		mockUgcfriendshipCli.EXPECT().GetAllFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(friendsResp, nil),

		mockacccli.EXPECT().GetUsersMap(gomock.Any(), gomock.Any()).Return(usermp, nil),
		// GetOneFriendInfo
		mockUgcfriendshipCli.EXPECT().GetOneFriendInfo(gomock.Any(), gomock.Any(), gomock.Any()).Return(targetFriendInfo, nil),
		mockseqcli.EXPECT().GenerateSequence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(seq, nil),
		mockUgcfriendshipCli.EXPECT().UpdateMultiFriendInfo(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockPushV2Cli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		sc                *conf.ServiceConfigT
		kafkasub          *event.SCybrosArbiterCallbackSub
		store             model.IStore
		cacheCli          cache.ICache
		dyconfig          conf.ISDyConfigHandler
		avatatHost        string
		CensoringProxyCli censoringproxyCli.IClient
		AvatarCli         avatar.IClient
		UserPrivilegeCli  user_privilege.IClient
		PushV2Cli         PushNotification.IClient
		UgcfriendshipCli  friendship.IClient
		AccountCli        account.IClient
		SeqClient         seqgen.IClient
	}
	type args struct {
		myUid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{myUid: 1},
			fields: fields{
				UgcfriendshipCli: mockUgcfriendshipCli,
				AccountCli:       mockacccli,
				PushV2Cli:        mockPushV2Cli,
				SeqClient:        mockseqcli,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HeadDynamicImageLogic{
				sc:                tt.fields.sc,
				kafkasub:          tt.fields.kafkasub,
				store:             tt.fields.store,
				cacheCli:          tt.fields.cacheCli,
				dyconfig:          tt.fields.dyconfig,
				avatatHost:        tt.fields.avatatHost,
				CensoringProxyCli: tt.fields.CensoringProxyCli,
				AvatarCli:         tt.fields.AvatarCli,
				UserPrivilegeCli:  tt.fields.UserPrivilegeCli,
				PushV2Cli:         tt.fields.PushV2Cli,
				UgcfriendshipCli:  tt.fields.UgcfriendshipCli,
				AccountCli:        tt.fields.AccountCli,
				SeqClient:         tt.fields.SeqClient,
			}
			s.notifyAllMyFriend(tt.args.myUid)
		})
	}
}

// go test -timeout 30s -run ^TestHeadDynamicImageLogic_editMyInfo$ golang.52tt.com/services/head-dynamic-image-logic/server -v -count=1
func TestHeadDynamicImageLogic_editMyInfo(t *testing.T) {
	ctl := gomock.NewController(t)
	defer ctl.Finish()

	mockseqcli := mockseq.NewMockIClient(ctl)
	mockUgcfriendshipCli := mockUgcfriendship.NewMockIClient(ctl)
	mockPushV2Cli := mockPushV2.NewMockIClient(ctl)
	seq := uint64(1)
	gomock.InOrder(
		mockseqcli.EXPECT().GenerateSequence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(seq, nil),
		mockUgcfriendshipCli.EXPECT().UpdateFriendInfo(gomock.Any(), gomock.Any()).Return(nil, nil),
		mockPushV2Cli.EXPECT().PushToUsers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
	)

	type fields struct {
		sc                *conf.ServiceConfigT
		kafkasub          *event.SCybrosArbiterCallbackSub
		store             model.IStore
		cacheCli          cache.ICache
		dyconfig          conf.ISDyConfigHandler
		avatatHost        string
		CensoringProxyCli censoringproxyCli.IClient
		AvatarCli         avatar.IClient
		UserPrivilegeCli  user_privilege.IClient
		PushV2Cli         PushNotification.IClient
		UgcfriendshipCli  friendship.IClient
		AccountCli        account.IClient
		SeqClient         seqgen.IClient
	}
	type args struct {
		uid uint32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			args: args{},
			fields: fields{
				SeqClient:        mockseqcli,
				UgcfriendshipCli: mockUgcfriendshipCli,
				PushV2Cli:        mockPushV2Cli,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HeadDynamicImageLogic{
				sc:                tt.fields.sc,
				kafkasub:          tt.fields.kafkasub,
				store:             tt.fields.store,
				cacheCli:          tt.fields.cacheCli,
				dyconfig:          tt.fields.dyconfig,
				avatatHost:        tt.fields.avatatHost,
				CensoringProxyCli: tt.fields.CensoringProxyCli,
				AvatarCli:         tt.fields.AvatarCli,
				UserPrivilegeCli:  tt.fields.UserPrivilegeCli,
				PushV2Cli:         tt.fields.PushV2Cli,
				UgcfriendshipCli:  tt.fields.UgcfriendshipCli,
				AccountCli:        tt.fields.AccountCli,
				SeqClient:         tt.fields.SeqClient,
			}
			s.editMyInfo(tt.args.uid)
		})
	}
}

func TestHeadDynamicImageLogic_getViolationnoticeImgBin(t *testing.T) {
	type fields struct {
		sc                *conf.ServiceConfigT
		kafkasub          *event.SCybrosArbiterCallbackSub
		store             model.IStore
		cacheCli          cache.ICache
		dyconfig          conf.ISDyConfigHandler
		avatatHost        string
		CensoringProxyCli censoringproxyCli.IClient
		AvatarCli         avatar.IClient
		UserPrivilegeCli  user_privilege.IClient
		PushV2Cli         PushNotification.IClient
		UgcfriendshipCli  friendship.IClient
		AccountCli        account.IClient
		SeqClient         seqgen.IClient
	}
	tests := []struct {
		name    string
		fields  fields
		want    []byte
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HeadDynamicImageLogic{
				sc:                tt.fields.sc,
				kafkasub:          tt.fields.kafkasub,
				store:             tt.fields.store,
				cacheCli:          tt.fields.cacheCli,
				dyconfig:          tt.fields.dyconfig,
				avatatHost:        tt.fields.avatatHost,
				CensoringProxyCli: tt.fields.CensoringProxyCli,
				AvatarCli:         tt.fields.AvatarCli,
				UserPrivilegeCli:  tt.fields.UserPrivilegeCli,
				PushV2Cli:         tt.fields.PushV2Cli,
				UgcfriendshipCli:  tt.fields.UgcfriendshipCli,
				AccountCli:        tt.fields.AccountCli,
				SeqClient:         tt.fields.SeqClient,
			}
			got, err := s.getViolationnoticeImgBin()
			if (err != nil) != tt.wantErr {
				t.Errorf("HeadDynamicImageLogic.getViolationnoticeImgBin() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("HeadDynamicImageLogic.getViolationnoticeImgBin() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestHeadDynamicImageLogic_SendIMMsg(t *testing.T) {
	type fields struct {
		sc                *conf.ServiceConfigT
		kafkasub          *event.SCybrosArbiterCallbackSub
		store             model.IStore
		cacheCli          cache.ICache
		dyconfig          conf.ISDyConfigHandler
		avatatHost        string
		CensoringProxyCli censoringproxyCli.IClient
		AvatarCli         avatar.IClient
		UserPrivilegeCli  user_privilege.IClient
		PushV2Cli         PushNotification.IClient
		UgcfriendshipCli  friendship.IClient
		AccountCli        account.IClient
		SeqClient         seqgen.IClient
	}
	type args struct {
		ctx     context.Context
		uid     uint32
		content string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &HeadDynamicImageLogic{
				sc:                tt.fields.sc,
				kafkasub:          tt.fields.kafkasub,
				store:             tt.fields.store,
				cacheCli:          tt.fields.cacheCli,
				dyconfig:          tt.fields.dyconfig,
				avatatHost:        tt.fields.avatatHost,
				CensoringProxyCli: tt.fields.CensoringProxyCli,
				AvatarCli:         tt.fields.AvatarCli,
				UserPrivilegeCli:  tt.fields.UserPrivilegeCli,
				PushV2Cli:         tt.fields.PushV2Cli,
				UgcfriendshipCli:  tt.fields.UgcfriendshipCli,
				AccountCli:        tt.fields.AccountCli,
				SeqClient:         tt.fields.SeqClient,
			}
			if err := s.SendIMMsg(tt.args.ctx, tt.args.uid, tt.args.content); (err != nil) != tt.wantErr {
				t.Errorf("HeadDynamicImageLogic.SendIMMsg() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
