-- 赛事PK相关表结构
-- 创建时间: 2024-01-01
-- 版本: v1.0.0

-- 赛事PK配置表
CREATE TABLE IF NOT EXISTS contest_pk_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    contest_id VARCHAR(64) NOT NULL UNIQUE COMMENT '赛事ID',
    contest_name VARCHAR(128) NOT NULL COMMENT '赛事名称',
    activity_service VARCHAR(64) NOT NULL COMMENT '运营活动服务标识',
    anchor_uid_a BIGINT NOT NULL COMMENT '主播A的UID',
    anchor_uid_b BIGINT NOT NULL COMMENT '主播B的UID',
    contest_begin_time BIGINT NOT NULL COMMENT '赛事开始时间戳',
    contest_end_time BIGINT NOT NULL COMMENT '赛事结束时间戳',
    pk_begin_time BIGINT NOT NULL COMMENT 'PK开始时间戳',
    pk_end_time BIGINT NOT NULL COMMENT 'PK结束时间戳',
    allow_extra_time TINYINT(1) DEFAULT 0 COMMENT '是否允许加时',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待开始，1-进行中，2-已结束，3-已取消',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    create_time BIGINT NOT NULL COMMENT '创建时间戳',
    update_time BIGINT NOT NULL COMMENT '更新时间戳',
    
    -- 索引
    INDEX idx_contest_id (contest_id),
    INDEX idx_anchor_uid_a (anchor_uid_a),
    INDEX idx_anchor_uid_b (anchor_uid_b),
    INDEX idx_time_range (pk_begin_time, pk_end_time),
    INDEX idx_activity_service (activity_service),
    INDEX idx_status_time (status, pk_begin_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='赛事PK配置表';

-- 赛事PK结果表
CREATE TABLE IF NOT EXISTS contest_pk_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    contest_id VARCHAR(64) NOT NULL UNIQUE COMMENT '赛事ID',
    winner_uid BIGINT DEFAULT 0 COMMENT '获胜者UID，0表示平局',
    final_score_a INT DEFAULT 0 COMMENT '主播A最终得分',
    final_score_b INT DEFAULT 0 COMMENT '主播B最终得分',
    actual_start_time BIGINT DEFAULT 0 COMMENT '实际开始时间戳',
    actual_end_time BIGINT DEFAULT 0 COMMENT '实际结束时间戳',
    is_extra_time TINYINT(1) DEFAULT 0 COMMENT '是否发生了加时',
    pk_duration INT DEFAULT 0 COMMENT 'PK实际时长（秒）',
    result_status TINYINT DEFAULT 0 COMMENT '结果状态：0-未开始，1-进行中，2-正常结束，3-异常结束',
    create_time BIGINT NOT NULL COMMENT '创建时间戳',
    update_time BIGINT NOT NULL COMMENT '更新时间戳',
    
    -- 索引
    INDEX idx_contest_id (contest_id),
    INDEX idx_winner_uid (winner_uid),
    INDEX idx_actual_end_time (actual_end_time),
    INDEX idx_result_status (result_status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='赛事PK结果表';

-- 赛事PK操作日志表
CREATE TABLE IF NOT EXISTS contest_pk_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    contest_id VARCHAR(64) NOT NULL COMMENT '赛事ID',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-创建，2-更新，3-取消，4-开始，5-结束',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    operation_data TEXT COMMENT '操作数据JSON',
    operation_reason VARCHAR(256) COMMENT '操作原因',
    operation_time BIGINT NOT NULL COMMENT '操作时间戳',
    
    -- 索引
    INDEX idx_contest_id (contest_id),
    INDEX idx_operation_time (operation_time),
    INDEX idx_operator (operator),
    INDEX idx_operation_type (operation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='赛事PK操作日志表';

-- 插入初始化数据（如果需要）
-- INSERT INTO contest_pk_config (...) VALUES (...);

-- 创建视图（如果需要）
-- CREATE VIEW contest_pk_summary AS ...;

-- 权限设置（根据实际需要调整）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON contest_pk_config TO 'channel_live_mgr'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON contest_pk_result TO 'channel_live_mgr'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON contest_pk_operation_log TO 'channel_live_mgr'@'%';
