-- 指定PK索引优化脚本
-- 执行前请先备份数据库，并在测试环境验证

-- 1. 查看当前表结构和索引
-- SHOW CREATE TABLE appoint_pk_info;
-- SHOW CREATE TABLE appoint_pk_rival_info;
-- SHOW INDEX FROM appoint_pk_info;
-- SHOW INDEX FROM appoint_pk_rival_info;

-- 2. 添加复合索引优化查询性能

-- 2.1 appoint_pk_info表索引优化
-- 优化时间范围查询：GetNeedProcAppointPkList
-- 原查询：WHERE begin_ts <= ? AND end_ts >= ?
CREATE INDEX idx_appoint_pk_time_range ON appoint_pk_info(begin_ts, end_ts) 
COMMENT '优化时间范围查询性能';

-- 优化用户相关查询：GetAppointPkInfoList, GetAnchorValidAppointPkList
-- 原查询：WHERE uid = ? AND begin_ts >= ? AND end_ts <= ?
CREATE INDEX idx_appoint_pk_uid_time ON appoint_pk_info(uid, begin_ts, end_ts) 
COMMENT '优化用户时间范围查询';

-- 优化用户结束时间查询：GetAnchorValidAppointPkList
-- 原查询：WHERE uid = ? AND end_ts >= ?
CREATE INDEX idx_appoint_pk_uid_endtime ON appoint_pk_info(uid, end_ts) 
COMMENT '优化用户有效PK查询';

-- 优化主键查询性能
-- 原查询：WHERE appoint_id = ?
-- 主键已有索引，但添加覆盖索引减少回表
CREATE INDEX idx_appoint_pk_id_cover ON appoint_pk_info(appoint_id, uid, begin_ts, end_ts) 
COMMENT '覆盖索引减少回表查询';

-- 2.2 appoint_pk_rival_info表索引优化
-- 优化对手信息查询：GetAppointPkRivalListByIds
-- 原查询：WHERE appoint_id IN(...)
CREATE INDEX idx_rival_appoint_id ON appoint_pk_rival_info(appoint_id) 
COMMENT '优化对手信息查询';

-- 优化对手详情查询：GetPkRivalDetailInfoListByUids
-- 原查询：JOIN查询，需要优化JOIN性能
CREATE INDEX idx_rival_uid_begintime ON appoint_pk_rival_info(uid, begin_ts) 
COMMENT '优化对手详情查询';

-- 覆盖索引优化
CREATE INDEX idx_rival_cover ON appoint_pk_rival_info(appoint_id, uid, begin_ts) 
COMMENT '覆盖索引减少回表';

-- 3. 删除可能重复或无用的索引（请根据实际情况调整）
-- 注意：删除索引前请确认没有其他查询依赖

-- 检查是否有重复索引的SQL
-- SELECT 
--   TABLE_NAME,
--   INDEX_NAME,
--   GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) AS COLUMNS
-- FROM INFORMATION_SCHEMA.STATISTICS 
-- WHERE TABLE_SCHEMA = DATABASE() 
--   AND TABLE_NAME IN ('appoint_pk_info', 'appoint_pk_rival_info')
-- GROUP BY TABLE_NAME, INDEX_NAME;

-- 4. 分析表统计信息，确保索引生效
ANALYZE TABLE appoint_pk_info;
ANALYZE TABLE appoint_pk_rival_info;

-- 5. 验证索引效果的查询语句
-- 执行以下EXPLAIN语句验证索引是否生效

-- 验证时间范围查询
-- EXPLAIN SELECT appoint_id, uid, begin_ts, end_ts 
-- FROM appoint_pk_info 
-- WHERE begin_ts <= UNIX_TIMESTAMP() AND end_ts >= UNIX_TIMESTAMP();

-- 验证用户时间查询  
-- EXPLAIN SELECT appoint_id, uid, begin_ts, end_ts 
-- FROM appoint_pk_info 
-- WHERE uid = 12345 AND end_ts >= UNIX_TIMESTAMP();

-- 验证JOIN查询
-- EXPLAIN SELECT a.appoint_id, a.uid, a.begin_ts, a.end_ts, r.uid as rival_uid, r.begin_ts as rival_begin_ts
-- FROM appoint_pk_info a 
-- INNER JOIN appoint_pk_rival_info r ON a.appoint_id = r.appoint_id
-- WHERE r.uid IN (12345, 67890) AND a.end_ts >= UNIX_TIMESTAMP();

-- 6. 监控索引使用情况
-- 定期检查索引使用统计
-- SELECT 
--   OBJECT_SCHEMA,
--   OBJECT_NAME,
--   INDEX_NAME,
--   COUNT_FETCH,
--   COUNT_INSERT,
--   COUNT_UPDATE,
--   COUNT_DELETE
-- FROM performance_schema.table_io_waits_summary_by_index_usage
-- WHERE OBJECT_SCHEMA = DATABASE() 
--   AND OBJECT_NAME IN ('appoint_pk_info', 'appoint_pk_rival_info')
-- ORDER BY COUNT_FETCH DESC;

-- 7. 索引维护建议
-- 7.1 定期重建索引（可选，根据数据变化频率决定）
-- ALTER TABLE appoint_pk_info ENGINE=InnoDB;
-- ALTER TABLE appoint_pk_rival_info ENGINE=InnoDB;

-- 7.2 监控索引碎片率
-- SELECT 
--   TABLE_SCHEMA,
--   TABLE_NAME,
--   DATA_LENGTH,
--   INDEX_LENGTH,
--   ROUND(INDEX_LENGTH/DATA_LENGTH,2) AS index_ratio
-- FROM information_schema.TABLES 
-- WHERE TABLE_SCHEMA = DATABASE() 
--   AND TABLE_NAME IN ('appoint_pk_info', 'appoint_pk_rival_info');

-- 执行完成后的验证清单：
-- □ 所有索引创建成功
-- □ EXPLAIN显示使用了新索引
-- □ 查询性能有明显提升
-- □ 没有影响写入性能
-- □ 监控指标正常
