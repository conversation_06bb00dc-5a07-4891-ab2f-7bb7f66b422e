# 赛事PK功能需求文档

## 需求背景

### 业务背景
为方便运营活动可以灵活的组织双人PK赛事，需要使得运营活动的服务能够直接配置直播PK，使得两个主播的房间能够在指定时间展开一场PK。

### 业务目标
提高活动组建灵活度，可以做赛事PK的活动，增强平台的运营能力和用户参与度。

### 业务价值
1. **运营灵活性提升**: 运营可以根据活动需要灵活安排主播PK赛事
2. **用户体验优化**: 用户可以观看高质量的预定赛事PK
3. **平台活跃度提升**: 通过赛事PK吸引更多用户参与和观看
4. **商业价值增长**: 赛事PK可以带来更多的礼物消费和用户付费

## 详细需求描述

### 1. 赛事PK配置管理

#### 1.1 创建赛事PK
**需求描述**: 支持运营服务通过接口创建赛事PK配置

**功能要求**:
- 支持配置赛事基本信息：赛事ID、赛事名称、运营活动服务标识
- 支持配置参赛主播：主播A的UID、主播B的UID
- 支持配置时间信息：赛事时间段（开始时间、结束时间）、PK场次时间（PK开始时间、PK结束时间）
- 支持配置PK规则：是否允许加时
- 记录操作人信息，便于追溯

**业务规则**:
- 赛事ID必须全局唯一
- 同一时间段内，同一主播只能参与一场赛事PK
- PK开始时间必须在赛事时间段内
- PK时长不能超过赛事时间段
- 主播A和主播B不能是同一人

#### 1.2 更新赛事PK配置
**需求描述**: 支持运营服务更新已创建的赛事PK配置

**功能要求**:
- 支持更新赛事基本信息和时间配置
- 支持更新PK规则设置
- 只有待开始状态的赛事才能更新
- 更新时需要重新校验时间冲突

#### 1.3 取消赛事PK
**需求描述**: 支持运营服务取消已创建的赛事PK

**功能要求**:
- 支持取消待开始或进行中的赛事
- 取消时需要记录取消原因
- 取消后需要清理相关的限制状态

### 2. 赛事PK查询功能

#### 2.1 单个赛事查询
**需求描述**: 支持根据赛事ID查询具体的赛事PK配置信息

#### 2.2 批量赛事查询
**需求描述**: 支持多维度的批量查询赛事PK配置

**查询维度**:
- 赛事ID列表筛选
- 运营活动服务筛选
- 时间范围筛选（基于PK开始时间）
- 赛事状态筛选（待开始、进行中、已结束、已取消）
- 分页参数（页码、页大小）

### 3. PK限制机制

#### 3.1 常规PK限制
**需求描述**: 在配置的赛事PK开启前10分钟内，赛事参与的双方不能再接受或向其他人发起任何常规的PK

**功能要求**:
- 自动检测机制：在赛事PK开始前10分钟自动设置主播限制状态
- 实时检查机制：在主播发起或接受常规PK时检查是否在限制期内
- 限制解除机制：赛事PK结束后自动解除限制状态

#### 3.2 PK可用性检查
**需求描述**: 提供接口检查主播在指定时间点是否可以参与常规PK

### 4. 时间冲突管理

#### 4.1 时间冲突检测
**需求描述**: 同一时间段内，用户作为主播只能参与在一场赛事PK中

#### 4.2 多活动隔离
**需求描述**: 同一时间段内可以有多个不同的运营活动来组织不同的赛事PK

### 5. 加时控制机制

#### 5.1 加时配置
**需求描述**: 支持通过配置来决定赛事PK是否可以加时

### 6. 自动化执行机制

#### 6.1 自动PK发起
**需求描述**: 到达指定的PK开始时间时，系统自动发起PK匹配

#### 6.2 状态自动更新
**需求描述**: 根据PK进展自动更新赛事状态

### 7. 结果事件发布

#### 7.1 结果事件内容
**需求描述**: 赛事PK的结果需要通过事件发布给运营活动的服务知晓

**事件内容**:
```
- contest_id: 赛事ID
- activity_service: 运营活动服务标识
- anchor_uid_a: 主播A的UID
- anchor_uid_b: 主播B的UID
- winner_uid: 获胜者UID（0表示平局）
- final_score_a: 主播A最终得分
- final_score_b: 主播B最终得分
- pk_begin_time: PK开始时间戳
- pk_end_time: PK结束时间戳
- actual_end_time: 实际结束时间戳
- is_extra_time: 是否发生了加时
```

#### 7.2 事件发布机制
**需求描述**: 通过Kafka消息队列发布赛事结果事件

**技术要求**:
- Topic名称：contest_pk_result
- 消息Key：contest_id
- 消息格式：Protobuf序列化
- 投递保证：至少一次投递

## 接口定义

### 1. 创建赛事PK
```protobuf
rpc CreateContestPk(CreateContestPkReq) returns (CreateContestPkResp);

message CreateContestPkReq {
    ContestPkConfig config = 1;
}

message CreateContestPkResp {
    string contest_id = 1;
}
```

### 2. 更新赛事PK
```protobuf
rpc UpdateContestPk(UpdateContestPkReq) returns (UpdateContestPkResp);
```

### 3. 取消赛事PK
```protobuf
rpc CancelContestPk(CancelContestPkReq) returns (CancelContestPkResp);
```

### 4. 查询赛事PK
```protobuf
rpc GetContestPkConfig(GetContestPkConfigReq) returns (GetContestPkConfigResp);
rpc BatchGetContestPkConfig(BatchGetContestPkConfigReq) returns (BatchGetContestPkConfigResp);
```

### 5. 检查PK可用性
```protobuf
rpc CheckAnchorPkAvailable(CheckAnchorPkAvailableReq) returns (CheckAnchorPkAvailableResp);
```

## 数据模型

### 赛事PK配置表
```sql
CREATE TABLE contest_pk_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    contest_id VARCHAR(64) NOT NULL COMMENT '赛事ID',
    contest_name VARCHAR(128) NOT NULL COMMENT '赛事名称',
    activity_service VARCHAR(64) NOT NULL COMMENT '运营活动服务标识',
    anchor_uid_a BIGINT NOT NULL COMMENT '主播A的UID',
    anchor_uid_b BIGINT NOT NULL COMMENT '主播B的UID',
    contest_begin_time BIGINT NOT NULL COMMENT '赛事开始时间戳',
    contest_end_time BIGINT NOT NULL COMMENT '赛事结束时间戳',
    pk_begin_time BIGINT NOT NULL COMMENT 'PK开始时间戳',
    pk_end_time BIGINT NOT NULL COMMENT 'PK结束时间戳',
    allow_extra_time TINYINT(1) DEFAULT 0 COMMENT '是否允许加时',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待开始，1-进行中，2-已结束，3-已取消',
    winner_uid BIGINT DEFAULT 0 COMMENT '获胜者UID，0表示平局',
    final_score_a INT DEFAULT 0 COMMENT '主播A最终得分',
    final_score_b INT DEFAULT 0 COMMENT '主播B最终得分',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    INDEX idx_contest_id (contest_id),
    INDEX idx_anchor_uid (anchor_uid_a, anchor_uid_b),
    INDEX idx_time_range (pk_begin_time, pk_end_time),
    INDEX idx_activity_service (activity_service)
);
```

## 业务流程

### 1. 赛事PK创建流程
1. 运营服务调用创建接口
2. 校验参数合法性
3. 检查时间冲突
4. 保存配置到数据库
5. 设置缓存
6. 添加定时任务

### 2. 赛事PK执行流程
1. 定时任务扫描待执行赛事
2. 设置主播PK限制（开始前10分钟）
3. 到达开始时间自动发起PK
4. 监控PK进展，更新状态
5. PK结束后发布结果事件
6. 清理限制状态

### 3. PK限制检查流程
1. 主播发起/接受常规PK时触发检查
2. 查询主播是否在赛事PK限制期内
3. 返回检查结果和限制原因
4. 如果被限制，拒绝PK操作

## 非功能性需求

### 1. 性能要求
- 赛事PK配置查询响应时间 < 200ms
- PK可用性检查响应时间 < 100ms
- 定时任务处理延迟 < 30秒

### 2. 可靠性要求
- 赛事PK配置数据需要持久化存储
- 关键操作需要事务保证
- 事件发布支持重试机制

### 3. 可用性要求
- 服务可用性 ≥ 99.9%
- 支持优雅降级
- 缓存失效时可以从数据库恢复

## 风险评估

### 1. 技术风险
- 定时任务可能因为服务重启导致遗漏
- 缓存和数据库数据不一致
- 高并发下的时间冲突检测

### 2. 业务风险
- 主播不在线导致PK无法开始
- 网络问题导致PK中断
- 时间配置错误导致赛事冲突

### 3. 风险应对
- 定时任务支持重启后恢复
- 缓存更新失败时降级到数据库
- 增加主播在线状态检查
- 完善参数校验和错误处理
