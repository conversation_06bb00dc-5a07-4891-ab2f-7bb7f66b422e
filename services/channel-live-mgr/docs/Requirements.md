# channel-live-mgr 需求文档

## 服务概述

channel-live-mgr是直播业务领域的核心Srv服务，负责管理直播房间的完整生命周期、PK功能、数据统计以及主播权限管理等核心业务逻辑。

## 服务定位

- **服务类型**: Srv服务
- **业务领域**: 直播管理
- **服务层级**: 为Logic服务提供直播相关基础能力的底层服务
- **核心职责**: 直播房间管理、PK功能、数据统计、主播权限管理

## 核心业务需求

### 1. 直播房间管理

#### 1.1 直播权限管理
- **需求描述**: 管理主播的直播房开通和回收权限
- **功能要求**:
  - 支持运营人员为主播开通直播权限
  - 支持设置直播权限的有效期（开始时间、结束时间）
  - 支持回收主播的直播权限
  - 支持设置直播房标签ID
  - 支持各种权限位标志管理（PK权限、排麦权限等）

#### 1.2 直播状态管理
- **需求描述**: 管理直播房间的实时状态
- **功能要求**:
  - 支持直播状态切换：开播、暂停、继续、关闭
  - 记录开播时的粉丝数和团粉丝数
  - 维护直播房间的基本信息（描述、麦位信息等）
  - 支持直播心跳机制，监控直播状态

#### 1.3 直播信息维护
- **需求描述**: 维护直播房间的基础信息
- **功能要求**:
  - 管理直播房间ID和直播ID的映射关系
  - 支持语音流ID的变化上报
  - 维护麦位信息和语音流信息

### 2. PK功能系统

#### 2.1 PK申请与匹配
- **需求描述**: 处理主播间的PK申请和匹配逻辑
- **功能要求**:
  - 支持主播发起PK申请
  - 支持PK申请的接受、拒绝、取消操作
  - 支持获取PK申请列表
  - 支持多种匹配类型：普通匹配、排位赛匹配、随机匹配、指定匹配

#### 2.2 PK状态管理
- **需求描述**: 管理PK过程中的各个阶段状态
- **功能要求**:
  - 支持PK阶段管理：开局、道具、最后一分钟、惩罚、结束
  - 支持PK分数统计和实时更新
  - 支持PK加时赛机制（防偷塔玩法）
  - 支持PK中的语音开关控制

#### 2.3 指定PK功能
- **需求描述**: 支持运营配置的指定PK活动
- **功能要求**:
  - 支持添加、更新、删除指定PK信息
  - 支持指定PK的邀约和应战机制
  - 支持指定PK的时间管理和倒计时

#### 2.4 PK道具系统
- **需求描述**: 管理PK过程中的道具使用
- **功能要求**:
  - 支持氛围道具、积分道具、百分比道具等多种道具类型
  - 支持道具配置的版本管理
  - 支持首杀机制和神秘人信息记录

### 3. 数据统计系统

#### 3.1 实时数据统计
- **需求描述**: 统计直播过程中的实时数据
- **功能要求**:
  - 统计观众数量和进房人数
  - 统计送礼流水和付费人数
  - 统计观看时长
  - 统计粉丝增长数据

#### 3.2 历史数据管理
- **需求描述**: 管理直播的历史记录和统计数据
- **功能要求**:
  - 记录直播历史记录（开始时间、结束时间、时长）
  - 统计主播的总流水数据
  - 统计主播的有效直播天数
  - 支持按时间范围查询历史数据

#### 3.3 排行榜功能
- **需求描述**: 提供各种维度的排行榜数据
- **功能要求**:
  - 直播送礼排行榜
  - PK送礼排行榜
  - 观看时长排行榜
  - 支持神秘人信息在排行榜中的展示

### 4. 主播管理系统

#### 4.1 主播信息管理
- **需求描述**: 管理主播的基础信息和状态
- **功能要求**:
  - 支持主播信息的查询和搜索
  - 支持分页获取主播列表
  - 支持批量获取主播信息
  - 维护主播的直播状态和PK状态

#### 4.2 主播积分系统
- **需求描述**: 管理主播的积分体系
- **功能要求**:
  - 支持积分的增加和扣除操作
  - 支持多种积分来源：时长任务、流水任务、提现扣除、运营发放等
  - 维护完整的积分变动流水记录
  - 支持积分的查询和统计

#### 4.3 主播黑名单管理
- **需求描述**: 管理主播黑名单功能
- **功能要求**:
  - 支持添加主播到黑名单
  - 支持从黑名单中移除主播
  - 支持查询主播是否在黑名单中
  - 支持分页获取黑名单列表

### 5. 权限管理系统

#### 5.1 直播权限控制
- **需求描述**: 控制主播的各种直播权限
- **功能要求**:
  - 支持PK权限的开通和关闭
  - 支持排麦权限的管理
  - 支持权限的批量设置

#### 5.2 推送限制管理
- **需求描述**: 管理推送消息的频率限制
- **功能要求**:
  - 支持每日推送次数限制
  - 支持用户推送白名单管理

### 6. 事件处理需求

#### 6.1 Kafka事件消费
- **需求描述**: 处理来自其他服务的业务事件
- **功能要求**:
  - 消费送礼事件，更新直播流水和PK分数
  - 消费进房/退房事件，更新观众统计
  - 消费麦位事件，处理PK相关逻辑
  - 消费骑士团事件，处理相关业务逻辑

#### 6.2 事件发布
- **需求描述**: 向其他服务发布业务事件
- **功能要求**:
  - 发布直播状态变化事件
  - 发布PK申请和状态变化事件

## 数据一致性需求

### 缓存与数据库一致性
- Redis缓存作为主要数据源，提供高性能访问
- MySQL作为持久化存储，保证数据可靠性
- 需要保证缓存和数据库的数据一致性

### 分布式事务处理
- 涉及多个服务调用时，需要保证数据一致性
- PK匹配和状态变更需要原子性操作

## 性能需求

### 高并发支持
- 支持大量用户同时观看直播
- 支持高频次的心跳请求
- 支持实时的数据统计更新

### 响应时间要求
- 直播状态查询：< 100ms
- PK申请处理：< 200ms
- 数据统计查询：< 500ms

## 可靠性需求

### 服务可用性
- 服务可用性要求：99.9%
- 支持服务的平滑重启和升级

### 数据可靠性
- 重要业务数据需要持久化存储
- 支持数据备份和恢复机制
