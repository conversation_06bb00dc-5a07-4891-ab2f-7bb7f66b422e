# 赛事PK功能实现说明

## 文件结构

### 1. 协议定义
- `proto/channel-live-mgr.proto` - 赛事PK相关的protobuf定义

### 2. 服务接口层
- `server/contest_pk_server.go` - 赛事PK gRPC接口实现
- `server/contest_pk_validator.go` - 请求参数校验方法
- `server/server.go` - 主服务文件（已添加contestPkManager字段）

### 3. 业务逻辑层
- `manager/contest_pk_manager.go` - 赛事PK核心管理器
- `manager/contest_pk_validator.go` - 业务规则校验器
- `manager/contest_pk_event_publisher.go` - 事件发布器
- `manager/contest_pk_timer.go` - 定时任务处理器

### 4. 数据存储层
- `mysql/contest_pk_store.go` - 赛事PK配置数据存储
- `mysql/contest_pk_result_store.go` - 赛事PK结果数据存储
- `cache/contest_pk_cache.go` - 缓存层实现

### 5. 数据库脚本
- `sql/contest_pk_tables.sql` - 数据库表结构

### 6. 测试代码
- `manager/contest_pk_manager_test.go` - 单元测试示例

## 接口分类

### 配置管理接口
- `CreateContestPk` - 创建赛事PK
- `UpdateContestPk` - 更新赛事PK配置
- `CancelContestPk` - 取消赛事PK
- `GetContestPkConfig` - 获取赛事PK配置
- `BatchGetContestPkConfig` - 批量获取赛事PK配置

### 查询接口
- `GetContestPkInfo` - 获取完整信息（配置+结果）
- `BatchGetContestPkInfo` - 批量获取完整信息
- `CheckAnchorPkAvailable` - 检查主播PK可用性

### 结果管理接口
- `StartContestPk` - 开始赛事PK
- `UpdateContestPkResult` - 更新赛事PK结果（实时更新分数）
- `FinishContestPk` - 结束赛事PK
- `GetContestPkResult` - 获取赛事PK结果

## 数据模型

### ContestPkConfig（配置信息）
- 赛事基本信息：ID、名称、活动服务标识
- 参赛主播：主播A和主播B的UID
- 时间配置：赛事时间段、PK时间段
- 规则配置：是否允许加时
- 状态信息：待开始、进行中、已结束、已取消

### ContestPkResult（结果信息）
- 结果数据：获胜者、最终分数
- 时间信息：实际开始时间、实际结束时间、PK时长
- 状态信息：未开始、进行中、正常结束、异常结束
- 扩展信息：是否加时等

### ContestPkInfo（完整信息）
- 包含配置信息和结果信息
- 支持选择性包含结果信息

## 业务流程

### 1. 创建阶段
1. 运营服务调用`CreateContestPk`创建赛事配置
2. 系统校验参数合法性和时间冲突
3. 保存配置到数据库和缓存
4. 添加定时任务（限制、开始、结束检查）

### 2. 限制阶段
1. 定时任务在开始前10分钟触发
2. 设置主播PK限制状态到缓存
3. 常规PK申请时检查限制状态

### 3. 执行阶段
1. 调用`StartContestPk`开始赛事（手动或自动）
2. 创建结果记录，状态设为进行中
3. 可通过`UpdateContestPkResult`实时更新分数
4. 调用`FinishContestPk`结束赛事

### 4. 结束阶段
1. 更新最终结果和状态
2. 发布结果事件给运营服务
3. 清理主播限制状态

## 集成说明

### 1. 服务初始化
在`server/server.go`的`NewChannelLiveMgrServer`函数中：
```go
// 初始化赛事PK管理器
contestPkManager := manager.NewContestPkManager(cacheClient, mysqlStore, channelLiveProd)

// 添加到服务结构体
server := &ChannelLiveMgrServer{
    // ... 其他字段
    contestPkManager: contestPkManager,
}
```

### 2. 与现有PK系统集成
在现有的PK申请逻辑中增加赛事PK限制检查：
```go
// 检查主播是否在赛事PK限制期内
available, err := s.contestPkManager.CheckAnchorPkAvailable(ctx, anchorUid, time.Now().Unix())
if !available.Available {
    return fmt.Errorf("主播在赛事PK限制期内: %s", available.Reason)
}
```

### 3. 定时任务启动
在服务启动时启动赛事PK定时任务：
```go
contestPkTimer := manager.NewContestPkTimer(cacheClient, mysqlStore, contestPkManager)
contestPkTimer.Start()
```

## 配置要求

### 1. 数据库配置
需要执行`sql/contest_pk_tables.sql`创建相关表。

### 2. Kafka配置
需要配置赛事结果事件的Topic：
```json
{
  "contest_pk_result_topic": "contest_pk_result"
}
```

### 3. 缓存配置
使用现有的Redis配置，新增赛事PK相关的缓存键。

## 监控和告警

### 1. 业务指标
- 赛事PK创建数量和成功率
- 自动开始成功率
- 事件发布成功率
- 定时任务执行情况

### 2. 性能指标
- 接口响应时间
- 数据库查询性能
- 缓存命中率

### 3. 错误监控
- 接口调用错误
- 数据库操作错误
- 事件发布失败
- 定时任务异常

## 测试建议

### 1. 单元测试
- 核心业务逻辑测试
- 参数校验测试
- 边界条件测试

### 2. 集成测试
- 完整业务流程测试
- 与现有系统集成测试
- 并发场景测试

### 3. 性能测试
- 接口并发性能测试
- 大量数据查询测试
- 缓存性能测试

## 部署注意事项

1. **数据库迁移**：先执行数据库脚本创建表结构
2. **配置更新**：更新服务配置文件
3. **代码部署**：部署新版本代码
4. **功能验证**：验证接口功能正常
5. **监控检查**：确认监控和告警正常工作
