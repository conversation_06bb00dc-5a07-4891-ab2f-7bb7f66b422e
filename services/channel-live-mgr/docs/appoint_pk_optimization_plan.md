# 指定PK模块优化方案

## 🔍 现状问题分析

### 1. 数据查询问题

#### 1.1 N+1查询问题
**问题描述**：
```go
// 在GetAppointPkInfoList中存在严重的N+1查询
for index, pkInfo := range pkInfoList {
    idList = append(idList, pkInfo.AppointId)
    count++
    
    if count == 5 || index == len(pkInfoList)-1 {
        // 每5个ID查询一次，仍然是多次查询
        rivalInfoList, err := s.mysqlStore.GetAppointPkRivalListByIds(ctx, idList)
        // ...
    }
}
```

**影响**：
- 数据库连接池压力大
- 响应时间随数据量线性增长
- 高并发时容易出现数据库连接超时

#### 1.2 缺少索引优化
**问题描述**：
```sql
-- 当前查询缺少复合索引
SELECT appoint_id, uid, begin_ts, end_ts FROM appoint_pk_info 
WHERE begin_ts <= ? AND end_ts >= ?

-- 字符串拼接IN查询，无法使用索引
WHERE appoint_id IN(1,2,3,4,5)
```

**影响**：
- 全表扫描，查询性能差
- 随着数据量增长性能急剧下降

#### 1.3 数据库连接管理问题
**问题描述**：
- 读写未分离，查询压力影响写入性能
- 缺少连接池监控和优化

### 2. 缓存架构问题

#### 2.1 缓存穿透风险
**问题描述**：
```go
// 直接查询缓存，没有防穿透机制
isAlreadyProc, err := s.cacheClient.CheckAppointPkInfoIsExist(pkInfo.AppointId)
if err != nil || isAlreadyProc {
    continue // 错误时直接跳过，可能导致重复处理
}
```

**影响**：
- 大量无效查询直接打到数据库
- 缓存失效时数据库压力激增

#### 2.2 缓存一致性问题
**问题描述**：
- 缓存更新和数据库更新不在同一事务中
- 缓存失效策略不完善
- 多实例环境下缓存同步问题

#### 2.3 缓存设计不合理
**问题描述**：
```go
// 缓存键设计过于简单，容易冲突
func getAppointPkInfoKey(appointId uint32) string {
    return fmt.Sprintf("appointpk_%d", appointId)
}

// 缓存过期时间硬编码，不够灵活
err = c.RedisClient.Set(key, byteData, time.Duration(expireTs)*time.Second).Err()
```

### 3. 定时任务逻辑问题

#### 3.1 固定时间间隔效率低
**问题描述**：
```go
// 所有定时任务使用固定间隔
go s.TimerHandle(60*time.Second, 0, s.TimerLoadNeedProcAppointPkFromMysql)
go s.TimerHandle(1*time.Second, i, s.TimerHandleNeedProcAppointPk)
go s.TimerHandle(2*time.Second, i, s.TimerHandleWaitingAppointPk)
```

**影响**：
- 无负载时仍然频繁执行，浪费资源
- 高负载时处理不及时，延迟增加

#### 3.2 分布式锁粒度过大
**问题描述**：
```go
// 整个处理流程使用一个大锁
getLock := s.cacheClient.GetAppointPkLock("LoadNeedProcFromMysql", 300)
```

**影响**：
- 多实例环境下并发度低
- 锁竞争激烈，处理效率差

#### 3.3 错误处理不完善
**问题描述**：
- 部分错误直接continue，可能导致数据丢失
- 缺少重试机制和降级策略
- 异常恢复能力弱

### 4. 代码质量架构问题

#### 4.1 单一文件过大
**问题描述**：
- `appoint_pk_svr.go` 文件超过1000行
- 业务逻辑、数据访问、缓存操作混合在一起
- 职责不清晰，难以维护

#### 4.2 缺少抽象层
**问题描述**：
- 直接在服务层操作缓存和数据库
- 缺少领域模型和业务规则抽象
- 代码复用性差

#### 4.3 测试覆盖率低
**问题描述**：
- 缺少单元测试
- 集成测试不完善
- 难以验证业务逻辑正确性

## 💡 优化技术方案

### 1. 数据查询优化

#### 1.1 批量查询优化
```go
// 优化方案：真正的批量查询
type AppointPkQueryService struct {
    store *mysql.Store
    cache *cache.ChannelLiveMgrCache
}

func (s *AppointPkQueryService) GetAppointPkInfoListOptimized(ctx context.Context, req *pb.GetAppointPkInfoListReq) (*pb.GetAppointPkInfoListResp, error) {
    // 1. 批量查询主表
    pkInfoList, err := s.store.GetAppointPkInfoList(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // 2. 提取所有ID，一次性查询对手信息
    appointIds := make([]uint32, len(pkInfoList))
    for i, info := range pkInfoList {
        appointIds[i] = info.AppointId
    }
    
    // 3. 一次性批量查询所有对手信息
    rivalInfoList, err := s.store.GetAppointPkRivalListByIds(ctx, appointIds)
    if err != nil {
        return nil, err
    }
    
    // 4. 构建映射关系
    rivalMap := make(map[uint32][]*mysql.AppointPkRivalInfo)
    for _, rival := range rivalInfoList {
        rivalMap[rival.AppointId] = append(rivalMap[rival.AppointId], rival)
    }
    
    // 5. 组装结果
    return s.buildResponse(pkInfoList, rivalMap), nil
}
```

#### 1.2 数据库索引优化
```sql
-- 添加复合索引
CREATE INDEX idx_appoint_pk_time_range ON appoint_pk_info(begin_ts, end_ts, uid);
CREATE INDEX idx_appoint_pk_uid_time ON appoint_pk_info(uid, begin_ts, end_ts);
CREATE INDEX idx_rival_appoint_id ON appoint_pk_rival_info(appoint_id);

-- 优化查询语句
-- 原查询
SELECT appoint_id, uid, begin_ts, end_ts FROM appoint_pk_info WHERE appoint_id IN(1,2,3,4,5)

-- 优化后：使用JOIN
SELECT a.appoint_id, a.uid, a.begin_ts, a.end_ts, r.uid as rival_uid, r.begin_ts as rival_begin_ts
FROM appoint_pk_info a 
LEFT JOIN appoint_pk_rival_info r ON a.appoint_id = r.appoint_id
WHERE a.begin_ts <= ? AND a.end_ts >= ?
ORDER BY a.appoint_id, r.begin_ts;
```

#### 1.3 读写分离
```go
type AppointPkStore struct {
    writeDB    *sqlx.DB
    readDB     *sqlx.DB
    readOnlyDB *sqlx.DB
}

func (s *AppointPkStore) GetAppointPkInfoList(ctx context.Context, req *pb.GetAppointPkInfoListReq) ([]*AppointPkInfo, error) {
    // 查询操作使用只读库
    return s.queryFromReadDB(ctx, req)
}

func (s *AppointPkStore) CreateAppointPkInfo(ctx context.Context, info *AppointPkInfo) error {
    // 写入操作使用主库
    return s.writeToMasterDB(ctx, info)
}
```

### 2. 缓存架构优化

#### 2.1 多级缓存架构
```go
type MultiLevelCache struct {
    l1Cache    *sync.Map          // 本地缓存（最热数据）
    l2Cache    *redis.Client      // Redis缓存（热数据）
    l3Store    *mysql.Store       // 数据库（全量数据）
    
    bloomFilter *bloom.Filter     // 布隆过滤器防穿透
    metrics     *CacheMetrics     // 缓存指标
}

func (c *MultiLevelCache) GetAppointPkInfo(ctx context.Context, appointId uint32) (*pb.AppointPkInfo, error) {
    // L1: 本地缓存
    if data, ok := c.l1Cache.Load(appointId); ok {
        c.metrics.L1Hit()
        return data.(*pb.AppointPkInfo), nil
    }
    
    // 布隆过滤器检查
    if !c.bloomFilter.Test([]byte(fmt.Sprintf("%d", appointId))) {
        c.metrics.BloomFilterBlock()
        return nil, ErrNotFound
    }
    
    // L2: Redis缓存
    if data, err := c.getFromRedis(ctx, appointId); err == nil {
        c.metrics.L2Hit()
        c.l1Cache.Store(appointId, data) // 回填L1
        return data, nil
    }
    
    // L3: 数据库
    data, err := c.l3Store.GetAppointPkInfo(ctx, appointId)
    if err != nil {
        return nil, err
    }
    
    c.metrics.L3Hit()
    // 异步回填缓存
    go c.backfillCache(ctx, appointId, data)
    
    return data, nil
}
```

#### 2.2 缓存一致性保证
```go
type CacheConsistencyManager struct {
    cache       *MultiLevelCache
    eventBus    EventBus
    versionMap  *sync.Map
}

func (c *CacheConsistencyManager) UpdateAppointPkInfo(ctx context.Context, info *pb.AppointPkInfo) error {
    // 1. 数据库事务
    tx, err := c.store.BeginTx(ctx)
    if err != nil {
        return err
    }
    defer tx.Rollback()
    
    // 2. 更新数据库
    if err := c.store.UpdateAppointPkInfo(ctx, tx, info); err != nil {
        return err
    }
    
    // 3. 提交事务
    if err := tx.Commit(); err != nil {
        return err
    }
    
    // 4. 发布缓存失效事件
    c.eventBus.Publish(CacheInvalidateEvent{
        Key:     fmt.Sprintf("appointpk_%d", info.AppointId),
        Version: time.Now().UnixNano(),
    })
    
    return nil
}
```

### 3. 定时任务优化

#### 3.1 自适应定时任务
```go
type AdaptiveTimer struct {
    baseInterval time.Duration
    maxInterval  time.Duration
    minInterval  time.Duration
    currentLoad  int64
    metrics      *TimerMetrics
}

func (t *AdaptiveTimer) GetNextInterval() time.Duration {
    load := atomic.LoadInt64(&t.currentLoad)
    
    switch {
    case load == 0:
        return t.maxInterval // 无负载时延长间隔
    case load > 100:
        return t.minInterval // 高负载时缩短间隔
    default:
        // 根据负载动态调整
        ratio := float64(load) / 100.0
        interval := time.Duration(float64(t.baseInterval) * (2.0 - ratio))
        return interval
    }
}

func (t *AdaptiveTimer) Run(ctx context.Context, handler func()) {
    for {
        select {
        case <-ctx.Done():
            return
        case <-time.After(t.GetNextInterval()):
            start := time.Now()
            handler()
            t.metrics.RecordExecution(time.Since(start))
        }
    }
}
```

#### 3.2 细粒度分布式锁
```go
type FineGrainedLockManager struct {
    locks map[string]*DistributedLock
    mutex sync.RWMutex
}

func (l *FineGrainedLockManager) LockAppointPk(appointId uint32) (*DistributedLock, error) {
    key := fmt.Sprintf("appoint_pk_lock_%d", appointId)
    
    l.mutex.RLock()
    lock, exists := l.locks[key]
    l.mutex.RUnlock()
    
    if !exists {
        l.mutex.Lock()
        if lock, exists = l.locks[key]; !exists {
            lock = NewDistributedLock(key, 30*time.Second)
            l.locks[key] = lock
        }
        l.mutex.Unlock()
    }
    
    if err := lock.Acquire(); err != nil {
        return nil, err
    }
    
    return lock, nil
}
```

### 4. 代码架构优化

#### 4.1 领域驱动设计
```go
// domain/appoint_pk/aggregate.go
type AppointPkAggregate struct {
    id       AppointPkId
    uid      UserId
    rivals   []Rival
    timeSlot TimeSlot
    status   Status
    events   []DomainEvent
}

func (a *AppointPkAggregate) SchedulePk(rival Rival, startTime time.Time) error {
    // 业务规则验证
    if err := a.validateSchedule(rival, startTime); err != nil {
        return err
    }
    
    // 状态变更
    a.rivals = append(a.rivals, rival)
    
    // 领域事件
    a.addEvent(PkScheduledEvent{
        AppointPkId: a.id,
        Rival:       rival,
        StartTime:   startTime,
    })
    
    return nil
}

// application/appoint_pk_service.go
type AppointPkService struct {
    repository AppointPkRepository
    eventBus   EventBus
    cache      CacheManager
}

func (s *AppointPkService) CreateAppointPk(ctx context.Context, cmd CreateAppointPkCommand) error {
    // 1. 创建聚合根
    aggregate := NewAppointPkAggregate(cmd.Uid, cmd.TimeSlot)
    
    // 2. 添加对手
    for _, rival := range cmd.Rivals {
        if err := aggregate.SchedulePk(rival, rival.StartTime); err != nil {
            return err
        }
    }
    
    // 3. 持久化
    if err := s.repository.Save(ctx, aggregate); err != nil {
        return err
    }
    
    // 4. 发布事件
    for _, event := range aggregate.GetEvents() {
        s.eventBus.Publish(event)
    }
    
    return nil
}
```

#### 4.2 六边形架构
```go
// ports/appoint_pk_service.go
type AppointPkService interface {
    CreateAppointPk(ctx context.Context, cmd CreateAppointPkCommand) error
    UpdateAppointPk(ctx context.Context, cmd UpdateAppointPkCommand) error
    GetAppointPkInfo(ctx context.Context, query GetAppointPkInfoQuery) (*AppointPkInfo, error)
}

// adapters/mysql_appoint_pk_repository.go
type MysqlAppointPkRepository struct {
    db *sqlx.DB
}

func (r *MysqlAppointPkRepository) Save(ctx context.Context, aggregate *AppointPkAggregate) error {
    return r.saveAggregate(ctx, aggregate)
}

// adapters/redis_appoint_pk_cache.go
type RedisAppointPkCache struct {
    client *redis.Client
}

func (c *RedisAppointPkCache) Get(ctx context.Context, id AppointPkId) (*AppointPkAggregate, error) {
    return c.getFromRedis(ctx, id)
}
```

## 📊 预期优化效果

### 性能提升
- **查询性能**：响应时间减少60%，吞吐量提升200%
- **缓存命中率**：从70%提升到95%
- **数据库压力**：查询量减少50%

### 可靠性提升
- **故障恢复时间**：从分钟级降到秒级
- **数据一致性**：强一致性保证
- **错误处理**：完善的重试和降级机制

### 可维护性提升
- **代码可读性**：清晰的分层架构
- **测试覆盖率**：达到80%以上
- **扩展性**：支持新功能快速开发

## 🚀 实施计划

### 第一阶段：基础优化（2周）
1. 数据库索引优化
2. 批量查询改造
3. 缓存穿透防护
4. 基础监控添加

### 第二阶段：架构优化（3周）
1. 多级缓存实现
2. 自适应定时任务
3. 细粒度锁优化
4. 错误处理完善

### 第三阶段：重构升级（4周）
1. 领域驱动设计
2. 六边形架构
3. 事件驱动架构
4. 完整测试覆盖
