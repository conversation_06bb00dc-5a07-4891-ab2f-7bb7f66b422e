# channel-live-mgr 技术方案和模块架构

## 整体架构设计

### 服务架构
channel-live-mgr采用典型的微服务架构，作为Srv层服务为上层Logic服务提供直播相关的基础能力。

```
┌─────────────────┐
│   Logic服务     │  (channel-live-logic等)
└─────────────────┘
         │
         ▼
┌─────────────────┐
│channel-live-mgr │  (本服务)
└─────────────────┘
         │
         ▼
┌─────────────────┐
│  基础服务层     │  (account, channel, etc.)
└─────────────────┘
```

### 技术栈
- **编程语言**: Go
- **RPC框架**: gRPC
- **缓存**: Redis
- **数据库**: MySQL
- **消息队列**: Kafka
- **配置管理**: JSON配置文件

## 核心模块架构

### 1. Server模块 (server/server.go)

**职责**: 服务启动、gRPC服务注册、依赖注入管理

**核心组件**:
- `ChannelLiveMgrServer`: 主服务结构体
- 各种Client客户端的初始化和管理
- Kafka订阅者的启动和管理
- 定时任务的启动

**关键依赖**:
```go
type ChannelLiveMgrServer struct {
    sc                  *conf.ServiceConfigT      // 配置管理
    cacheClient         *cache.ChannelLiveMgrCache // Redis缓存客户端
    mysqlStore          *mysql.Store               // MySQL存储
    mgr                 *manager.Manager           // 业务逻辑管理器
    // ... 其他客户端
}
```

### 2. Manager模块 (manager/manager.go)

**职责**: 核心业务逻辑处理，事件处理，业务流程编排

**核心功能**:
- 直播事件处理 (`HandlerLivePresentEvent`)
- PK事件处理 (`HandlerPkPresentEvent`)
- 进房/退房事件处理 (`HandlerChannelEvent`)
- 送礼事件处理 (`HandlerConsumeEvent`)

**依赖的客户端**:
```go
type Manager struct {
    channelCli        *channel.Client           // 频道服务
    channelMicCli     *channelmic.Client       // 麦位服务
    accountCli        *account.Client          // 账号服务
    cacheClient       *cache.ChannelLiveMgrCache // 缓存客户端
    mysqlStore        *mysql.Store             // 数据存储
    pushClient        *PushNotification.Client // 推送服务
    userProfileCli    *user_profile_api.Client // 用户资料服务
    ukwCli            *ukw.Client              // 神秘人服务
    // ... 其他依赖
}
```

### 3. Cache模块 (cache/)

**职责**: Redis缓存操作的封装，提供高性能的数据访问

**核心功能**:
- 直播状态缓存管理
- PK信息缓存管理
- 用户数据缓存管理
- 统计数据缓存管理

**缓存策略**:
- 直播状态：实时更新，TTL根据业务需求设置
- 统计数据：定期更新，支持增量计算
- 用户信息：按需缓存，设置合理的过期时间

### 4. MySQL存储模块 (mysql/)

**职责**: 数据持久化存储，提供数据的可靠性保证

**核心表结构**:
- 直播权限表：存储主播的直播权限信息
- 直播记录表：存储直播的历史记录
- PK记录表：存储PK的历史数据
- 积分流水表：存储主播积分变动记录

### 5. 事件处理模块 (event/)

**职责**: Kafka事件的订阅和处理

**事件订阅者**:
- `KafkaPresentSubscriber`: 送礼事件订阅
- `KafkaChannelSubscriber`: 频道事件订阅
- `KafkaMicEventSubscriber`: 麦位事件订阅
- `KnightGroupSub`: 骑士团事件订阅
- `UkwSub`: 神秘人事件订阅
- `RevenueExtGameSub`: 外部游戏收入事件订阅

**事件处理流程**:
```
Kafka事件 → 事件订阅者 → Manager业务处理 → 缓存更新 → 数据库持久化
```

### 6. 配置管理模块 (conf/)

**职责**: 服务配置的解析和管理

**配置内容**:
- Redis连接配置
- MySQL连接配置
- Kafka配置
- 业务规则配置（道具配置、状态时长配置等）

## 接口设计架构

### 1. 直播管理接口组

**核心接口**:
- `SetChannelLiveInfo`: 设置直播权限
- `SetChannelLiveStatus`: 设置直播状态
- `GetChannelLiveStatus`: 获取直播状态
- `ChannelLiveHeartbeat`: 直播心跳

**设计原则**:
- 状态变更接口保证原子性
- 查询接口优先从缓存获取数据
- 心跳接口支持高并发访问

### 2. PK功能接口组

**核心接口**:
- `ApplyPk`: PK申请
- `HandlerApply`: 处理PK申请
- `StartPkMatch`: 开始PK匹配
- `GetPkInfo`: 获取PK信息

**设计原则**:
- PK状态变更需要同步更新双方信息
- 匹配算法支持多种匹配策略
- PK数据实时性要求高

### 3. 数据统计接口组

**核心接口**:
- `GetChannelLiveData`: 获取直播数据
- `GetChannelLiveRankUser`: 获取送礼排行
- `BatchGetChannelLiveStatus`: 批量获取直播状态

**设计原则**:
- 支持批量查询提升性能
- 统计数据支持多维度查询
- 历史数据支持分页查询

### 4. 主播管理接口组

**核心接口**:
- `GetAnchorList`: 获取主播列表
- `AddChannelLiveAnchorScore`: 添加主播积分
- `GetChannelLiveAnchorScore`: 获取主播积分

**设计原则**:
- 主播信息支持多种查询方式
- 积分操作保证事务性
- 支持权限控制和黑名单管理

## 数据流架构

### 1. 写入数据流

```
外部事件(Kafka) → Manager处理 → 缓存更新 → 异步持久化到MySQL
```

### 2. 读取数据流

```
gRPC请求 → 缓存查询 → (缓存未命中)数据库查询 → 更新缓存 → 返回结果
```

### 3. 事件发布流

```
业务逻辑处理 → 生成事件 → Kafka发布 → 其他服务消费
```

## 性能优化设计

### 1. 缓存策略
- **多级缓存**: 内存缓存 + Redis缓存
- **缓存预热**: 服务启动时预加载热点数据
- **缓存更新**: 采用Cache-Aside模式

### 2. 数据库优化
- **读写分离**: 读操作使用只读实例
- **索引优化**: 针对查询模式建立合适索引
- **分页查询**: 大数据量查询支持分页

### 3. 并发控制
- **连接池**: 数据库和Redis连接池管理
- **限流**: 针对高频接口实现限流保护
- **异步处理**: 非关键路径采用异步处理

## 可靠性设计

### 1. 容错机制
- **服务降级**: 依赖服务不可用时的降级策略
- **重试机制**: 网络异常时的重试策略
- **熔断器**: 防止级联故障

### 2. 数据一致性
- **最终一致性**: 缓存和数据库的最终一致性保证
- **事务处理**: 关键业务操作的事务性保证
- **数据校验**: 定期数据一致性校验

### 3. 监控告警
- **性能监控**: 接口响应时间、QPS监控
- **错误监控**: 错误率、异常监控
- **业务监控**: 关键业务指标监控

## 赛事PK功能设计方案

### 需求背景
为方便运营活动可以灵活的组织双人PK赛事，需要支持运营服务直接配置直播PK，使得两个主播的房间能够在指定时间展开一场PK。

### 方案对比分析

#### 现有 AppointPkInfo 功能分析
服务中已存在 `AppointPkInfo` 指定PK功能，具有以下特点：

**功能特性**:
- 支持一个主播在时间段内与多个对手进行PK（一对多模式）
- 有完整的邀约、应战、弃权状态管理
- 有定时任务处理和推送通知机制
- 主要用于运营指定PK活动

**数据结构**:
```protobuf
message AppointPkInfo {
   uint32 appoint_id = 1;
   uint32 uid = 2;                      // 一个主播
   uint32 begin_ts = 3;
   uint32 end_ts = 4;
   repeated PkRivalInfo rival_list = 5; // 多个对手
   uint32 update_ts = 6;
   string operator = 7;
}
```

#### 新需求 ContestPk 与现有功能对比

| 维度 | AppointPkInfo (现有) | ContestPk (新需求) |
|------|---------------------|-------------------|
| **业务场景** | 运营指定PK活动 | 赛事PK活动 |
| **对手关系** | 一对多（一个主播vs多个对手） | 一对一（两个主播对战） |
| **时间管理** | 时间段内的多场PK | 精确的单场PK时间 |
| **PK限制** | 无明确限制机制 | 需要10分钟前限制常规PK |
| **结果处理** | 简单的胜负推送 | 需要发布详细结果事件给运营服务 |
| **加时控制** | 无特殊加时控制 | 需要可配置的加时开关 |
| **活动隔离** | 无活动概念 | 支持多个运营活动同时进行 |
| **业务流程** | 邀约→应战→开始PK | 配置→限制期→自动开始→结果发布 |

#### 技术方案决策

**决策结论**: 新建独立的 ContestPk 功能模块，与现有 AppointPkInfo 并行存在

**决策理由**:
1. **业务语义不同**: AppointPkInfo是指定PK，ContestPk是赛事PK，服务不同场景
2. **数据结构差异大**: 一对多 vs 一对一的根本性差异
3. **业务流程差异**: 处理逻辑和状态管理完全不同
4. **扩展性考虑**: 独立实现有利于各自功能演进，避免相互影响
5. **代码可维护性**: 职责明确，便于后续维护和扩展

### ContestPk 技术实现方案

#### 1. 数据模型设计
```sql
CREATE TABLE contest_pk_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    contest_id VARCHAR(64) NOT NULL COMMENT '赛事ID',
    contest_name VARCHAR(128) NOT NULL COMMENT '赛事名称',
    activity_service VARCHAR(64) NOT NULL COMMENT '运营活动服务标识',
    anchor_uid_a BIGINT NOT NULL COMMENT '主播A的UID',
    anchor_uid_b BIGINT NOT NULL COMMENT '主播B的UID',
    contest_begin_time BIGINT NOT NULL COMMENT '赛事开始时间戳',
    contest_end_time BIGINT NOT NULL COMMENT '赛事结束时间戳',
    pk_begin_time BIGINT NOT NULL COMMENT 'PK开始时间戳',
    pk_end_time BIGINT NOT NULL COMMENT 'PK结束时间戳',
    allow_extra_time TINYINT(1) DEFAULT 0 COMMENT '是否允许加时',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待开始，1-进行中，2-已结束，3-已取消',
    winner_uid BIGINT DEFAULT 0 COMMENT '获胜者UID，0表示平局',
    final_score_a INT DEFAULT 0 COMMENT '主播A最终得分',
    final_score_b INT DEFAULT 0 COMMENT '主播B最终得分',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    INDEX idx_contest_id (contest_id),
    INDEX idx_anchor_uid (anchor_uid_a, anchor_uid_b),
    INDEX idx_time_range (pk_begin_time, pk_end_time),
    INDEX idx_activity_service (activity_service)
);
```

#### 2. 核心模块架构
- **ContestPkManager**: 赛事PK管理器，负责核心业务逻辑
- **ContestPkCache**: 赛事PK缓存层，提供高性能数据访问
- **ContestPkTimer**: 定时任务处理器，处理自动化流程
- **ContestPkEvent**: 事件发布器，向运营服务发布结果事件

#### 3. 接口设计
```protobuf
service ChannelLiveMgr {
    // 赛事PK管理接口
    rpc CreateContestPk(CreateContestPkReq) returns (CreateContestPkResp);
    rpc UpdateContestPk(UpdateContestPkReq) returns (UpdateContestPkResp);
    rpc CancelContestPk(CancelContestPkReq) returns (CancelContestPkResp);
    rpc GetContestPkConfig(GetContestPkConfigReq) returns (GetContestPkConfigResp);
    rpc BatchGetContestPkConfig(BatchGetContestPkConfigReq) returns (BatchGetContestPkConfigResp);
    rpc CheckAnchorPkAvailable(CheckAnchorPkAvailableReq) returns (CheckAnchorPkAvailableResp);
}
```

#### 4. 业务流程设计
1. **配置阶段**: 运营服务调用接口创建赛事PK配置
2. **限制阶段**: 开始前10分钟自动限制主播常规PK
3. **执行阶段**: 到达指定时间自动发起PK匹配
4. **结果阶段**: PK结束后发布结果事件给运营服务

#### 5. 与现有系统集成
- **PK申请检查**: 在 ApplyPk 接口中增加赛事PK限制检查
- **PK结束处理**: 在PK结束逻辑中增加赛事结果处理
- **缓存策略**: 独立的缓存键空间，避免与现有功能冲突
- **事件发布**: 新增赛事结果事件类型

## 扩展性设计

### 1. 水平扩展
- **无状态设计**: 服务实例无状态，支持水平扩展
- **负载均衡**: 支持多实例负载均衡
- **分片策略**: 数据分片策略支持

### 2. 功能扩展
- **插件化设计**: 新功能以插件方式扩展
- **配置驱动**: 业务规则通过配置驱动
- **版本兼容**: 接口版本兼容性保证

## 部署架构

### 1. 服务部署
- **容器化部署**: Docker容器部署
- **服务发现**: 支持服务注册和发现
- **配置管理**: 配置文件外部化管理

### 2. 依赖服务
- **Redis集群**: 高可用Redis集群
- **MySQL主从**: 主从复制保证数据可靠性
- **Kafka集群**: 高可用消息队列集群
