# channel-live-mgr 技术方案和模块架构

## 整体架构设计

### 服务架构
channel-live-mgr采用典型的微服务架构，作为Srv层服务为上层Logic服务提供直播相关的基础能力。

```
┌─────────────────┐
│   Logic服务     │  (channel-live-logic等)
└─────────────────┘
         │
         ▼
┌─────────────────┐
│channel-live-mgr │  (本服务)
└─────────────────┘
         │
         ▼
┌─────────────────┐
│  基础服务层     │  (account, channel, etc.)
└─────────────────┘
```

### 技术栈
- **编程语言**: Go
- **RPC框架**: gRPC
- **缓存**: Redis
- **数据库**: MySQL
- **消息队列**: Kafka
- **配置管理**: JSON配置文件

## 核心模块架构

### 1. Server模块 (server/server.go)

**职责**: 服务启动、gRPC服务注册、依赖注入管理

**核心组件**:
- `ChannelLiveMgrServer`: 主服务结构体
- 各种Client客户端的初始化和管理
- Kafka订阅者的启动和管理
- 定时任务的启动

**关键依赖**:
```go
type ChannelLiveMgrServer struct {
    sc                  *conf.ServiceConfigT      // 配置管理
    cacheClient         *cache.ChannelLiveMgrCache // Redis缓存客户端
    mysqlStore          *mysql.Store               // MySQL存储
    mgr                 *manager.Manager           // 业务逻辑管理器
    // ... 其他客户端
}
```

### 2. Manager模块 (manager/manager.go)

**职责**: 核心业务逻辑处理，事件处理，业务流程编排

**核心功能**:
- 直播事件处理 (`HandlerLivePresentEvent`)
- PK事件处理 (`HandlerPkPresentEvent`)
- 进房/退房事件处理 (`HandlerChannelEvent`)
- 送礼事件处理 (`HandlerConsumeEvent`)

**依赖的客户端**:
```go
type Manager struct {
    channelCli        *channel.Client           // 频道服务
    channelMicCli     *channelmic.Client       // 麦位服务
    accountCli        *account.Client          // 账号服务
    cacheClient       *cache.ChannelLiveMgrCache // 缓存客户端
    mysqlStore        *mysql.Store             // 数据存储
    pushClient        *PushNotification.Client // 推送服务
    userProfileCli    *user_profile_api.Client // 用户资料服务
    ukwCli            *ukw.Client              // 神秘人服务
    // ... 其他依赖
}
```

### 3. Cache模块 (cache/)

**职责**: Redis缓存操作的封装，提供高性能的数据访问

**核心功能**:
- 直播状态缓存管理
- PK信息缓存管理
- 用户数据缓存管理
- 统计数据缓存管理

**缓存策略**:
- 直播状态：实时更新，TTL根据业务需求设置
- 统计数据：定期更新，支持增量计算
- 用户信息：按需缓存，设置合理的过期时间

### 4. MySQL存储模块 (mysql/)

**职责**: 数据持久化存储，提供数据的可靠性保证

**核心表结构**:
- 直播权限表：存储主播的直播权限信息
- 直播记录表：存储直播的历史记录
- PK记录表：存储PK的历史数据
- 积分流水表：存储主播积分变动记录

### 5. 事件处理模块 (event/)

**职责**: Kafka事件的订阅和处理

**事件订阅者**:
- `KafkaPresentSubscriber`: 送礼事件订阅
- `KafkaChannelSubscriber`: 频道事件订阅
- `KafkaMicEventSubscriber`: 麦位事件订阅
- `KnightGroupSub`: 骑士团事件订阅
- `UkwSub`: 神秘人事件订阅
- `RevenueExtGameSub`: 外部游戏收入事件订阅

**事件处理流程**:
```
Kafka事件 → 事件订阅者 → Manager业务处理 → 缓存更新 → 数据库持久化
```

### 6. 配置管理模块 (conf/)

**职责**: 服务配置的解析和管理

**配置内容**:
- Redis连接配置
- MySQL连接配置
- Kafka配置
- 业务规则配置（道具配置、状态时长配置等）

## 接口设计架构

### 1. 直播管理接口组

**核心接口**:
- `SetChannelLiveInfo`: 设置直播权限
- `SetChannelLiveStatus`: 设置直播状态
- `GetChannelLiveStatus`: 获取直播状态
- `ChannelLiveHeartbeat`: 直播心跳

**设计原则**:
- 状态变更接口保证原子性
- 查询接口优先从缓存获取数据
- 心跳接口支持高并发访问

### 2. PK功能接口组

**核心接口**:
- `ApplyPk`: PK申请
- `HandlerApply`: 处理PK申请
- `StartPkMatch`: 开始PK匹配
- `GetPkInfo`: 获取PK信息

**设计原则**:
- PK状态变更需要同步更新双方信息
- 匹配算法支持多种匹配策略
- PK数据实时性要求高

### 3. 数据统计接口组

**核心接口**:
- `GetChannelLiveData`: 获取直播数据
- `GetChannelLiveRankUser`: 获取送礼排行
- `BatchGetChannelLiveStatus`: 批量获取直播状态

**设计原则**:
- 支持批量查询提升性能
- 统计数据支持多维度查询
- 历史数据支持分页查询

### 4. 主播管理接口组

**核心接口**:
- `GetAnchorList`: 获取主播列表
- `AddChannelLiveAnchorScore`: 添加主播积分
- `GetChannelLiveAnchorScore`: 获取主播积分

**设计原则**:
- 主播信息支持多种查询方式
- 积分操作保证事务性
- 支持权限控制和黑名单管理

## 数据流架构

### 1. 写入数据流

```
外部事件(Kafka) → Manager处理 → 缓存更新 → 异步持久化到MySQL
```

### 2. 读取数据流

```
gRPC请求 → 缓存查询 → (缓存未命中)数据库查询 → 更新缓存 → 返回结果
```

### 3. 事件发布流

```
业务逻辑处理 → 生成事件 → Kafka发布 → 其他服务消费
```

## 性能优化设计

### 1. 缓存策略
- **多级缓存**: 内存缓存 + Redis缓存
- **缓存预热**: 服务启动时预加载热点数据
- **缓存更新**: 采用Cache-Aside模式

### 2. 数据库优化
- **读写分离**: 读操作使用只读实例
- **索引优化**: 针对查询模式建立合适索引
- **分页查询**: 大数据量查询支持分页

### 3. 并发控制
- **连接池**: 数据库和Redis连接池管理
- **限流**: 针对高频接口实现限流保护
- **异步处理**: 非关键路径采用异步处理

## 可靠性设计

### 1. 容错机制
- **服务降级**: 依赖服务不可用时的降级策略
- **重试机制**: 网络异常时的重试策略
- **熔断器**: 防止级联故障

### 2. 数据一致性
- **最终一致性**: 缓存和数据库的最终一致性保证
- **事务处理**: 关键业务操作的事务性保证
- **数据校验**: 定期数据一致性校验

### 3. 监控告警
- **性能监控**: 接口响应时间、QPS监控
- **错误监控**: 错误率、异常监控
- **业务监控**: 关键业务指标监控

## 赛事PK功能设计方案

### 需求背景
为方便运营活动可以灵活的组织双人PK赛事，需要支持运营服务直接配置直播PK，使得两个主播的房间能够在指定时间展开一场PK。

### 方案对比分析

#### 现有 AppointPkInfo 功能分析
服务中已存在 `AppointPkInfo` 指定PK功能，具有以下特点：

**功能特性**:
- 支持一个主播在时间段内与多个对手进行PK（一对多模式）
- 有完整的邀约、应战、弃权状态管理
- 有定时任务处理和推送通知机制
- 主要用于运营指定PK活动

**数据结构**:
```protobuf
message AppointPkInfo {
   uint32 appoint_id = 1;
   uint32 uid = 2;                      // 一个主播
   uint32 begin_ts = 3;
   uint32 end_ts = 4;
   repeated PkRivalInfo rival_list = 5; // 多个对手
   uint32 update_ts = 6;
   string operator = 7;
}
```

#### 新需求 ContestPk 与现有功能对比

| 维度 | AppointPkInfo (现有) | ContestPk (新需求) |
|------|---------------------|-------------------|
| **业务场景** | 运营指定PK活动 | 赛事PK活动 |
| **对手关系** | 一对多（一个主播vs多个对手） | 一对一（两个主播对战） |
| **时间管理** | 时间段内的多场PK | 精确的单场PK时间 |
| **PK限制** | 无明确限制机制 | 需要10分钟前限制常规PK |
| **结果处理** | 简单的胜负推送 | 需要发布详细结果事件给运营服务 |
| **加时控制** | 无特殊加时控制 | 需要可配置的加时开关 |
| **活动隔离** | 无活动概念 | 支持多个运营活动同时进行 |
| **业务流程** | 邀约→应战→开始PK | 配置→限制期→自动开始→结果发布 |

#### 技术方案决策

**决策结论**: 新建独立的 ContestPk 功能模块，与现有 AppointPkInfo 并行存在

**决策理由**:
1. **业务语义不同**: AppointPkInfo是指定PK，ContestPk是赛事PK，服务不同场景
2. **数据结构差异大**: 一对多 vs 一对一的根本性差异
3. **业务流程差异**: 处理逻辑和状态管理完全不同
4. **扩展性考虑**: 独立实现有利于各自功能演进，避免相互影响
5. **代码可维护性**: 职责明确，便于后续维护和扩展

### ContestPk 技术实现方案

#### 1. 整体架构设计

##### 1.1 模块分层架构
```
┌─────────────────────────────────────────────────────────┐
│                    gRPC接口层                           │
│  CreateContestPk | UpdateContestPk | GetContestPkConfig │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   业务逻辑层                            │
│  ContestPkManager | ContestPkValidator | ContestPkTimer │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                    缓存层                               │
│     ContestPkCache | RestrictionCache | TimerCache     │
└─────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────┐
│                   存储层                                │
│        MySQL存储 | Kafka事件发布 | Redis缓存           │
└─────────────────────────────────────────────────────────┘
```

##### 1.2 核心组件设计
- **ContestPkServer**: gRPC接口实现，参数校验和响应处理
- **ContestPkManager**: 核心业务逻辑管理器，协调各个组件
- **ContestPkValidator**: 业务规则校验器，时间冲突检测等
- **ContestPkTimer**: 定时任务处理器，自动化流程控制
- **ContestPkCache**: 缓存管理器，高性能数据访问
- **ContestPkStore**: 数据存储层，MySQL操作封装
- **ContestPkEventPublisher**: 事件发布器，Kafka消息发布

#### 2. 数据模型设计

##### 2.1 数据库表设计
```sql
-- 赛事PK配置主表
CREATE TABLE contest_pk_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    contest_id VARCHAR(64) NOT NULL UNIQUE COMMENT '赛事ID',
    contest_name VARCHAR(128) NOT NULL COMMENT '赛事名称',
    activity_service VARCHAR(64) NOT NULL COMMENT '运营活动服务标识',
    anchor_uid_a BIGINT NOT NULL COMMENT '主播A的UID',
    anchor_uid_b BIGINT NOT NULL COMMENT '主播B的UID',
    contest_begin_time BIGINT NOT NULL COMMENT '赛事开始时间戳',
    contest_end_time BIGINT NOT NULL COMMENT '赛事结束时间戳',
    pk_begin_time BIGINT NOT NULL COMMENT 'PK开始时间戳',
    pk_end_time BIGINT NOT NULL COMMENT 'PK结束时间戳',
    allow_extra_time TINYINT(1) DEFAULT 0 COMMENT '是否允许加时',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待开始，1-进行中，2-已结束，3-已取消',
    winner_uid BIGINT DEFAULT 0 COMMENT '获胜者UID，0表示平局',
    final_score_a INT DEFAULT 0 COMMENT '主播A最终得分',
    final_score_b INT DEFAULT 0 COMMENT '主播B最终得分',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,

    -- 索引设计
    INDEX idx_contest_id (contest_id),
    INDEX idx_anchor_uid_a (anchor_uid_a),
    INDEX idx_anchor_uid_b (anchor_uid_b),
    INDEX idx_time_range (pk_begin_time, pk_end_time),
    INDEX idx_activity_service (activity_service),
    INDEX idx_status_time (status, pk_begin_time),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='赛事PK配置表';

-- 赛事PK操作日志表
CREATE TABLE contest_pk_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    contest_id VARCHAR(64) NOT NULL COMMENT '赛事ID',
    operation_type TINYINT NOT NULL COMMENT '操作类型：1-创建，2-更新，3-取消，4-开始，5-结束',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    operation_data TEXT COMMENT '操作数据JSON',
    operation_reason VARCHAR(256) COMMENT '操作原因',
    operation_time BIGINT NOT NULL COMMENT '操作时间',

    INDEX idx_contest_id (contest_id),
    INDEX idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='赛事PK操作日志表';
```

##### 2.2 缓存数据结构设计
```go
// 赛事PK配置缓存
type ContestPkConfigCache struct {
    ContestId        string                 `json:"contest_id"`
    Config           *pb.ContestPkConfig    `json:"config"`
    CacheTime        int64                  `json:"cache_time"`
    TTL              int64                  `json:"ttl"`
}

// 主播PK限制缓存
type AnchorPkRestriction struct {
    AnchorUID        uint64 `json:"anchor_uid"`
    ContestId        string `json:"contest_id"`
    RestrictBegin    int64  `json:"restrict_begin"`
    RestrictEnd      int64  `json:"restrict_end"`
    Reason           string `json:"reason"`
}

// 定时任务缓存
type ContestPkTimerTask struct {
    ContestId        string `json:"contest_id"`
    TaskType         string `json:"task_type"`  // restrict, start, finish
    ExecuteTime      int64  `json:"execute_time"`
    RetryCount       int    `json:"retry_count"`
}
```

#### 3. 核心业务逻辑设计

##### 3.1 ContestPkManager 设计
```go
type ContestPkManager struct {
    cacheClient       *cache.ChannelLiveMgrCache
    mysqlStore        *mysql.Store
    kafkaProducer     *kafka_prod.KafkaProduce
    validator         *ContestPkValidator
    timer             *ContestPkTimer
    eventPublisher    *ContestPkEventPublisher
    config            *conf.ServiceConfigT
    ctx               context.Context
}

// 核心方法设计
func (m *ContestPkManager) CreateContestPk(ctx context.Context, config *pb.ContestPkConfig) (*pb.CreateContestPkResp, error)
func (m *ContestPkManager) UpdateContestPk(ctx context.Context, config *pb.ContestPkConfig) error
func (m *ContestPkManager) CancelContestPk(ctx context.Context, contestId, operator, reason string) error
func (m *ContestPkManager) GetContestPkConfig(ctx context.Context, contestId string) (*pb.ContestPkConfig, error)
func (m *ContestPkManager) BatchGetContestPkConfig(ctx context.Context, req *pb.BatchGetContestPkConfigReq) (*pb.BatchGetContestPkConfigResp, error)
func (m *ContestPkManager) CheckAnchorPkAvailable(ctx context.Context, anchorUid uint64, checkTime int64) (*pb.CheckAnchorPkAvailableResp, error)
func (m *ContestPkManager) StartContestPk(ctx context.Context, contestId string) error
func (m *ContestPkManager) FinishContestPk(ctx context.Context, contestId string, winnerUid uint64, scoreA, scoreB uint32, isExtraTime bool) error
```

##### 3.2 ContestPkValidator 设计
```go
type ContestPkValidator struct {
    mysqlStore  *mysql.Store
    cacheClient *cache.ChannelLiveMgrCache
}

// 校验方法设计
func (v *ContestPkValidator) ValidateCreateRequest(ctx context.Context, config *pb.ContestPkConfig) error
func (v *ContestPkValidator) ValidateUpdateRequest(ctx context.Context, config *pb.ContestPkConfig) error
func (v *ContestPkValidator) CheckTimeConflict(ctx context.Context, anchorUidA, anchorUidB uint64, beginTime, endTime int64, excludeContestId string) error
func (v *ContestPkValidator) CheckContestIdUnique(ctx context.Context, contestId string) error
func (v *ContestPkValidator) CheckAnchorValid(ctx context.Context, anchorUid uint64) error
func (v *ContestPkValidator) CheckTimeValid(ctx context.Context, contestBeginTime, contestEndTime, pkBeginTime, pkEndTime int64) error
```

##### 3.3 ContestPkTimer 设计
```go
type ContestPkTimer struct {
    cacheClient    *cache.ChannelLiveMgrCache
    mysqlStore     *mysql.Store
    manager        *ContestPkManager
    config         *conf.ServiceConfigT
    stopChan       chan bool
}

// 定时任务方法设计
func (t *ContestPkTimer) Start() error
func (t *ContestPkTimer) Stop() error
func (t *ContestPkTimer) ProcessRestrictionTasks() error    // 处理限制任务
func (t *ContestPkTimer) ProcessStartTasks() error         // 处理开始任务
func (t *ContestPkTimer) ProcessFinishTasks() error        // 处理结束任务
func (t *ContestPkTimer) AddTimerTask(contestId string, taskType string, executeTime int64) error
func (t *ContestPkTimer) RemoveTimerTask(contestId string, taskType string) error
```

#### 4. 接口设计详细说明

##### 4.1 gRPC接口实现
```go
// ContestPkServer gRPC服务实现
type ContestPkServer struct {
    manager *ContestPkManager
    pb.UnimplementedChannelLiveMgrServer
}

func (s *ContestPkServer) CreateContestPk(ctx context.Context, req *pb.CreateContestPkReq) (*pb.CreateContestPkResp, error) {
    // 1. 参数校验
    if err := s.validateCreateRequest(req); err != nil {
        return nil, status.Errorf(codes.InvalidArgument, "参数校验失败: %v", err)
    }

    // 2. 业务逻辑处理
    resp, err := s.manager.CreateContestPk(ctx, req.Config)
    if err != nil {
        return nil, status.Errorf(codes.Internal, "创建赛事PK失败: %v", err)
    }

    return resp, nil
}
```

##### 4.2 接口参数校验规则
```go
func (s *ContestPkServer) validateCreateRequest(req *pb.CreateContestPkReq) error {
    config := req.Config

    // 基本参数校验
    if config.ContestId == "" {
        return fmt.Errorf("赛事ID不能为空")
    }
    if config.ContestName == "" {
        return fmt.Errorf("赛事名称不能为空")
    }
    if config.ActivityService == "" {
        return fmt.Errorf("活动服务标识不能为空")
    }
    if config.AnchorUidA == 0 || config.AnchorUidB == 0 {
        return fmt.Errorf("主播UID不能为空")
    }
    if config.AnchorUidA == config.AnchorUidB {
        return fmt.Errorf("主播A和主播B不能是同一人")
    }

    // 时间参数校验
    now := time.Now().Unix()
    if config.ContestBeginTime <= uint64(now) {
        return fmt.Errorf("赛事开始时间不能早于当前时间")
    }
    if config.ContestEndTime <= config.ContestBeginTime {
        return fmt.Errorf("赛事结束时间必须晚于开始时间")
    }
    if config.PkBeginTime < config.ContestBeginTime || config.PkBeginTime >= config.ContestEndTime {
        return fmt.Errorf("PK开始时间必须在赛事时间段内")
    }
    if config.PkEndTime <= config.PkBeginTime || config.PkEndTime > config.ContestEndTime {
        return fmt.Errorf("PK结束时间必须在赛事时间段内且晚于PK开始时间")
    }

    return nil
}
```

#### 5. 缓存策略设计

##### 5.1 缓存层次结构
```
L1缓存（内存）: 热点赛事配置，TTL=5分钟
L2缓存（Redis）: 全量赛事配置，TTL=24小时
L3存储（MySQL）: 持久化存储
```

##### 5.2 缓存更新策略
```go
// Cache-Aside模式实现
func (m *ContestPkManager) GetContestPkConfig(ctx context.Context, contestId string) (*pb.ContestPkConfig, error) {
    // 1. 尝试从缓存获取
    config, err := m.cacheClient.GetContestPkConfig(contestId)
    if err == nil && config != nil {
        return config, nil
    }

    // 2. 缓存未命中，从数据库获取
    config, err = m.mysqlStore.GetContestPkConfig(ctx, contestId)
    if err != nil {
        return nil, err
    }

    // 3. 更新缓存
    go func() {
        if err := m.cacheClient.SetContestPkConfig(contestId, config); err != nil {
            log.Errorf("SetContestPkConfig cache failed: %v", err)
        }
    }()

    return config, nil
}
```

#### 6. 定时任务设计

##### 6.1 定时任务类型
```go
const (
    TimerTaskTypeRestriction = "restriction"  // 设置PK限制
    TimerTaskTypeStart       = "start"        // 开始PK
    TimerTaskTypeFinish      = "finish"       // 结束PK检查
)
```

##### 6.2 定时任务执行流程
```go
func (t *ContestPkTimer) ProcessRestrictionTasks() error {
    now := time.Now().Unix()

    // 1. 扫描需要设置限制的赛事（开始前10分钟）
    configs, err := t.mysqlStore.GetContestPkConfigsByTimeRange(
        ctx,
        uint64(now),
        uint64(now+600), // 10分钟后
        pb.ContestPkStatus_CONTEST_PK_PENDING,
    )
    if err != nil {
        return err
    }

    // 2. 为每个赛事设置主播限制
    for _, config := range configs {
        restrictBegin := int64(config.PkBeginTime) - 600 // 开始前10分钟
        restrictEnd := int64(config.PkEndTime)

        // 设置主播A的限制
        restriction := &cache.AnchorPkRestriction{
            AnchorUID:     config.AnchorUidA,
            ContestId:     config.ContestId,
            RestrictBegin: restrictBegin,
            RestrictEnd:   restrictEnd,
            Reason:        fmt.Sprintf("参与赛事PK: %s", config.ContestName),
        }
        t.cacheClient.SetContestPkRestriction(config.AnchorUidA, restriction)

        // 设置主播B的限制
        restriction.AnchorUID = config.AnchorUidB
        t.cacheClient.SetContestPkRestriction(config.AnchorUidB, restriction)
    }

    return nil
}
```

#### 7. 事件发布设计

##### 7.1 事件发布器设计
```go
type ContestPkEventPublisher struct {
    kafkaProducer *kafka_prod.KafkaProduce
    config        *conf.ServiceConfigT
}

func (p *ContestPkEventPublisher) PublishContestPkResult(ctx context.Context, event *pb.ContestPkResultEvent) error {
    // 1. 序列化事件数据
    eventData, err := proto.Marshal(event)
    if err != nil {
        return fmt.Errorf("marshal event failed: %v", err)
    }

    // 2. 发布到Kafka
    message := &kafka.Message{
        Topic: p.config.ContestPkResultTopic,
        Key:   event.ContestId,
        Value: eventData,
        Headers: map[string]string{
            "event_type":        "contest_pk_result",
            "activity_service":  event.ActivityService,
            "contest_id":        event.ContestId,
            "timestamp":         fmt.Sprintf("%d", time.Now().Unix()),
        },
    }

    return p.kafkaProducer.SendMessage(ctx, message)
}
```

##### 7.2 事件重试机制
```go
func (p *ContestPkEventPublisher) PublishWithRetry(ctx context.Context, event *pb.ContestPkResultEvent, maxRetries int) error {
    var lastErr error

    for i := 0; i < maxRetries; i++ {
        if err := p.PublishContestPkResult(ctx, event); err == nil {
            return nil
        } else {
            lastErr = err
            log.Warnf("PublishContestPkResult retry %d failed: %v", i+1, err)
            time.Sleep(time.Duration(i+1) * time.Second) // 指数退避
        }
    }

    return fmt.Errorf("publish failed after %d retries, last error: %v", maxRetries, lastErr)
}
```

## 扩展性设计

### 1. 水平扩展
- **无状态设计**: 服务实例无状态，支持水平扩展
- **负载均衡**: 支持多实例负载均衡
- **分片策略**: 数据分片策略支持

### 2. 功能扩展
- **插件化设计**: 新功能以插件方式扩展
- **配置驱动**: 业务规则通过配置驱动
- **版本兼容**: 接口版本兼容性保证

## 部署架构

### 1. 服务部署
- **容器化部署**: Docker容器部署
- **服务发现**: 支持服务注册和发现
- **配置管理**: 配置文件外部化管理

### 2. 依赖服务
- **Redis集群**: 高可用Redis集群
- **MySQL主从**: 主从复制保证数据可靠性
- **Kafka集群**: 高可用消息队列集群
