# channel-live-mgr 服务

## 服务简介

channel-live-mgr是直播业务领域的核心Srv服务，负责管理直播房间的完整生命周期、PK功能、数据统计以及主播权限管理等核心业务逻辑。作为直播生态系统中的关键组件，为上层Logic服务提供稳定可靠的直播相关基础能力。

## 功能特性

### 🎥 直播房间管理
- **权限管理**: 主播直播权限的开通、回收和权限控制
- **状态管理**: 直播房间开播、暂停、继续、关闭状态管理
- **信息维护**: 直播房间基础信息、标签、麦位信息维护
- **心跳监控**: 直播状态实时监控和心跳机制

### ⚔️ PK功能系统
- **PK申请**: 主播间PK申请、接受、拒绝、取消操作
- **匹配系统**: 支持普通匹配、排位赛、随机匹配、指定匹配
- **状态管理**: PK各阶段状态管理（开局、道具、最后一分钟、惩罚、结束）
- **道具系统**: 氛围道具、积分道具、首杀机制
- **加时赛**: 防偷塔玩法和加时机制

### 📊 数据统计系统
- **实时统计**: 观众数、送礼流水、观看时长实时统计
- **历史数据**: 直播记录、总流水、粉丝增长历史数据
- **排行榜**: 送礼排行、观看时长排行、PK排行
- **数据分析**: 多维度数据查询和分析

### 👑 主播管理系统
- **信息管理**: 主播基础信息查询、搜索、批量操作
- **积分系统**: 主播积分增减、流水记录、多来源管理
- **权限控制**: PK权限、排麦权限等权限管理
- **黑名单**: 主播黑名单管理功能

## 接口能力

### 直播管理类接口
| 接口名称 | 功能描述 | 调用频率 |
|---------|---------|---------|
| SetChannelLiveInfo | 设置直播权限信息 | 低频 |
| SetChannelLiveStatus | 设置直播状态 | 中频 |
| GetChannelLiveStatus | 获取直播状态 | 高频(26W) |
| ChannelLiveHeartbeat | 直播心跳 | 高频 |
| BatchGetChannelLiveStatus | 批量获取直播状态 | 高频(25W) |

### PK功能类接口
| 接口名称 | 功能描述 | 调用频率 |
|---------|---------|---------|
| ApplyPk | PK申请 | 中频 |
| HandlerApply | 处理PK申请 | 中频 |
| StartPkMatch | 开始PK匹配 | 中频 |
| GetPkInfo | 获取PK信息 | 高频(9W) |
| SetPkStatus | 设置PK状态 | 中频 |

### 数据统计类接口
| 接口名称 | 功能描述 | 调用频率 |
|---------|---------|---------|
| GetChannelLiveData | 获取直播数据 | 中频(10W) |
| GetChannelLiveTotalData | 获取总流水数据 | 中频 |
| GetChannelLiveRankUser | 获取送礼排行 | 中频 |
| GetChannelLiveHistoryRecord | 获取直播历史记录 | 中频(36W) |

### 主播管理类接口
| 接口名称 | 功能描述 | 调用频率 |
|---------|---------|---------|
| GetAnchorList | 分页获取主播列表 | 低频 |
| AddChannelLiveAnchorScore | 添加主播积分 | 中频 |
| GetChannelLiveAnchorScore | 获取主播积分 | 中频 |
| SearchAnchor | 搜索主播 | 低频 |

### 配置管理类接口
| 接口名称 | 功能描述 | 调用频率 |
|---------|---------|---------|
| GetItemConfig | 获取道具配置 | 中频(14W) |
| BatchGetChannelLiveStatusSimple | 批量获取简单直播状态 | 高频(90W) |

## 技术架构

### 核心组件
- **Server层**: gRPC服务注册、依赖管理、服务启动
- **Manager层**: 核心业务逻辑处理、事件处理、流程编排
- **Cache层**: Redis缓存操作封装、高性能数据访问
- **MySQL层**: 数据持久化存储、数据可靠性保证
- **Event层**: Kafka事件订阅处理、异步事件处理

### 依赖服务
- **基础服务**: account(账号)、channel(频道)、channelmic(麦位)
- **业务服务**: user_profile_api(用户资料)、ukw(神秘人)、nobility(贵族)
- **推送服务**: PushNotification(推送通知)
- **数据服务**: channellivefans(直播粉丝)、guild(公会)

### 存储架构
- **Redis**: 实时数据缓存、状态管理、高频访问数据
- **MySQL**: 持久化存储、历史数据、配置数据
- **Kafka**: 事件消息队列、异步处理、服务解耦

## 启动方式

### 环境要求
- Go 1.16+
- Redis 6.0+
- MySQL 8.0+
- Kafka 2.8+

### 配置文件
服务使用JSON格式配置文件 `channel-live-mgr.json`，包含以下配置项：
- Redis连接配置
- MySQL连接配置
- Kafka配置
- 业务规则配置

### 启动命令
```bash
# 开发环境启动
go run main.go -config=channel-live-mgr.json

# 生产环境启动
./channel-live-mgr -config=channel-live-mgr.json
```

### Docker启动
```bash
# 构建镜像
docker build -t channel-live-mgr .

# 运行容器
docker run -d \
  --name channel-live-mgr \
  -p 8080:8080 \
  -v /path/to/config:/app/config \
  channel-live-mgr
```

## 监控指标

### 性能指标
- **QPS**: 每秒请求数
- **响应时间**: 接口平均响应时间
- **错误率**: 接口错误率统计
- **并发数**: 当前并发连接数

### 业务指标
- **直播房间数**: 当前在线直播房间数
- **PK场次**: 当前进行中的PK场次
- **用户活跃度**: 直播观众活跃度
- **数据处理量**: 事件处理量统计

### 系统指标
- **CPU使用率**: 服务CPU使用情况
- **内存使用率**: 服务内存使用情况
- **网络IO**: 网络输入输出统计
- **磁盘IO**: 磁盘读写统计

## 日志说明

### 日志级别
- **ERROR**: 错误日志，需要立即关注
- **WARN**: 警告日志，需要关注但不影响服务
- **INFO**: 信息日志，记录重要业务操作
- **DEBUG**: 调试日志，详细的执行信息

### 关键日志
- 直播状态变更日志
- PK申请和匹配日志
- 积分变动日志
- 事件处理日志
- 错误和异常日志

## 故障排查

### 常见问题

#### 1. 直播状态不一致
**现象**: 直播状态显示异常
**排查步骤**:
1. 检查Redis缓存数据
2. 检查MySQL数据库数据
3. 检查心跳机制是否正常
4. 检查事件处理是否有延迟

#### 2. PK匹配失败
**现象**: PK匹配一直失败
**排查步骤**:
1. 检查匹配算法配置
2. 检查主播权限设置
3. 检查PK黑名单
4. 检查匹配队列状态

#### 3. 数据统计异常
**现象**: 统计数据不准确
**排查步骤**:
1. 检查Kafka事件消费情况
2. 检查缓存数据更新
3. 检查数据库事务处理
4. 检查定时任务执行

### 性能问题排查
1. 检查数据库连接池状态
2. 检查Redis连接和响应时间
3. 检查Kafka消费延迟
4. 检查内存使用情况
5. 分析慢查询日志

## 版本历史

### v1.0.0
- 基础直播管理功能
- PK申请和匹配功能
- 基础数据统计功能

### v1.1.0
- 新增指定PK功能
- 优化匹配算法
- 新增主播积分系统

### v1.2.0
- 新增道具系统
- 优化缓存策略
- 新增监控指标

## 联系方式

如有问题或建议，请联系开发团队。
