## 赛事PK功能 设计方案

### 需求背景

为方便运营活动可以灵活的组织双人PK赛事，需要支持运营服务直接配置直播PK，使得两个主播的房间能够在指定时间展开一场PK。

### 方案对比分析

#### 现有 AppointPkInfo （指定PK）功能分析

服务中已存在 `AppointPkInfo` 指定PK功能，具有以下特点：

**功能特性**:

- 支持一个主播在时间段内与多个对手进行PK（一对多模式）
- 有完整的邀约、应战、弃权状态管理
- 有定时任务处理和推送通知机制
- 主要用于运营指定PK活动

**数据结构**:

```protobuf
message AppointPkInfo {
   uint32 appoint_id = 1;
   uint32 uid = 2;                      // 一个主播
   uint32 begin_ts = 3;
   uint32 end_ts = 4;
   repeated PkRivalInfo rival_list = 5; // 多个对手
   uint32 update_ts = 6;
   string operator = 7;
}
```

#### 新需求 ContestPk 与现有功能对比

| 维度         | AppointPkInfo (现有)         | ContestPk (新需求)             |
| ------------ | ---------------------------- | ------------------------------ |
| **业务场景** | 运营指定PK活动               | 赛事PK活动                     |
| **对手关系** | 一对多（一个主播vs多个对手） | 一对一（两个主播对战）         |
| **时间管理** | 时间段内的多场PK             | 精确的单场PK时间               |
| **PK限制**   | 无明确限制机制               | 需要10分钟前限制常规PK         |
| **结果处理** | 简单的胜负推送               | 需要发布详细结果事件给运营服务 |
| **加时控制** | 无特殊加时控制               | 需要可配置的加时开关           |
| **活动隔离** | 无活动概念                   | 支持多个运营活动同时进行       |
| **业务流程** | 邀约→应战→开始PK             | 配置→限制期→自动开始→结果发布  |

#### 技术方案决策

**决策结论**: 新建独立的 ContestPk 功能模块，与现有 AppointPkInfo 并行存在

**决策理由**:

1. **业务语义不同**: AppointPkInfo是指定PK，ContestPk是赛事PK，服务不同场景
2. **数据结构差异大**: 一对多 vs 一对一的根本性差异
3. **业务流程差异**: 处理逻辑和状态管理完全不同
4. **扩展性考虑**: 独立实现有利于各自功能演进，避免相互影响
5. **代码可维护性**: 职责明确，便于后续维护和扩展

### ContestPk 技术实现方案

#### 1. 数据模型设计

```sql
CREATE TABLE contest_pk_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    contest_id VARCHAR(64) NOT NULL COMMENT '赛事ID',
    contest_name VARCHAR(128) NOT NULL COMMENT '赛事名称',
    activity_service VARCHAR(64) NOT NULL COMMENT '运营活动服务标识',
    anchor_uid_a BIGINT NOT NULL COMMENT '主播A的UID',
    anchor_uid_b BIGINT NOT NULL COMMENT '主播B的UID',
    contest_begin_time BIGINT NOT NULL COMMENT '赛事开始时间戳',
    contest_end_time BIGINT NOT NULL COMMENT '赛事结束时间戳',
    pk_begin_time BIGINT NOT NULL COMMENT 'PK开始时间戳',
    pk_end_time BIGINT NOT NULL COMMENT 'PK结束时间戳',
    allow_extra_time TINYINT(1) DEFAULT 0 COMMENT '是否允许加时',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0-待开始，1-进行中，2-已结束，3-已取消',
    winner_uid BIGINT DEFAULT 0 COMMENT '获胜者UID，0表示平局',
    final_score_a INT DEFAULT 0 COMMENT '主播A最终得分',
    final_score_b INT DEFAULT 0 COMMENT '主播B最终得分',
    operator VARCHAR(64) NOT NULL COMMENT '操作人',
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    INDEX idx_contest_id (contest_id),
    INDEX idx_anchor_uid (anchor_uid_a, anchor_uid_b),
    INDEX idx_time_range (pk_begin_time, pk_end_time),
    INDEX idx_activity_service (activity_service)
);
```

#### 2. 核心模块架构

- **ContestPkManager**: 赛事PK管理器，负责核心业务逻辑
- **ContestPkCache**: 赛事PK缓存层，提供高性能数据访问
- **ContestPkTimer**: 定时任务处理器，处理自动化流程
- **ContestPkEvent**: 事件发布器，向运营服务发布结果事件

#### 3. 接口设计

```protobuf
service ChannelLiveMgr {
    // 赛事PK管理接口
    rpc CreateContestPk(CreateContestPkReq) returns (CreateContestPkResp);
    rpc UpdateContestPk(UpdateContestPkReq) returns (UpdateContestPkResp);
    rpc CancelContestPk(CancelContestPkReq) returns (CancelContestPkResp);
    rpc GetContestPkConfig(GetContestPkConfigReq) returns (GetContestPkConfigResp);
    rpc BatchGetContestPkConfig(BatchGetContestPkConfigReq) returns (BatchGetContestPkConfigResp);
    rpc CheckAnchorPkAvailable(CheckAnchorPkAvailableReq) returns (CheckAnchorPkAvailableResp);
}
```

#### 4. 业务流程设计

1. **配置阶段**: 运营服务调用接口创建赛事PK配置
2. **限制阶段**: 开始前10分钟自动限制主播常规PK
3. **执行阶段**: 到达指定时间自动发起PK匹配
4. **结果阶段**: PK结束后发布结果事件给运营服务

#### 5. 与现有系统集成

- **PK申请检查**: 在 ApplyPk 接口中增加赛事PK限制检查
- **PK结束处理**: 在PK结束逻辑中增加赛事结果处理
- **缓存策略**: 独立的缓存键空间，避免与现有功能冲突
- **事件发布**: 新增赛事结果事件类型