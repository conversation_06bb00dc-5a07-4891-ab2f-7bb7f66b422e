# 指定PK（AppointPk）功能详细分析

## 1. 需求功能点分析

### 1.1 核心需求
指定PK功能是为了支持运营活动中的**预约式PK赛事**，主要解决以下业务需求：

1. **运营指定PK对战**：运营人员可以预先配置主播之间的PK对战安排
2. **时间段内多场PK**：支持一个主播在指定时间段内与多个对手依次进行PK
3. **自动化邀约流程**：系统自动在指定时间发起PK邀约，无需人工干预
4. **应战确认机制**：双方主播需要确认应战才能开始PK
5. **弃权和胜负判定**：支持主播弃权，自动判定胜负结果

### 1.2 业务场景
- **运营活动赛事**：如主播PK大赛、擂台赛等
- **定时PK安排**：预先安排好的主播对战时间表
- **多轮淘汰赛**：一个主播需要在不同时间与不同对手PK

## 2. 数据模型设计

### 2.1 核心数据结构

#### AppointPkInfo（指定PK信息）
```protobuf
message AppointPkInfo {
    uint32 appoint_id = 1;                   // 指定PK的唯一ID
    uint32 uid = 2;                          // 主播UID（发起方）
    uint32 begin_ts = 3;                     // 整个PK时间段开始时间
    uint32 end_ts = 4;                       // 整个PK时间段结束时间
    repeated PkRivalInfo rival_list = 5;     // 对手列表（按时间排序）
    uint32 update_ts = 6;                    // 更新时间
    string operator = 7;                     // 操作人
}
```

#### PkRivalInfo（PK对手信息）
```protobuf
message PkRivalInfo {
    uint32 uid = 1;                          // 对手UID
    uint32 pk_begin_ts = 2;                  // 与该对手PK的开始时间
}
```

#### AppointPkEvent（指定PK事件）
```protobuf
message AppointPkEvent {
    uint32 event_type = 1;                   // 事件类型
    uint32 my_uid = 2;                       // 当前主播UID
    uint32 other_uid = 3;                    // 对手UID
    string other_account = 4;                // 对手账号
    string other_nickname = 5;               // 对手昵称
    uint32 count_down_end_time = 6;          // 倒计时结束时间
    int32 other_sex = 7;                     // 对手性别
    uint32 count_down_time = 8;              // 倒计时时长
}
```

### 2.2 事件类型定义
```protobuf
enum AppointPkEventType {
    InValid = 0;                             // 无效
    AppointPkInvite = 1;                     // 指定PK邀约
    AppointPkAbstain = 2;                    // 指定PK弃权
    AppointPkWin = 3;                        // 指定PK胜利
    AppointPkWaiting = 4;                    // 等待对方应战
}
```

## 3. 技术实现架构

### 3.1 存储层设计

#### 3.1.1 MySQL存储
- **appoint_pk_info表**：存储指定PK的基本配置信息
- **appoint_pk_rival_info表**：存储PK对手的详细信息

#### 3.1.2 Redis缓存设计
1. **指定PK信息缓存**
   - Key: `appointpk_{appoint_id}`
   - 存储：AppointPkInfo的protobuf序列化数据

2. **主播PK信息索引**
   - Key: `anchor_appointpk_{uid}`
   - 结构：ZSet，按结束时间排序
   - 用途：快速查询主播的PK安排

3. **待处理PK队列**
   - Key: `np_appoint_pk_{index}`
   - 结构：ZSet，按开始时间排序
   - 用途：定时任务处理队列

4. **等待应战队列**
   - Key: `wait_appoint_pk_{index}`
   - 结构：ZSet，按倒计时结束时间排序
   - 用途：应战超时处理

5. **推送重试队列**
   - Key: `waiting_pk_push_{index}`
   - 结构：ZSet，按重试时间排序
   - 用途：推送失败重试

6. **应战标志计数**
   - Key: `flag_{myUid}_{otherUid}_{appointId}`
   - 结构：String，计数器
   - 用途：记录双方应战确认次数

### 3.2 定时任务设计

#### 3.2.1 数据加载任务（60秒间隔）
- **功能**：从MySQL加载需要处理的指定PK信息到缓存
- **实现**：`TimerLoadNeedProcAppointPkFromMysql`
- **逻辑**：
  1. 查询当前时间前后的指定PK配置
  2. 加载到Redis缓存
  3. 设置主播PK信息索引
  4. 添加到待处理队列

#### 3.2.2 PK邀约处理任务（1秒间隔）
- **功能**：处理到时间的PK邀约
- **实现**：`TimerHandleNeedProcAppointPk`
- **逻辑**：
  1. 从待处理队列获取到时间的PK
  2. 检查主播直播状态
  3. 发送PK邀约推送
  4. 添加到等待应战队列
  5. 设置下一场PK时间

#### 3.2.3 应战超时处理任务（2秒间隔）
- **功能**：处理应战超时的PK
- **实现**：`TimerHandleWaitingAppointPk`
- **逻辑**：
  1. 检查等待应战队列中超时的PK
  2. 判断双方应战状态
  3. 确定胜负结果
  4. 发送结果通知
  5. 发布PK结果事件

#### 3.2.4 推送重试任务（2秒间隔）
- **功能**：重试失败的推送
- **实现**：`TimerHandleWaitingAppointPkPush`
- **逻辑**：
  1. 获取需要重试的推送
  2. 重新发送推送消息
  3. 清理过期的重试记录

### 3.3 业务流程实现

#### 3.3.1 PK邀约流程
```
1. 定时任务检测到PK开始时间
2. 检查双方主播直播状态
3. 发送PK邀约推送给双方
4. 设置应战倒计时
5. 添加到等待应战队列
```

#### 3.3.2 应战确认流程
```
1. 主播调用AcceptAppointPk接口
2. 验证PK有效性和时间
3. 增加应战计数器
4. 如果双方都应战：
   - 检查直播状态
   - 调用StartPk开始PK
   - 发布PK开始事件
5. 如果只有一方应战：
   - 发送等待对方应战推送
```

#### 3.3.3 超时处理流程
```
1. 定时任务检测应战超时
2. 检查应战计数器状态
3. 确定胜负结果：
   - 双方都未应战：无结果
   - 一方应战：应战方胜利
   - 双方都应战但状态异常：按直播状态判定
4. 发送结果推送
5. 发布PK结果事件
```

## 4. 接口设计

### 4.1 管理接口（已废弃）
- `AddAppointPkInfo`：添加指定PK信息
- `UpdateAppointPkInfo`：更新指定PK信息
- `DelAppointPkInfo`：删除指定PK信息
- `GetAppointPkInfoList`：获取指定PK信息列表

### 4.2 客户端接口
- `GetAppointPkInfo`：主播进房获取PK推送信息
- `AcceptAppointPk`：接受指定PK邀约
- `ConfirmAppointPkPush`：确认收到推送（停止重试）

## 5. 关键技术特点

### 5.1 分布式锁机制
- 使用Redis SetNX实现分布式锁
- 防止多实例重复处理同一任务
- 锁超时机制避免死锁

### 5.2 队列分片设计
- 多个处理队列（NeedProcAppointPkQueueCnt = 8）
- 按appointId取模分片
- 提高并发处理能力

### 5.3 状态一致性保证
- 使用计数器确保双方应战状态同步
- 事务性操作保证数据一致性
- 失败重试机制

### 5.4 推送可靠性
- 推送失败自动重试
- 客户端确认机制
- 超时清理机制

## 6. 业务价值

### 6.1 运营价值
1. **自动化运营**：减少人工干预，提高运营效率
2. **灵活配置**：支持复杂的PK赛事安排
3. **用户体验**：及时的推送通知和状态反馈

### 6.2 技术价值
1. **高可用性**：分布式设计，支持多实例部署
2. **高性能**：缓存优化，减少数据库压力
3. **可扩展性**：队列分片，支持大规模并发

## 7. 核心算法和逻辑

### 7.1 时间调度算法
```go
// 对手列表按时间排序
sort.Slice(pkInfo.GetRivalList(), func(i, j int) bool {
    return pkInfo.GetRivalList()[i].PkBeginTs <= pkInfo.GetRivalList()[j].PkBeginTs
})

// 找到当前时间点的对手
for index, rivalInfo := range pkInfo.GetRivalList() {
    if beginTs == rivalInfo.PkBeginTs {
        rivalUid = rivalInfo.Uid
        // 设置下一个对手的时间
        if index != len(pkInfo.GetRivalList())-1 {
            nextBeginTs = pkInfo.GetRivalList()[index+1].PkBeginTs
        }
        break
    }
}
```

### 7.2 应战状态管理
```go
// 使用Redis计数器确保原子性
newCnt, err := s.cacheClient.IncrAcceptAppointPkFlag(appointPkInfo.GetUid(), rivalUid, appointId)

// 根据计数器值判断状态
if newCnt == 2 {
    // 双方都应战，开始PK
    s.StartPk(ctx, myUid, otherUid, myChannelId, otherChannelId, 0, 0, int(pb.ChannelLivePKMatchType_CPK_Match_Appoint))
} else if newCnt == 1 {
    // 只有一方应战，等待对方
    s.HandleAppointPush(ctx, pbLogic.AppointPkEventType_AppointPkWaiting, myUid, otherUser, downEndTs, downTs, true)
}
```

### 7.3 直播状态检查逻辑
```go
func (s *ChannelLiveMgrServer) getAppointPkResByLiveStatus(ctx context.Context, myUid, otherUId uint32) (bool, bool, uint32, uint32) {
    // 获取双方直播状态
    liveInfoResp, err := s.BatchGetChannelLiveStatus(ctx, &pb.BatchGetChannelLiveStatusReq{
        UidList: []uint32{myUid, otherUId},
    })

    // 判断直播状态有效性
    myValid := (myStatus == pb.EnumChannelLiveStatus_OPEN || myStatus == pb.EnumChannelLiveStatus_CONTINUE)
    otherValid := (otherStatus == pb.EnumChannelLiveStatus_OPEN || otherStatus == pb.EnumChannelLiveStatus_CONTINUE)

    return myValid, otherValid, myChannelId, otherChannelId
}
```

## 8. 错误处理和容错机制

### 8.1 分布式锁容错
- **锁超时**：设置合理的锁超时时间（300秒）
- **锁释放**：使用defer确保锁的释放
- **锁竞争**：多实例环境下的锁竞争处理

### 8.2 数据一致性保证
- **缓存失效**：MySQL数据变更时及时更新缓存
- **事务回滚**：关键操作失败时的数据回滚
- **重试机制**：网络异常时的自动重试

### 8.3 异常场景处理
1. **主播离线**：检查直播状态，自动判定胜负
2. **网络异常**：推送失败重试机制
3. **时间偏差**：容忍一定的时间误差
4. **并发冲突**：使用原子操作避免竞态条件

## 9. 性能优化策略

### 9.1 缓存优化
- **预加载**：定时任务预加载热点数据
- **分层缓存**：Redis + 本地缓存
- **过期策略**：合理设置缓存过期时间

### 9.2 队列优化
- **分片处理**：多队列并行处理
- **批量操作**：减少Redis访问次数
- **异步处理**：非关键路径异步执行

### 9.3 数据库优化
- **索引优化**：关键字段建立索引
- **分页查询**：大数据量分页处理
- **连接池**：数据库连接池管理

## 10. 监控和运维

### 10.1 关键指标监控
- **PK成功率**：应战成功/邀约总数
- **推送成功率**：推送成功/推送总数
- **处理延迟**：从邀约到开始的时间
- **队列积压**：各个队列的数据量

### 10.2 告警机制
- **队列积压告警**：队列数据量超过阈值
- **处理失败告警**：连续处理失败
- **推送失败告警**：推送成功率低于阈值

### 10.3 日志记录
- **关键操作日志**：邀约、应战、开始、结束
- **错误日志**：异常情况详细记录
- **性能日志**：处理时间和资源消耗

## 11. 与赛事PK的区别

| 特性 | 指定PK (AppointPk) | 赛事PK (ContestPk) |
|------|-------------------|-------------------|
| 使用场景 | 运营指定的一对多PK | 运营配置的一对一赛事 |
| 数据模型 | 一个主播+多个对手+时间点 | 两个主播+时间段+规则 |
| 时间管理 | 多个离散时间点 | 连续时间段 |
| 状态管理 | 邀约-应战-结果 | 配置-执行-结果 |
| 自动化程度 | 半自动（需应战确认） | 全自动（可配置） |
| 适用场景 | 擂台赛、挑战赛 | 正式比赛、锦标赛 |
| 技术复杂度 | 高（多状态管理） | 中（流程化管理） |
| 用户交互 | 需要主播确认应战 | 可配置自动开始 |

## 12. 总结

指定PK功能是一个复杂的分布式系统，具有以下特点：

### 12.1 技术亮点
1. **分布式定时任务**：多实例环境下的任务调度
2. **状态机管理**：复杂的PK状态流转
3. **实时推送**：及时的消息通知机制
4. **容错设计**：完善的异常处理和恢复

### 12.2 业务价值
1. **提升运营效率**：自动化的PK组织和管理
2. **增强用户体验**：流畅的PK邀约和应战流程
3. **支持复杂场景**：灵活的一对多PK安排

### 12.3 设计思想
1. **事件驱动**：基于时间和状态的事件处理
2. **最终一致性**：分布式环境下的数据一致性
3. **可扩展性**：支持大规模并发和数据量
