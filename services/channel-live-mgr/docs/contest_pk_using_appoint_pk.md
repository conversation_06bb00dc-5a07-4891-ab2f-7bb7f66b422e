# 使用指定PK功能实现赛事PK需求

## 1. 需求重新分析

### 1.1 赛事PK的实际需求
- **参与者**：主播 vs 主播（双方都是主播）
- **应战机制**：需要双方确认接受PK
- **未应战处理**：
  - 一方未应战 → 判该方输/对方赢
  - 双方都未应战 → 判平局
  - 双方都应战 → 正常进行PK
- **时间管理**：运营可以指定PK开始时间
- **运营配置**：支持运营人员配置PK对战

### 1.2 与指定PK功能的匹配度分析

| 需求点 | 指定PK现有能力 | 匹配程度 | 说明 |
|--------|---------------|----------|------|
| 主播vs主播 | ✅ 支持 | 100% | 完全匹配 |
| 双方确认应战 | ✅ 支持 | 100% | AcceptAppointPk接口 |
| 未应战判定 | ✅ 支持 | 100% | TimerHandleWaitingAppointPk |
| 指定时间PK | ✅ 支持 | 100% | PkRivalInfo.pk_begin_ts |
| 运营配置 | ✅ 支持 | 100% | AddAppointPkInfo接口 |
| 胜负结果 | ✅ 支持 | 100% | 自动判定+事件发布 |

**结论：指定PK功能完全可以满足赛事PK的需求！**

## 2. 实现方案设计

### 2.1 数据模型映射

#### 赛事PK配置 → AppointPkInfo
```protobuf
message AppointPkInfo {
    uint32 appoint_id = 1;           // 赛事ID
    uint32 uid = 2;                  // 主播A的UID
    uint32 begin_ts = 3;             // 赛事开始时间
    uint32 end_ts = 4;               // 赛事结束时间
    repeated PkRivalInfo rival_list = 5; // 对手信息（只有一个对手）
    uint32 update_ts = 6;            // 更新时间
    string operator = 7;             // 运营操作人
}

message PkRivalInfo {
    uint32 uid = 1;                  // 主播B的UID
    uint32 pk_begin_ts = 2;          // PK开始时间
}
```

#### 赛事PK结果 → 现有事件机制
```go
// 利用现有的PK结果事件
type ContestPkResult struct {
    ContestId    string
    WinnerUid    uint32
    LoserUid     uint32
    Result       string // "win", "lose", "draw"
    Reason       string // "normal", "abstain", "timeout"
}
```

### 2.2 接口适配层设计

```go
// 赛事PK服务适配器
type ContestPkAdapter struct {
    appointPkService *AppointPkService
}

// 创建赛事PK
func (c *ContestPkAdapter) CreateContestPk(ctx context.Context, req *CreateContestPkReq) (*CreateContestPkResp, error) {
    // 转换为AppointPkInfo
    appointPkInfo := &pb.AppointPkInfo{
        AppointId: generateAppointId(req.ContestId),
        Uid:       req.AnchorUidA,
        BeginTs:   req.ContestBeginTime,
        EndTs:     req.ContestEndTime,
        RivalList: []*pb.PkRivalInfo{
            {
                Uid:        req.AnchorUidB,
                PkBeginTs:  req.PkBeginTime,
            },
        },
        Operator: req.Operator,
    }
    
    // 调用现有的指定PK接口
    return c.appointPkService.AddAppointPkInfo(ctx, &pb.AddAppointPkInfoReq{
        Info: appointPkInfo,
    })
}

// 获取赛事PK信息
func (c *ContestPkAdapter) GetContestPkInfo(ctx context.Context, req *GetContestPkInfoReq) (*GetContestPkInfoResp, error) {
    // 通过contestId查询对应的appointId
    appointId := c.getAppointIdByContestId(req.ContestId)
    
    // 调用现有接口获取信息
    appointInfo, err := c.appointPkService.GetAppointPkInfo(ctx, &pb.GetAppointPkInfoReq{
        Uid: appointId, // 这里需要适配
    })
    
    // 转换返回结果
    return c.convertToContestPkInfo(appointInfo), nil
}

// 检查主播PK可用性
func (c *ContestPkAdapter) CheckAnchorPkAvailable(ctx context.Context, req *CheckAnchorPkAvailableReq) (*CheckAnchorPkAvailableResp, error) {
    // 查询主播在指定时间是否有PK安排
    appointPkList, err := c.appointPkService.GetAnchorAppointPkInfoList(req.AnchorUid, req.CheckTime)
    if err != nil {
        return nil, err
    }
    
    // 判断是否可用
    available := len(appointPkList) == 0
    reason := ""
    if !available {
        reason = "主播在该时间段已有赛事PK安排"
    }
    
    return &CheckAnchorPkAvailableResp{
        Available: available,
        Reason:    reason,
    }, nil
}
```

### 2.3 业务流程映射

#### 2.3.1 赛事PK创建流程
```
1. 运营调用CreateContestPk接口
2. 适配器转换为AppointPkInfo格式
3. 调用现有AddAppointPkInfo接口
4. 定时任务自动加载到处理队列
5. 返回创建成功结果
```

#### 2.3.2 赛事PK执行流程
```
1. 定时任务检测到PK开始时间
2. 发送PK邀约给双方主播
3. 主播调用AcceptAppointPk确认应战
4. 根据应战情况：
   - 双方都应战：开始正常PK
   - 一方应战：应战方获胜
   - 双方都不应战：判定平局
5. 发布PK结果事件
```

#### 2.3.3 结果查询流程
```
1. 调用GetContestPkResult接口
2. 适配器查询对应的AppointPk结果
3. 转换为赛事PK结果格式
4. 返回结果信息
```

## 3. 实现细节

### 3.1 ID映射管理
```go
// 赛事ID与指定PK ID的映射
type ContestPkMapping struct {
    ContestId string
    AppointId uint32
    AnchorA   uint32
    AnchorB   uint32
    CreateTime int64
}

// ID映射服务
type IdMappingService struct {
    cache map[string]*ContestPkMapping
    store IdMappingStore
}

func (i *IdMappingService) CreateMapping(contestId string, anchorA, anchorB uint32) uint32 {
    appointId := i.generateAppointId()
    mapping := &ContestPkMapping{
        ContestId:  contestId,
        AppointId:  appointId,
        AnchorA:    anchorA,
        AnchorB:    anchorB,
        CreateTime: time.Now().Unix(),
    }
    
    i.cache[contestId] = mapping
    i.store.Save(mapping)
    
    return appointId
}
```

### 3.2 事件转换器
```go
// 事件转换器
type EventConverter struct {
    mappingService *IdMappingService
}

func (e *EventConverter) ConvertAppointPkEvent(appointEvent *AppointPkEvent) *ContestPkEvent {
    mapping := e.mappingService.GetByAppointId(appointEvent.AppointId)
    
    return &ContestPkEvent{
        ContestId:     mapping.ContestId,
        EventType:     e.convertEventType(appointEvent.EventType),
        AnchorUidA:    mapping.AnchorA,
        AnchorUidB:    mapping.AnchorB,
        WinnerUid:     appointEvent.WinnerUid,
        Timestamp:     appointEvent.Timestamp,
    }
}
```

### 3.3 配置验证增强
```go
// 赛事PK特定的验证规则
type ContestPkValidator struct {
    appointPkValidator *AppointPkValidator
}

func (c *ContestPkValidator) ValidateContestPk(config *ContestPkConfig) error {
    // 基础验证
    if err := c.appointPkValidator.Validate(config); err != nil {
        return err
    }
    
    // 赛事PK特定验证
    if config.AnchorUidA == config.AnchorUidB {
        return errors.New("赛事PK的两个主播不能是同一人")
    }
    
    // 时间冲突检查
    if err := c.checkTimeConflict(config); err != nil {
        return err
    }
    
    return nil
}
```

## 4. 优势分析

### 4.1 技术优势
1. **复用成熟功能**：指定PK功能已经过生产验证，稳定可靠
2. **开发成本低**：只需要适配层，无需重新开发核心逻辑
3. **维护成本低**：复用现有的监控、告警、运维体系
4. **风险可控**：不影响现有指定PK功能的使用

### 4.2 业务优势
1. **功能完整**：完全满足赛事PK的所有需求
2. **扩展性好**：可以支持更复杂的赛事安排
3. **运营友好**：复用现有的运营工具和流程
4. **用户体验一致**：主播端的操作体验保持一致

### 4.3 架构优势
1. **职责清晰**：适配器模式，职责边界明确
2. **可测试性好**：适配层逻辑简单，易于测试
3. **可维护性高**：代码结构清晰，易于理解和修改
4. **向后兼容**：不影响现有系统的使用

## 5. 实施计划

### 5.1 第一阶段：适配层开发（1周）
- 实现ContestPkAdapter
- 实现ID映射服务
- 实现事件转换器
- 基础单元测试

### 5.2 第二阶段：接口集成（1周）
- 集成到gRPC服务
- 实现接口校验
- 集成测试
- 性能测试

### 5.3 第三阶段：上线验证（1周）
- 灰度发布
- 功能验证
- 性能监控
- 问题修复

## 6. 总结

**使用指定PK功能实现赛事PK是最优方案**：

1. **需求匹配度100%**：完全满足赛事PK的所有需求
2. **实现成本最低**：只需要适配层，无需重新开发
3. **风险最小**：复用成熟稳定的功能
4. **维护成本最低**：统一的技术栈和运维体系

这个方案既满足了业务需求，又最大化地利用了现有的技术资产，是一个典型的"巧妙复用"的优秀设计方案。
