# ContestPkEventPublisher 修复说明

## 修复的问题

### 1. Ka<PERSON><PERSON>生产者模块问题
**问题**：`golang.52tt.com/protocol/kafka_prod` 不存在
**修复**：
- 更改为项目实际使用的模块：`golang.52tt.com/services/channel-live-mgr/kafka_prod`
- 使用现有的KafkaProduce结构体

### 2. 协议结构体缺失问题
**问题**：`ContestPkStatusChangeEvent` 等协议结构体不存在
**修复**：
- 发现`ContestPkResultEvent`已经存在于proto文件中（第1346-1359行）
- 状态变更事件改为使用JSON格式的简化版本
- 移除了不存在的协议结构体引用
- **注意**：之前错误地重复添加了`ContestPkResultEvent`定义，已删除重复部分

### 3. BuildContestPkResultEvent方法参数不足
**问题**：原方法入参不足，无法正确构造PK结果
**修复**：
```go
// 修复前
func BuildContestPkResultEvent(config *pb.ContestPkConfig, isExtraTime bool, actualEndTime uint64) *pb.ContestPkResultEvent

// 修复后
func BuildContestPkResultEvent(config *pb.ContestPkConfig, result *pb.ContestPkResult) *pb.ContestPkResultEvent
```

### 4. Kafka消息发布方式问题
**问题**：使用了不存在的发布方法
**修复**：
- 在`kafka_prod/channel_live_event_produce.go`中添加了专用方法：
  - `ProduceContestPkResultEvent`
  - `ProduceContestPkStatusChangeEvent`
- 简化了消息发布逻辑

## 修复后的文件结构

### 1. ContestPkEventPublisher (`manager/contest_pk_event_publisher.go`)
```go
type ContestPkEventPublisher struct {
    kafkaProducer *kafka_prod.KafkaProduce
}

// 主要方法
- PublishContestPkResult() - 发布赛事PK结果事件
- PublishContestPkResultWithRetry() - 带重试的发布
- PublishContestPkStatusChange() - 发布状态变更事件（JSON格式）
- BuildContestPkResultEvent() - 构建结果事件
- IsEventPublishEnabled() - 检查发布是否启用
```

### 2. Kafka生产者扩展 (`kafka_prod/channel_live_event_produce.go`)
```go
// 新增方法
- ProduceContestPkResultEvent(contestId string, eventData []byte)
- ProduceContestPkStatusChangeEvent(contestId string, eventData []byte)
```

### 3. Proto定义 (`proto/channel-live-mgr.proto`)
```protobuf
// 使用已存在的消息类型（第1346-1359行）
message ContestPkResultEvent {
    string contest_id = 1;
    string activity_service = 2;
    uint64 anchor_uid_a = 3;
    uint64 anchor_uid_b = 4;
    uint64 winner_uid = 5;
    uint32 final_score_a = 6;
    uint32 final_score_b = 7;
    uint64 pk_begin_time = 8;
    uint64 pk_end_time = 9;
    uint64 actual_end_time = 10;
    bool is_extra_time = 11;
}
```

## 使用方式

### 1. 发布赛事PK结果事件
```go
// 在ContestPkManager中
func (m *ContestPkManager) publishContestPkResultEvent(ctx context.Context, config *pb.ContestPkConfig, result *pb.ContestPkResult) error {
    event := m.eventPublisher.BuildContestPkResultEvent(config, result)
    return m.eventPublisher.PublishContestPkResultWithRetry(ctx, event, 3)
}
```

### 2. 发布状态变更事件
```go
// 状态变更时调用
err := eventPublisher.PublishContestPkStatusChange(ctx, contestId, oldStatus, newStatus, operator)
```

## 事件格式

### 1. 赛事PK结果事件
- **Topic**: 使用现有的kafka topic
- **Key**: contestId（确保同一赛事消息有序）
- **Value**: ContestPkResultEvent的protobuf序列化数据

### 2. 状态变更事件
- **Topic**: 使用现有的kafka topic
- **Key**: contestId
- **Value**: JSON格式的状态变更数据
```json
{
  "contest_id": "contest_001",
  "old_status": 0,
  "new_status": 1,
  "operator": "admin",
  "timestamp": 1640995200,
  "event_type": "contest_pk_status_change"
}
```

## 集成说明

### 1. 初始化
```go
// 在ContestPkManager中
eventPublisher := NewContestPkEventPublisher(kafkaProducer)
```

### 2. 错误处理
- 事件发布失败不影响主业务流程
- 提供重试机制（最多3次）
- 详细的错误日志记录

### 3. 监控建议
- 监控事件发布成功率
- 监控Kafka消息积压情况
- 监控事件处理延迟

## 注意事项

1. **向后兼容**：修复保持了与现有系统的兼容性
2. **错误隔离**：事件发布失败不会影响核心业务逻辑
3. **消息顺序**：使用contestId作为key保证同一赛事消息有序
4. **资源管理**：复用现有的kafka生产者，不增加额外连接

## 测试建议

1. **单元测试**：测试事件构建和发布逻辑
2. **集成测试**：验证与Kafka的集成
3. **性能测试**：测试高并发下的事件发布性能
4. **故障测试**：测试Kafka不可用时的降级处理
