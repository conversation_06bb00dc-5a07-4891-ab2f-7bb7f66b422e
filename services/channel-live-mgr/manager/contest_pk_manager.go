package manager

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/kafka_prod"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/mysql"
)

// ContestPkManager 赛事PK管理器
type ContestPkManager struct {
	cacheClient    *cache.ChannelLiveMgrCache
	mysqlStore     *mysql.Store
	kafkaProducer  *kafka_prod.KafkaProduce
	validator      *ContestPkValidator
	eventPublisher *ContestPkEventPublisher
}

// NewContestPkManager 创建赛事PK管理器
func NewContestPkManager(cacheClient *cache.ChannelLiveMgrCache, mysqlStore *mysql.Store, kafkaProducer *kafka_prod.KafkaProduce) *ContestPkManager {
	manager := &ContestPkManager{
		cacheClient:   cacheClient,
		mysqlStore:    mysqlStore,
		kafkaProducer: kafkaProducer,
	}

	manager.validator = NewContestPkValidator(mysqlStore, cacheClient)
	manager.eventPublisher = NewContestPkEventPublisher(kafkaProducer)

	return manager
}

// CreateContestPk 创建赛事PK
func (m *ContestPkManager) CreateContestPk(ctx context.Context, config *pb.ContestPkConfig) (*pb.CreateContestPkResp, error) {
	log.InfoWithCtx(ctx, "CreateContestPk start contestId:%s", config.ContestId)

	// 1. 参数校验
	if err := m.validator.ValidateCreateRequest(ctx, config); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk ValidateCreateRequest failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 检查赛事ID唯一性
	if err := m.validator.CheckContestIdUnique(ctx, config.ContestId); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk CheckContestIdUnique failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("赛事ID已存在: %s", config.ContestId)
	}

	// 3. 检查时间冲突
	if err := m.validator.CheckTimeConflict(ctx, config.AnchorUidA, config.AnchorUidB, int64(config.PkBeginTime), int64(config.PkEndTime), ""); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk CheckTimeConflict failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("时间冲突: %v", err)
	}

	// 4. 设置默认值
	nowTs := uint64(time.Now().Unix())
	config.Status = pb.ContestPkStatus_CONTEST_PK_PENDING
	config.CreateTime = nowTs
	config.UpdateTime = nowTs

	// 5. 保存到数据库
	if err := m.mysqlStore.CreateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk CreateContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("保存数据库失败: %v", err)
	}

	// 6. 设置缓存
	if err := m.cacheClient.SetContestPkConfig(config.ContestId, config); err != nil {
		log.WarnWithCtx(ctx, "CreateContestPk SetContestPkConfig cache failed contestId:%s err:%v", config.ContestId, err)
		// 缓存失败不影响主流程
	}

	// 7. 添加定时任务
	if err := m.addTimerTasks(ctx, config); err != nil {
		log.WarnWithCtx(ctx, "CreateContestPk addTimerTasks failed contestId:%s err:%v", config.ContestId, err)
		// 定时任务失败不影响主流程
	}

	log.InfoWithCtx(ctx, "CreateContestPk success contestId:%s", config.ContestId)
	return &pb.CreateContestPkResp{
		ContestId: config.ContestId,
	}, nil
}

// UpdateContestPk 更新赛事PK配置
func (m *ContestPkManager) UpdateContestPk(ctx context.Context, config *pb.ContestPkConfig) error {
	log.InfoWithCtx(ctx, "UpdateContestPk start contestId:%s", config.ContestId)

	// 1. 参数校验
	if err := m.validator.ValidateUpdateRequest(ctx, config); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk ValidateUpdateRequest failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 检查赛事是否存在且可更新
	existingConfig, err := m.GetContestPkConfig(ctx, config.ContestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk GetContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("赛事不存在: %s", config.ContestId)
	}

	if existingConfig.Status != pb.ContestPkStatus_CONTEST_PK_PENDING {
		log.ErrorWithCtx(ctx, "UpdateContestPk contest status not pending contestId:%s status:%d", config.ContestId, existingConfig.Status)
		return fmt.Errorf("只能更新待开始状态的赛事")
	}

	// 3. 检查时间冲突（排除当前赛事）
	if err := m.validator.CheckTimeConflict(ctx, config.AnchorUidA, config.AnchorUidB, int64(config.PkBeginTime), int64(config.PkEndTime), config.ContestId); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk CheckTimeConflict failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("时间冲突: %v", err)
	}

	// 4. 更新时间戳
	config.UpdateTime = uint64(time.Now().Unix())
	config.CreateTime = existingConfig.CreateTime // 保持原创建时间

	// 5. 更新数据库
	if err := m.mysqlStore.UpdateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk UpdateContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	// 6. 更新缓存
	if err := m.cacheClient.SetContestPkConfig(config.ContestId, config); err != nil {
		log.WarnWithCtx(ctx, "UpdateContestPk SetContestPkConfig cache failed contestId:%s err:%v", config.ContestId, err)
	}

	// 7. 重新添加定时任务
	if err := m.addTimerTasks(ctx, config); err != nil {
		log.WarnWithCtx(ctx, "UpdateContestPk addTimerTasks failed contestId:%s err:%v", config.ContestId, err)
	}

	log.InfoWithCtx(ctx, "UpdateContestPk success contestId:%s", config.ContestId)
	return nil
}

// CancelContestPk 取消赛事PK
func (m *ContestPkManager) CancelContestPk(ctx context.Context, contestId, operator, reason string) error {
	log.InfoWithCtx(ctx, "CancelContestPk start contestId:%s operator:%s reason:%s", contestId, operator, reason)

	// 1. 获取赛事配置
	config, err := m.GetContestPkConfig(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContestPk GetContestPkConfig failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("赛事不存在: %s", contestId)
	}

	// 2. 检查状态是否可取消
	if config.Status == pb.ContestPkStatus_CONTEST_PK_FINISHED || config.Status == pb.ContestPkStatus_CONTEST_PK_CANCELLED {
		log.ErrorWithCtx(ctx, "CancelContestPk contest cannot be cancelled contestId:%s status:%d", contestId, config.Status)
		return fmt.Errorf("赛事状态不允许取消")
	}

	// 3. 更新状态
	config.Status = pb.ContestPkStatus_CONTEST_PK_CANCELLED
	config.Operator = operator
	config.UpdateTime = uint64(time.Now().Unix())

	// 4. 更新数据库
	if err := m.mysqlStore.UpdateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "CancelContestPk UpdateContestPkConfig failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	// 5. 更新缓存
	if err := m.cacheClient.SetContestPkConfig(contestId, config); err != nil {
		log.WarnWithCtx(ctx, "CancelContestPk SetContestPkConfig cache failed contestId:%s err:%v", contestId, err)
	}

	// 6. 清理主播限制
	m.cacheClient.DelContestPkRestriction(config.AnchorUidA)
	m.cacheClient.DelContestPkRestriction(config.AnchorUidB)

	log.InfoWithCtx(ctx, "CancelContestPk success contestId:%s", contestId)
	return nil
}

// GetContestPkConfig 获取赛事PK配置
func (m *ContestPkManager) GetContestPkConfig(ctx context.Context, contestId string) (*pb.ContestPkConfig, error) {
	// 1. 尝试从缓存获取
	config, err := m.cacheClient.GetContestPkConfig(contestId)
	if err == nil && config != nil {
		return config, nil
	}

	// 2. 缓存未命中，从数据库获取
	config, err = m.mysqlStore.GetContestPkConfig(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkConfig from mysql failed contestId:%s err:%v", contestId, err)
		return nil, err
	}

	// 3. 更新缓存
	go func() {
		if err := m.cacheClient.SetContestPkConfig(contestId, config); err != nil {
			log.Errorf("GetContestPkConfig SetContestPkConfig cache failed contestId:%s err:%v", contestId, err)
		}
	}()

	return config, nil
}

// BatchGetContestPkConfig 批量获取赛事PK配置
func (m *ContestPkManager) BatchGetContestPkConfig(ctx context.Context, req *pb.BatchGetContestPkConfigReq) (*pb.BatchGetContestPkConfigResp, error) {
	configs, totalCount, err := m.mysqlStore.BatchGetContestPkConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkConfig failed err:%v", err)
		return nil, err
	}

	// 异步更新缓存
	go func() {
		if err := m.cacheClient.BatchSetContestPkConfig(configs); err != nil {
			log.Errorf("BatchGetContestPkConfig BatchSetContestPkConfig cache failed err:%v", err)
		}
	}()

	nextPage := uint32(0)
	if req.Page > 0 && len(configs) == int(req.PageSize) {
		nextPage = req.Page + 1
	}

	return &pb.BatchGetContestPkConfigResp{
		ConfigList: configs,
		TotalCount: totalCount,
		NextPage:   nextPage,
	}, nil
}

// CheckAnchorPkAvailable 检查主播是否可以参与常规PK
func (m *ContestPkManager) CheckAnchorPkAvailable(ctx context.Context, anchorUid uint64, checkTime int64) (*pb.CheckAnchorPkAvailableResp, error) {
	// 1. 检查缓存中的限制信息
	restriction, err := m.cacheClient.GetContestPkRestriction(anchorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorPkAvailable GetContestPkRestriction failed anchorUid:%d err:%v", anchorUid, err)
		// 缓存查询失败，继续检查数据库
	}

	if restriction != nil {
		// 检查是否在限制时间内
		if uint64(checkTime) >= restriction.RestrictBegin && uint64(checkTime) <= restriction.RestrictEnd {
			// 获取阻塞的赛事信息
			blockingContest, _ := m.GetContestPkConfig(ctx, restriction.ContestID)

			return &pb.CheckAnchorPkAvailableResp{
				Available:       false,
				Reason:          restriction.Reason,
				BlockingContest: blockingContest,
			}, nil
		}
	}

	// 2. 从数据库检查是否有冲突的赛事
	hasConflict, err := m.mysqlStore.CheckAnchorContestPkConflict(ctx, anchorUid, anchorUid, uint64(checkTime), uint64(checkTime), "")
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorPkAvailable CheckAnchorContestPkConflict failed anchorUid:%d err:%v", anchorUid, err)
		return nil, fmt.Errorf("检查冲突失败: %v", err)
	}

	if hasConflict {
		return &pb.CheckAnchorPkAvailableResp{
			Available: false,
			Reason:    "主播有进行中的赛事PK",
		}, nil
	}

	return &pb.CheckAnchorPkAvailableResp{
		Available: true,
	}, nil
}

// StartContestPk 开始赛事PK
func (m *ContestPkManager) StartContestPk(ctx context.Context, contestId, operator string, actualStartTime uint64) (*pb.ContestPkResult, error) {
	log.InfoWithCtx(ctx, "StartContestPk start contestId:%s operator:%s", contestId, operator)

	// 1. 获取赛事配置
	config, err := m.GetContestPkConfig(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartContestPk GetContestPkConfig failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("获取赛事配置失败: %v", err)
	}

	// 2. 检查赛事状态
	if config.Status != pb.ContestPkStatus_CONTEST_PK_PENDING {
		log.ErrorWithCtx(ctx, "StartContestPk contest status not pending contestId:%s status:%d", contestId, config.Status)
		return nil, fmt.Errorf("赛事状态不是待开始")
	}

	// 3. 设置默认开始时间
	if actualStartTime == 0 {
		actualStartTime = uint64(time.Now().Unix())
	}

	// 4. 创建结果记录
	result := &pb.ContestPkResult{
		ContestId:       contestId,
		WinnerUid:       0,
		FinalScoreA:     0,
		FinalScoreB:     0,
		ActualStartTime: actualStartTime,
		ActualEndTime:   0,
		IsExtraTime:     false,
		PkDuration:      0,
		ResultStatus:    pb.ContestPkResultStatus_RESULT_IN_PROGRESS,
	}

	// 5. 保存结果到数据库
	if err := m.mysqlStore.CreateContestPkResult(ctx, nil, result); err != nil {
		log.ErrorWithCtx(ctx, "StartContestPk CreateContestPkResult failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("创建结果记录失败: %v", err)
	}

	// 6. 更新赛事状态为进行中
	config.Status = pb.ContestPkStatus_CONTEST_PK_RUNNING
	config.UpdateTime = actualStartTime
	if err := m.mysqlStore.UpdateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "StartContestPk UpdateContestPkConfig failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("更新赛事状态失败: %v", err)
	}

	// 7. 更新缓存
	m.cacheClient.SetContestPkConfig(contestId, config)

	log.InfoWithCtx(ctx, "StartContestPk success contestId:%s", contestId)
	return result, nil
}

// UpdateContestPkResult 更新赛事PK结果
func (m *ContestPkManager) UpdateContestPkResult(ctx context.Context, contestId string, scoreA, scoreB uint32, operator, updateReason string) (*pb.ContestPkResult, error) {
	log.InfoWithCtx(ctx, "UpdateContestPkResult start contestId:%s scoreA:%d scoreB:%d", contestId, scoreA, scoreB)

	// 1. 获取当前结果
	result, err := m.mysqlStore.GetContestPkResult(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPkResult GetContestPkResult failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("获取结果记录失败: %v", err)
	}

	// 2. 检查结果状态
	if result.ResultStatus != pb.ContestPkResultStatus_RESULT_IN_PROGRESS {
		log.ErrorWithCtx(ctx, "UpdateContestPkResult result status not in progress contestId:%s status:%d", contestId, result.ResultStatus)
		return nil, fmt.Errorf("赛事PK不在进行中")
	}

	// 3. 更新分数
	result.FinalScoreA = scoreA
	result.FinalScoreB = scoreB

	// 4. 保存到数据库
	if err := m.mysqlStore.UpdateContestPkResult(ctx, nil, result); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPkResult UpdateContestPkResult failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("更新结果记录失败: %v", err)
	}

	log.InfoWithCtx(ctx, "UpdateContestPkResult success contestId:%s", contestId)
	return result, nil
}

// FinishContestPk 结束赛事PK
func (m *ContestPkManager) FinishContestPk(ctx context.Context, contestId string, winnerUid uint64, finalScoreA, finalScoreB uint32, isExtraTime bool, actualEndTime uint64, operator, finishReason string) (*pb.ContestPkResult, error) {
	log.InfoWithCtx(ctx, "FinishContestPk start contestId:%s winnerUid:%d", contestId, winnerUid)

	// 1. 获取赛事配置
	config, err := m.GetContestPkConfig(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FinishContestPk GetContestPkConfig failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("获取赛事配置失败: %v", err)
	}

	// 2. 获取当前结果
	result, err := m.mysqlStore.GetContestPkResult(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "FinishContestPk GetContestPkResult failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("获取结果记录失败: %v", err)
	}

	// 3. 设置默认结束时间
	if actualEndTime == 0 {
		actualEndTime = uint64(time.Now().Unix())
	}

	// 4. 更新结果信息
	result.WinnerUid = winnerUid
	result.FinalScoreA = finalScoreA
	result.FinalScoreB = finalScoreB
	result.ActualEndTime = actualEndTime
	result.IsExtraTime = isExtraTime
	result.ResultStatus = pb.ContestPkResultStatus_RESULT_FINISHED

	// 计算PK时长
	if result.ActualStartTime > 0 {
		result.PkDuration = uint32(actualEndTime - result.ActualStartTime)
	}

	// 5. 保存结果到数据库
	if err := m.mysqlStore.UpdateContestPkResult(ctx, nil, result); err != nil {
		log.ErrorWithCtx(ctx, "FinishContestPk UpdateContestPkResult failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("更新结果记录失败: %v", err)
	}

	// 6. 更新赛事状态为已结束
	config.Status = pb.ContestPkStatus_CONTEST_PK_FINISHED
	config.UpdateTime = actualEndTime
	if err := m.mysqlStore.UpdateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "FinishContestPk UpdateContestPkConfig failed contestId:%s err:%v", contestId, err)
		return nil, fmt.Errorf("更新赛事状态失败: %v", err)
	}

	// 7. 更新缓存
	m.cacheClient.SetContestPkConfig(contestId, config)

	// 8. 发布结果事件
	if err := m.publishContestPkResultEvent(ctx, config, result); err != nil {
		log.ErrorWithCtx(ctx, "FinishContestPk publishContestPkResultEvent failed contestId:%s err:%v", contestId, err)
		// 事件发布失败不影响主流程
	}

	// 9. 清理主播限制
	m.cacheClient.DelContestPkRestriction(config.AnchorUidA)
	m.cacheClient.DelContestPkRestriction(config.AnchorUidB)

	log.InfoWithCtx(ctx, "FinishContestPk success contestId:%s", contestId)
	return result, nil
}

// GetContestPkResult 获取赛事PK结果
func (m *ContestPkManager) GetContestPkResult(ctx context.Context, contestId string) (*pb.ContestPkResult, error) {
	return m.mysqlStore.GetContestPkResult(ctx, contestId)
}

// GetContestPkInfo 获取赛事PK完整信息
func (m *ContestPkManager) GetContestPkInfo(ctx context.Context, contestId string, includeResult bool) (*pb.ContestPkInfo, error) {
	// 1. 获取配置信息
	config, err := m.GetContestPkConfig(ctx, contestId)
	if err != nil {
		return nil, err
	}

	info := &pb.ContestPkInfo{
		Config: config,
	}

	// 2. 如果需要包含结果信息
	if includeResult {
		result, err := m.mysqlStore.GetContestPkResult(ctx, contestId)
		if err != nil {
			// 结果不存在不算错误，可能还没开始
			log.WarnWithCtx(ctx, "GetContestPkInfo GetContestPkResult failed contestId:%s err:%v", contestId, err)
		} else {
			info.Result = result
		}
	}

	return info, nil
}

// publishContestPkResultEvent 发布赛事PK结果事件
func (m *ContestPkManager) publishContestPkResultEvent(ctx context.Context, config *pb.ContestPkConfig, result *pb.ContestPkResult) error {
	event := &pb.ContestPkResultEvent{
		ContestId:       config.ContestId,
		ActivityService: config.ActivityService,
		AnchorUidA:      config.AnchorUidA,
		AnchorUidB:      config.AnchorUidB,
		WinnerUid:       result.WinnerUid,
		FinalScoreA:     result.FinalScoreA,
		FinalScoreB:     result.FinalScoreB,
		PkBeginTime:     config.PkBeginTime,
		PkEndTime:       config.PkEndTime,
		ActualEndTime:   result.ActualEndTime,
		IsExtraTime:     result.IsExtraTime,
	}

	return m.eventPublisher.PublishContestPkResultWithRetry(ctx, event, 3)
}

// addTimerTasks 添加定时任务
func (m *ContestPkManager) addTimerTasks(ctx context.Context, config *pb.ContestPkConfig) error {
	// 添加限制任务（开始前10分钟）
	restrictTime := config.PkBeginTime - 600 // 10分钟 = 600秒
	if err := m.cacheClient.AddContestPkTimer("restriction", config.ContestId, restrictTime, 0); err != nil {
		log.ErrorWithCtx(ctx, "addTimerTasks AddContestPkTimer restriction failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	// 添加开始任务
	if err := m.cacheClient.AddContestPkTimer("start", config.ContestId, config.PkBeginTime, 0); err != nil {
		log.ErrorWithCtx(ctx, "addTimerTasks AddContestPkTimer start failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	// 添加结束检查任务
	finishTime := config.PkEndTime + 300 // 结束后5分钟检查
	if err := m.cacheClient.AddContestPkTimer("finish", config.ContestId, finishTime, 0); err != nil {
		log.ErrorWithCtx(ctx, "addTimerTasks AddContestPkTimer finish failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	return nil
}
