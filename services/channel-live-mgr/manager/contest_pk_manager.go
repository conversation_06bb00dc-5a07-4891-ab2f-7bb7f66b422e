package manager

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/kafka_prod"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"services/channel-live-mgr/cache"
	"services/channel-live-mgr/mysql"
)

// ContestPkManager 赛事PK管理器
type ContestPkManager struct {
	cacheClient    *cache.ChannelLiveMgrCache
	mysqlStore     *mysql.Store
	kafkaProducer  *kafka_prod.KafkaProduce
	validator      *ContestPkValidator
	eventPublisher *ContestPkEventPublisher
}

// NewContestPkManager 创建赛事PK管理器
func NewContestPkManager(cacheClient *cache.ChannelLiveMgrCache, mysqlStore *mysql.Store, kafkaProducer *kafka_prod.KafkaProduce) *ContestPkManager {
	manager := &ContestPkManager{
		cacheClient:   cacheClient,
		mysqlStore:    mysqlStore,
		kafkaProducer: kafkaProducer,
	}

	manager.validator = NewContestPkValidator(mysqlStore, cacheClient)
	manager.eventPublisher = NewContestPkEventPublisher(kafkaProducer)

	return manager
}

// CreateContestPk 创建赛事PK
func (m *ContestPkManager) CreateContestPk(ctx context.Context, config *pb.ContestPkConfig) (*pb.CreateContestPkResp, error) {
	log.InfoWithCtx(ctx, "CreateContestPk start contestId:%s", config.ContestId)

	// 1. 参数校验
	if err := m.validator.ValidateCreateRequest(ctx, config); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk ValidateCreateRequest failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 检查赛事ID唯一性
	if err := m.validator.CheckContestIdUnique(ctx, config.ContestId); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk CheckContestIdUnique failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("赛事ID已存在: %s", config.ContestId)
	}

	// 3. 检查时间冲突
	if err := m.validator.CheckTimeConflict(ctx, config.AnchorUidA, config.AnchorUidB, int64(config.PkBeginTime), int64(config.PkEndTime), ""); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk CheckTimeConflict failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("时间冲突: %v", err)
	}

	// 4. 设置默认值
	nowTs := uint64(time.Now().Unix())
	config.Status = pb.ContestPkStatus_CONTEST_PK_PENDING
	config.CreateTime = nowTs
	config.UpdateTime = nowTs

	// 5. 保存到数据库
	if err := m.mysqlStore.CreateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk CreateContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return nil, fmt.Errorf("保存数据库失败: %v", err)
	}

	// 6. 设置缓存
	if err := m.cacheClient.SetContestPkConfig(config.ContestId, config); err != nil {
		log.WarnWithCtx(ctx, "CreateContestPk SetContestPkConfig cache failed contestId:%s err:%v", config.ContestId, err)
		// 缓存失败不影响主流程
	}

	// 7. 添加定时任务
	if err := m.addTimerTasks(ctx, config); err != nil {
		log.WarnWithCtx(ctx, "CreateContestPk addTimerTasks failed contestId:%s err:%v", config.ContestId, err)
		// 定时任务失败不影响主流程
	}

	log.InfoWithCtx(ctx, "CreateContestPk success contestId:%s", config.ContestId)
	return &pb.CreateContestPkResp{
		ContestId: config.ContestId,
	}, nil
}

// UpdateContestPk 更新赛事PK配置
func (m *ContestPkManager) UpdateContestPk(ctx context.Context, config *pb.ContestPkConfig) error {
	log.InfoWithCtx(ctx, "UpdateContestPk start contestId:%s", config.ContestId)

	// 1. 参数校验
	if err := m.validator.ValidateUpdateRequest(ctx, config); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk ValidateUpdateRequest failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 检查赛事是否存在且可更新
	existingConfig, err := m.GetContestPkConfig(ctx, config.ContestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk GetContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("赛事不存在: %s", config.ContestId)
	}

	if existingConfig.Status != pb.ContestPkStatus_CONTEST_PK_PENDING {
		log.ErrorWithCtx(ctx, "UpdateContestPk contest status not pending contestId:%s status:%d", config.ContestId, existingConfig.Status)
		return fmt.Errorf("只能更新待开始状态的赛事")
	}

	// 3. 检查时间冲突（排除当前赛事）
	if err := m.validator.CheckTimeConflict(ctx, config.AnchorUidA, config.AnchorUidB, int64(config.PkBeginTime), int64(config.PkEndTime), config.ContestId); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk CheckTimeConflict failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("时间冲突: %v", err)
	}

	// 4. 更新时间戳
	config.UpdateTime = uint64(time.Now().Unix())
	config.CreateTime = existingConfig.CreateTime // 保持原创建时间

	// 5. 更新数据库
	if err := m.mysqlStore.UpdateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk UpdateContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	// 6. 更新缓存
	if err := m.cacheClient.SetContestPkConfig(config.ContestId, config); err != nil {
		log.WarnWithCtx(ctx, "UpdateContestPk SetContestPkConfig cache failed contestId:%s err:%v", config.ContestId, err)
	}

	// 7. 重新添加定时任务
	if err := m.addTimerTasks(ctx, config); err != nil {
		log.WarnWithCtx(ctx, "UpdateContestPk addTimerTasks failed contestId:%s err:%v", config.ContestId, err)
	}

	log.InfoWithCtx(ctx, "UpdateContestPk success contestId:%s", config.ContestId)
	return nil
}

// CancelContestPk 取消赛事PK
func (m *ContestPkManager) CancelContestPk(ctx context.Context, contestId, operator, reason string) error {
	log.InfoWithCtx(ctx, "CancelContestPk start contestId:%s operator:%s reason:%s", contestId, operator, reason)

	// 1. 获取赛事配置
	config, err := m.GetContestPkConfig(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContestPk GetContestPkConfig failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("赛事不存在: %s", contestId)
	}

	// 2. 检查状态是否可取消
	if config.Status == pb.ContestPkStatus_CONTEST_PK_FINISHED || config.Status == pb.ContestPkStatus_CONTEST_PK_CANCELLED {
		log.ErrorWithCtx(ctx, "CancelContestPk contest cannot be cancelled contestId:%s status:%d", contestId, config.Status)
		return fmt.Errorf("赛事状态不允许取消")
	}

	// 3. 更新状态
	config.Status = pb.ContestPkStatus_CONTEST_PK_CANCELLED
	config.Operator = operator
	config.UpdateTime = uint64(time.Now().Unix())

	// 4. 更新数据库
	if err := m.mysqlStore.UpdateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "CancelContestPk UpdateContestPkConfig failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	// 5. 更新缓存
	if err := m.cacheClient.SetContestPkConfig(contestId, config); err != nil {
		log.WarnWithCtx(ctx, "CancelContestPk SetContestPkConfig cache failed contestId:%s err:%v", contestId, err)
	}

	// 6. 清理主播限制
	m.cacheClient.DelContestPkRestriction(config.AnchorUidA)
	m.cacheClient.DelContestPkRestriction(config.AnchorUidB)

	log.InfoWithCtx(ctx, "CancelContestPk success contestId:%s", contestId)
	return nil
}

// GetContestPkConfig 获取赛事PK配置
func (m *ContestPkManager) GetContestPkConfig(ctx context.Context, contestId string) (*pb.ContestPkConfig, error) {
	// 1. 尝试从缓存获取
	config, err := m.cacheClient.GetContestPkConfig(contestId)
	if err == nil && config != nil {
		return config, nil
	}

	// 2. 缓存未命中，从数据库获取
	config, err = m.mysqlStore.GetContestPkConfig(ctx, contestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkConfig from mysql failed contestId:%s err:%v", contestId, err)
		return nil, err
	}

	// 3. 更新缓存
	go func() {
		if err := m.cacheClient.SetContestPkConfig(contestId, config); err != nil {
			log.Errorf("GetContestPkConfig SetContestPkConfig cache failed contestId:%s err:%v", contestId, err)
		}
	}()

	return config, nil
}

// BatchGetContestPkConfig 批量获取赛事PK配置
func (m *ContestPkManager) BatchGetContestPkConfig(ctx context.Context, req *pb.BatchGetContestPkConfigReq) (*pb.BatchGetContestPkConfigResp, error) {
	configs, totalCount, err := m.mysqlStore.BatchGetContestPkConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkConfig failed err:%v", err)
		return nil, err
	}

	// 异步更新缓存
	go func() {
		if err := m.cacheClient.BatchSetContestPkConfig(configs); err != nil {
			log.Errorf("BatchGetContestPkConfig BatchSetContestPkConfig cache failed err:%v", err)
		}
	}()

	nextPage := uint32(0)
	if req.Page > 0 && len(configs) == int(req.PageSize) {
		nextPage = req.Page + 1
	}

	return &pb.BatchGetContestPkConfigResp{
		ConfigList: configs,
		TotalCount: totalCount,
		NextPage:   nextPage,
	}, nil
}

// CheckAnchorPkAvailable 检查主播是否可以参与常规PK
func (m *ContestPkManager) CheckAnchorPkAvailable(ctx context.Context, anchorUid uint64, checkTime int64) (*pb.CheckAnchorPkAvailableResp, error) {
	// 1. 检查缓存中的限制信息
	restriction, err := m.cacheClient.GetContestPkRestriction(anchorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorPkAvailable GetContestPkRestriction failed anchorUid:%d err:%v", anchorUid, err)
		// 缓存查询失败，继续检查数据库
	}

	if restriction != nil {
		// 检查是否在限制时间内
		if uint64(checkTime) >= restriction.RestrictBegin && uint64(checkTime) <= restriction.RestrictEnd {
			// 获取阻塞的赛事信息
			blockingContest, _ := m.GetContestPkConfig(ctx, restriction.ContestID)

			return &pb.CheckAnchorPkAvailableResp{
				Available:       false,
				Reason:          restriction.Reason,
				BlockingContest: blockingContest,
			}, nil
		}
	}

	// 2. 从数据库检查是否有冲突的赛事
	hasConflict, err := m.mysqlStore.CheckAnchorContestPkConflict(ctx, anchorUid, anchorUid, uint64(checkTime), uint64(checkTime), "")
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorPkAvailable CheckAnchorContestPkConflict failed anchorUid:%d err:%v", anchorUid, err)
		return nil, fmt.Errorf("检查冲突失败: %v", err)
	}

	if hasConflict {
		return &pb.CheckAnchorPkAvailableResp{
			Available: false,
			Reason:    "主播有进行中的赛事PK",
		}, nil
	}

	return &pb.CheckAnchorPkAvailableResp{
		Available: true,
	}, nil
}

// addTimerTasks 添加定时任务
func (m *ContestPkManager) addTimerTasks(ctx context.Context, config *pb.ContestPkConfig) error {
	// 添加限制任务（开始前10分钟）
	restrictTime := config.PkBeginTime - 600 // 10分钟 = 600秒
	if err := m.cacheClient.AddContestPkTimer("restriction", config.ContestId, restrictTime, 0); err != nil {
		log.ErrorWithCtx(ctx, "addTimerTasks AddContestPkTimer restriction failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	// 添加开始任务
	if err := m.cacheClient.AddContestPkTimer("start", config.ContestId, config.PkBeginTime, 0); err != nil {
		log.ErrorWithCtx(ctx, "addTimerTasks AddContestPkTimer start failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	// 添加结束检查任务
	finishTime := config.PkEndTime + 300 // 结束后5分钟检查
	if err := m.cacheClient.AddContestPkTimer("finish", config.ContestId, finishTime, 0); err != nil {
		log.ErrorWithCtx(ctx, "addTimerTasks AddContestPkTimer finish failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	return nil
}
