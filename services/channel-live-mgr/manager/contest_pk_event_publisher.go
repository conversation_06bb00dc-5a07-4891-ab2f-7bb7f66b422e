package manager

import (
	"context"
	"fmt"
	"time"

	"github.com/golang/protobuf/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/kafka_prod"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

// ContestPkEventPublisher 赛事PK事件发布器
type ContestPkEventPublisher struct {
	kafkaProducer *kafka_prod.KafkaProduce
}

// NewContestPkEventPublisher 创建赛事PK事件发布器
func NewContestPkEventPublisher(kafkaProducer *kafka_prod.KafkaProduce) *ContestPkEventPublisher {
	return &ContestPkEventPublisher{
		kafkaProducer: kafkaProducer,
	}
}

// PublishContestPkResult 发布赛事PK结果事件
func (p *ContestPkEventPublisher) PublishContestPkResult(ctx context.Context, event *pb.ContestPkResultEvent) error {
	log.InfoWithCtx(ctx, "PublishContestPkResult start contestId:%s", event.ContestId)

	// 1. 序列化事件数据
	eventData, err := proto.Marshal(event)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishContestPkResult Marshal failed contestId:%s err:%v", event.ContestId, err)
		return fmt.Errorf("marshal event failed: %v", err)
	}

	// 2. 构造Kafka消息
	message := &kafka_prod.Message{
		Topic: "contest_pk_result",
		Key:   event.ContestId,
		Value: eventData,
		Headers: map[string]string{
			"event_type":       "contest_pk_result",
			"activity_service": event.ActivityService,
			"contest_id":       event.ContestId,
			"timestamp":        fmt.Sprintf("%d", time.Now().Unix()),
			"version":          "1.0",
		},
	}

	// 3. 发布到Kafka
	err = p.kafkaProducer.SendMessage(ctx, message)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishContestPkResult SendMessage failed contestId:%s err:%v", event.ContestId, err)
		return fmt.Errorf("send kafka message failed: %v", err)
	}

	log.InfoWithCtx(ctx, "PublishContestPkResult success contestId:%s", event.ContestId)
	return nil
}

// PublishContestPkResultWithRetry 带重试的发布赛事PK结果事件
func (p *ContestPkEventPublisher) PublishContestPkResultWithRetry(ctx context.Context, event *pb.ContestPkResultEvent, maxRetries int) error {
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if err := p.PublishContestPkResult(ctx, event); err == nil {
			return nil
		} else {
			lastErr = err
			log.WarnWithCtx(ctx, "PublishContestPkResult retry %d failed contestId:%s err:%v", i+1, event.ContestId, err)

			// 指数退避
			if i < maxRetries-1 {
				sleepTime := time.Duration(i+1) * time.Second
				time.Sleep(sleepTime)
			}
		}
	}

	log.ErrorWithCtx(ctx, "PublishContestPkResult failed after %d retries contestId:%s lastErr:%v", maxRetries, event.ContestId, lastErr)
	return fmt.Errorf("publish failed after %d retries, last error: %v", maxRetries, lastErr)
}

// PublishContestPkStatusChange 发布赛事PK状态变更事件
func (p *ContestPkEventPublisher) PublishContestPkStatusChange(ctx context.Context, contestId string, oldStatus, newStatus pb.ContestPkStatus, operator string) error {
	log.InfoWithCtx(ctx, "PublishContestPkStatusChange contestId:%s oldStatus:%d newStatus:%d operator:%s", contestId, oldStatus, newStatus, operator)

	// 构造状态变更事件
	statusEvent := &pb.ContestPkStatusChangeEvent{
		ContestId: contestId,
		OldStatus: oldStatus,
		NewStatus: newStatus,
		Operator:  operator,
		Timestamp: uint64(time.Now().Unix()),
	}

	// 序列化事件数据
	eventData, err := proto.Marshal(statusEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishContestPkStatusChange Marshal failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("marshal status event failed: %v", err)
	}

	// 构造Kafka消息
	message := &kafka_prod.Message{
		Topic: "contest_pk_status_change",
		Key:   contestId,
		Value: eventData,
		Headers: map[string]string{
			"event_type": "contest_pk_status_change",
			"contest_id": contestId,
			"old_status": fmt.Sprintf("%d", oldStatus),
			"new_status": fmt.Sprintf("%d", newStatus),
			"operator":   operator,
			"timestamp":  fmt.Sprintf("%d", time.Now().Unix()),
		},
	}

	// 发布到Kafka
	err = p.kafkaProducer.SendMessage(ctx, message)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishContestPkStatusChange SendMessage failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("send kafka status change message failed: %v", err)
	}

	log.InfoWithCtx(ctx, "PublishContestPkStatusChange success contestId:%s", contestId)
	return nil
}

// BuildContestPkResultEvent 构建赛事PK结果事件
func (p *ContestPkEventPublisher) BuildContestPkResultEvent(config *pb.ContestPkConfig, isExtraTime bool, actualEndTime uint64) *pb.ContestPkResultEvent {
	return &pb.ContestPkResultEvent{
		ContestId:       config.ContestId,
		ActivityService: config.ActivityService,
		AnchorUidA:      config.AnchorUidA,
		AnchorUidB:      config.AnchorUidB,
		WinnerUid:       config.WinnerUid,
		FinalScoreA:     config.FinalScoreA,
		FinalScoreB:     config.FinalScoreB,
		PkBeginTime:     config.PkBeginTime,
		PkEndTime:       config.PkEndTime,
		ActualEndTime:   actualEndTime,
		IsExtraTime:     isExtraTime,
	}
}

// ValidateResultEvent 校验结果事件
func (p *ContestPkEventPublisher) ValidateResultEvent(event *pb.ContestPkResultEvent) error {
	if event.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if event.ActivityService == "" {
		return fmt.Errorf("活动服务标识不能为空")
	}
	if event.AnchorUidA == 0 || event.AnchorUidB == 0 {
		return fmt.Errorf("主播UID不能为空")
	}
	if event.PkBeginTime == 0 || event.PkEndTime == 0 {
		return fmt.Errorf("PK时间不能为空")
	}
	if event.ActualEndTime == 0 {
		return fmt.Errorf("实际结束时间不能为空")
	}

	// 检查获胜者UID
	if event.WinnerUid != 0 && event.WinnerUid != event.AnchorUidA && event.WinnerUid != event.AnchorUidB {
		return fmt.Errorf("获胜者UID必须是参赛主播之一或0（平局）")
	}

	// 检查分数合理性
	if event.WinnerUid == event.AnchorUidA && event.FinalScoreA <= event.FinalScoreB {
		return fmt.Errorf("主播A获胜但分数不高于主播B")
	}
	if event.WinnerUid == event.AnchorUidB && event.FinalScoreB <= event.FinalScoreA {
		return fmt.Errorf("主播B获胜但分数不高于主播A")
	}
	if event.WinnerUid == 0 && event.FinalScoreA != event.FinalScoreB {
		return fmt.Errorf("平局时两个主播分数应该相等")
	}

	return nil
}

// PublishBatchContestPkResults 批量发布赛事PK结果事件
func (p *ContestPkEventPublisher) PublishBatchContestPkResults(ctx context.Context, events []*pb.ContestPkResultEvent) error {
	if len(events) == 0 {
		return nil
	}

	log.InfoWithCtx(ctx, "PublishBatchContestPkResults start count:%d", len(events))

	successCount := 0
	failedCount := 0

	for _, event := range events {
		if err := p.PublishContestPkResult(ctx, event); err != nil {
			log.ErrorWithCtx(ctx, "PublishBatchContestPkResults failed contestId:%s err:%v", event.ContestId, err)
			failedCount++
		} else {
			successCount++
		}
	}

	log.InfoWithCtx(ctx, "PublishBatchContestPkResults finished total:%d success:%d failed:%d", len(events), successCount, failedCount)

	if failedCount > 0 {
		return fmt.Errorf("批量发布部分失败，成功:%d，失败:%d", successCount, failedCount)
	}

	return nil
}

// GetEventTopicByType 根据事件类型获取Topic
func (p *ContestPkEventPublisher) GetEventTopicByType(eventType string) string {
	switch eventType {
	case "contest_pk_result":
		return "contest_pk_result"
	case "contest_pk_status_change":
		return "contest_pk_status_change"
	case "contest_pk_created":
		return "contest_pk_lifecycle"
	case "contest_pk_updated":
		return "contest_pk_lifecycle"
	case "contest_pk_cancelled":
		return "contest_pk_lifecycle"
	default:
		return "contest_pk_default"
	}
}

// IsEventPublishEnabled 检查事件发布是否启用
func (p *ContestPkEventPublisher) IsEventPublishEnabled() bool {
	return p.kafkaProducer != nil
}

// GetKafkaProducerStatus 获取Kafka生产者状态
func (p *ContestPkEventPublisher) GetKafkaProducerStatus() string {
	if p.kafkaProducer == nil {
		return "disabled"
	}
	// 这里可以添加更多状态检查逻辑
	return "enabled"
}
