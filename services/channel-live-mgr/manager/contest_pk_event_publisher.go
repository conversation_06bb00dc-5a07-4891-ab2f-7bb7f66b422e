package manager

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/kafka_prod"
)

// ContestPkEventPublisher 赛事PK事件发布器
type ContestPkEventPublisher struct {
	kafkaProducer *kafka_prod.KafkaProduce
}

// NewContestPkEventPublisher 创建赛事PK事件发布器
func NewContestPkEventPublisher(kafkaProducer *kafka_prod.KafkaProduce) *ContestPkEventPublisher {
	return &ContestPkEventPublisher{
		kafkaProducer: kafkaProducer,
	}
}

// PublishContestPkResult 发布赛事PK结果事件
func (p *ContestPkEventPublisher) PublishContestPkResult(ctx context.Context, event *pb.ContestPkResultEvent) error {
	log.InfoWithCtx(ctx, "PublishContestPkResult start contestId:%s", event.ContestId)

	// 1. 序列化事件数据
	eventData, err := proto.Marshal(event)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishContestPkResult Marshal failed contestId:%s err:%v", event.ContestId, err)
		return fmt.Errorf("marshal event failed: %v", err)
	}

	// 2. 使用现有的kafka生产者发布事件
	// 这里复用现有的produce方法，使用contest_pk_result作为topic
	p.kafkaProducer.ProduceContestPkResultEvent(event.ContestId, eventData)

	log.InfoWithCtx(ctx, "PublishContestPkResult success contestId:%s", event.ContestId)
	return nil
}

// PublishContestPkResultWithRetry 带重试的发布赛事PK结果事件
func (p *ContestPkEventPublisher) PublishContestPkResultWithRetry(ctx context.Context, event *pb.ContestPkResultEvent, maxRetries int) error {
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		if err := p.PublishContestPkResult(ctx, event); err == nil {
			return nil
		} else {
			lastErr = err
			log.WarnWithCtx(ctx, "PublishContestPkResult retry %d failed contestId:%s err:%v", i+1, event.ContestId, err)

			// 指数退避
			if i < maxRetries-1 {
				sleepTime := time.Duration(i+1) * time.Second
				time.Sleep(sleepTime)
			}
		}
	}

	log.ErrorWithCtx(ctx, "PublishContestPkResult failed after %d retries contestId:%s lastErr:%v", maxRetries, event.ContestId, lastErr)
	return fmt.Errorf("publish failed after %d retries, last error: %v", maxRetries, lastErr)
}

// PublishContestPkStatusChange 发布赛事PK状态变更事件（简化版本）
func (p *ContestPkEventPublisher) PublishContestPkStatusChange(ctx context.Context, contestId string, oldStatus, newStatus pb.ContestPkStatus, operator string) error {
	log.InfoWithCtx(ctx, "PublishContestPkStatusChange contestId:%s oldStatus:%d newStatus:%d operator:%s", contestId, oldStatus, newStatus, operator)

	// 构造简化的状态变更事件数据
	statusChangeData := map[string]interface{}{
		"contest_id": contestId,
		"old_status": int32(oldStatus),
		"new_status": int32(newStatus),
		"operator":   operator,
		"timestamp":  time.Now().Unix(),
		"event_type": "contest_pk_status_change",
	}

	// 序列化为JSON
	eventData, err := json.Marshal(statusChangeData)
	if err != nil {
		log.ErrorWithCtx(ctx, "PublishContestPkStatusChange Marshal failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("marshal status event failed: %v", err)
	}

	// 发布到Kafka
	p.kafkaProducer.ProduceContestPkStatusChangeEvent(contestId, eventData)

	log.InfoWithCtx(ctx, "PublishContestPkStatusChange success contestId:%s", contestId)
	return nil
}

// BuildContestPkResultEvent 构建赛事PK结果事件
func (p *ContestPkEventPublisher) BuildContestPkResultEvent(config *pb.ContestPkConfig, result *pb.ContestPkResult) *pb.ContestPkResultEvent {
	return &pb.ContestPkResultEvent{
		ContestId:       config.ContestId,
		ActivityService: config.ActivityService,
		AnchorUidA:      config.AnchorUidA,
		AnchorUidB:      config.AnchorUidB,
		WinnerUid:       result.WinnerUid,
		FinalScoreA:     result.FinalScoreA,
		FinalScoreB:     result.FinalScoreB,
		PkBeginTime:     config.PkBeginTime,
		PkEndTime:       config.PkEndTime,
		ActualEndTime:   result.ActualEndTime,
		IsExtraTime:     result.IsExtraTime,
	}
}

// ValidateResultEvent 校验结果事件
func (p *ContestPkEventPublisher) ValidateResultEvent(event *pb.ContestPkResultEvent) error {
	if event.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if event.ActivityService == "" {
		return fmt.Errorf("活动服务标识不能为空")
	}
	if event.AnchorUidA == 0 || event.AnchorUidB == 0 {
		return fmt.Errorf("主播UID不能为空")
	}
	if event.PkBeginTime == 0 || event.PkEndTime == 0 {
		return fmt.Errorf("PK时间不能为空")
	}
	if event.ActualEndTime == 0 {
		return fmt.Errorf("实际结束时间不能为空")
	}

	// 检查获胜者UID
	if event.WinnerUid != 0 && event.WinnerUid != event.AnchorUidA && event.WinnerUid != event.AnchorUidB {
		return fmt.Errorf("获胜者UID必须是参赛主播之一或0（平局）")
	}

	// 检查分数合理性
	if event.WinnerUid == event.AnchorUidA && event.FinalScoreA <= event.FinalScoreB {
		return fmt.Errorf("主播A获胜但分数不高于主播B")
	}
	if event.WinnerUid == event.AnchorUidB && event.FinalScoreB <= event.FinalScoreA {
		return fmt.Errorf("主播B获胜但分数不高于主播A")
	}
	if event.WinnerUid == 0 && event.FinalScoreA != event.FinalScoreB {
		return fmt.Errorf("平局时两个主播分数应该相等")
	}

	return nil
}

// IsEventPublishEnabled 检查事件发布是否启用
func (p *ContestPkEventPublisher) IsEventPublishEnabled() bool {
	return p.kafkaProducer != nil
}
