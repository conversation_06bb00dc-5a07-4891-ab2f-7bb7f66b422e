package manager

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/mysql"
)

// ContestPkValidator 赛事PK校验器
type ContestPkValidator struct {
	mysqlStore  *mysql.Store
	cacheClient *cache.ChannelLiveMgrCache
}

// NewContestPkValidator 创建赛事PK校验器
func NewContestPkValidator(mysqlStore *mysql.Store, cacheClient *cache.ChannelLiveMgrCache) *ContestPkValidator {
	return &ContestPkValidator{
		mysqlStore:  mysqlStore,
		cacheClient: cacheClient,
	}
}

// ValidateCreateRequest 校验创建请求
func (v *ContestPkValidator) ValidateCreateRequest(ctx context.Context, config *pb.ContestPkConfig) error {
	// 基本参数校验
	if config.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if config.ContestName == "" {
		return fmt.Errorf("赛事名称不能为空")
	}
	if config.ActivityService == "" {
		return fmt.Errorf("活动服务标识不能为空")
	}
	if config.AnchorUidA == 0 || config.AnchorUidB == 0 {
		return fmt.Errorf("主播UID不能为空")
	}
	if config.AnchorUidA == config.AnchorUidB {
		return fmt.Errorf("主播A和主播B不能是同一人")
	}
	if config.Operator == "" {
		return fmt.Errorf("操作人不能为空")
	}

	// 时间参数校验
	if err := v.CheckTimeValid(ctx, int64(config.ContestBeginTime), int64(config.ContestEndTime), int64(config.PkBeginTime), int64(config.PkEndTime)); err != nil {
		return err
	}

	// 主播有效性校验
	if err := v.CheckAnchorValid(ctx, config.AnchorUidA); err != nil {
		return fmt.Errorf("主播A无效: %v", err)
	}
	if err := v.CheckAnchorValid(ctx, config.AnchorUidB); err != nil {
		return fmt.Errorf("主播B无效: %v", err)
	}

	return nil
}

// ValidateUpdateRequest 校验更新请求
func (v *ContestPkValidator) ValidateUpdateRequest(ctx context.Context, config *pb.ContestPkConfig) error {
	// 基本参数校验（与创建相同）
	if err := v.ValidateCreateRequest(ctx, config); err != nil {
		return err
	}

	// 检查是否在限制期内（开始前10分钟内不允许更新关键信息）
	now := time.Now().Unix()
	restrictTime := int64(config.PkBeginTime) - 600 // 开始前10分钟

	if now >= restrictTime {
		return fmt.Errorf("赛事开始前10分钟内不允许更新")
	}

	return nil
}

// CheckTimeConflict 检查时间冲突
func (v *ContestPkValidator) CheckTimeConflict(ctx context.Context, anchorUidA, anchorUidB uint64, beginTime, endTime int64, excludeContestId string) error {
	// 检查主播A的时间冲突
	hasConflictA, err := v.mysqlStore.CheckAnchorContestPkConflict(ctx, anchorUidA, 0, uint64(beginTime), uint64(endTime), excludeContestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckTimeConflict CheckAnchorContestPkConflict A failed anchorUid:%d err:%v", anchorUidA, err)
		return fmt.Errorf("检查主播A时间冲突失败: %v", err)
	}
	if hasConflictA {
		return fmt.Errorf("主播A在该时间段已有其他赛事PK")
	}

	// 检查主播B的时间冲突
	hasConflictB, err := v.mysqlStore.CheckAnchorContestPkConflict(ctx, anchorUidB, 0, uint64(beginTime), uint64(endTime), excludeContestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckTimeConflict CheckAnchorContestPkConflict B failed anchorUid:%d err:%v", anchorUidB, err)
		return fmt.Errorf("检查主播B时间冲突失败: %v", err)
	}
	if hasConflictB {
		return fmt.Errorf("主播B在该时间段已有其他赛事PK")
	}

	return nil
}

// CheckContestIdUnique 检查赛事ID唯一性
func (v *ContestPkValidator) CheckContestIdUnique(ctx context.Context, contestId string) error {
	// 先检查缓存
	config, err := v.cacheClient.GetContestPkConfig(contestId)
	if err == nil && config != nil {
		return fmt.Errorf("赛事ID已存在")
	}

	// 检查数据库
	_, err = v.mysqlStore.GetContestPkConfig(ctx, contestId)
	if err == nil {
		return fmt.Errorf("赛事ID已存在")
	}

	// 如果是"not found"错误，说明ID可用
	return nil
}

// CheckAnchorValid 检查主播有效性
func (v *ContestPkValidator) CheckAnchorValid(ctx context.Context, anchorUid uint64) error {
	// 这里可以调用account服务检查主播是否存在
	// 暂时简单校验UID不为0
	if anchorUid == 0 {
		return fmt.Errorf("主播UID无效")
	}

	// TODO: 调用account服务验证主播是否存在
	// accountInfo, err := v.accountClient.GetAccountInfo(ctx, anchorUid)
	// if err != nil {
	//     return fmt.Errorf("主播不存在: %v", err)
	// }

	return nil
}

// CheckTimeValid 检查时间有效性
func (v *ContestPkValidator) CheckTimeValid(ctx context.Context, contestBeginTime, contestEndTime, pkBeginTime, pkEndTime int64) error {
	now := time.Now().Unix()

	// 检查赛事时间
	if contestBeginTime <= now {
		return fmt.Errorf("赛事开始时间不能早于当前时间")
	}
	if contestEndTime <= contestBeginTime {
		return fmt.Errorf("赛事结束时间必须晚于开始时间")
	}

	// 检查PK时间
	if pkBeginTime < contestBeginTime || pkBeginTime >= contestEndTime {
		return fmt.Errorf("PK开始时间必须在赛事时间段内")
	}
	if pkEndTime <= pkBeginTime || pkEndTime > contestEndTime {
		return fmt.Errorf("PK结束时间必须在赛事时间段内且晚于PK开始时间")
	}

	// 检查PK时长（不能超过4小时）
	pkDuration := pkEndTime - pkBeginTime
	if pkDuration > 4*3600 {
		return fmt.Errorf("PK时长不能超过4小时")
	}

	// 检查PK最短时长（不能少于10分钟）
	if pkDuration < 600 {
		return fmt.Errorf("PK时长不能少于10分钟")
	}

	// 检查提前量（至少提前30分钟创建）
	advanceTime := contestBeginTime - now
	if advanceTime < 1800 { // 30分钟
		return fmt.Errorf("赛事开始时间至少要提前30分钟")
	}

	return nil
}

// CheckContestStatus 检查赛事状态
func (v *ContestPkValidator) CheckContestStatus(ctx context.Context, contestId string, expectedStatus pb.ContestPkStatus) error {
	config, err := v.mysqlStore.GetContestPkConfig(ctx, contestId)
	if err != nil {
		return fmt.Errorf("赛事不存在: %s", contestId)
	}

	if config.Status != expectedStatus {
		return fmt.Errorf("赛事状态不符合要求，当前状态: %d，期望状态: %d", config.Status, expectedStatus)
	}

	return nil
}

// CheckOperatorPermission 检查操作人权限
func (v *ContestPkValidator) CheckOperatorPermission(ctx context.Context, operator string, operation string) error {
	// 这里可以实现权限检查逻辑
	// 暂时简单校验操作人不为空
	if operator == "" {
		return fmt.Errorf("操作人不能为空")
	}

	// TODO: 实现具体的权限检查逻辑
	// 可以根据operation类型检查不同的权限
	switch operation {
	case "create":
		// 检查创建权限
	case "update":
		// 检查更新权限
	case "cancel":
		// 检查取消权限
	default:
		return fmt.Errorf("未知操作类型: %s", operation)
	}

	return nil
}

// ValidateContestPkResult 校验赛事PK结果
func (v *ContestPkValidator) ValidateContestPkResult(ctx context.Context, contestId string, winnerUid uint64, scoreA, scoreB uint32) error {
	// 获取赛事配置
	config, err := v.mysqlStore.GetContestPkConfig(ctx, contestId)
	if err != nil {
		return fmt.Errorf("赛事不存在: %s", contestId)
	}

	// 检查赛事状态
	if config.Status != pb.ContestPkStatus_CONTEST_PK_RUNNING {
		return fmt.Errorf("赛事状态不是进行中，无法设置结果")
	}

	// 检查获胜者UID
	if winnerUid != 0 && winnerUid != config.AnchorUidA && winnerUid != config.AnchorUidB {
		return fmt.Errorf("获胜者UID必须是参赛主播之一或0（平局）")
	}

	// 检查分数合理性
	if scoreA < 0 || scoreB < 0 {
		return fmt.Errorf("分数不能为负数")
	}

	// 检查获胜者与分数的一致性
	if winnerUid == config.AnchorUidA && scoreA <= scoreB {
		return fmt.Errorf("主播A获胜但分数不高于主播B")
	}
	if winnerUid == config.AnchorUidB && scoreB <= scoreA {
		return fmt.Errorf("主播B获胜但分数不高于主播A")
	}
	if winnerUid == 0 && scoreA != scoreB {
		return fmt.Errorf("平局时两个主播分数应该相等")
	}

	return nil
}

// CheckBatchQueryParams 检查批量查询参数
func (v *ContestPkValidator) CheckBatchQueryParams(ctx context.Context, req *pb.BatchGetContestPkConfigReq) error {
	// 检查分页参数
	if req.Page < 0 {
		return fmt.Errorf("页码不能为负数")
	}
	if req.PageSize < 0 || req.PageSize > 100 {
		return fmt.Errorf("页大小必须在1-100之间")
	}

	// 检查时间范围
	if req.TimeBegin > 0 && req.TimeEnd > 0 && req.TimeBegin >= req.TimeEnd {
		return fmt.Errorf("开始时间必须早于结束时间")
	}

	// 检查赛事ID列表长度
	if len(req.ContestIdList) > 50 {
		return fmt.Errorf("单次查询的赛事ID数量不能超过50个")
	}

	return nil
}
