package manager

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/mysql"
)

const (
	TimerTaskTypeRestriction = "restriction" // 设置PK限制
	TimerTaskTypeStart       = "start"       // 开始PK
	TimerTaskTypeFinish      = "finish"      // 结束PK检查
)

// ContestPkTimer 赛事PK定时任务处理器
type ContestPkTimer struct {
	cacheClient *cache.ChannelLiveMgrCache
	mysqlStore  *mysql.Store
	manager     *ContestPkManager
	stopChan    chan bool
	isRunning   bool
}

// NewContestPkTimer 创建赛事PK定时任务处理器
func NewContestPkTimer(cacheClient *cache.ChannelLiveMgrCache, mysqlStore *mysql.Store, manager *ContestPkManager) *ContestPkTimer {
	return &ContestPkTimer{
		cacheClient: cacheClient,
		mysqlStore:  mysqlStore,
		manager:     manager,
		stopChan:    make(chan bool),
		isRunning:   false,
	}
}

// Start 启动定时任务
func (t *ContestPkTimer) Start() error {
	if t.isRunning {
		return fmt.Errorf("timer is already running")
	}

	t.isRunning = true
	log.Infof("ContestPkTimer started")

	// 启动定时任务协程
	go t.timerLoop()

	return nil
}

// Stop 停止定时任务
func (t *ContestPkTimer) Stop() error {
	if !t.isRunning {
		return fmt.Errorf("timer is not running")
	}

	t.stopChan <- true
	t.isRunning = false
	log.Infof("ContestPkTimer stopped")

	return nil
}

// timerLoop 定时任务主循环
func (t *ContestPkTimer) timerLoop() {
	ticker := time.NewTicker(60 * time.Second) // 每分钟执行一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			t.processAllTasks()
		case <-t.stopChan:
			log.Infof("ContestPkTimer loop stopped")
			return
		}
	}
}

// processAllTasks 处理所有类型的定时任务
func (t *ContestPkTimer) processAllTasks() {
	ctx := context.Background()

	// 处理限制任务
	if err := t.ProcessRestrictionTasks(ctx); err != nil {
		log.Errorf("ProcessRestrictionTasks failed: %v", err)
	}

	// 处理开始任务
	if err := t.ProcessStartTasks(ctx); err != nil {
		log.Errorf("ProcessStartTasks failed: %v", err)
	}

	// 处理结束检查任务
	if err := t.ProcessFinishTasks(ctx); err != nil {
		log.Errorf("ProcessFinishTasks failed: %v", err)
	}
}

// ProcessRestrictionTasks 处理限制任务
func (t *ContestPkTimer) ProcessRestrictionTasks(ctx context.Context) error {
	now := uint64(time.Now().Unix())

	// 1. 从缓存获取需要处理的限制任务
	contestIds, err := t.cacheClient.GetContestPkTimerList(TimerTaskTypeRestriction, now-300, now+300, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcessRestrictionTasks GetContestPkTimerList failed: %v", err)
		return err
	}

	if len(contestIds) == 0 {
		return nil
	}

	log.InfoWithCtx(ctx, "ProcessRestrictionTasks processing count:%d", len(contestIds))

	// 2. 处理每个赛事的限制任务
	for _, contestId := range contestIds {
		if err := t.processRestrictionTask(ctx, contestId); err != nil {
			log.ErrorWithCtx(ctx, "processRestrictionTask failed contestId:%s err:%v", contestId, err)
			continue
		}

		// 移除已处理的任务
		t.cacheClient.RemoveContestPkTimer(TimerTaskTypeRestriction, contestId, 0)
	}

	return nil
}

// processRestrictionTask 处理单个限制任务
func (t *ContestPkTimer) processRestrictionTask(ctx context.Context, contestId string) error {
	// 获取赛事配置
	config, err := t.manager.GetContestPkConfig(ctx, contestId)
	if err != nil {
		return fmt.Errorf("get contest config failed: %v", err)
	}

	// 检查赛事状态
	if config.Status != pb.ContestPkStatus_CONTEST_PK_PENDING {
		log.WarnWithCtx(ctx, "processRestrictionTask contest status not pending contestId:%s status:%d", contestId, config.Status)
		return nil
	}

	// 计算限制时间
	restrictBegin := config.PkBeginTime - 600 // 开始前10分钟
	restrictEnd := config.PkEndTime

	// 设置主播A的限制
	restrictionA := &cache.ContestPkRestriction{
		AnchorUID:     config.AnchorUidA,
		ContestID:     config.ContestId,
		RestrictBegin: restrictBegin,
		RestrictEnd:   restrictEnd,
		Reason:        fmt.Sprintf("参与赛事PK: %s", config.ContestName),
	}
	if err := t.cacheClient.SetContestPkRestriction(config.AnchorUidA, restrictionA); err != nil {
		log.ErrorWithCtx(ctx, "SetContestPkRestriction A failed contestId:%s anchorUid:%d err:%v", contestId, config.AnchorUidA, err)
	}

	// 设置主播B的限制
	restrictionB := &cache.ContestPkRestriction{
		AnchorUID:     config.AnchorUidB,
		ContestID:     config.ContestId,
		RestrictBegin: restrictBegin,
		RestrictEnd:   restrictEnd,
		Reason:        fmt.Sprintf("参与赛事PK: %s", config.ContestName),
	}
	if err := t.cacheClient.SetContestPkRestriction(config.AnchorUidB, restrictionB); err != nil {
		log.ErrorWithCtx(ctx, "SetContestPkRestriction B failed contestId:%s anchorUid:%d err:%v", contestId, config.AnchorUidB, err)
	}

	log.InfoWithCtx(ctx, "processRestrictionTask success contestId:%s", contestId)
	return nil
}

// ProcessStartTasks 处理开始任务
func (t *ContestPkTimer) ProcessStartTasks(ctx context.Context) error {
	now := uint64(time.Now().Unix())

	// 1. 从缓存获取需要开始的赛事
	contestIds, err := t.cacheClient.GetContestPkTimerList(TimerTaskTypeStart, now-60, now+60, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcessStartTasks GetContestPkTimerList failed: %v", err)
		return err
	}

	if len(contestIds) == 0 {
		return nil
	}

	log.InfoWithCtx(ctx, "ProcessStartTasks processing count:%d", len(contestIds))

	// 2. 处理每个赛事的开始任务
	for _, contestId := range contestIds {
		if err := t.processStartTask(ctx, contestId); err != nil {
			log.ErrorWithCtx(ctx, "processStartTask failed contestId:%s err:%v", contestId, err)
			continue
		}

		// 移除已处理的任务
		t.cacheClient.RemoveContestPkTimer(TimerTaskTypeStart, contestId, 0)
	}

	return nil
}

// processStartTask 处理单个开始任务
func (t *ContestPkTimer) processStartTask(ctx context.Context, contestId string) error {
	// 获取分布式锁，防止重复处理
	lockKey := fmt.Sprintf("start_%s", contestId)
	if !t.cacheClient.GetContestPkLock(lockKey, 300) {
		log.WarnWithCtx(ctx, "processStartTask get lock failed contestId:%s", contestId)
		return nil
	}
	defer t.cacheClient.ReleaseContestPkLock(lockKey)

	// 获取赛事配置
	config, err := t.manager.GetContestPkConfig(ctx, contestId)
	if err != nil {
		return fmt.Errorf("get contest config failed: %v", err)
	}

	// 检查赛事状态
	if config.Status != pb.ContestPkStatus_CONTEST_PK_PENDING {
		log.WarnWithCtx(ctx, "processStartTask contest status not pending contestId:%s status:%d", contestId, config.Status)
		return nil
	}

	// 检查时间是否到达
	now := uint64(time.Now().Unix())
	if now < config.PkBeginTime {
		log.WarnWithCtx(ctx, "processStartTask time not reached contestId:%s now:%d beginTime:%d", contestId, now, config.PkBeginTime)
		return nil
	}

	// 更新赛事状态为进行中
	config.Status = pb.ContestPkStatus_CONTEST_PK_RUNNING
	config.UpdateTime = now

	if err := t.mysqlStore.UpdateContestPkConfig(ctx, nil, config); err != nil {
		log.ErrorWithCtx(ctx, "processStartTask UpdateContestPkConfig failed contestId:%s err:%v", contestId, err)
		return fmt.Errorf("update contest status failed: %v", err)
	}

	// 更新缓存
	t.cacheClient.SetContestPkConfig(contestId, config)

	// TODO: 这里可以调用PK匹配接口自动发起PK
	// err = t.startContestPk(ctx, config)

	log.InfoWithCtx(ctx, "processStartTask success contestId:%s", contestId)
	return nil
}

// ProcessFinishTasks 处理结束检查任务
func (t *ContestPkTimer) ProcessFinishTasks(ctx context.Context) error {
	now := uint64(time.Now().Unix())

	// 1. 从缓存获取需要检查结束的赛事
	contestIds, err := t.cacheClient.GetContestPkTimerList(TimerTaskTypeFinish, now-300, now+300, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "ProcessFinishTasks GetContestPkTimerList failed: %v", err)
		return err
	}

	if len(contestIds) == 0 {
		return nil
	}

	log.InfoWithCtx(ctx, "ProcessFinishTasks processing count:%d", len(contestIds))

	// 2. 处理每个赛事的结束检查任务
	for _, contestId := range contestIds {
		if err := t.processFinishTask(ctx, contestId); err != nil {
			log.ErrorWithCtx(ctx, "processFinishTask failed contestId:%s err:%v", contestId, err)
			continue
		}

		// 移除已处理的任务
		t.cacheClient.RemoveContestPkTimer(TimerTaskTypeFinish, contestId, 0)
	}

	return nil
}

// processFinishTask 处理单个结束检查任务
func (t *ContestPkTimer) processFinishTask(ctx context.Context, contestId string) error {
	// 获取赛事配置
	config, err := t.manager.GetContestPkConfig(ctx, contestId)
	if err != nil {
		return fmt.Errorf("get contest config failed: %v", err)
	}

	// 检查赛事状态
	if config.Status == pb.ContestPkStatus_CONTEST_PK_FINISHED || config.Status == pb.ContestPkStatus_CONTEST_PK_CANCELLED {
		log.InfoWithCtx(ctx, "processFinishTask contest already finished contestId:%s status:%d", contestId, config.Status)
		return nil
	}

	// 检查是否超时
	now := uint64(time.Now().Unix())
	if now <= config.PkEndTime {
		log.InfoWithCtx(ctx, "processFinishTask contest not timeout contestId:%s now:%d endTime:%d", contestId, now, config.PkEndTime)
		return nil
	}

	// 如果赛事超时且还在进行中，自动结束
	if config.Status == pb.ContestPkStatus_CONTEST_PK_RUNNING {
		log.WarnWithCtx(ctx, "processFinishTask contest timeout, auto finish contestId:%s", contestId)

		// 这里可以调用结束PK的逻辑
		// err = t.finishContestPk(ctx, contestId, 0, 0, 0, false)
	}

	// 清理主播限制
	t.cacheClient.DelContestPkRestriction(config.AnchorUidA)
	t.cacheClient.DelContestPkRestriction(config.AnchorUidB)

	log.InfoWithCtx(ctx, "processFinishTask success contestId:%s", contestId)
	return nil
}

// AddTimerTask 添加定时任务
func (t *ContestPkTimer) AddTimerTask(contestId string, taskType string, executeTime int64) error {
	return t.cacheClient.AddContestPkTimer(taskType, contestId, uint64(executeTime), 0)
}

// RemoveTimerTask 移除定时任务
func (t *ContestPkTimer) RemoveTimerTask(contestId string, taskType string) error {
	return t.cacheClient.RemoveContestPkTimer(taskType, contestId, 0)
}

// IsRunning 检查定时器是否在运行
func (t *ContestPkTimer) IsRunning() bool {
	return t.isRunning
}
