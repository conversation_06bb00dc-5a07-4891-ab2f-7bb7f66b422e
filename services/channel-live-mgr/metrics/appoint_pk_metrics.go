package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// AppointPkMetrics 指定PK相关的监控指标
type AppointPkMetrics struct {
	// 查询性能指标
	QueryDuration prometheus.HistogramVec
	QueryTotal    prometheus.CounterVec
	QueryErrors   prometheus.CounterVec

	// 缓存性能指标
	CacheHits   prometheus.CounterVec
	CacheMisses prometheus.CounterVec
	CacheErrors prometheus.CounterVec

	// 定时任务指标
	TimerExecutions prometheus.CounterVec
	TimerDuration   prometheus.HistogramVec
	TimerErrors     prometheus.CounterVec

	// 业务指标
	AppointPkTotal    prometheus.CounterVec
	AppointPkActive   prometheus.GaugeVec
	AppointPkSuccess  prometheus.CounterVec
	AppointPkFailures prometheus.CounterVec

	// 队列指标
	QueueLength prometheus.GaugeVec
	QueueDelay  prometheus.HistogramVec

	// 布隆过滤器指标
	BloomFilterHits   prometheus.CounterVec
	BloomFilterMisses prometheus.CounterVec
}

// NewAppointPkMetrics 创建指定PK监控指标
func NewAppointPkMetrics() *AppointPkMetrics {
	return &AppointPkMetrics{
		// 查询性能指标
		QueryDuration: *promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "appoint_pk_query_duration_seconds",
				Help:    "Duration of appoint PK queries",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation", "table"},
		),
		QueryTotal: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_query_total",
				Help: "Total number of appoint PK queries",
			},
			[]string{"operation", "table", "status"},
		),
		QueryErrors: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_query_errors_total",
				Help: "Total number of appoint PK query errors",
			},
			[]string{"operation", "table", "error_type"},
		),

		// 缓存性能指标
		CacheHits: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_cache_hits_total",
				Help: "Total number of appoint PK cache hits",
			},
			[]string{"cache_type", "operation"},
		),
		CacheMisses: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_cache_misses_total",
				Help: "Total number of appoint PK cache misses",
			},
			[]string{"cache_type", "operation"},
		),
		CacheErrors: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_cache_errors_total",
				Help: "Total number of appoint PK cache errors",
			},
			[]string{"cache_type", "operation", "error_type"},
		),

		// 定时任务指标
		TimerExecutions: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_timer_executions_total",
				Help: "Total number of appoint PK timer executions",
			},
			[]string{"timer_type", "status"},
		),
		TimerDuration: *promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "appoint_pk_timer_duration_seconds",
				Help:    "Duration of appoint PK timer executions",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"timer_type"},
		),
		TimerErrors: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_timer_errors_total",
				Help: "Total number of appoint PK timer errors",
			},
			[]string{"timer_type", "error_type"},
		),

		// 业务指标
		AppointPkTotal: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_total",
				Help: "Total number of appoint PKs",
			},
			[]string{"status"},
		),
		AppointPkActive: *promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "appoint_pk_active",
				Help: "Number of active appoint PKs",
			},
			[]string{"type"},
		),
		AppointPkSuccess: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_success_total",
				Help: "Total number of successful appoint PKs",
			},
			[]string{"type"},
		),
		AppointPkFailures: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_failures_total",
				Help: "Total number of failed appoint PKs",
			},
			[]string{"type", "reason"},
		),

		// 队列指标
		QueueLength: *promauto.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "appoint_pk_queue_length",
				Help: "Length of appoint PK processing queues",
			},
			[]string{"queue_type", "queue_index"},
		),
		QueueDelay: *promauto.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "appoint_pk_queue_delay_seconds",
				Help:    "Delay in appoint PK queue processing",
				Buckets: []float64{0.1, 0.5, 1, 2, 5, 10, 30, 60, 120},
			},
			[]string{"queue_type"},
		),

		// 布隆过滤器指标
		BloomFilterHits: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_bloom_filter_hits_total",
				Help: "Total number of bloom filter hits",
			},
			[]string{"filter_type"},
		),
		BloomFilterMisses: *promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "appoint_pk_bloom_filter_misses_total",
				Help: "Total number of bloom filter misses",
			},
			[]string{"filter_type"},
		),
	}
}

// RecordQueryDuration 记录查询耗时
func (m *AppointPkMetrics) RecordQueryDuration(operation, table string, duration time.Duration) {
	m.QueryDuration.WithLabelValues(operation, table).Observe(duration.Seconds())
}

// RecordQueryTotal 记录查询总数
func (m *AppointPkMetrics) RecordQueryTotal(operation, table, status string) {
	m.QueryTotal.WithLabelValues(operation, table, status).Inc()
}

// RecordQueryError 记录查询错误
func (m *AppointPkMetrics) RecordQueryError(operation, table, errorType string) {
	m.QueryErrors.WithLabelValues(operation, table, errorType).Inc()
}

// RecordCacheHit 记录缓存命中
func (m *AppointPkMetrics) RecordCacheHit(cacheType, operation string) {
	m.CacheHits.WithLabelValues(cacheType, operation).Inc()
}

// RecordCacheMiss 记录缓存未命中
func (m *AppointPkMetrics) RecordCacheMiss(cacheType, operation string) {
	m.CacheMisses.WithLabelValues(cacheType, operation).Inc()
}

// RecordCacheError 记录缓存错误
func (m *AppointPkMetrics) RecordCacheError(cacheType, operation, errorType string) {
	m.CacheErrors.WithLabelValues(cacheType, operation, errorType).Inc()
}

// RecordTimerExecution 记录定时任务执行
func (m *AppointPkMetrics) RecordTimerExecution(timerType, status string) {
	m.TimerExecutions.WithLabelValues(timerType, status).Inc()
}

// RecordTimerDuration 记录定时任务耗时
func (m *AppointPkMetrics) RecordTimerDuration(timerType string, duration time.Duration) {
	m.TimerDuration.WithLabelValues(timerType).Observe(duration.Seconds())
}

// RecordTimerError 记录定时任务错误
func (m *AppointPkMetrics) RecordTimerError(timerType, errorType string) {
	m.TimerErrors.WithLabelValues(timerType, errorType).Inc()
}

// RecordAppointPkTotal 记录指定PK总数
func (m *AppointPkMetrics) RecordAppointPkTotal(status string) {
	m.AppointPkTotal.WithLabelValues(status).Inc()
}

// SetAppointPkActive 设置活跃指定PK数量
func (m *AppointPkMetrics) SetAppointPkActive(pkType string, count float64) {
	m.AppointPkActive.WithLabelValues(pkType).Set(count)
}

// RecordAppointPkSuccess 记录指定PK成功
func (m *AppointPkMetrics) RecordAppointPkSuccess(pkType string) {
	m.AppointPkSuccess.WithLabelValues(pkType).Inc()
}

// RecordAppointPkFailure 记录指定PK失败
func (m *AppointPkMetrics) RecordAppointPkFailure(pkType, reason string) {
	m.AppointPkFailures.WithLabelValues(pkType, reason).Inc()
}

// SetQueueLength 设置队列长度
func (m *AppointPkMetrics) SetQueueLength(queueType, queueIndex string, length float64) {
	m.QueueLength.WithLabelValues(queueType, queueIndex).Set(length)
}

// RecordQueueDelay 记录队列延迟
func (m *AppointPkMetrics) RecordQueueDelay(queueType string, delay time.Duration) {
	m.QueueDelay.WithLabelValues(queueType).Observe(delay.Seconds())
}

// RecordBloomFilterHit 记录布隆过滤器命中
func (m *AppointPkMetrics) RecordBloomFilterHit(filterType string) {
	m.BloomFilterHits.WithLabelValues(filterType).Inc()
}

// RecordBloomFilterMiss 记录布隆过滤器未命中
func (m *AppointPkMetrics) RecordBloomFilterMiss(filterType string) {
	m.BloomFilterMisses.WithLabelValues(filterType).Inc()
}

// 全局指标实例
var GlobalAppointPkMetrics = NewAppointPkMetrics()
