package kafka_prod

import (
	"context"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/publisher"
	"golang.52tt.com/pkg/log"
	pbLogic "golang.52tt.com/protocol/app/channel-live-logic"
	"strconv"
)

type KafkaProduce struct {
	producer publisher.Publisher
	topic    string
	//wg       sync.WaitGroup
}

func NewKafkaProduce(addr []string, clientId, topic string) (*KafkaProduce, error) {
	log.Debugf("NewKafkaProduce %s, %v", topic, addr)

	kfkProd := &KafkaProduce{
		topic: topic,
	}

	option := []publisher.Option{
		publisher.WithSuccessMessageHandler(func(msg *publisher.ProducerMessage) {
			log.Infof("[mylog] sent message success, topic: %s, key: %s", msg.Top<PERSON>, msg.Key)
		}),
		publisher.WithErrorMessageHandler(func(err *publisher.ProducerError) {
			log.Errorf("[mylog] sent message failed, topic: %s, key: %s, error: %s", err.Msg.Topic, err.Msg.Key, err.Err)
		}),
		publisher.WithTopicRegisterHandler([]string{topic}),
	}

	pub, err := kafka.NewSyncPublisher(addr, nil, option...)
	if err != nil {
		log.Errorf("init kafka producer fail, err: %v", err)
		return nil, err
	}

	kfkProd.producer = pub

	/*
		err := kafka.Init(addr, clientId)
		if err != nil {
			log.Errorf("kafka init err %s kafka %v", err.Error(), kafka)
			return nil
		}
	*/
	return kfkProd, nil
}

/*
func (k *KafkaProduce) Init(addr []string, clientId string) error {
	pc := sarama.NewConfig()
	pc.ClientID = clientId
	pc.Producer.RequiredAcks = sarama.WaitForLocal
	pc.Producer.Return.Successes = true
	pc.Producer.Return.Errors = true
	pc.ChannelBufferSize = 2048

	producer, err := sarama.NewAsyncProducer(addr, pc)
	if err != nil {
		log.Warnf("Failed to create kafka producer to brokers %v err %", addr, err)
		return err
	}

	k.wg.Add(2)
	go func() {
		defer func() {
			log.Debugf("asyncProducerMon Successes chan reader stops")
			k.wg.Done()
		}()

		for succ := range producer.Successes() {
			if succ != nil {
				log.Debugf("asyncProducerMon send message - %s %s %d %d", succ.Topic, succ.Key, succ.Partition, succ.Offset)
			}
		}
	}()

	go func() {
		defer func() {
			log.Debugf("asyncProducerMon Errors chan reader stops")
			k.wg.Done()
		}()

		for err := range producer.Errors() {
			if err != nil {
				log.Debugf("asyncProducerMon failed to send message %v err %v", err.Msg, err.Err)
			}
		}
	}()

	k.producer = producer
	return nil
}
*/

func (k *KafkaProduce) Close() {
	k.producer.Close()
}

func (k *KafkaProduce) produce(topic, key string, value []byte) {
	/*
		msg := &sarama.ProducerMessage{
			Topic: topic,
			Key:   sarama.StringEncoder(key),
			Value: sarama.ByteEncoder(value),
		}
		k.producer.Input() <- msg
	*/

	res := k.producer.Publish(context.Background(), &publisher.ProducerMessage{
		Topic: topic,
		Key:   publisher.StringEncoder(key),
		Value: publisher.ByteEncoder(value),
	})
	log.Infof("send msg with topic %s and msg %s, res %+v ", k.topic, value, res)
}

func (k *KafkaProduce) ProduceChannelLiveEvent(event *pbLogic.ChannelLiveKafkaEvent) error {

	log.Infof("ProduceChannelLiveEvent event:%v topic:%v", event, k.topic)

	eventBin, err := proto.Marshal(event)
	if err != nil {
		log.Errorf("ProduceChannelLiveEvent err:%v", err)
		return err
	}

	k.produce(k.topic, strconv.Itoa(int(event.AnchorUid)), eventBin)

	return nil
}

func (k *KafkaProduce) ProdPkApplyEvent(event *pbLogic.PkApplyKafkaEvent) error {
	log.Infof("ProdPkApplyEvent event:%v topic:%v", event, k.topic)

	eventBin, err := proto.Marshal(event)
	if err != nil {
		log.Errorf("ProdPkApplyEvent err:%v", err)
		return err
	}

	k.produce(k.topic, strconv.Itoa(int(event.TargetUid)), eventBin)

	return nil
}

// ProduceContestPkResultEvent 发布赛事PK结果事件
func (k *KafkaProduce) ProduceContestPkResultEvent(contestId string, eventData []byte) {
	log.Infof("ProduceContestPkResultEvent contestId:%s topic:%s", contestId, k.topic)

	// 使用contestId作为key，确保同一赛事的消息有序
	k.produce(k.topic, contestId, eventData)
}

// ProduceContestPkStatusChangeEvent 发布赛事PK状态变更事件
func (k *KafkaProduce) ProduceContestPkStatusChangeEvent(contestId string, eventData []byte) {
	log.Infof("ProduceContestPkStatusChangeEvent contestId:%s topic:%s", contestId, k.topic)

	// 使用contestId作为key，确保同一赛事的消息有序
	k.produce(k.topic, contestId, eventData)
}
