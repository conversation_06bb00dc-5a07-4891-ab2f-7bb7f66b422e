package cache

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
)

// BloomFilter Redis实现的布隆过滤器
type BloomFilter struct {
	client     *redis.Client
	keyPrefix  string
	hashCount  int    // 哈希函数数量
	bitSize    uint32 // 位数组大小
	expireTime time.Duration
}

// NewBloomFilter 创建布隆过滤器
func NewBloomFilter(client *redis.Client, keyPrefix string, bitSize uint32, hashCount int, expireTime time.Duration) *BloomFilter {
	return &BloomFilter{
		client:     client,
		keyPrefix:  keyPrefix,
		hashCount:  hashCount,
		bitSize:    bitSize,
		expireTime: expireTime,
	}
}

// Add 添加元素到布隆过滤器
func (bf *BloomFilter) Add(ctx context.Context, key string) error {
	redisKey := bf.getRedisKey()

	// 计算多个哈希值
	hashes := bf.getHashes(key)

	// 使用pipeline批量设置位
	pipe := bf.client.Pipeline()
	for _, hash := range hashes {
		pipe.SetBit(redisKey, int64(hash), 1)
	}

	// 设置过期时间
	pipe.Expire(redisKey, bf.expireTime)

	_, err := pipe.Exec()
	if err != nil {
		log.ErrorWithCtx(ctx, "BloomFilter Add failed key:%s err:%v", key, err)
		return err
	}

	return nil
}

// Test 测试元素是否可能存在
func (bf *BloomFilter) Test(ctx context.Context, key string) bool {
	redisKey := bf.getRedisKey()

	// 计算多个哈希值
	hashes := bf.getHashes(key)

	// 使用pipeline批量检查位
	pipe := bf.client.Pipeline()
	cmds := make([]*redis.IntCmd, len(hashes))
	for i, hash := range hashes {
		cmds[i] = pipe.GetBit(redisKey, int64(hash))
	}

	_, err := pipe.Exec()
	if err != nil {
		log.ErrorWithCtx(ctx, "BloomFilter Test failed key:%s err:%v", key, err)
		// 出错时返回true，避免缓存穿透
		return true
	}

	// 所有位都必须为1
	for _, cmd := range cmds {
		if cmd.Val() == 0 {
			return false
		}
	}

	return true
}

// BatchAdd 批量添加元素
func (bf *BloomFilter) BatchAdd(ctx context.Context, keys []string) error {
	if len(keys) == 0 {
		return nil
	}

	redisKey := bf.getRedisKey()
	pipe := bf.client.Pipeline()

	for _, key := range keys {
		hashes := bf.getHashes(key)
		for _, hash := range hashes {
			pipe.SetBit(redisKey, int64(hash), 1)
		}
	}

	// 设置过期时间
	pipe.Expire(redisKey, bf.expireTime)

	_, err := pipe.Exec()
	if err != nil {
		log.ErrorWithCtx(ctx, "BloomFilter BatchAdd failed keys:%v err:%v", keys, err)
		return err
	}

	return nil
}

// Clear 清空布隆过滤器
func (bf *BloomFilter) Clear(ctx context.Context) error {
	redisKey := bf.getRedisKey()
	err := bf.client.Del(redisKey).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "BloomFilter Clear failed err:%v", err)
		return err
	}
	return nil
}

// getRedisKey 获取Redis键名
func (bf *BloomFilter) getRedisKey() string {
	return fmt.Sprintf("bloom:%s", bf.keyPrefix)
}

// getHashes 计算多个哈希值
func (bf *BloomFilter) getHashes(key string) []uint32 {
	hashes := make([]uint32, bf.hashCount)

	// 使用简单的哈希函数组合
	h1 := bf.hash1(key)
	h2 := bf.hash2(key)

	for i := 0; i < bf.hashCount; i++ {
		hash := (h1 + uint32(i)*h2) % bf.bitSize
		hashes[i] = hash
	}

	return hashes
}

// hash1 第一个哈希函数 (DJB2)
func (bf *BloomFilter) hash1(key string) uint32 {
	var hash uint32 = 5381
	for _, c := range key {
		hash = ((hash << 5) + hash) + uint32(c)
	}
	return hash % bf.bitSize
}

// hash2 第二个哈希函数 (SDBM)
func (bf *BloomFilter) hash2(key string) uint32 {
	var hash uint32 = 0
	for _, c := range key {
		hash = uint32(c) + (hash << 6) + (hash << 16) - hash
	}
	return hash % bf.bitSize
}

// AppointPkBloomFilter 指定PK专用的布隆过滤器
type AppointPkBloomFilter struct {
	*BloomFilter
}

// NewAppointPkBloomFilter 创建指定PK布隆过滤器
func NewAppointPkBloomFilter(client *redis.Client) *AppointPkBloomFilter {
	// 配置参数：
	// bitSize: 1000000 (约1MB内存)
	// hashCount: 7 (较好的误判率)
	// expireTime: 24小时
	bf := NewBloomFilter(client, "appoint_pk", 1000000, 7, 24*time.Hour)
	return &AppointPkBloomFilter{BloomFilter: bf}
}

// AddAppointPk 添加指定PK ID
func (apf *AppointPkBloomFilter) AddAppointPk(ctx context.Context, appointId uint32) error {
	key := strconv.FormatUint(uint64(appointId), 10)
	return apf.Add(ctx, key)
}

// TestAppointPk 测试指定PK ID是否可能存在
func (apf *AppointPkBloomFilter) TestAppointPk(ctx context.Context, appointId uint32) bool {
	key := strconv.FormatUint(uint64(appointId), 10)
	return apf.Test(ctx, key)
}

// BatchAddAppointPks 批量添加指定PK IDs
func (apf *AppointPkBloomFilter) BatchAddAppointPks(ctx context.Context, appointIds []uint32) error {
	keys := make([]string, len(appointIds))
	for i, id := range appointIds {
		keys[i] = strconv.FormatUint(uint64(id), 10)
	}
	return apf.BatchAdd(ctx, keys)
}

// RefreshFromDB 从数据库刷新布隆过滤器
func (apf *AppointPkBloomFilter) RefreshFromDB(ctx context.Context, getAllAppointIds func() ([]uint32, error)) error {
	// 清空现有数据
	if err := apf.Clear(ctx); err != nil {
		return err
	}

	// 从数据库获取所有ID
	appointIds, err := getAllAppointIds()
	if err != nil {
		log.ErrorWithCtx(ctx, "AppointPkBloomFilter RefreshFromDB getAllAppointIds failed err:%v", err)
		return err
	}

	// 批量添加到布隆过滤器
	if len(appointIds) > 0 {
		if err := apf.BatchAddAppointPks(ctx, appointIds); err != nil {
			log.ErrorWithCtx(ctx, "AppointPkBloomFilter RefreshFromDB BatchAddAppointPks failed err:%v", err)
			return err
		}
	}

	log.InfoWithCtx(ctx, "AppointPkBloomFilter RefreshFromDB success count:%d", len(appointIds))
	return nil
}
