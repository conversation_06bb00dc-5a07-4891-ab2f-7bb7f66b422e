package cache

import (
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/opentracing/opentracing-go"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/conf"
	//"golang.52tt.com/services/channel-live-mgr/pk"
	"sort"
	"strconv"
	"sync"
	"time"
)

const (
	R_Channel_Live_Info               = "r_channel_live_info"               //所有直播权限，以UID为file
	R_Channel_Live_Info_By_Channel_Id = "r_channel_live_info_by_channel_id" //所有直播权限，以channelID为file

	R_Channel_Live_Ticker    = "R_Channel_Live_Ticker_v1"
	R_Channel_Live_PK_Ticker = "R_Channel_Live_PK_Ticker" //pk状态定时器
	R_Channel_Live_Status    = "R_Channel_Live_Status"    //hash table 所有直播状态

	G_SendGiftAudienceCnt = "SendGiftAudienceCnt"
	G_GiftValue           = "GiftValue"
	G_AudienceCnt         = "AudienceCnt"
	G_AnchorGiftValue     = "AnchorGiftValue"
	G_BeginTime           = "G_BeginTime"
	G_EndTime             = "G_EndTime"
	G_AnchorKnightValue   = "AnchorKnightValue" // 骑士流水
	G_GameGiftValue       = "GameGiftValue"     // 互动游戏流水
	G_GameTs              = "GameTs"            // 互动游戏时长

	G_ReportBeginTime = "G_ReportBeginTime"

	R_Channel_Live_ChannelID_Status = "R_Channel_Live_ChannelID_Status" //hash table，应客户要求加一个简单版的直播信息

	ChannelLiveAnchorBackList = "channel_live_anchor_back_list"

	AudieWatchTimeBoundary = 300 * 24 * 3600 // 作为判断是观看主播开始时间还是观看结算时长的时间界限
)

var gRedis *redis.Client
var cEasyCache = NewEasyCache()

type ChannelLiveMgrCache struct {
	RedisClient     *redis.Client
	RedisFlagClient *redis.Client
	RedisWebClient  *redis.Client //web活动那边的redis，读取匹配分数用

	tracer opentracing.Tracer
	//startPkEvalSha string
	//stopPkEvalSha string

	// 布隆过滤器
	appointPkBloomFilter *AppointPkBloomFilter
}

type ChannelLivePkCommonInfo struct {
	CreateTime       uint32
	ChallengeUid     uint32
	BeChallengeUid   uint32
	ChallengeScore   uint32 //发起
	BeChallengeScore uint32 //
	FirstShootIndex  uint32 //首次送礼锁
	FirstShootUid    uint32
	PkStatus         uint32 //PK状态
}

type ChannelLivePkInfo struct {
	AUid       uint32 `json:"a_uid,omitempty"`
	AChannelId uint32 `json:"a_channel_id,omitempty"`
	BUid       uint32 `json:"b_uid,omitempty"`
	BChannelId uint32 `json:"b_channel_id,omitempty"`
}

type TickTask struct {
	TaskType    uint32 `json:"task_type"` //事件类型 0PK定时
	AUid        uint32 `json:"a_uid"`
	BUid        uint32 `json:"b_uid"`
	AChannelId  uint32 `json:"a_channel_id,omitempty"`
	BChannelId  uint32 `json:"b_channel_id,omitempty"`
	Status      uint32 `json:"status"` //pk阶段
	BeginTs     uint32 `json:"begin_ts"`
	Source      uint32 `json:"source"`
	MatchSource int32  `json:"match_source"`
}

type ApplyPk struct {
	Uid             uint32 `json:"uid"`
	ChannelId       uint32 `json:"channel_id"`
	ChannelLiveId   uint32 `json:"channel_live_id"`
	ChannelClientId string `json:"channel_client_id"`
	ApplyTime       uint32 `json:"apply_time"`
	ApplyStatus     uint32 `json:"apply_status,omitempty"`
	Account         string `json:"account"`
	ApplyID         int64  `json:"apply_id"`
}

type LiveRecord struct {
	Uid           uint32 `json:"uid"`
	ChannelId     uint32 `json:"channel_id"`
	ChannelLiveId uint32 `json:"channel_live_id"`
	Score         uint32 `json:"score"`
}

// 结束直播数据统计
type ChannelLiveData struct {
	AudienceCnt         uint32
	GiftValue           uint64
	SendGiftAudienceCnt uint32
	AnchorGiftValue     uint64
	BeginTime           uint64
	EndTime             uint64
	KnightValue         uint64
	GameGiftValue       uint64 // 互动游戏流水
	GameTs              uint64 // 互动游戏时长
}

type RankUser struct {
	Uid        uint32
	Score      uint32
	StrUidInfo string
}

func (s ApplyPk) MarshalBinary() (data []byte, err error) {
	return json.Marshal(s)
}
func (s *ApplyPk) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, s)
}

func NewChannelLiveMgrCache(r *redis.Client, webR *redis.Client, fr *redis.Client, tracer opentracing.Tracer) *ChannelLiveMgrCache {
	gRedis = r

	// 初始化布隆过滤器
	appointPkBloomFilter := NewAppointPkBloomFilter(r)

	return &ChannelLiveMgrCache{
		RedisClient:          r,
		RedisFlagClient:      fr,
		RedisWebClient:       webR,
		tracer:               tracer,
		appointPkBloomFilter: appointPkBloomFilter,
	}
}

// pk状态对方channelID，方便批量获取
func PkOpponentsChannelID(channelID uint32) string {
	return fmt.Sprintf("channel_pk_simple_%v", channelID)
}

func GetGlobalRedis() *redis.Client {
	return gRedis
}

func (s *ChannelLiveMgrCache) SetNoAuthFlag(val int, keys ...string) {

	pipe := s.RedisFlagClient.Pipeline()
	defer pipe.Close()

	for _, k := range keys {
		if val == 0 {
			pipe.Del(k)
		} else {
			pipe.Set(k, val, time.Hour*24*30)
		}
	}
	pipe.Exec()
}

func (s *ChannelLiveMgrCache) GetNoAuthFlag(key string) bool {
	res, err := s.RedisFlagClient.Get(key).Int()
	if nil != err {
		log.Errorf("GetFlag key:%v err:%v", key, err)
		return false
	}
	return res == 1
}

func (s *ChannelLiveMgrCache) GetChannelLiveInfo(id uint32, ty pb.EnumIDType) (*pb.ChannelLiveInfo, error) {

	field := strconv.FormatUint(uint64(id), 10)

	key := R_Channel_Live_Info
	if ty == pb.EnumIDType_CHANNEL_ID {
		key = R_Channel_Live_Info_By_Channel_Id
	}

	str, err := s.RedisClient.HGet(key, field).Result()
	chLiveInfo := &pb.ChannelLiveInfo{}

	if err != nil {
		log.Errorf("GetChannelLiveInfo result nil uid:%v ty:%v", id, ty)
		return chLiveInfo, err
	}

	err = proto.Unmarshal([]byte(str), chLiveInfo)

	if err != nil {
		return chLiveInfo, err
	}

	return chLiveInfo, err
}

func (s *ChannelLiveMgrCache) SetChannelLiveInfo(uid uint32, chLiveInfo *pb.ChannelLiveInfo) error {

	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()

	str, _ := proto.Marshal(chLiveInfo)
	field := strconv.FormatUint(uint64(uid), 10)

	pipe.HSet(R_Channel_Live_Info, field, str)

	fieldChannelID := strconv.FormatUint(uint64(chLiveInfo.ChannelId), 10)

	pipe.HSet(R_Channel_Live_Info_By_Channel_Id, fieldChannelID, str)

	pipe.Exec()

	return nil
}

func (s *ChannelLiveMgrCache) DelChannelLiveInfo(uid, channelId uint32) error {
	pipe := s.RedisClient.Pipeline()

	defer pipe.Close()
	field := strconv.FormatUint(uint64(uid), 10)
	pipe.HDel(R_Channel_Live_Info, field)
	fieldChannelID := strconv.FormatUint(uint64(channelId), 10)
	pipe.HDel(R_Channel_Live_Info_By_Channel_Id, fieldChannelID)

	pipe.Exec()

	return nil
}

func (s *ChannelLiveMgrCache) DelChannelLiveInfoByChannelID(channelId uint32) error {
	fieldChannelID := strconv.FormatUint(uint64(channelId), 10)
	s.RedisClient.HDel(R_Channel_Live_Info_By_Channel_Id, fieldChannelID)
	return nil
}

func (s *ChannelLiveMgrCache) GetAnchorByUidList(uidList ...uint32) (map[uint32]*pb.ChannelLiveInfo, error) {
	result := make([]string, 0)

	strUidList := make([]string, 0)
	for _, uid := range uidList {
		strUidList = append(strUidList, fmt.Sprintf("%v", uid))
	}

	arrResult, _ := s.RedisClient.HMGet(R_Channel_Live_Info, strUidList...).Result()
	for _, i := range arrResult {
		if i == nil {
			continue
		}
		result = append(result, i.(string))
	}
	nowTs := uint32(time.Now().Unix())
	chLiveInfoMap := make(map[uint32]*pb.ChannelLiveInfo)
	for _, str := range result {
		chLiveInfo := &pb.ChannelLiveInfo{}

		err := proto.Unmarshal([]byte(str), chLiveInfo)
		if err != nil {
			continue
		}

		if chLiveInfo.EndTime < nowTs {
			continue
		}

		chLiveInfoMap[chLiveInfo.Uid] = chLiveInfo
	}

	return chLiveInfoMap, nil
}

func (s *ChannelLiveMgrCache) GetAllChannelLiveInfo() (map[uint32]*pb.ChannelLiveInfo, map[uint32]*pb.ChannelLiveInfo, error) {

	nowTs := uint32(time.Now().Unix())
	ch2LiveInfoMap := make(map[uint32]*pb.ChannelLiveInfo)
	chLiveInfoMap := make(map[uint32]*pb.ChannelLiveInfo)

	mapResult, _ := s.RedisClient.HGetAll(R_Channel_Live_Info_By_Channel_Id).Result()

	for _, str := range mapResult {
		chLiveInfo := &pb.ChannelLiveInfo{}

		err := proto.Unmarshal([]byte(str), chLiveInfo)
		if err != nil {
			continue
		}

		if chLiveInfo.EndTime < nowTs {
			continue
		}

		chLiveInfoMap[chLiveInfo.Uid] = chLiveInfo
		ch2LiveInfoMap[chLiveInfo.ChannelId] = chLiveInfo
	}

	return chLiveInfoMap, ch2LiveInfoMap, nil
}

func ChannelLiveStatusKey(uid uint32) string {
	return fmt.Sprintf("r_channel_live_status_%v", uid)
}

func ChannelLiveStatusChannelKey(channelId uint32) string {
	return fmt.Sprintf("r_channel_live_channelid_status_%v", channelId)
}

func ChannelLiveMicKey(channelId uint32) string {
	return fmt.Sprintf("r_channel_live_mic_%v", channelId)
}

func beginWatchKey(channelId uint32) string {
	return fmt.Sprintf("channel_live_begin_watch_%v", channelId)
}

func (s *ChannelLiveMgrCache) GetWatchTimeKey(channelId uint32) string {
	return fmt.Sprintf("channel_live_watch_time_%v", channelId)
}

func audieWatchTimeKey(channelId uint32) string {
	return fmt.Sprintf("audie_watch_time_%v", channelId)
}

// 更新主播心跳时间
func (s *ChannelLiveMgrCache) UpdateChannelLiveHeartBeat(uid uint32, nowTs float64) error {
	if nowTs == 0 {
		nowTs = float64(time.Now().Unix())
	}
	_, err := s.RedisClient.ZAdd(R_Channel_Live_Ticker, redis.Z{
		Score:  nowTs,
		Member: uid,
	}).Result()
	return err
}

func (s ChannelLiveMgrCache) GetDayReportCnt(uid uint32) int64 {
	strKey := time.Now().Format("2006-01-02")
	strUid := fmt.Sprintf("%v", uid)
	newCnt, _ := s.RedisClient.HIncrBy(strKey, strUid, 1).Result()
	s.RedisClient.Expire(strKey, time.Second*24*3600)
	return newCnt
}

func (s *ChannelLiveMgrCache) DelChannelLiveHeartBeat(uid uint32) {
	s.RedisClient.ZRem(R_Channel_Live_Ticker, uid).Result()
}

func (s *ChannelLiveMgrCache) GetTimeOutHeartBeatList(expire int64, del bool) []uint32 {
	nowTs := time.Now().Unix()
	//5分钟超时
	maxScore := strconv.FormatInt(nowTs-expire, 10)
	result, _ := s.RedisClient.ZRangeByScore(R_Channel_Live_Ticker, redis.ZRangeBy{
		Min:    "0",
		Max:    maxScore,
		Offset: 0,
		Count:  1024 * 10, //会超??
	}).Result()

	uids := make([]uint32, 0)

	for _, str := range result {
		uid, err := strconv.ParseInt(str, 10, 32)

		if err != nil {
			continue
		}
		uids = append(uids, uint32(uid))
	}

	if del {
		s.RedisClient.ZRemRangeByScore(R_Channel_Live_Ticker, "0", maxScore).Result()
	}

	return uids
}

func (s *ChannelLiveMgrCache) SetUserWatchStatus(channelId, uid, ty uint32) {
	strUid := fmt.Sprintf("%v", uid)

	nowTs := uint64(time.Now().Unix())
	audieWatchKey := audieWatchTimeKey(channelId)
	switch ty {
	case 0:
		// 结束观看，结算时间
		beginWatchTs, err := s.RedisClient.ZScore(audieWatchKey, strUid).Result()
		if err != nil && err != redis.Nil {
			log.Errorf("SetUserWatchStatus RedisClient.ZScore failed key:%v mem:%v err:%v", audieWatchKey, strUid, err)
			break
		}

		if beginWatchTs == 0 {
			log.Debugf("SetUserWatchStatus key:%v mem:%v beginWatchTs is zero", audieWatchKey, strUid)
			break
		}

		log.Debugf("SetUserWatchStatus key:%v mem:%v nowTs:%v beginWatch:%v", audieWatchKey, strUid, nowTs, beginWatchTs)
		watchTs := nowTs - uint64(beginWatchTs)
		err = s.RedisClient.ZAdd(audieWatchKey, redis.Z{
			Score:  float64(watchTs),
			Member: strUid,
		}).Err()
		if err != nil {
			log.Errorf("SetUserWatchStatus edisClient.ZAdd failed key:%v mem:%v err:%v", audieWatchKey, strUid, err)
		}
	case 1:
		// 开始观看
		totalTs, err := s.RedisClient.ZScore(audieWatchKey, strUid).Result()
		if err != nil && err != redis.Nil {
			log.Errorf("SetUserWatchStatus RedisClient.ZScore failed key:%v mem:%v err:%v", audieWatchKey, strUid, err)
			break
		}

		log.Debugf("SetUserWatchStatus key:%v mem:%v nowTs:%v totalTs:%v", audieWatchKey, strUid, nowTs, totalTs)
		// 要累加之前的结束时间，把开始时间向前移
		beginWatchTs := float64(nowTs) - totalTs
		err = s.RedisClient.ZAdd(audieWatchKey, redis.Z{
			Score:  beginWatchTs,
			Member: strUid,
		}).Err()
		if err != nil {
			log.Errorf("SetUserWatchStatus edisClient.ZAdd failed key:%v mem:%v err:%v", audieWatchKey, strUid, err)
		}

	}
}

// 取观看列表
func (s *ChannelLiveMgrCache) GetChannelLiveWatchRank(channelId uint32, count int) ([]*RankUser, error) {
	watchRank := make([]*RankUser, 0)

	audieWatchKey := audieWatchTimeKey(channelId)
	nowTs := uint64(time.Now().Unix())

	// 取还在观看未退房的用户列表
	watchIngResList, err := s.RedisClient.ZRangeByScoreWithScores(audieWatchKey, redis.ZRangeBy{
		Min:   fmt.Sprintf("%d", AudieWatchTimeBoundary),
		Max:   fmt.Sprintf("%d", nowTs),
		Count: int64(count),
	}).Result()
	if err != nil {
		log.Errorf("GetChannelLiveWatchRank RedisClient.ZRangeByScoreWithScores failed key:%v", audieWatchKey)
		return watchRank, err
	}

	// 取已经结束观看，结束时长的用户列表
	watchEdResList, err := s.RedisClient.ZRevRangeByScoreWithScores(audieWatchKey, redis.ZRangeBy{
		Min:   "0",
		Max:   fmt.Sprintf("%d", AudieWatchTimeBoundary),
		Count: int64(count),
	}).Result()
	if err != nil {
		log.Errorf("GetChannelLiveWatchRank RedisClient.ZRevRangeByScoreWithScores failed key:%v", audieWatchKey)
		return watchRank, err
	}

	userWatchTime := make(map[uint32]uint32)
	for _, watchIngRes := range watchIngResList {
		uid, _ := strconv.Atoi(watchIngRes.Member.(string))
		ts := nowTs - uint64(watchIngRes.Score)
		userWatchTime[uint32(uid)] = uint32(ts)
	}

	for _, watchEdRes := range watchEdResList {
		uid, _ := strconv.Atoi(watchEdRes.Member.(string))
		userWatchTime[uint32(uid)] = uint32(watchEdRes.Score)
	}

	for uid, wtime := range userWatchTime {
		user := &RankUser{
			Uid:   uid,
			Score: wtime,
		}
		watchRank = append(watchRank, user)
	}

	log.Debugf("GetChannelLiveWatchRank watchRank:%v", watchRank)

	return watchRank, nil
}

func channelLievDataKey(channelId uint32, perfix string) string {
	return fmt.Sprintf("channel_live_data_2_%v_%v", channelId, perfix)
}

func (s *ChannelLiveMgrCache) GetChannelLiveDataByType(channelId uint32, dataType string) (uint64, error) {
	key := channelLievDataKey(channelId, dataType)

	strVal, err := s.RedisClient.Get(key).Result()
	if err != nil && err != redis.Nil {
		return 0, err
	}

	if err == redis.Nil {
		return 0, nil
	}

	val, _ := strconv.ParseUint(strVal, 10, 32)
	return val, nil
}

func (s *ChannelLiveMgrCache) GetChannelLiveData(channelId uint32) *ChannelLiveData {
	chData := &ChannelLiveData{}

	keys := []string{}
	keys = append(keys, channelLievDataKey(channelId, G_GiftValue))
	keys = append(keys, channelLievDataKey(channelId, G_AnchorGiftValue))
	keys = append(keys, channelLievDataKey(channelId, G_BeginTime))
	keys = append(keys, channelLievDataKey(channelId, G_EndTime))
	keys = append(keys, channelLievDataKey(channelId, G_AnchorKnightValue))
	keys = append(keys, channelLievDataKey(channelId, G_GameGiftValue))
	keys = append(keys, channelLievDataKey(channelId, G_GameTs))

	results, err := s.RedisClient.MGet(keys...).Result()
	if err == nil {
		for i, s := range results {
			var val uint64 = 0
			if s != nil {
				str := s.(string)
				val, _ = strconv.ParseUint(str, 10, 32)
			}
			if i == 0 {
				chData.GiftValue = val
			} else if i == 1 {
				chData.AnchorGiftValue = val
			} else if i == 2 {
				chData.BeginTime = val
			} else if i == 3 {
				chData.EndTime = val
			} else if i == 4 {
				chData.KnightValue = val
			} else if i == 5 {
				chData.GameGiftValue = val
			} else if i == 6 {
				chData.GameTs = val
			}
		}
	}

	key2 := channelLievDataKey(channelId, G_AudienceCnt)
	chData.AudienceCnt = uint32(s.RedisClient.HLen(key2).Val())

	key2 = channelLievDataKey(channelId, G_SendGiftAudienceCnt)
	chData.SendGiftAudienceCnt = uint32(s.RedisClient.HLen(key2).Val())

	return chData
}

func (s ChannelLiveMgrCache) GetReportBeginTime(channelId uint32) uint32 {
	key := channelLievDataKey(channelId, G_ReportBeginTime)
	res, err := s.RedisClient.Get(key).Result()
	if err == nil {
		result, _ := strconv.ParseUint(res, 10, 32)
		return uint32(result)
	}
	return 0
}

// 更新本场直播统计数据
// 去重的数据，观众人数，送礼人数，送礼值等
func (s *ChannelLiveMgrCache) UpdateChannelLiveData(channelId uint32, value int64, file string) (int64, error) {

	if file == G_BeginTime || file == G_EndTime || file == G_ReportBeginTime {
		key := channelLievDataKey(channelId, file)
		_, err := s.RedisClient.Set(key, value, time.Second*3600*24*60).Result()
		return 0, err
	}

	if file == G_GiftValue || file == G_AnchorGiftValue || file == G_AnchorKnightValue || file == G_GameGiftValue || file == G_GameTs {
		key := channelLievDataKey(channelId, file)
		newCnt, err := s.RedisClient.IncrBy(key, value).Result()
		return newCnt, err
	}

	//AudienceCnt,SendGiftAudienceCnt
	fileKey := fmt.Sprintf("%v", value)
	key2 := channelLievDataKey(channelId, file)
	_, err := s.RedisClient.HSet(key2, fileKey, 1).Result()

	return 0, err
}

// 主播使用，更新直播状态
func (s *ChannelLiveMgrCache) SetChannelLiveStatus(uid, channelId uint32, chLiveStatus *pb.ChannelLiveStatus, reset bool) error {
	//直播状态设置
	statusKey := ChannelLiveStatusKey(uid)
	skey := ChannelLiveStatusChannelKey(channelId)

	str, err := proto.Marshal(chLiveStatus)
	if err != nil {
		return err
	}

	pipe := s.RedisClient.Pipeline()

	pipe.HSet(R_Channel_Live_Status, statusKey, str) //直播中设置直播信息

	//记录开播时候的值，结束的时候计算增量
	if chLiveStatus.Status == pb.EnumChannelLiveStatus_OPEN {
		s.UpdateChannelLiveData(channelId, time.Now().Unix(), G_ReportBeginTime)
		if reset {
			s.UpdateChannelLiveData(channelId, time.Now().Unix(), G_BeginTime)
		}
		//简单版状态，提供给批量接口
		simpleStatus := &pb.ChannelLiveStatusSimple{
			AnchorUid: uid,
			ChannelId: channelId,
			Status:    uint32(chLiveStatus.Status),
			BeginTime: uint32(time.Now().Unix()),
		}
		simStr, _ := proto.Marshal(simpleStatus)
		pipe.HSet(R_Channel_Live_ChannelID_Status, skey, simStr)
	} else if chLiveStatus.Status == pb.EnumChannelLiveStatus_CLOSE {
		s.UpdateChannelLiveData(channelId, time.Now().Unix(), G_EndTime)
		pipe.HDel(R_Channel_Live_ChannelID_Status, skey)
		//pipe.HDel(R_Channel_Live_Status, statusKey) //结束直播删除
	}

	pipe.Exec()

	return nil
}

func (s *ChannelLiveMgrCache) IncrKey(key string, expire time.Duration) int64 {
	id, _ := s.RedisClient.Incr(key).Result()
	if expire != 0 {
		s.RedisClient.Expire(key, expire)
	}
	return id
}

// 获取直播状态
func (s *ChannelLiveMgrCache) GetChannelLiveStatus(uid uint32) (*pb.ChannelLiveStatus, error) {
	key := ChannelLiveStatusKey(uid)
	chLiveStatus := &pb.ChannelLiveStatus{}
	str, err := s.RedisClient.HGet(R_Channel_Live_Status, key).Result()
	if err != nil && err != redis.Nil {
		log.Errorf("GetChannelLiveStatus HGet err:%v %v", err, uid)
		return chLiveStatus, err
	}

	if len(str) > 0 {
		err = proto.Unmarshal([]byte(str), chLiveStatus)

		if err != nil {
			log.Errorf("GetChannelLiveStatus Unmarshal err:%v %v", err, uid)
			return chLiveStatus, err
		}
	}

	return chLiveStatus, nil
}

// 取全部直播固定部分的数据,直播中的
func (s *ChannelLiveMgrCache) GetAllChannelLiveStatus() ([]*pb.ChannelLiveStatusSimple, error) {
	statusList := make([]*pb.ChannelLiveStatusSimple, 0)

	res, err := s.RedisClient.HGetAll(R_Channel_Live_ChannelID_Status).Result()
	if err != nil {
		return statusList, err
	}

	for _, item := range res {
		if len(item) == 0 {
			continue
		}
		sta := &pb.ChannelLiveStatusSimple{}
		err = proto.Unmarshal([]byte(item), sta)
		if err != nil {
			log.Errorf("GetAllChannelLiveStatus proto.Unmarshal failed item:%s err:%v", item, err)
			continue
		}

		statusList = append(statusList, sta)
	}

	return statusList, nil
}

// 直播房的麦位列表
func (s *ChannelLiveMgrCache) GetChannelLiveMicList(channelId uint32) ([]*pb.PkMicSpace, error) {
	channelMicList := make([]*pb.PkMicSpace, 0, 10)
	micKey := ChannelLiveMicKey(channelId)

	out, err := s.RedisClient.HGetAll(micKey).Result()
	if err != nil {
		log.Errorf("GetChannelLiveMic HGetAll fail. channelId:%d, err:%v", channelId, err)
	}

	for _, str := range out {
		micInfo := &pb.PkMicSpace{}
		err = proto.Unmarshal([]byte(str), micInfo)
		if err != nil {
			log.Errorf("GetChannelLiveMic Unmarshal fail. channelId:%d, err:%v", channelId, err)
			continue
		}
		channelMicList = append(channelMicList, micInfo)
	}
	return channelMicList, nil
}

func (s *ChannelLiveMgrCache) UpdateMicInfo(channelId uint32, micList []*pb.PkMicSpace) error {

	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()

	micKey := ChannelLiveMicKey(channelId)
	for _, mic := range micList {
		str, err := proto.Marshal(mic)
		if err != nil {
			continue
		}
		pipe.HSet(micKey, fmt.Sprintf("mic_%d", mic.MicId), str)
	}
	pipe.Exec()
	return nil
}

// 包括PK信息，应该需要分开
func (s *ChannelLiveMgrCache) GetChannelLiveStatusInfo(uid, channelId uint32, channelLiveId uint64) (*pb.ChannelLiveStatusInfo, error) {

	chLiveInfo := &pb.ChannelLiveStatusInfo{}

	aLiveStatus, err := s.GetChannelLiveStatus(uid)
	if err != nil {
		return chLiveInfo, err
	}

	chLiveInfo.ChannelLiveStatus = aLiveStatus
	return chLiveInfo, nil
}

func (s *ChannelLiveMgrCache) BatchGetChannelPkStatusSimple(channelList []uint32) []uint32 {
	pkstatus := make([]uint32, 0)
	return pkstatus
}

// 批量取对手PK房间
func (s *ChannelLiveMgrCache) BatchGetOpPkChannelID(channel_id_list []uint32) map[uint32]uint32 {
	opChannelIdMap := make(map[uint32]uint32)

	keys := make([]string, 0)
	for _, cid := range channel_id_list {
		keys = append(keys, PkOpponentsChannelID(cid))
	}

	results, err := s.RedisClient.MGet(keys...).Result()
	if nil != err {
		return opChannelIdMap
	}

	for index, s := range results {
		var opCid uint32 = 0
		if s != nil {
			str := s.(string)
			val, _ := strconv.ParseUint(str, 10, 32)
			opCid = uint32(val)
		}
		if index < len(channel_id_list) {
			opChannelIdMap[channel_id_list[index]] = opCid
		}
	}

	return opChannelIdMap
}

// 根据channelID拿直播状态列表
func (s *ChannelLiveMgrCache) GetChannelLiveStatusSimple(channelList []uint32, isCache bool) ([]*pb.ChannelLiveStatusSimple, error) {

	//var eKey func(cid uint32) string
	eKey := func(cid uint32) string {
		return fmt.Sprintf("get_channel_live_status_simple_%v", cid)
	}

	mapStatus := make(map[uint32]*pb.ChannelLiveStatusSimple)

	lackChannelIDList := make([]uint32, 0)
	for _, cid := range channelList {
		bHit := false
		key := eKey(cid)
		v, ok := cEasyCache.Get(key)
		if (ok && v != nil) && isCache {
			r := v.(*pb.ChannelLiveStatusSimple)
			if r != nil {
				bHit = true
				mapStatus[cid] = r
			}
		}
		if !bHit {
			lackChannelIDList = append(lackChannelIDList, cid)
		}
	}

	channelStatusList := make([]*pb.ChannelLiveStatusSimple, 0, len(channelList))

	if len(lackChannelIDList) > 0 {
		keys := make([]string, 0, len(lackChannelIDList))

		for _, cid := range lackChannelIDList {
			keys = append(keys, ChannelLiveStatusChannelKey(cid))
		}

		var items []interface{}
		items, err := s.RedisClient.HMGet(R_Channel_Live_ChannelID_Status, keys...).Result()
		if err != nil {
			log.Errorf("GetChannelLiveStatusSimple HMGet err:%v", err)
		}

		for i := 0; i < len(items); i++ {
			chLiveStatus := &pb.ChannelLiveStatusSimple{}
			if nil != items[i] {
				value := []byte(items[i].(string))
				if value != nil {
					err = proto.Unmarshal(value, chLiveStatus)
					if err != nil {
						log.Errorf("GetChannelLiveStatusSimple proto.Unmarshal value:%v err:%v", value, err)
					}
					/*
						if err != nil {
							//continue
						}
					*/
				}
			}
			//缓存
			cEasyCache.Set(eKey(chLiveStatus.ChannelId), chLiveStatus, conf.GetLiveCacheTs())
			mapStatus[chLiveStatus.ChannelId] = chLiveStatus
		}
	}

	//opCidMap := pk.BatchGetOpPkChannelID(channelList)

	emptyStu := &pb.ChannelLiveStatusSimple{}
	for _, cid := range channelList {
		v, ok := mapStatus[cid]
		if !ok {
			channelStatusList = append(channelStatusList, emptyStu)
		} else {
			//v.PkChannelId = opCidMap[cid]
			channelStatusList = append(channelStatusList, v)
		}
	}

	return channelStatusList, nil
}

// 批量接口没有麦位信息,uid
func (s *ChannelLiveMgrCache) BatchGetChannelLiveStatus(uidList []uint32) (map[uint32]*pb.ChannelLiveStatus, error) {

	channelStatusList := make(map[uint32]*pb.ChannelLiveStatus)

	lackUidList := make([]uint32, 0, len(uidList))
	for _, uid := range uidList {
		bHit := false
		key := fmt.Sprintf("batch_get_channel_live_status_%v", uid)
		v, ok := cEasyCache.Get(key)
		if ok && v != nil {
			s, ok := v.(*pb.ChannelLiveStatus)
			if ok && s != nil {
				bHit = true
				channelStatusList[uid] = s
			}
		}
		if !bHit {
			lackUidList = append(lackUidList, uid)
		}
	}

	if len(lackUidList) == 0 {
		return channelStatusList, nil
	}

	keys := make([]string, 0, len(lackUidList))
	for _, uid := range lackUidList {
		keys = append(keys, ChannelLiveStatusKey(uid))
	}

	var items []interface{}
	items, err := s.RedisClient.HMGet(R_Channel_Live_Status, keys...).Result()
	if err != nil {
		return channelStatusList, err
	}

	for i := 0; i < len(items); i++ {
		if nil == items[i] {
			continue
		}

		value := []byte(items[i].(string))
		if value == nil {
			continue
		}
		chLiveStatus := &pb.ChannelLiveStatus{}
		err = proto.Unmarshal(value, chLiveStatus)
		if err != nil {
			continue
		}
		channelStatusList[chLiveStatus.Uid] = chLiveStatus

		key := fmt.Sprintf("batch_get_channel_live_status_%v", chLiveStatus.Uid)
		cEasyCache.Set(key, chLiveStatus, conf.GetLiveCacheTs())
	}

	return channelStatusList, nil
}

func (s *ChannelLiveMgrCache) GenNewChannelLiveId(inc int64, fix bool) (uint64, error) {
	key := "r_channel_live_id"

	if fix {
		s.RedisClient.Set(key, inc, 0)
		inc = 1
	}
	channelLiveId, err := s.RedisClient.IncrBy(key, inc).Result()
	return uint64(channelLiveId), err
}

func ChannelApplyKey(channelId uint32) string {
	return fmt.Sprintf("r_channel_live_pk_apply_list_%d", channelId)
}

// pk相关
func (s *ChannelLiveMgrCache) ApplyPk(applyUid, applyChannelId, channelLiveId, targetChannelId uint32, applyId, applyTime int64) error {
	//applyTime := time.Now().Unix()

	applypk := ApplyPk{
		Uid:             applyUid,
		ChannelId:       applyChannelId,
		ChannelClientId: "",
		ChannelLiveId:   channelLiveId,
		ApplyTime:       uint32(applyTime),
		Account:         "", //
		ApplyID:         applyId,
	}

	_, err := s.RedisClient.ZAdd(ChannelApplyKey(targetChannelId),
		redis.Z{
			Score:  float64(applyTime),
			Member: applypk}).Result()

	if err != nil {
		return err
	}

	return nil
}

func (s *ChannelLiveMgrCache) CheckQPSLimit(key string) bool {
	cnt, _ := s.RedisClient.Incr(key).Result()
	if cnt == 1 {
		s.RedisClient.Expire(key, 30*time.Second)
	}
	return cnt == 1
}

func (s *ChannelLiveMgrCache) GetApplyList(channelId uint32) ([]ApplyPk, error) {
	applyTime := int64(time.Now().Unix())
	applys := make([]ApplyPk, 0)
	key := ChannelApplyKey(channelId)
	results, err := s.RedisClient.ZRangeByScore(key, redis.ZRangeBy{
		Min:   strconv.FormatInt(applyTime-10, 10),
		Max:   strconv.FormatInt(applyTime+1, 10),
		Count: 1024,
	}).Result()

	if err != nil {
		log.Errorf("GetApplyList err:%v", err)
		return applys, err
	}

	for i := 0; i < len(results); i++ {
		value := []byte(results[i])
		apply := ApplyPk{}
		err = apply.UnmarshalBinary(value)
		if err != nil {
			continue
		}
		applys = append(applys, apply)
	}

	return applys, nil
}

func (s *ChannelLiveMgrCache) DelApply(channelId, applyChannelId uint32) (bool, *ApplyPk, error) {
	applyTime := time.Now().Unix()
	key := ChannelApplyKey(channelId)
	results, err := s.RedisClient.ZRangeByScore(key, redis.ZRangeBy{
		Min:   strconv.FormatInt(applyTime-30, 10),
		Max:   strconv.FormatInt(applyTime+1, 10),
		Count: 100,
	}).Result()
	if err != nil {
		log.Errorf("DelApply ZRangeByScore failed %d %d err:%v", channelId, applyChannelId, err)
	}

	for i := 0; i < len(results); i++ {
		value := []byte(results[i])
		apply := ApplyPk{}
		err = apply.UnmarshalBinary(value)
		if err != nil {
			continue
		}
		if apply.ChannelId == applyChannelId {
			s.RedisClient.ZRem(key, apply).Result()
			return true, &apply, nil
		}
	}
	return false, nil, nil
}

func (s *ChannelLiveMgrCache) ClearApply(channelId uint32) error {
	key := ChannelApplyKey(channelId)
	_, err := s.RedisClient.ZRemRangeByScore(key, "0", "-1").Result()
	return err
}

// PK状态定时器
func (s *ChannelLiveMgrCache) AddTaskToTick(task *TickTask, triggerTime uint32) error {

	mem, err := json.Marshal(task)
	if err != nil {
		log.Errorf("AddTaskToTick Marshal task:%v", task)
		return err
	}

	_, serr := s.RedisClient.ZAdd(R_Channel_Live_PK_Ticker, redis.Z{
		Score:  float64(triggerTime),
		Member: mem,
	}).Result()

	log.Debugf("AddTaskToTick task:%v triggerTime:%v serr:%v", task, triggerTime, serr)

	return nil
}

func (s *ChannelLiveMgrCache) GetLock(key string) (bool, error) {
	return s.RedisClient.SetNX(key, 1, time.Second*10).Result()
}

func (s *ChannelLiveMgrCache) GetLockWithTtl(key string, ttl time.Duration) (bool, error) {
	return s.RedisClient.SetNX(key, 1, ttl).Result()
}

func (s *ChannelLiveMgrCache) UnLock(key string) {
	s.RedisClient.Del(key).Result()
}

func (s *ChannelLiveMgrCache) GetTriggerTask() ([]*TickTask, error) {
	nowTime := time.Now().Unix()
	maxScore := strconv.FormatInt(nowTime, 10)
	res, _ := s.RedisClient.ZRangeByScore(R_Channel_Live_PK_Ticker, redis.ZRangeBy{
		Min:    "0",
		Max:    maxScore,
		Offset: 0,
		Count:  1024,
	}).Result()

	tasks := make([]*TickTask, 0)

	for _, str := range res {
		task := &TickTask{}
		err := json.Unmarshal([]byte(str), task)
		if err != nil {
			log.Errorf("GetTriggerTask Unmarshal err:%v", err)
			continue
		}
		tasks = append(tasks, task)
	}

	//删掉已经取出的任务
	s.RedisClient.ZRemRangeByScore(R_Channel_Live_PK_Ticker, "0", maxScore)

	return tasks, nil
}

// 直播送礼榜KEY
func sendGiftRankKey(channelId uint32) string {
	key := fmt.Sprintf("live_rank_%v", channelId)
	return key
}

// 直播送礼榜
func (s *ChannelLiveMgrCache) UpdateSendGiftRank(channelId, sendUid, value uint32) error {
	key := sendGiftRankKey(channelId)

	s.RedisClient.ZIncr(key, redis.Z{
		Member: sendUid,
		Score:  float64(value),
	}).Result()

	return nil
}

func (s *ChannelLiveMgrCache) GetSendGiftRank(channelId uint32) ([]*RankUser, error) {
	ranks := make([]*RankUser, 0)
	key := sendGiftRankKey(channelId)
	results, err := s.RedisClient.ZRevRangeWithScores(key, 0, 100).Result()
	if err != nil {
		log.Errorf("GetSendGiftRank ZRangeWithScores fail err:%v", err)
	}

	for _, user := range results {
		Uid, _ := strconv.ParseInt(user.Member.(string), 10, 32)
		ranks = append(ranks, &RankUser{
			Uid:   uint32(Uid),
			Score: uint32(user.Score),
		})
	}
	return ranks, nil
}

// 直播结束，清理直播数据
func (s *ChannelLiveMgrCache) OnChannelLiveFinish(channelId uint32) {
	//清理送礼排行榜

	delKeyList := make([]string, 0)
	pipe := s.RedisClient.Pipeline() //

	delKeyList = append(delKeyList, sendGiftRankKey(channelId))

	//观众人数
	delKeyList = append(delKeyList, channelLievDataKey(channelId, G_AudienceCnt))

	//送礼人数
	delKeyList = append(delKeyList, channelLievDataKey(channelId, G_SendGiftAudienceCnt))

	//房间流水
	delKeyList = append(delKeyList, channelLievDataKey(channelId, G_GiftValue))

	//主播流水
	delKeyList = append(delKeyList, channelLievDataKey(channelId, G_AnchorGiftValue))

	//骑士流水
	delKeyList = append(delKeyList, channelLievDataKey(channelId, G_AnchorKnightValue))

	//互动游戏流水
	delKeyList = append(delKeyList, channelLievDataKey(channelId, G_GameGiftValue))

	//互动游戏时长
	delKeyList = append(delKeyList, channelLievDataKey(channelId, G_GameTs))

	//开始观看时间戳
	delKeyList = append(delKeyList, beginWatchKey(channelId))

	//观看时长
	delKeyList = append(delKeyList, audieWatchTimeKey(channelId))

	//PK申请列表
	appllyKey := ChannelApplyKey(channelId)
	pipe.ZRemRangeByScore(appllyKey, "0", "-1")
	pipe.Del(delKeyList...)

	pipe.Exec()
}

func (s *ChannelLiveMgrCache) ItemToolKey(channelId, beginTs, getUid uint32) string {
	key := fmt.Sprintf("pk_item_tool_%v_%v_%v", channelId, beginTs, getUid)
	return key
}

// 用户获得道具
func (s *ChannelLiveMgrCache) GainItemToUser(channelId, beginTs, getUid uint32, itemId string) error {
	key := s.ItemToolKey(channelId, beginTs, getUid)
	ns := time.Now().Unix()
	item := &pb.ToolItem{
		ItemId: itemId,
		BeUsed: false,
		ItemNs: ns,
	}
	js, _ := proto.Marshal(item)
	strItemNs := fmt.Sprintf("%v", ns)
	s.RedisClient.HSet(key, strItemNs, js).Result()
	s.RedisClient.Expire(key, time.Second*60*10)
	return nil
}

func (s *ChannelLiveMgrCache) UseItem(channelId, beginTs, getUid uint32, itemId string, itemNs int64) error {
	log.Debugf("UseItem uid:%v getUid:%v itemID:%v", channelId, getUid, itemId)

	key := s.ItemToolKey(channelId, beginTs, getUid)
	item := &pb.ToolItem{
		ItemId: itemId,
		BeUsed: true,
		ItemNs: itemNs,
	}
	strItemNs := fmt.Sprintf("%v", itemNs)
	str, _ := proto.Marshal(item)
	s.RedisClient.HSet(key, strItemNs, str)
	return nil
}

func (s *ChannelLiveMgrCache) GetUserItemList(channelId, beginTs, getUid uint32) ([]*pb.ToolItem, error) {
	key := s.ItemToolKey(channelId, beginTs, getUid)
	res, _ := s.RedisClient.HGetAll(key).Result()

	items := make([]*pb.ToolItem, 0)
	for _, val := range res {
		item := &pb.ToolItem{}
		err := proto.Unmarshal([]byte(val), item)
		if err != nil {
			continue
		}
		items = append(items, item)
	}

	sort.Slice(items, func(i, j int) bool {
		return items[i].ItemNs < items[j].ItemNs
	})

	return items, nil
}

func (s *ChannelLiveMgrCache) GetValidPushUidList(ts time.Time, uidList []uint32, anchorUid uint32) []uint32 {
	var totalCnt, anchorCnt uint64 = 2, 1

	uidStrList := make([]string, 0, len(uidList))
	for _, uid := range uidList {
		uidStrList = append(uidStrList, fmt.Sprintf("%v", uid))
	}

	validRecUidMap := make(map[string]interface{})
	validUidList := make([]string, 0)
	nowTs := time.Now()

	//自然小时内，已经收到2次推送的不在推
	reciveKey := fmt.Sprintf("channel-live-fans-receive-%v", nowTs.Format("2006-01-02-15"))
	receiveRes, err := s.RedisClient.HMGet(reciveKey, uidStrList...).Result()
	if err == nil {
		for i := 0; i < len(receiveRes); i++ {
			uidStr := uidStrList[i]
			if nil == receiveRes[i] {
				validRecUidMap[uidStr] = 1
				validUidList = append(validUidList, uidStrList[i])
				continue
			}
			str := receiveRes[i].(string)
			cnt, _ := strconv.ParseUint(str, 10, 32)
			if cnt >= totalCnt {
				continue
			}
			validRecUidMap[uidStr] = cnt + 1
			validUidList = append(validUidList, uidStrList[i])
		}
	}

	//当前小时已经收到过这个主播推送的
	invalidPushUidMap := make(map[string]interface{})
	validPushUidList := make([]uint32, 0)
	pushKey := fmt.Sprintf("channel-live-anchor-push-%v-%v", anchorUid, ts.Format("2006-01-02-15"))
	pushRes, err := s.RedisClient.HMGet(pushKey, validUidList...).Result()

	if err == nil {
		for i := 0; i < len(pushRes); i++ {
			uidStr := validUidList[i]
			if nil == pushRes[i] {
				uid, _ := strconv.ParseUint(validUidList[i], 10, 32)
				validPushUidList = append(validPushUidList, uint32(uid))
				invalidPushUidMap[uidStr] = 1
				continue
			}
			str := pushRes[i].(string)
			cnt, _ := strconv.ParseUint(str, 10, 32)
			if cnt >= anchorCnt {
				delete(validRecUidMap, uidStr)
				continue
			}
			uid, _ := strconv.ParseUint(validUidList[i], 10, 32)
			validPushUidList = append(validPushUidList, uint32(uid))
			invalidPushUidMap[uidStr] = cnt + 1
		}
	}

	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()

	pipe.HMSet(reciveKey, validRecUidMap)
	pipe.HMSet(pushKey, invalidPushUidMap)

	pipe.Expire(reciveKey, time.Hour*2)
	pipe.Expire(pushKey, time.Hour*2)
	pipe.Exec()

	return validPushUidList
}

type ChannelLiveValue struct {
	DayNo uint32 `json:"day_no"`
	Value uint32 `json:"value"`
}

// 记录PK值
func (s *ChannelLiveMgrCache) RecordPKValue(uid, pkValue uint32) {
	key := fmt.Sprintf("pk_value_record_%v", uid)
	re := &ChannelLiveValue{
		DayNo: 0,
		Value: pkValue,
	}
	mem, _ := json.Marshal(re)
	s.RedisClient.LPush(key, mem)
}

func (s *ChannelLiveMgrCache) GetPKAvg(uid, count uint32) uint32 {
	key := fmt.Sprintf("pk_value_record_%v", uid)
	var total uint32 = 0
	results, err := s.RedisClient.LRange(key, 0, int64(count-1)).Result()
	if err != nil {
		log.Errorf("GetPKRecord err:%v", err)
		return 0
	}

	log.Debugf("GetPKMatchValue uid:%v pk_rc_results:%v", uid, results)

	if len(results) == 0 {
		return 0
	}

	for _, item := range results {
		re := &ChannelLiveValue{}
		json.Unmarshal([]byte(item), re)
		total = total + re.Value
	}

	sz, _ := s.RedisClient.LLen(key).Result()
	if sz >= 1024 {
		pipe := s.RedisClient.Pipeline()
		defer pipe.Close()
		for i := 0; i < 200; i++ {
			pipe.RPop(key)
		}
		pipe.Exec()
	}

	return total / uint32(len(results))
}

func GetDayNo(ts uint32) uint32 {
	dayNo := (ts - 1577808000) / (3600 * 24)
	return dayNo
}

func (s *ChannelLiveMgrCache) RecordChannelLiveValue(uid, beginTs, value uint32) {
	key := fmt.Sprintf("channel_live_record_%v", uid)
	dayNo := GetDayNo(beginTs)
	newC := &ChannelLiveValue{
		DayNo: dayNo,
		Value: value,
	}
	oldC := &ChannelLiveValue{}
	strMem, err := s.RedisClient.LPop(key).Result()
	if err == nil {
		json.Unmarshal([]byte(strMem), oldC)
	}

	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()

	if oldC.DayNo == dayNo {
		newC.Value = newC.Value + oldC.Value
		mem, _ := json.Marshal(newC)
		pipe.LPush(key, mem)
	} else {
		mem, _ := json.Marshal(oldC)
		pipe.LPush(key, mem)

		mem, _ = json.Marshal(newC)
		pipe.LPush(key, mem)
	}
	pipe.Exec()
}

func (s *ChannelLiveMgrCache) GetChannelLiveAvg(uid, days uint32) uint32 {
	var total uint32 = 0
	key := fmt.Sprintf("channel_live_record_%v", uid)

	results, err := s.RedisClient.LRange(key, 0, int64(days-1)).Result()
	if err != nil {
		log.Errorf("GetChannelLiveValues err:%v", err)
		return 0
	}

	if len(results) == 0 {
		return 0
	}

	for _, item := range results {
		rc := &ChannelLiveValue{}
		json.Unmarshal([]byte(item), rc)
		total = total + rc.Value
	}

	sz, _ := s.RedisClient.LLen(key).Result()
	if sz > 1024 {
		pipe := s.RedisClient.Pipeline()
		defer pipe.Close()
		for i := 0; i < 600; i++ {
			pipe.RPop(key)
		}
		pipe.Exec()
	}

	return total / uint32(len(results))
}

func (s *ChannelLiveMgrCache) GetMatchScore(uid uint32, matchType pb.ChannelLivePKMatchType) (int, error) {
	dayKey := time.Now().Format("20060102")
	key := fmt.Sprintf("match_value_%s_%v_%v", dayKey, matchType, uid)
	res, err := s.RedisClient.Get(key).Result()
	if err != nil {
		return 0, err
	}

	return strconv.Atoi(res)
}

func (s *ChannelLiveMgrCache) SetMatchScore(uid uint32, matchType pb.ChannelLivePKMatchType, score uint32) {
	dayKey := time.Now().Format("20060102")
	key := fmt.Sprintf("match_value_%s_%v_%v", dayKey, matchType, uid)

	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()
	pipe.Set(key, score, time.Hour*24).Result()
	pipe.Expire(key, 24*3600*time.Second)
	pipe.Exec()
}

type MatchMember struct {
	Uid       uint32 `json:"uid"`
	ChannelId uint32 `json:"channel_id"`
	Value     uint32 `json:"value"`
}

func (s MatchMember) MarshalBinary() (data []byte, err error) {
	return json.Marshal(s)
}
func (s *MatchMember) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, s)
}

func (s *ChannelLiveMgrCache) AddToMatchPool(matchType pb.ChannelLivePKMatchType, uid, channelId, value, beginTs uint32) {
	m := &MatchMember{
		Uid:       uid,
		ChannelId: channelId,
		Value:     0,
	}

	member, err := json.Marshal(m)
	if err != nil {
		log.Errorf("AddToMatchPool Marshal err:%v", err)
		return
	}

	key := fmt.Sprintf("match_pool_%v", matchType)

	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()

	pipe.ZAdd(key, redis.Z{
		Score:  float64(beginTs),
		Member: member,
	}).Result()

	if value > 0 {
		matchValueKey := fmt.Sprintf("match_value_%v", matchType)
		pipe.HSet(matchValueKey, fmt.Sprintf("%v", uid), value).Result()
	}
	pipe.Exec()
}

type MatchMembers []*MatchMember

func (w MatchMembers) Len() int {
	return len(w)
}

func (w MatchMembers) Less(i, j int) bool {
	return w[i].Value > w[j].Value
}

func (w MatchMembers) Swap(i, j int) {
	w[i], w[j] = w[j], w[i]
}

func (s *ChannelLiveMgrCache) GetMatchPool(matchType pb.ChannelLivePKMatchType, min, max uint32) ([]*MatchMember, error) {
	key := fmt.Sprintf("match_pool_%v", matchType)
	matchValueKey := fmt.Sprintf("match_value_%v", matchType)

	results, err := s.RedisClient.ZRangeByScore(key, redis.ZRangeBy{
		Min:    fmt.Sprintf("%v", min),
		Max:    fmt.Sprintf("%v", max),
		Offset: 0,
		Count:  1024,
	}).Result()

	members := MatchMembers{}

	if err != nil {
		log.Errorf("GetMatchPool ZRangeByScore err:%v", err)
		return members, err
	}

	log.Debugf("GetMatchPool matchType:%v min:%v max:%v result:%v", matchType, min, max, results)

	fields := make([]string, 0)
	for _, item := range results {
		member := &MatchMember{}
		err := json.Unmarshal([]byte(item), member)
		if err != nil {
			log.Errorf("GetMatchPool Unmarshal err:%v", err)
			continue
		}
		members = append(members, member)
		fields = append(fields, fmt.Sprintf("%v", member.Uid))
	}

	var items []interface{}
	items, _ = s.RedisClient.HMGet(matchValueKey, fields...).Result()
	if len(items) == len(fields) {
		for i, v := range items {
			if v == nil {
				continue
			}
			s := v.(string)
			value, _ := strconv.ParseInt(s, 10, 32)
			members[i].Value = uint32(value)
		}
	}

	sort.Sort(members)

	log.Debugf("GetMatchPool return members:%v", members)

	return members, nil
}

func (s *ChannelLiveMgrCache) DelFromMatchPool(matchType pb.ChannelLivePKMatchType, members []MatchMember) error {
	key := fmt.Sprintf("match_pool_%v", matchType)

	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()

	for _, m := range members {
		pipe.ZRem(key, m).Result()
	}
	/*	fields := make([]string, 0)
		for _, m := range members {
			fields = append(fields, fmt.Sprintf("%v", m.Uid))
		}*/

	pipe.Exec()
	return nil
}

/*
func (s *ChannelLiveMgrCache) GetPkRankScore(uid uint32) uint32 {
	field := fmt.Sprintf("%v", uid)
	val, _ := s.RedisWebClient.ZScore("pk:final:rank:v2", field).Result()

	//score := (uint64(val) >> 32) & 0x00000000ffffffff

	log.Infof("GetPkRankScore uid:%v val:%v\n", uid, val)

	return uint32(val)
}
*/

func (s *ChannelLiveMgrCache) CheckIsAnchorInBackList(uid uint32) bool {
	res, _ := s.RedisClient.ZScore(ChannelLiveAnchorBackList, fmt.Sprintf("%v", uid)).Result()
	return res > 0
}

func (s *ChannelLiveMgrCache) AddAnchorInBackList(uidList []uint32) {
	pipe := s.RedisClient.Pipeline()
	defer pipe.Close()

	for _, uid := range uidList {
		pipe.ZAdd(ChannelLiveAnchorBackList, redis.Z{
			Score:  float64(time.Now().Unix()),
			Member: fmt.Sprintf("%v", uid),
		})
	}
	pipe.Exec()
}

func (s *ChannelLiveMgrCache) GetAnchorBackList(off, count int64) []uint32 {
	res, _ := s.RedisClient.ZRevRange(ChannelLiveAnchorBackList, off, count).Result()
	uidList := make([]uint32, 0)
	for _, s := range res {
		uid, _ := strconv.ParseUint(s, 10, 32)
		uidList = append(uidList, uint32(uid))
	}
	return uidList
}

func (s *ChannelLiveMgrCache) DelAnchorBackList(uidList []uint32) {
	uidMembers := make([]interface{}, 0, len(uidList))
	for _, uid := range uidList {
		uidMembers = append(uidMembers, fmt.Sprintf("%v", uid))
	}
	s.RedisClient.ZRem(ChannelLiveAnchorBackList, uidMembers...)
}

func (c *ChannelLiveMgrCache) getUkwKey(cid uint32, strUidInfo string) string {
	if cid != 0 {
		return fmt.Sprintf("ukw-%d-%s", cid, strUidInfo)
	}

	return strUidInfo
}

func (c *ChannelLiveMgrCache) SetUkwInfo(cid uint32, strUidInfo string, ukw *pb.UkwInfo) error {
	value, err := json.Marshal(ukw)
	if err != nil {
		return err
	}

	ukwKey := c.getUkwKey(cid, strUidInfo)
	err = c.RedisClient.Set(ukwKey, value, time.Duration(2*24*3600)*time.Second).Err()
	return err
}

func (c *ChannelLiveMgrCache) GetUkwInfo(cid uint32, strUidInfo string) (*pb.UkwInfo, error) {
	ukwInfo := &pb.UkwInfo{}

	ukwKey := c.getUkwKey(cid, strUidInfo)
	value, err := c.RedisClient.Get(ukwKey).Result()
	if err != nil && err != redis.Nil {
		return ukwInfo, err
	}

	if err == redis.Nil {
		return ukwInfo, nil
	}

	err = json.Unmarshal([]byte(value), ukwInfo)
	if err != nil {
		log.Errorf("GetUkwInfo Unmarshal failed cid:%d strInfo:%v err:%v", cid, strUidInfo, err)
	}

	return ukwInfo, nil
}

func (c *ChannelLiveMgrCache) DelUkwInfo(cid uint32, strUidInfo string) error {
	ukwKey := c.getUkwKey(cid, strUidInfo)
	err := c.RedisClient.Del(ukwKey).Err()
	return err
}

func (c *ChannelLiveMgrCache) getAnchorGameKey(cid uint32) string {
	return fmt.Sprintf("anchor_game_%d", cid)
}

func (c *ChannelLiveMgrCache) SetAnchorGameTs(cid uint32, ts int64) error {
	key := c.getAnchorGameKey(cid)
	err := c.RedisClient.Set(key, ts, time.Hour*24*7).Err()
	if err != nil {
		return err
	}

	return nil
}

func (c *ChannelLiveMgrCache) GetAnchorGameTs(cid uint32) (int64, error) {
	key := c.getAnchorGameKey(cid)
	res, err := c.RedisClient.Get(key).Int64()
	if err != nil {
		return 0, err
	}

	return res, nil
}

// 内存cache
type EasyItem struct {
	expire_at int64
	item      interface{}
}
type EasyCache struct {
	items sync.Map
}

func (e *EasyCache) Set(key string, item interface{}, expire int64) {
	v := &EasyItem{
		expire_at: time.Now().Unix() + expire,
		item:      item,
	}
	e.items.Store(key, v)
}

func (e *EasyCache) Get(key string) (interface{}, bool) {
	val, ok := e.items.Load(key)
	if ok {
		em := val.(*EasyItem)
		if em != nil && em.expire_at < time.Now().Unix() {
			e.items.Delete(key)
			return nil, false
		}
		return em.item, ok
	}
	return nil, false
}

func NewEasyCache() *EasyCache {
	e := &EasyCache{
		items: sync.Map{},
	}

	go func() {
		t := time.NewTicker(time.Minute)
		defer t.Stop()

		for range t.C {
			func() {
				if r := recover(); r != nil {
					log.Errorf("NewEasyCache r:%v", r)
				}

				log.Debugf("NewEasyCache begin")

				nowTs := time.Now().Unix()
				e.items.Range(func(key, value interface{}) bool {
					item := value.(*EasyItem)
					if nil != item {
						if nowTs >= item.expire_at {
							e.items.Delete(key)
						}
					}
					return true
				})
			}()
		}

		/*
			for {
				select {
				case <-time.After(time.Minute):
					func() {
						if r := recover(); r != nil {
							log.Errorf("NewEasyCache r:%v", r)
						}
						nowTs := time.Now().Unix()
						e.items.Range(func(key, value interface{}) bool {
							item := value.(*EasyItem)
							if nil != item {
								if nowTs >= item.expire_at {
									e.items.Delete(key)
								}
							}
							return true
						})
					}()
				}
			}
		*/
	}()

	return e
}
