package cache

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis"
	"github.com/golang/protobuf/proto"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

const (
	// 缓存键前缀
	ContestPkConfigPrefix      = "contest_pk_config:"
	ContestPkRestrictionPrefix = "contest_pk_restriction:"
	ContestPkTimerPrefix       = "contest_pk_timer:"

	// 缓存过期时间
	ContestPkConfigTTL      = 24 * time.Hour // 配置缓存24小时
	ContestPkRestrictionTTL = 2 * time.Hour  // 限制缓存2小时
	ContestPkTimerTTL       = 1 * time.Hour  // 定时器缓存1小时
)

// ContestPkRestriction 主播赛事PK限制信息
type ContestPkRestriction struct {
	AnchorUID     uint64 `json:"anchor_uid"`
	ContestID     string `json:"contest_id"`
	RestrictBegin uint64 `json:"restrict_begin"` // 限制开始时间
	RestrictEnd   uint64 `json:"restrict_end"`   // 限制结束时间
	Reason        string `json:"reason"`
}

// getContestPkConfigKey 获取赛事PK配置缓存键
func getContestPkConfigKey(contestId string) string {
	return ContestPkConfigPrefix + contestId
}

// getContestPkRestrictionKey 获取主播赛事PK限制缓存键
func getContestPkRestrictionKey(anchorUid uint64) string {
	return fmt.Sprintf("%s%d", ContestPkRestrictionPrefix, anchorUid)
}

// getContestPkTimerKey 获取赛事PK定时器缓存键
func getContestPkTimerKey(timerType string, index uint32) string {
	return fmt.Sprintf("%s%s_%d", ContestPkTimerPrefix, timerType, index)
}

// SetContestPkConfig 设置赛事PK配置缓存
func (c *ChannelLiveMgrCache) SetContestPkConfig(contestId string, config *pb.ContestPkConfig) error {
	key := getContestPkConfigKey(contestId)

	byteData, err := proto.Marshal(config)
	if err != nil {
		log.Errorf("SetContestPkConfig Marshal failed contestId:%s err:%v", contestId, err)
		return err
	}

	err = c.RedisClient.Set(key, byteData, ContestPkConfigTTL).Err()
	if err != nil {
		log.Errorf("SetContestPkConfig Redis Set failed contestId:%s err:%v", contestId, err)
		return err
	}

	log.Debugf("SetContestPkConfig success contestId:%s", contestId)
	return nil
}

// GetContestPkConfig 获取赛事PK配置缓存
func (c *ChannelLiveMgrCache) GetContestPkConfig(contestId string) (*pb.ContestPkConfig, error) {
	key := getContestPkConfigKey(contestId)

	byteData, err := c.RedisClient.Get(key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		log.Errorf("GetContestPkConfig Redis Get failed contestId:%s err:%v", contestId, err)
		return nil, err
	}

	config := &pb.ContestPkConfig{}
	err = proto.Unmarshal(byteData, config)
	if err != nil {
		log.Errorf("GetContestPkConfig Unmarshal failed contestId:%s err:%v", contestId, err)
		return nil, err
	}

	log.Debugf("GetContestPkConfig success contestId:%s", contestId)
	return config, nil
}

// DelContestPkConfig 删除赛事PK配置缓存
func (c *ChannelLiveMgrCache) DelContestPkConfig(contestId string) error {
	key := getContestPkConfigKey(contestId)

	err := c.RedisClient.Del(key).Err()
	if err != nil {
		log.Errorf("DelContestPkConfig failed contestId:%s err:%v", contestId, err)
		return err
	}

	log.Debugf("DelContestPkConfig success contestId:%s", contestId)
	return nil
}

// SetContestPkRestriction 设置主播赛事PK限制
func (c *ChannelLiveMgrCache) SetContestPkRestriction(anchorUid uint64, restriction *ContestPkRestriction) error {
	key := getContestPkRestrictionKey(anchorUid)

	byteData, err := json.Marshal(restriction)
	if err != nil {
		log.Errorf("SetContestPkRestriction Marshal failed anchorUid:%d err:%v", anchorUid, err)
		return err
	}

	err = c.RedisClient.Set(key, byteData, ContestPkRestrictionTTL).Err()
	if err != nil {
		log.Errorf("SetContestPkRestriction Redis Set failed anchorUid:%d err:%v", anchorUid, err)
		return err
	}

	log.Debugf("SetContestPkRestriction success anchorUid:%d contestId:%s", anchorUid, restriction.ContestID)
	return nil
}

// GetContestPkRestriction 获取主播赛事PK限制
func (c *ChannelLiveMgrCache) GetContestPkRestriction(anchorUid uint64) (*ContestPkRestriction, error) {
	key := getContestPkRestrictionKey(anchorUid)

	byteData, err := c.RedisClient.Get(key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存未命中
		}
		log.Errorf("GetContestPkRestriction Redis Get failed anchorUid:%d err:%v", anchorUid, err)
		return nil, err
	}

	restriction := &ContestPkRestriction{}
	err = json.Unmarshal(byteData, restriction)
	if err != nil {
		log.Errorf("GetContestPkRestriction Unmarshal failed anchorUid:%d err:%v", anchorUid, err)
		return nil, err
	}

	log.Debugf("GetContestPkRestriction success anchorUid:%d contestId:%s", anchorUid, restriction.ContestID)
	return restriction, nil
}

// DelContestPkRestriction 删除主播赛事PK限制
func (c *ChannelLiveMgrCache) DelContestPkRestriction(anchorUid uint64) error {
	key := getContestPkRestrictionKey(anchorUid)

	err := c.RedisClient.Del(key).Err()
	if err != nil {
		log.Errorf("DelContestPkRestriction failed anchorUid:%d err:%v", anchorUid, err)
		return err
	}

	log.Debugf("DelContestPkRestriction success anchorUid:%d", anchorUid)
	return nil
}

// AddContestPkTimer 添加赛事PK定时器任务
func (c *ChannelLiveMgrCache) AddContestPkTimer(timerType string, contestId string, executeTime uint64, index uint32) error {
	key := getContestPkTimerKey(timerType, index)

	err := c.RedisClient.ZAdd(key, redis.Z{
		Score:  float64(executeTime),
		Member: contestId,
	}).Err()

	if err != nil {
		log.Errorf("AddContestPkTimer failed timerType:%s contestId:%s executeTime:%d err:%v", timerType, contestId, executeTime, err)
		return err
	}

	// 设置过期时间
	c.RedisClient.Expire(key, ContestPkTimerTTL)

	log.Debugf("AddContestPkTimer success timerType:%s contestId:%s executeTime:%d", timerType, contestId, executeTime)
	return nil
}

// GetContestPkTimerList 获取需要执行的定时器任务列表
func (c *ChannelLiveMgrCache) GetContestPkTimerList(timerType string, beginTime, endTime uint64, index uint32) ([]string, error) {
	key := getContestPkTimerKey(timerType, index)

	results, err := c.RedisClient.ZRangeByScore(key, redis.ZRangeBy{
		Min:    fmt.Sprintf("%d", beginTime),
		Max:    fmt.Sprintf("%d", endTime),
		Offset: 0,
		Count:  100, // 限制每次处理的数量
	}).Result()

	if err != nil {
		log.Errorf("GetContestPkTimerList failed timerType:%s beginTime:%d endTime:%d err:%v", timerType, beginTime, endTime, err)
		return nil, err
	}

	log.Debugf("GetContestPkTimerList success timerType:%s count:%d", timerType, len(results))
	return results, nil
}

// RemoveContestPkTimer 移除已执行的定时器任务
func (c *ChannelLiveMgrCache) RemoveContestPkTimer(timerType string, contestId string, index uint32) error {
	key := getContestPkTimerKey(timerType, index)

	err := c.RedisClient.ZRem(key, contestId).Err()
	if err != nil {
		log.Errorf("RemoveContestPkTimer failed timerType:%s contestId:%s err:%v", timerType, contestId, err)
		return err
	}

	log.Debugf("RemoveContestPkTimer success timerType:%s contestId:%s", timerType, contestId)
	return nil
}

// BatchSetContestPkConfig 批量设置赛事PK配置缓存
func (c *ChannelLiveMgrCache) BatchSetContestPkConfig(configs []*pb.ContestPkConfig) error {
	if len(configs) == 0 {
		return nil
	}

	pipe := c.RedisClient.Pipeline()
	for _, config := range configs {
		key := getContestPkConfigKey(config.ContestId)
		byteData, err := proto.Marshal(config)
		if err != nil {
			log.Errorf("BatchSetContestPkConfig Marshal failed contestId:%s err:%v", config.ContestId, err)
			continue
		}
		pipe.Set(key, byteData, ContestPkConfigTTL)
	}

	_, err := pipe.Exec()
	if err != nil {
		log.Errorf("BatchSetContestPkConfig Pipeline Exec failed err:%v", err)
		return err
	}

	log.Debugf("BatchSetContestPkConfig success count:%d", len(configs))
	return nil
}

// GetContestPkLock 获取赛事PK分布式锁
func (c *ChannelLiveMgrCache) GetContestPkLock(lockKey string, expireSeconds int) bool {
	lockValue := fmt.Sprintf("%d", time.Now().UnixNano())
	result := c.RedisClient.SetNX(fmt.Sprintf("contest_pk_lock:%s", lockKey), lockValue, time.Duration(expireSeconds)*time.Second)
	return result.Val()
}

// ReleaseContestPkLock 释放赛事PK分布式锁
func (c *ChannelLiveMgrCache) ReleaseContestPkLock(lockKey string) error {
	return c.RedisClient.Del(fmt.Sprintf("contest_pk_lock:%s", lockKey)).Err()
}
