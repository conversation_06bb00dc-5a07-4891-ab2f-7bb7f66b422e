package server

import (
	"context"
	"database/sql"
	"time"

	"github.com/go-redis/redis"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"golang.52tt.com/services/channel-live-mgr/metrics"
)

// ==================== 管理接口 (运营后台使用) ====================

// AddAppointPkInfo 添加指定PK信息
func (s *ChannelLiveMgrServer) AddAppointPkInfo(ctx context.Context, in *pb.AddAppointPkInfoReq) (*pb.AddAppointPkInfoResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("AddAppointPkInfo", time.Since(start))
	}()

	out := &pb.AddAppointPkInfoResp{}

	_, pkTs := conf.GetAppointPkTsConf()
	if pkTs == 0 {
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("add", "config_error")
		return out, protocol.NewExactServerError(nil, -2, "指定pk每场pk时间为0")
	}

	nowTs := uint32(time.Now().Unix())
	log.DebugWithCtx(ctx, "AddAppointPkInfo begin in:%v pkTs:%d nowTs:%d", in, pkTs, nowTs)

	// 验证时间冲突
	err := s.checkAppointPkTs(ctx, in.GetInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAppointPkInfo checkAppointPkTs in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("add", "time_conflict")
		return out, err
	}

	// 数据库事务操作
	err = s.mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		appointId, sqlErr := s.mysqlStore.AddAppointPkInfo(ctx, tx, in.GetInfo().GetUid(),
			in.GetInfo().GetBeginTs(), in.GetInfo().GetEndTs(), nowTs, in.GetInfo().GetOperator())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "AddAppointPkInfo mysqlStore.AddAppointPkInfo failed in:%v err:%v", in, sqlErr)
			return sqlErr
		}

		for _, rivalInfo := range in.GetInfo().GetRivalList() {
			sqlErr = s.mysqlStore.AddAppointPkRivalInfo(ctx, tx, appointId, rivalInfo.GetUid(),
				rivalInfo.GetPkBeginTs(), nowTs)
			if sqlErr != nil {
				log.ErrorWithCtx(ctx, "AddAppointPkInfo mysqlStore.AddAppointPkRivalInfo failed in:%v err:%v", in, sqlErr)
				return sqlErr
			}
		}
		return nil
	})

	if err != nil {
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("add", "database_error")
		return out, err
	}

	metrics.GlobalAppointPkMetrics.RecordAppointPkSuccess("add")
	log.DebugWithCtx(ctx, "AddAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

// GetAppointPkInfoList 获取指定PK信息列表
func (s *ChannelLiveMgrServer) GetAppointPkInfoList(ctx context.Context, in *pb.GetAppointPkInfoListReq) (*pb.GetAppointPkInfoListResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("GetAppointPkInfoList", time.Since(start))
	}()

	out := &pb.GetAppointPkInfoListResp{}

	log.DebugWithCtx(ctx, "GetAppointPkInfoList begin in:%v", in)

	// 使用优化后的批量查询方法，避免N+1查询问题
	pkInfoListWithRivals, err := s.mysqlStore.GetAppointPkInfoListOptimized(ctx, in.GetUid(), in.GetBeginTs(), in.GetEndTs(),
		in.GetPage()*in.GetPageSize(), in.GetPageSize(), pb.GetAppointPkInfoListReq_QueryType(in.GetQueryType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfoList GetAppointPkInfoListOptimized failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordQueryError("GetAppointPkInfoList", "appoint_pk_info", "optimized_query_failed")
		return out, err
	}

	// 获取总数
	totalCnt, err := s.mysqlStore.GetAppointPkInfoTotalCnt(ctx, in.GetUid(), in.GetBeginTs(), in.GetEndTs(),
		pb.GetAppointPkInfoListReq_QueryType(in.GetQueryType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfoList GetAppointPkInfoTotalCnt failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordQueryError("GetAppointPkInfoList", "appoint_pk_info", "total_count_failed")
		return out, err
	}

	log.DebugWithCtx(ctx, "GetAppointPkInfoList pkInfoListWithRivals count:%d", len(pkInfoListWithRivals))

	// 直接使用优化后的数据结构，无需额外查询
	for _, pkInfoWithRivals := range pkInfoListWithRivals {
		rivalList := make([]*pb.PkRivalInfo, 0)
		for _, rival := range pkInfoWithRivals.Rivals {
			tmpInfo := &pb.PkRivalInfo{
				Uid:       rival.Uid,
				PkBeginTs: rival.BeginTs,
			}
			rivalList = append(rivalList, tmpInfo)
		}

		tmpInfo := &pb.AppointPkInfo{
			AppointId: pkInfoWithRivals.AppointId,
			Uid:       pkInfoWithRivals.Uid,
			BeginTs:   pkInfoWithRivals.BeginTs,
			EndTs:     pkInfoWithRivals.EndTs,
			RivalList: rivalList,
			UpdateTs:  pkInfoWithRivals.UpdateTs,
			Operator:  pkInfoWithRivals.Operator,
		}

		out.InfoList = append(out.InfoList, tmpInfo)
	}

	out.TotalCnt = totalCnt
	out.NextPage = in.GetPage() + 1
	if uint32(len(pkInfoListWithRivals)) < in.GetPageSize() {
		out.NextPage = 0
	}

	metrics.GlobalAppointPkMetrics.RecordQueryTotal("GetAppointPkInfoList", "appoint_pk_info", "success")
	log.DebugWithCtx(ctx, "GetAppointPkInfoList end in:%v out:%v", in, out)
	return out, nil
}

// ==================== 客户端接口 (主播端使用) ====================

// GetAppointPkInfo 获取指定PK推送信息
func (s *ChannelLiveMgrServer) GetAppointPkInfo(ctx context.Context, in *pb.GetAppointPkInfoReq) (*pb.GetAppointPkInfoResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("GetAppointPkInfo", time.Since(start))
	}()

	out := &pb.GetAppointPkInfoResp{}

	log.DebugWithCtx(ctx, "GetAppointPkInfo begin in:%v", in)

	pkPushEv, err := s.cacheClient.GetAnchorAppointPkPushEvent(in.GetUid())
	if err != nil && err != redis.Nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfo GetAnchorAppointPkPushEvent failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordCacheError("appoint_pk", "get_push_event", "get_failed")
		return out, err
	}

	out.PushEventInfo = pkPushEv

	if pkPushEv != nil {
		metrics.GlobalAppointPkMetrics.RecordCacheHit("appoint_pk", "get_push_event")
	} else {
		metrics.GlobalAppointPkMetrics.RecordCacheMiss("appoint_pk", "get_push_event")
	}

	log.DebugWithCtx(ctx, "GetAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

// AcceptAppointPk 接受指定PK邀约
func (s *ChannelLiveMgrServer) AcceptAppointPk(ctx context.Context, in *pb.AcceptAppointPkReq) (*pb.AcceptAppointPkResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("AcceptAppointPk", time.Since(start))
	}()

	out := &pb.AcceptAppointPkResp{}

	log.DebugWithCtx(ctx, "AcceptAppointPk begin in:%v", in)

	nowTs := uint32(time.Now().Unix())

	// 获取双方的指定PK信息
	myPkInfoList, err := s.cacheClient.GetAnchorAppointPkInfoList(in.GetMyUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordCacheError("appoint_pk", "accept", "get_my_pk_list_failed")
		return out, err
	}

	otherPkInfoList, err := s.cacheClient.GetAnchorAppointPkInfoList(in.GetOtherUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordCacheError("appoint_pk", "accept", "get_other_pk_list_failed")
		return out, err
	}

	// 查找共同的指定PK (这里简化处理，实际逻辑在appoint_pk_svr.go中)
	// TODO: 将完整的AcceptAppointPk逻辑迁移到这里

	metrics.GlobalAppointPkMetrics.RecordAppointPkSuccess("accept")
	log.InfoWithCtx(ctx, "AcceptAppointPk end in:%v out:%v", in, out)
	return out, nil
}

// ConfirmAppointPkPush 确认收到推送
func (s *ChannelLiveMgrServer) ConfirmAppointPkPush(ctx context.Context, in *pb.ConfirmAppointPkPushReq) (*pb.ConfirmAppointPkPushResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("ConfirmAppointPkPush", time.Since(start))
	}()

	out := &pb.ConfirmAppointPkPushResp{}

	log.DebugWithCtx(ctx, "ConfirmAppointPkPush begin in:%v", in)

	err := s.cacheClient.DelWaitingAppointPkPush(in.GetMyUid(), in.GetOtherUid(),
		in.GetMyUid()%WaitingAppointPkPushQueueCnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConfirmAppointPkPush DelWaitingAppointPkPush failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordCacheError("appoint_pk", "confirm_push", "del_waiting_failed")
		return out, err
	}

	metrics.GlobalAppointPkMetrics.RecordAppointPkSuccess("confirm_push")
	log.DebugWithCtx(ctx, "ConfirmAppointPkPush end in:%v out:%v", in, out)
	return out, nil
}
