package server

import (
	"context"
	"fmt"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

// ==================== 赛事PK配置管理接口 ====================

// CreateContestPk 创建赛事PK
func (s *ChannelLiveMgrServer) CreateContestPk(ctx context.Context, req *pb.CreateContestPkReq) (*pb.CreateContestPkResp, error) {
	log.InfoWithCtx(ctx, "CreateContestPk start contestId:%s", req.Config.ContestId)

	// 1. 参数校验
	if err := s.validateCreateContestPkRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk validateCreateContestPkRequest failed contestId:%s err:%v", req.Config.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	resp, err := s.contestPkManager.CreateContestPk(ctx, req.Config)
	if err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPk CreateContestPk failed contestId:%s err:%v", req.Config.ContestId, err)
		return nil, fmt.Errorf("创建赛事PK失败: %v", err)
	}

	log.InfoWithCtx(ctx, "CreateContestPk success contestId:%s", resp.ContestId)
	return resp, nil
}

// UpdateContestPk 更新赛事PK配置
func (s *ChannelLiveMgrServer) UpdateContestPk(ctx context.Context, req *pb.UpdateContestPkReq) (*pb.UpdateContestPkResp, error) {
	log.InfoWithCtx(ctx, "UpdateContestPk start contestId:%s", req.Config.ContestId)

	// 1. 参数校验
	if err := s.validateUpdateContestPkRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk validateUpdateContestPkRequest failed contestId:%s err:%v", req.Config.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	err := s.contestPkManager.UpdateContestPk(ctx, req.Config)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPk UpdateContestPk failed contestId:%s err:%v", req.Config.ContestId, err)
		return nil, fmt.Errorf("更新赛事PK失败: %v", err)
	}

	log.InfoWithCtx(ctx, "UpdateContestPk success contestId:%s", req.Config.ContestId)
	return &pb.UpdateContestPkResp{}, nil
}

// CancelContestPk 取消赛事PK
func (s *ChannelLiveMgrServer) CancelContestPk(ctx context.Context, req *pb.CancelContestPkReq) (*pb.CancelContestPkResp, error) {
	log.InfoWithCtx(ctx, "CancelContestPk start contestId:%s operator:%s", req.ContestId, req.Operator)

	// 1. 参数校验
	if err := s.validateCancelContestPkRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "CancelContestPk validateCancelContestPkRequest failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	err := s.contestPkManager.CancelContestPk(ctx, req.ContestId, req.Operator, req.Reason)
	if err != nil {
		log.ErrorWithCtx(ctx, "CancelContestPk CancelContestPk failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("取消赛事PK失败: %v", err)
	}

	log.InfoWithCtx(ctx, "CancelContestPk success contestId:%s", req.ContestId)
	return &pb.CancelContestPkResp{}, nil
}

// GetContestPkConfig 获取赛事PK配置
func (s *ChannelLiveMgrServer) GetContestPkConfig(ctx context.Context, req *pb.GetContestPkConfigReq) (*pb.GetContestPkConfigResp, error) {
	log.InfoWithCtx(ctx, "GetContestPkConfig start contestId:%s", req.ContestId)

	// 1. 参数校验
	if req.ContestId == "" {
		log.ErrorWithCtx(ctx, "GetContestPkConfig contestId is empty")
		return nil, fmt.Errorf("赛事ID不能为空")
	}

	// 2. 业务逻辑处理
	config, err := s.contestPkManager.GetContestPkConfig(ctx, req.ContestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkConfig GetContestPkConfig failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("获取赛事PK配置失败: %v", err)
	}

	log.InfoWithCtx(ctx, "GetContestPkConfig success contestId:%s", req.ContestId)
	return &pb.GetContestPkConfigResp{
		Config: config,
	}, nil
}

// BatchGetContestPkConfig 批量获取赛事PK配置
func (s *ChannelLiveMgrServer) BatchGetContestPkConfig(ctx context.Context, req *pb.BatchGetContestPkConfigReq) (*pb.BatchGetContestPkConfigResp, error) {
	log.InfoWithCtx(ctx, "BatchGetContestPkConfig start")

	// 1. 参数校验
	if err := s.validateBatchGetContestPkConfigRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkConfig validateBatchGetContestPkConfigRequest failed err:%v", err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	resp, err := s.contestPkManager.BatchGetContestPkConfig(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkConfig BatchGetContestPkConfig failed err:%v", err)
		return nil, fmt.Errorf("批量获取赛事PK配置失败: %v", err)
	}

	log.InfoWithCtx(ctx, "BatchGetContestPkConfig success count:%d", len(resp.ConfigList))
	return resp, nil
}

// ==================== 赛事PK查询接口 ====================

// GetContestPkInfo 获取赛事PK完整信息
func (s *ChannelLiveMgrServer) GetContestPkInfo(ctx context.Context, req *pb.GetContestPkInfoReq) (*pb.GetContestPkInfoResp, error) {
	log.InfoWithCtx(ctx, "GetContestPkInfo start contestId:%s includeResult:%t", req.ContestId, req.IncludeResult)

	// 1. 参数校验
	if req.ContestId == "" {
		log.ErrorWithCtx(ctx, "GetContestPkInfo contestId is empty")
		return nil, fmt.Errorf("赛事ID不能为空")
	}

	// 2. 业务逻辑处理
	info, err := s.contestPkManager.GetContestPkInfo(ctx, req.ContestId, req.IncludeResult)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkInfo GetContestPkInfo failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("获取赛事PK信息失败: %v", err)
	}

	log.InfoWithCtx(ctx, "GetContestPkInfo success contestId:%s", req.ContestId)
	return &pb.GetContestPkInfoResp{
		Info: info,
	}, nil
}

// BatchGetContestPkInfo 批量获取赛事PK完整信息
func (s *ChannelLiveMgrServer) BatchGetContestPkInfo(ctx context.Context, req *pb.BatchGetContestPkInfoReq) (*pb.BatchGetContestPkInfoResp, error) {
	log.InfoWithCtx(ctx, "BatchGetContestPkInfo start")

	// 1. 参数校验
	if err := s.validateBatchGetContestPkInfoRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkInfo validateBatchGetContestPkInfoRequest failed err:%v", err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	resp, err := s.contestPkManager.BatchGetContestPkInfo(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkInfo BatchGetContestPkInfo failed err:%v", err)
		return nil, fmt.Errorf("批量获取赛事PK信息失败: %v", err)
	}

	log.InfoWithCtx(ctx, "BatchGetContestPkInfo success count:%d", len(resp.InfoList))
	return resp, nil
}

// CheckAnchorPkAvailable 检查主播是否可以参与常规PK
func (s *ChannelLiveMgrServer) CheckAnchorPkAvailable(ctx context.Context, req *pb.CheckAnchorPkAvailableReq) (*pb.CheckAnchorPkAvailableResp, error) {
	log.InfoWithCtx(ctx, "CheckAnchorPkAvailable start anchorUid:%d", req.AnchorUid)

	// 1. 参数校验
	if req.AnchorUid == 0 {
		log.ErrorWithCtx(ctx, "CheckAnchorPkAvailable anchorUid is zero")
		return nil, fmt.Errorf("主播UID不能为空")
	}

	// 2. 设置默认检查时间
	checkTime := req.CheckTime
	if checkTime == 0 {
		checkTime = uint64(time.Now().Unix())
	}

	// 3. 业务逻辑处理
	resp, err := s.contestPkManager.CheckAnchorPkAvailable(ctx, req.AnchorUid, int64(checkTime))
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorPkAvailable CheckAnchorPkAvailable failed anchorUid:%d err:%v", req.AnchorUid, err)
		return nil, fmt.Errorf("检查主播PK可用性失败: %v", err)
	}

	log.InfoWithCtx(ctx, "CheckAnchorPkAvailable success anchorUid:%d available:%t", req.AnchorUid, resp.Available)
	return resp, nil
}

// ==================== 赛事PK结果管理接口 ====================

// StartContestPk 开始赛事PK
func (s *ChannelLiveMgrServer) StartContestPk(ctx context.Context, req *pb.StartContestPkReq) (*pb.StartContestPkResp, error) {
	log.InfoWithCtx(ctx, "StartContestPk start contestId:%s operator:%s", req.ContestId, req.Operator)

	// 1. 参数校验
	if err := s.validateStartContestPkRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "StartContestPk validateStartContestPkRequest failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	result, err := s.contestPkManager.StartContestPk(ctx, req.ContestId, req.Operator, req.ActualStartTime)
	if err != nil {
		log.ErrorWithCtx(ctx, "StartContestPk StartContestPk failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("开始赛事PK失败: %v", err)
	}

	log.InfoWithCtx(ctx, "StartContestPk success contestId:%s", req.ContestId)
	return &pb.StartContestPkResp{
		Result: result,
	}, nil
}

// UpdateContestPkResult 更新赛事PK结果
func (s *ChannelLiveMgrServer) UpdateContestPkResult(ctx context.Context, req *pb.UpdateContestPkResultReq) (*pb.UpdateContestPkResultResp, error) {
	log.InfoWithCtx(ctx, "UpdateContestPkResult start contestId:%s scoreA:%d scoreB:%d", req.ContestId, req.ScoreA, req.ScoreB)

	// 1. 参数校验
	if err := s.validateUpdateContestPkResultRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPkResult validateUpdateContestPkResultRequest failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	result, err := s.contestPkManager.UpdateContestPkResult(ctx, req.ContestId, req.ScoreA, req.ScoreB, req.Operator, req.UpdateReason)
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPkResult UpdateContestPkResult failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("更新赛事PK结果失败: %v", err)
	}

	log.InfoWithCtx(ctx, "UpdateContestPkResult success contestId:%s", req.ContestId)
	return &pb.UpdateContestPkResultResp{
		Result: result,
	}, nil
}

// FinishContestPk 结束赛事PK
func (s *ChannelLiveMgrServer) FinishContestPk(ctx context.Context, req *pb.FinishContestPkReq) (*pb.FinishContestPkResp, error) {
	log.InfoWithCtx(ctx, "FinishContestPk start contestId:%s winnerUid:%d", req.ContestId, req.WinnerUid)

	// 1. 参数校验
	if err := s.validateFinishContestPkRequest(req); err != nil {
		log.ErrorWithCtx(ctx, "FinishContestPk validateFinishContestPkRequest failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("参数校验失败: %v", err)
	}

	// 2. 业务逻辑处理
	result, err := s.contestPkManager.FinishContestPk(ctx, req.ContestId, req.WinnerUid, req.FinalScoreA, req.FinalScoreB, req.IsExtraTime, req.ActualEndTime, req.Operator, req.FinishReason)
	if err != nil {
		log.ErrorWithCtx(ctx, "FinishContestPk FinishContestPk failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("结束赛事PK失败: %v", err)
	}

	log.InfoWithCtx(ctx, "FinishContestPk success contestId:%s", req.ContestId)
	return &pb.FinishContestPkResp{
		Result: result,
	}, nil
}

// GetContestPkResult 获取赛事PK结果
func (s *ChannelLiveMgrServer) GetContestPkResult(ctx context.Context, req *pb.GetContestPkResultReq) (*pb.GetContestPkResultResp, error) {
	log.InfoWithCtx(ctx, "GetContestPkResult start contestId:%s", req.ContestId)

	// 1. 参数校验
	if req.ContestId == "" {
		log.ErrorWithCtx(ctx, "GetContestPkResult contestId is empty")
		return nil, fmt.Errorf("赛事ID不能为空")
	}

	// 2. 业务逻辑处理
	result, err := s.contestPkManager.GetContestPkResult(ctx, req.ContestId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkResult GetContestPkResult failed contestId:%s err:%v", req.ContestId, err)
		return nil, fmt.Errorf("获取赛事PK结果失败: %v", err)
	}

	log.InfoWithCtx(ctx, "GetContestPkResult success contestId:%s", req.ContestId)
	return &pb.GetContestPkResultResp{
		Result: result,
	}, nil
}
