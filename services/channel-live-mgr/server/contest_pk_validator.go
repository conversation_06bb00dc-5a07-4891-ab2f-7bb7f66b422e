package server

import (
	"fmt"
	"time"

	pb "golang.52tt.com/protocol/services/channellivemgr"
)

// ==================== 赛事PK请求校验方法 ====================

// validateCreateContestPkRequest 校验创建赛事PK请求
func (s *ChannelLiveMgrServer) validateCreateContestPkRequest(req *pb.CreateContestPkReq) error {
	if req.Config == nil {
		return fmt.Errorf("赛事配置不能为空")
	}

	config := req.Config

	// 基本参数校验
	if config.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if config.ContestName == "" {
		return fmt.Errorf("赛事名称不能为空")
	}
	if config.ActivityService == "" {
		return fmt.Errorf("活动服务标识不能为空")
	}
	if config.AnchorUidA == 0 || config.AnchorUidB == 0 {
		return fmt.Errorf("主播UID不能为空")
	}
	if config.AnchorUidA == config.AnchorUidB {
		return fmt.Errorf("主播A和主播B不能是同一人")
	}
	if config.Operator == "" {
		return fmt.Errorf("操作人不能为空")
	}

	// 时间参数校验
	now := time.Now().Unix()
	if config.ContestBeginTime <= uint64(now) {
		return fmt.Errorf("赛事开始时间不能早于当前时间")
	}
	if config.ContestEndTime <= config.ContestBeginTime {
		return fmt.Errorf("赛事结束时间必须晚于开始时间")
	}
	if config.PkBeginTime < config.ContestBeginTime || config.PkBeginTime >= config.ContestEndTime {
		return fmt.Errorf("PK开始时间必须在赛事时间段内")
	}
	if config.PkEndTime <= config.PkBeginTime || config.PkEndTime > config.ContestEndTime {
		return fmt.Errorf("PK结束时间必须在赛事时间段内且晚于PK开始时间")
	}

	// 检查PK时长合理性
	pkDuration := config.PkEndTime - config.PkBeginTime
	if pkDuration < 600 { // 最少10分钟
		return fmt.Errorf("PK时长不能少于10分钟")
	}
	if pkDuration > 14400 { // 最多4小时
		return fmt.Errorf("PK时长不能超过4小时")
	}

	// 检查提前量
	advanceTime := config.ContestBeginTime - uint64(now)
	if advanceTime < 1800 { // 至少提前30分钟
		return fmt.Errorf("赛事开始时间至少要提前30分钟")
	}

	return nil
}

// validateUpdateContestPkRequest 校验更新赛事PK请求
func (s *ChannelLiveMgrServer) validateUpdateContestPkRequest(req *pb.UpdateContestPkReq) error {
	if req.Config == nil {
		return fmt.Errorf("赛事配置不能为空")
	}

	// 使用与创建相同的校验逻辑
	createReq := &pb.CreateContestPkReq{Config: req.Config}
	return s.validateCreateContestPkRequest(createReq)
}

// validateCancelContestPkRequest 校验取消赛事PK请求
func (s *ChannelLiveMgrServer) validateCancelContestPkRequest(req *pb.CancelContestPkReq) error {
	if req.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if req.Operator == "" {
		return fmt.Errorf("操作人不能为空")
	}
	if req.Reason == "" {
		return fmt.Errorf("取消原因不能为空")
	}

	return nil
}

// validateBatchGetContestPkConfigRequest 校验批量获取赛事PK配置请求
func (s *ChannelLiveMgrServer) validateBatchGetContestPkConfigRequest(req *pb.BatchGetContestPkConfigReq) error {
	// 检查分页参数
	if req.Page < 0 {
		return fmt.Errorf("页码不能为负数")
	}
	if req.PageSize < 0 || req.PageSize > 100 {
		return fmt.Errorf("页大小必须在1-100之间")
	}

	// 设置默认分页参数
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	// 检查时间范围
	if req.TimeBegin > 0 && req.TimeEnd > 0 && req.TimeBegin >= req.TimeEnd {
		return fmt.Errorf("开始时间必须早于结束时间")
	}

	// 检查赛事ID列表长度
	if len(req.ContestIdList) > 50 {
		return fmt.Errorf("单次查询的赛事ID数量不能超过50个")
	}

	return nil
}

// validateBatchGetContestPkInfoRequest 校验批量获取赛事PK信息请求
func (s *ChannelLiveMgrServer) validateBatchGetContestPkInfoRequest(req *pb.BatchGetContestPkInfoReq) error {
	// 检查分页参数
	if req.Page < 0 {
		return fmt.Errorf("页码不能为负数")
	}
	if req.PageSize < 0 || req.PageSize > 100 {
		return fmt.Errorf("页大小必须在1-100之间")
	}

	// 设置默认分页参数
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	// 检查时间范围
	if req.TimeBegin > 0 && req.TimeEnd > 0 && req.TimeBegin >= req.TimeEnd {
		return fmt.Errorf("开始时间必须早于结束时间")
	}

	// 检查赛事ID列表长度
	if len(req.ContestIdList) > 50 {
		return fmt.Errorf("单次查询的赛事ID数量不能超过50个")
	}

	return nil
}

// validateStartContestPkRequest 校验开始赛事PK请求
func (s *ChannelLiveMgrServer) validateStartContestPkRequest(req *pb.StartContestPkReq) error {
	if req.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if req.Operator == "" {
		return fmt.Errorf("操作人不能为空")
	}

	// 检查开始时间合理性
	if req.ActualStartTime > 0 {
		now := uint64(time.Now().Unix())
		// 开始时间不能太早（超过1小时前）或太晚（超过1小时后）
		if req.ActualStartTime < now-3600 || req.ActualStartTime > now+3600 {
			return fmt.Errorf("实际开始时间不合理")
		}
	}

	return nil
}

// validateUpdateContestPkResultRequest 校验更新赛事PK结果请求
func (s *ChannelLiveMgrServer) validateUpdateContestPkResultRequest(req *pb.UpdateContestPkResultReq) error {
	if req.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if req.Operator == "" {
		return fmt.Errorf("操作人不能为空")
	}

	// 检查分数合理性
	if req.ScoreA < 0 || req.ScoreB < 0 {
		return fmt.Errorf("分数不能为负数")
	}

	// 检查分数上限（防止异常数据）
	maxScore := uint32(1000000) // 100万分上限
	if req.ScoreA > maxScore || req.ScoreB > maxScore {
		return fmt.Errorf("分数不能超过%d", maxScore)
	}

	return nil
}

// validateFinishContestPkRequest 校验结束赛事PK请求
func (s *ChannelLiveMgrServer) validateFinishContestPkRequest(req *pb.FinishContestPkReq) error {
	if req.ContestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}
	if req.Operator == "" {
		return fmt.Errorf("操作人不能为空")
	}
	if req.FinishReason == "" {
		return fmt.Errorf("结束原因不能为空")
	}

	// 检查分数合理性
	if req.FinalScoreA < 0 || req.FinalScoreB < 0 {
		return fmt.Errorf("最终分数不能为负数")
	}

	// 检查获胜者与分数的一致性
	if req.WinnerUid != 0 {
		// 有获胜者的情况下，检查分数逻辑
		if req.FinalScoreA == req.FinalScoreB {
			return fmt.Errorf("有获胜者时分数不能相等")
		}
	} else {
		// 平局的情况下，分数应该相等
		if req.FinalScoreA != req.FinalScoreB {
			return fmt.Errorf("平局时两个主播分数应该相等")
		}
	}

	// 检查结束时间合理性
	if req.ActualEndTime > 0 {
		now := uint64(time.Now().Unix())
		// 结束时间不能太早（超过1天前）或太晚（超过1小时后）
		if req.ActualEndTime < now-86400 || req.ActualEndTime > now+3600 {
			return fmt.Errorf("实际结束时间不合理")
		}
	}

	return nil
}

// validateContestIdFormat 校验赛事ID格式
func (s *ChannelLiveMgrServer) validateContestIdFormat(contestId string) error {
	if contestId == "" {
		return fmt.Errorf("赛事ID不能为空")
	}

	// 检查长度
	if len(contestId) < 3 || len(contestId) > 64 {
		return fmt.Errorf("赛事ID长度必须在3-64个字符之间")
	}

	// 检查字符（只允许字母、数字、下划线、连字符）
	for _, char := range contestId {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return fmt.Errorf("赛事ID只能包含字母、数字、下划线和连字符")
		}
	}

	return nil
}

// validateOperatorFormat 校验操作人格式
func (s *ChannelLiveMgrServer) validateOperatorFormat(operator string) error {
	if operator == "" {
		return fmt.Errorf("操作人不能为空")
	}

	// 检查长度
	if len(operator) > 64 {
		return fmt.Errorf("操作人长度不能超过64个字符")
	}

	return nil
}

// validateTimeRange 校验时间范围
func (s *ChannelLiveMgrServer) validateTimeRange(beginTime, endTime uint64) error {
	if beginTime > 0 && endTime > 0 {
		if beginTime >= endTime {
			return fmt.Errorf("开始时间必须早于结束时间")
		}

		// 检查时间范围不能太大（最多查询1年）
		maxRange := uint64(365 * 24 * 3600) // 1年
		if endTime-beginTime > maxRange {
			return fmt.Errorf("查询时间范围不能超过1年")
		}
	}

	return nil
}
