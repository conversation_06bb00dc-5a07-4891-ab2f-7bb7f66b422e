package server

import (
	"context"
	"errors"
	"fmt"
	channellivestats "golang.52tt.com/clients/channel-live-stats"
	"golang.52tt.com/clients/greenbaba"
	channel_base_api "golang.52tt.com/protocol/services/channel-base-api"
	"sort"
	"strings"
	"sync"

	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	"github.com/opentracing/opentracing-go"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	activeFans "golang.52tt.com/clients/active-fans"
	"golang.52tt.com/clients/anchorcontract"
	anchorcontract_go "golang.52tt.com/clients/anchorcontract-go"
	apicenter "golang.52tt.com/clients/apicenter/apiserver"
	"golang.52tt.com/clients/channel"
	channel_follow "golang.52tt.com/clients/channel-follow"
	channellivefans "golang.52tt.com/clients/channel-live-fans"
	"golang.52tt.com/clients/channelmic"
	"golang.52tt.com/clients/channelrobot"
	"golang.52tt.com/clients/friendol"
	"golang.52tt.com/clients/nobility"
	"golang.52tt.com/clients/public"
	PushNotification "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	channelpb "golang.52tt.com/protocol/app/channel"
	gaPush "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/common/status"
	anchorcontract_go2 "golang.52tt.com/protocol/services/anchorcontract-go"
	pushPb "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/channel-live-mgr/kafka_prod"
	"golang.52tt.com/services/channel-live-mgr/match"
	"golang.52tt.com/services/channel-live-mgr/pk"

	pbLogic "golang.52tt.com/protocol/app/channel-live-logic"

	"time"

	"golang.52tt.com/clients/account"
	active_fans "golang.52tt.com/clients/active-fans"
	channel_live_pk "golang.52tt.com/clients/channel-live-pk"
	"golang.52tt.com/clients/entertainmentrecommendback"
	"golang.52tt.com/clients/guild"
	friendShipCli "golang.52tt.com/clients/ugc/friendship"
	apiPB "golang.52tt.com/protocol/services/apicenter/apiserver"
	channellivepkPB "golang.52tt.com/protocol/services/channel-live-pk"
	channel_scheme_middle "golang.52tt.com/protocol/services/channel-scheme-middle"
	liveFansPb "golang.52tt.com/protocol/services/channellivefans"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	channelMicPb "golang.52tt.com/protocol/services/channelmicsvr"
	channelsvrPb "golang.52tt.com/protocol/services/channelsvr"
	entertainmentPB "golang.52tt.com/protocol/services/entertainmentRecommendBackSvr"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"golang.52tt.com/services/channel-live-mgr/event"
	"golang.52tt.com/services/channel-live-mgr/manager"
	"golang.52tt.com/services/channel-live-mgr/mysql"
	"golang.52tt.com/services/notify"
)

var easyCache = cache.NewEasyCache()     //通用回包缓存
var easyCacheFlag = cache.NewEasyCache() //没权限标志缓存

var HeartBeats = sync.Map{}

const (
	WatchAudieCountLimit = 100 // 观看排行榜获取观众数量
)

var MapTagId2Name = map[uint32]string{
	0:    "",
	2003: "大神带飞",
	2010: "相亲交友",
	2002: "点唱厅",
	2001: "扩列",
	2012: "Pia戏",
	2005: "踢保",
	2007: "小故事",
	2016: "一起玩",
	2015: "大神带飞",
	2014: "CP战",
	2013: "派对",
	3001: "音乐",
	3002: "情感",
	3003: "二次元",
	3004: "故事",
	3005: "脱口秀",
}

type ChannelLiveMgrServer struct {
	sc                  *conf.ServiceConfigT
	cacheClient         *cache.ChannelLiveMgrCache
	redisClient         *redis.Client
	mysqlStore          *mysql.Store
	mgr                 *manager.Manager
	stop                chan bool
	accountCli          *account.Client
	channelClient       *channel.Client
	channelmicClient    *channelmic.Client
	apiClient           *apicenter.Client
	pushClient          *PushNotification.Client
	liveFansClient      *channellivefans.Client
	ctx                 context.Context
	channelKfkSub       *event.KafkaChannelSubscriber
	presentKfkSub       *event.KafkaPresentSubscriber
	micKfkSub           *event.KafkaMicEventSubscriber
	enterCli            *entertainmentrecommendback.Client
	channelFollowClient *channel_follow.Client
	activeCli           *activeFans.Client
	friendShipCli       *friendShipCli.Client
	apiCli              *apicenter.Client
	channelBaseApiCli   channel_base_api.ChannelBaseApiClient
	robotCli            *channelrobot.Client
	nobilityClient      *nobility.Client
	activeFanClient     *active_fans.Client
	friendOLClient      *friendol.Client
	channelLiveProd     *kafka_prod.KafkaProduce
	pkApplyProd         *kafka_prod.KafkaProduce
	anchorContractGoCli *anchorcontract_go.Client
	publicCli           *public.Client
	//channelLiveMissionCli *channellivemission.Client
	knightKfkSub           *event.KnightGroupSub
	ykwKfkSub              *event.UkwSub
	guildCli               *guild.Client
	extGameKfkSub          *event.RevenueExtGameSub
	channeLivePkCli        *channel_live_pk.Client
	channelSchemeMiddleCli *channel_scheme_middle.Client
	anchorContractClient   *anchorcontract.Client
	greenCli               greenbaba.IClient
	chLiveStats            channellivestats.IClient
	contestPkManager       *manager.ContestPkManager
}

func GetHeartBeat(cid uint32) int64 {
	tmp, ok := HeartBeats.Load(cid)
	if !ok {
		return 0
	}
	return tmp.(int64)
}

func SetHeartBeat(cid uint32, ts int64) {
	HeartBeats.Store(cid, ts)
}

func NewChannelLiveMgrServer(ctx context.Context, cfg config.Configer) (*ChannelLiveMgrServer, error) {

	sc := &conf.ServiceConfigT{}

	cfgPath := ctx.Value("configfile").(string)
	if cfgPath == "" {
		return nil, errors.New("configfile  not exist")
	}
	err := sc.Parse(cfgPath)
	if err != nil {
		return nil, err
	}

	log.ErrorWithCtx(ctx, "NewChannelLiveMgrServer sc:%v", sc)

	redisClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfig().Protocol,
		Addr:               sc.GetRedisConfig().Addr(),
		PoolSize:           sc.GetRedisConfig().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfig().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfig().DB,
	})

	/*
		redisClientWeb := redis.NewClient(&redis.Options{
			Network:            sc.GetRedisConfig2().Protocol,
			Addr:               sc.GetRedisConfig2().Addr(),
			PoolSize:           sc.GetRedisConfig2().PoolSize,
			IdleCheckFrequency: sc.GetRedisConfig2().IdleCheckFrequency(),
			DB:                 sc.GetRedisConfig2().DB,
		})
	*/

	redisGClient := redis.NewClient(&redis.Options{
		Network:            sc.GetRedisConfigQueueMic().Protocol,
		Addr:               sc.GetRedisConfigQueueMic().Addr(),
		PoolSize:           sc.GetRedisConfigQueueMic().PoolSize,
		IdleCheckFrequency: sc.GetRedisConfigQueueMic().IdleCheckFrequency(),
		DB:                 sc.GetRedisConfigQueueMic().DB,
	})

	redisFlagClient := redis.NewClient(&redis.Options{
		Network:            sc.GetFlagRedis().Protocol,
		Addr:               sc.GetFlagRedis().Addr(),
		PoolSize:           sc.GetFlagRedis().PoolSize,
		IdleCheckFrequency: sc.GetFlagRedis().IdleCheckFrequency(),
		DB:                 sc.GetFlagRedis().DB,
	})

	log.DebugWithCtx(ctx, "Initialized redis connection pool to %s://%s/%d", sc.GetRedisConfig().Protocol, sc.GetRedisConfig().Addr(), sc.GetRedisConfig().DB)
	redisTracer := tracing.Init("channel-live-mgr_redis")
	cacheClient := cache.NewChannelLiveMgrCache(redisClient, nil, redisFlagClient, redisTracer)

	mysqlDb, err := sqlx.Connect("mysql", sc.GetMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}

	readonlyMysqlDb, err := sqlx.Connect("mysql", sc.GetReadonlyMysqlConnectionString())
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to create mysql %v", err)
		return nil, err
	}

	mysqlStore := mysql.NewMysql(mysqlDb, readonlyMysqlDb)
	mysqlStore.CreateTables()

	pushClient, err := PushNotification.NewClient()
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to new PushNotificationClient %+v", err)
		return nil, err
	}

	accountCli, _ := account.NewClient()

	liveFansClient, _ := channellivefans.NewClient()
	channelClient := channel.NewClient()
	channelmicClient := channelmic.NewClient()
	friendShipCli, _ := friendShipCli.NewClient()
	apiCli := apicenter.NewClient()

	enterCli := entertainmentrecommendback.NewClient()
	activeCli, _ := activeFans.NewClient()
	robotCli := channelrobot.NewClient()
	nobilityClient, _ := nobility.NewClient()
	activeFanClient, _ := active_fans.NewTracedClient(opentracing.GlobalTracer())
	friendOLClient := friendol.NewTracedClient(opentracing.GlobalTracer())
	channelFollowClient, _ := channel_follow.NewTracedClient(opentracing.GlobalTracer())
	anchorContractGoCli, _ := anchorcontract_go.NewClient()
	publicCli := public.NewClient()
	guildCli := guild.NewClient()
	channeLivePkCli, _ := channel_live_pk.NewClient()
	channelSchemeMiddleCli, _ := channel_scheme_middle.NewClient(ctx)
	anchorContractClient := anchorcontract.NewClient()
	greenCli_ := greenbaba.NewIClient()
	chLiveStats_ := channellivestats.NewIClient()

	channelLiveProd, err := kafka_prod.NewKafkaProduce(sc.GetChannelLiveKafkaConfig().BrokerList(), sc.GetChannelLiveKafkaConfig().ClientID, sc.GetChannelLiveKafkaConfig().Topics)
	if err != nil {
		log.Errorf("kafka_prod.NewKafkaProduce failed err:%v", err)
		return nil, err
	}

	pkApplyProd, err := kafka_prod.NewKafkaProduce(sc.GetPKApplyKafkaConfig().BrokerList(), sc.GetPKApplyKafkaConfig().ClientID, sc.GetPKApplyKafkaConfig().Topics)
	if err != nil {
		log.Errorf("kafka_prod.NewKafkaProduce failed err:%v", err)
		return nil, err
	}

	mgr := manager.NewManager(cacheClient, mysqlStore, redisGClient, pushClient, accountCli, sc, channelLiveProd, ctx)

	// 初始化赛事PK管理器
	contestPkManager := manager.NewContestPkManager(cacheClient, mysqlStore, channelLiveProd)

	presentKfkSub, err := event.NewPresentKafkaSubscriber(sc.GetPresentKafkaConfig(), mgr, sc.GetJoinGroupGiftID())
	if err != nil {
		log.Errorf("event.NewPresentKafkaSubscriber failed err:%v", err)
		return nil, err
	}

	/*
		err = presentKfkSub.Start()
		if err != nil {
			log.Errorf("presentKfkSub.Start() failed err:%v", err)
			return nil, err
		}
	*/

	channelKfkSub, err := event.NewChannelKafkaSubscriber(sc.GetChannelKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewChannelKafkaSubscriber failed err:%v", err)
		return nil, err
	}

	/*
		err = channelKfkSub.Start()
		if err != nil {
			log.Errorf("channelKfkSub.Start() failed err:%v", err)
			return nil, err
		}
	*/

	channelMicKfkSub, err := event.NewMicKafkaSubscriber(sc.GetChannelMicKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewMicKafkaSubscriber failed err:%v", err)
		return nil, err
	}

	/*
		err = channelMicKfkSub.Start()
		if err != nil {
			log.Errorf("channelMicKfkSub.Start() failed err:%v", err)
			return nil, err
		}
	*/

	log.DebugWithCtx(ctx, "Initialized kafka %v", sc.GetChannelLiveKafkaConfig())

	knightKfkSub, err := event.NewKnightGroupSub(sc.GetKnightKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewKnightGroupSub failed err:%v", err)
		return nil, err
	}

	/*
		err = knightKfkSub.Start()
		if err != nil {
			log.Errorf("knightKfkSub.Start() failed err:%v", err)
			return nil, err
		}
	*/

	ykwKfkSub, err := event.NewUkwSub(sc.GetYkwKafkaConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewUkwSub failed err:%v", err)
		return nil, err
	}

	/*
		err = ykwKfkSub.Start()
		if err != nil {
			log.Errorf("ykwKfkSub.Start() failed err:%v", err)
			return nil, err
		}
	*/

	extGameSub, err := event.NewRevenueExtGameSub(sc.GetExtGameKfkConfig(), mgr)
	if err != nil {
		log.Errorf("event.NewRevenueExtGameSub failed err:%v", err)
		return nil, err
	}

	/*
		err = extGameSub.Start()
		if err != nil {
			log.Errorf("extGameSub.Start() failed err:%v", err)
			return nil, err
		}
	*/

	channelBaseApiClient, err := channel_base_api.NewClient(context.Background())
	if err != nil {
		log.Errorf("channel_base_api.NewClient() failed err:%v", err)
		return nil, err
	}

	server := &ChannelLiveMgrServer{
		sc:                     sc,
		mysqlStore:             mysqlStore,
		cacheClient:            cacheClient,
		redisClient:            redisGClient,
		mgr:                    mgr,
		stop:                   make(chan bool),
		apiClient:              apicenter.NewClient(),
		ctx:                    ctx,
		pushClient:             pushClient,
		presentKfkSub:          presentKfkSub,
		channelKfkSub:          channelKfkSub,
		liveFansClient:         liveFansClient,
		channelClient:          channelClient,
		channelmicClient:       channelmicClient,
		channelFollowClient:    channelFollowClient,
		enterCli:               enterCli,
		activeCli:              activeCli,
		friendShipCli:          friendShipCli,
		apiCli:                 apiCli,
		robotCli:               robotCli,
		accountCli:             accountCli,
		nobilityClient:         nobilityClient,
		friendOLClient:         friendOLClient,
		activeFanClient:        activeFanClient,
		channelLiveProd:        channelLiveProd,
		pkApplyProd:            pkApplyProd,
		anchorContractGoCli:    anchorContractGoCli,
		publicCli:              publicCli,
		knightKfkSub:           knightKfkSub,
		ykwKfkSub:              ykwKfkSub,
		guildCli:               guildCli,
		extGameKfkSub:          extGameSub,
		channeLivePkCli:        channeLivePkCli,
		channelSchemeMiddleCli: channelSchemeMiddleCli,
		anchorContractClient:   anchorContractClient,
		micKfkSub:              channelMicKfkSub,
		greenCli:               greenCli_,
		chLiveStats:            chLiveStats_,
		channelBaseApiCli:      channelBaseApiClient,
		contestPkManager:       contestPkManager,
	}

	//匹配
	matchFunc := func(ctx context.Context, uida, uidb, channelida, chanenlidb, scoreA, scoreB uint32, source int) error {
		_, err := server.StartPk(ctx, uida, uidb, channelida, chanenlidb, scoreA, scoreB, source)
		return err
	} //

	go LoopFunc(server.mgr.HandlerPkProcess, time.Second)
	go LoopFunc(func() { match.MatchTick(server.cacheClient, matchFunc) }, time.Second)
	go LoopFunc(server.mgr.GenMonthAnchorScoreProcess, time.Second)

	server.StartAppointPkTimer()

	return server, nil
}

func (s *ChannelLiveMgrServer) GetHeartBeatTimeOut(ctx context.Context, in *pb.GetHeartBeatTimeOutReq) (*pb.GetHeartBeatTimeOutResp, error) {
	out := &pb.GetHeartBeatTimeOutResp{
		UidList: make([]uint32, 0),
	}

	bLock, _ := s.cacheClient.GetLock("channel_live_HandlerLiveHeartBeatTimeOut")

	if !bLock {
		return out, nil
	}

	uids := s.cacheClient.GetTimeOutHeartBeatList(in.Expire, in.Del)

	s.cacheClient.UnLock("channel_live_HandlerLiveHeartBeatTimeOut")

	out.UidList = uids

	return out, nil
}

func (s *ChannelLiveMgrServer) GenChannelLiveChannelID(ctx context.Context, uid uint32) (uint32, error) {
	var channelID uint32 = 0

	channelLiveinfo := &pb.ChannelLiveInfo{}
	err := s.mysqlStore.GetChannelLiveInfo(ctx, channelLiveinfo, uid)
	if err == nil && channelLiveinfo.ChannelId != 0 {
		channelID = channelLiveinfo.ChannelId
		return channelID, nil
	}

	resp, err := s.channelClient.GetUserChannelRoleList(ctx, uid, uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "GenChannelLiveChannelID GetUserChannelRoleList err:%v", err)
		return 0, err
	}

	for _, roleInfo := range resp.RoleList {
		if roleInfo.ChannelInfo == nil {
			continue
		}
		if (*roleInfo.ChannelInfo.ChannelBindType) == 7 && (*roleInfo.ChannelInfo.CreaterUid) == uid {
			channelID = *roleInfo.ChannelInfo.ChannelBaseinfo.ChannelId
			break
		}
	}

	if channelID == 0 {
		cresp, err := s.channelClient.CreateChannel(ctx, uid, uid, uint32(channelpb.ChannelType_RADIO_LIVE_CHANNEL_TYPE),
			0, "听听房间", "")
		if err != nil {
			log.ErrorWithCtx(ctx, "GenChannelLiveChannelID CreateChannel err:%v", err)
			return channelID, err
		}
		channelID = *cresp.CreateResp.ChannelId

		_, setSchemeErr := s.channelSchemeMiddleCli.SetCurChannelScheme(ctx, &channel_scheme_middle.SetCurChannelSchemeReq{
			OpUid:                uid,
			Cid:                  channelID,
			SchemeId:             10010,
			Source:               channel_scheme_middle.Source_SET_RADIO_LIVE_CREATE,
			AppId:                0,
			MarketId:             0,
			NotSetMicAndMinigame: true,
		})
		if setSchemeErr != nil {
			log.ErrorWithCtx(ctx, "GenChannelLiveChannelID SetCurChannelScheme uid:%d cid:%d err:%v",
				uid, channelID, setSchemeErr)
			return 0, setSchemeErr
		}
	}

	return channelID, nil
}

// 增加操作记录
func (s *ChannelLiveMgrServer) addAnchorOperRecord(anchorUid, cid, tagId, operType uint32, operUser string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	nowTs := uint32(time.Now().Unix())

	userInfo, err := s.accountCli.GetUser(ctx, anchorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "addAnchorOperRecord GetUser failed anchorUid:%d err:%+v", anchorUid, err)
	}

	contractInfo, err := s.anchorContractGoCli.GetUserContractCacheInfo(ctx, anchorUid, anchorUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "addAnchorOperRecord GetUserContractCacheInfo failed anchorUid:%d err:%+v", anchorUid, err)
	}

	var guildId uint32
	if contractInfo.GetContract().GetGuildId() != 0 && contractInfo.GetContract().GetExpireTime() >= nowTs {
		guildId = contractInfo.GetContract().GetGuildId()
	}

	guildInfo, err := s.guildCli.GetGuild(ctx, guildId)
	if err != nil {
		log.ErrorWithCtx(ctx, "addAnchorOperRecord GetGuild failed anchorUid:%d err:%+v", anchorUid, err)
	}

	if tagId == 0 {
		tagInfo, err := s.enterCli.GetChannelTag(ctx, anchorUid, &entertainmentPB.GetChannelTagReq{
			ChannelId: &cid,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "addAnchorOperRecord GetChannelTag failed anchorUid:%d err:%+v", anchorUid, err)
		} else {
			tagId = tagInfo.GetTagInfo().GetTagId()
		}
	}

	Err := s.mysqlStore.AddAnchorOperLog(ctx, mysql.AnchorOperLog{
		Uid:       anchorUid,
		Tid:       userInfo.GetAlias(),
		NickName:  userInfo.GetNickname(),
		GuildId:   guildId,
		GuildName: guildInfo.GetName(),
		TagId:     tagId,
		OperType:  operType,
		OperUser:  operUser,
	})
	if Err != nil {
		log.ErrorWithCtx(ctx, "addAnchorOperRecord AddAnchorOperLog failed anchorUid:%d err:%+v", anchorUid, Err)
	}

	log.DebugWithCtx(ctx, "addAnchorOperRecord end anchorUid:%d operType:%d", anchorUid, operType)
	return nil
}

// 开启主播权限
func (s *ChannelLiveMgrServer) SetChannelLiveInfo(ctx context.Context, in *pb.SetChannelLiveInfoReq) (out *pb.SetChannelLiveInfoResp, err error) {
	out = &pb.SetChannelLiveInfoResp{}

	//暂时没有时间段需求，先给个大的时间
	in.ChannelLiveInfo.BeginTime = uint32(time.Now().Unix())
	in.ChannelLiveInfo.EndTime = 1993315950

	channelID := in.ChannelLiveInfo.ChannelId
	if channelID == 0 {
		channelID, err = s.GenChannelLiveChannelID(ctx, in.ChannelLiveInfo.Uid)
		if err != nil || channelID == 0 {
			log.ErrorWithCtx(ctx, "SetChannelLiveInfo GenChannelLiveChannelID failed in:%+v id:%d err:%+v", in, channelID, err)
			return out, err
		}
	}

	//清空密码
	var pwdFlag, recommen uint32 = 0, 0x2 //开启推荐位
	pwd := ""
	_, err = s.channelClient.ModifyChannel(ctx, in.ChannelLiveInfo.Uid, &channelsvrPb.ModifyChannelReq{
		ChannelId:        &channelID,
		OpUid:            &in.ChannelLiveInfo.Uid,
		PwdFlag:          &pwdFlag,
		Passwd:           &pwd,
		SwitchFlagBitmap: &recommen,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo ModifyChannel failed in:%v err:%v", in, err)
	}

	_, err = s.channelmicClient.SetChannelMicMode(ctx, in.ChannelLiveInfo.Uid, &channelMicPb.SetChannelMicModeReq{
		Uid:       in.ChannelLiveInfo.Uid,
		ChannelId: channelID,
		MicMode:   uint32(channelpb.EChannelMicMode_LIVE_MIC_SPACE_MODE),
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo SetChannelMicMode in:%v err:%v", in, err)
	}

	in.ChannelLiveInfo.ChannelId = channelID
	err = s.mysqlStore.SetChannelLiveInfo(ctx, in.GetChannelLiveInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo DB  err:%v in:%v", err, in)
		return out, err
	}

	//清缓存
	err = s.cacheClient.DelChannelLiveInfo(in.ChannelLiveInfo.Uid, in.ChannelLiveInfo.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo DelChannelLiveInfo failed in:%v err:%v", in, err)
	}

	// 操作记录，直接起协程
	go s.addAnchorOperRecord(in.GetChannelLiveInfo().GetUid(), in.GetChannelLiveInfo().GetChannelId(),
		in.GetChannelLiveInfo().GetTagId(), uint32(pb.OperateType_AddAnchor), in.GetChannelLiveInfo().GetOperName())

	log.DebugWithCtx(ctx, "SetChannelLiveInfo cache err(%s) uid(%d) channelLiveInfo(%+v)", err, in.ChannelLiveInfo.Uid, in.GetChannelLiveInfo())

	//清掉没有直播权限标志
	s.cacheClient.SetNoAuthFlag(0, AuthFlagKey(pb.EnumIDType_USER_ID, in.ChannelLiveInfo.Uid), AuthFlagKey(pb.EnumIDType_CHANNEL_ID, in.ChannelLiveInfo.ChannelId))

	//关闭排麦开关
	simple, err := s.channelClient.GetChannelSimpleInfo(ctx, in.ChannelLiveInfo.Uid, channelID)
	if nil != err {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo GetChannelSimpleInfo uid:%v cid:%v err:%v", in.ChannelLiveInfo.Uid, channelID, err)
	} else {
		if (*simple.SwitchFlag)|manager.OPEN_NORMAL_QUEUE_UP_MIC > 0 {
			flagBit := (*simple.SwitchFlag) & (^manager.OPEN_NORMAL_QUEUE_UP_MIC)
			s.mgr.SwitchMicQueue(channelID, in.ChannelLiveInfo.Uid, flagBit)
		}
	}

	//创建粉丝团
	_, err = s.liveFansClient.CreateFansGroup(s.ctx, in.ChannelLiveInfo.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo CreateFansGroup in:%v err:%v", in, err)
	}

	err = s.apiClient.FollowOrUnFollowYuYinFuWuHao(ctx, in.ChannelLiveInfo.Uid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo FollowOrUnFollowYuYinFuWuHao in:%v err:%v", in, err)
	}

	var level uint32 = 4
	_, serr := s.enterCli.AutoAddLivePrepareChannel(ctx, in.ChannelLiveInfo.Uid, &entertainmentPB.AutoAddLivePrepareChannelReq{
		ChannelId: &in.ChannelLiveInfo.ChannelId,
		TagId:     &in.ChannelLiveInfo.TagId,
		Level:     &level,
		Uid:       &in.ChannelLiveInfo.Uid,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo AutoAddLivePrepareChannel failed in:%+v serr:%v", in, serr)
	}

	//TT语音助手消息
	msg := "恭喜获得[开启听听]权限，在娱乐界面进入听听房间（5.3.0以上版本支持），点此去开启>"
	s.SendTTMsg(in.ChannelLiveInfo.Uid, msg, "点此去开启>", "tt://m.52tt.com/home?main_tab=channel&second_tab=sec_entertainment")

	permissionPushMsg := &pbLogic.ChannelLivePermissionPush{
		AnchorUid:   in.ChannelLiveInfo.Uid,
		ChannelId:   in.ChannelLiveInfo.ChannelId,
		Ty:          1,
		PkAuthority: 0,
	}

	pushBin, _ := proto.Marshal(permissionPushMsg)

	pushMsg := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_LIVE_OPEN_PERMISSION),
		Content: pushBin,
	}

	pushByte, _ := proto.Marshal(pushMsg)

	noiffycation := &pushPb.CompositiveNotification{
		Sequence:           in.ChannelLiveInfo.Uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPb.ProxyNotification{
			Type:       uint32(pushPb.ProxyNotification_PUSH),
			Payload:    pushByte,
			Policy:     pushPb.ProxyNotification_DEFAULT,
			ExpireTime: 600,
		},
	}
	err = s.pushClient.PushToUsers(ctx, []uint32{in.ChannelLiveInfo.Uid}, noiffycation)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveInfo PushToUsers in:%+v err:%v", in, err)
	}

	log.InfoWithCtx(ctx, "SetChannelLiveInfo end in:%+v out:%+v", in, out)

	return out, nil
}

func (s *ChannelLiveMgrServer) SetChannelLiveTag(ctx context.Context, in *pb.SetChannelLiveTagReq) (out *pb.SetChannelLiveTagResp, err error) {
	out = &pb.SetChannelLiveTagResp{}

	log.DebugWithCtx(ctx, "SetChannelLiveTag AutoAddLivePrepareChannel in:%v", in)

	if in.Level == 0 {
		in.Level = 4
	}

	_, serr := s.enterCli.AutoAddLivePrepareChannel(ctx, in.ChannelId, &entertainmentPB.AutoAddLivePrepareChannelReq{
		ChannelId: &in.ChannelId,
		TagId:     &in.TagId,
		Level:     &in.Level,
		Uid:       &in.Uid,
	})
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTag AutoAddLivePrepareChannel serr:%v", serr)
		return out, serr
	}

	chLiveInfo, err := s.cacheClient.GetChannelLiveInfo(in.ChannelId, pb.EnumIDType_CHANNEL_ID)
	if chLiveInfo.ChannelId != 0 {
		chLiveInfo.TagId = in.TagId
		s.cacheClient.SetChannelLiveInfo(chLiveInfo.Uid, chLiveInfo)
	}
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveTag GetChannelLiveInfo failed in:%v err:%v",
			in, err)
	}

	// 操作记录，直接起协程
	go s.addAnchorOperRecord(chLiveInfo.GetUid(), in.GetChannelId(), in.GetTagId(), uint32(pb.OperateType_ModifyTag), in.GetOperUser())

	msg := fmt.Sprintf("亲爱的达人：您目前的听听标签已更换为【%s】，请按照平台规范继续开启听听，感谢您的支持与配合!", MapTagId2Name[in.GetTagId()])
	s.SendTTMsg(chLiveInfo.GetUid(), msg, "", "")
	return out, nil
}

// 删除主播权限
func (s *ChannelLiveMgrServer) DelChannelLiveInfo(ctx context.Context, in *pb.DelChannelLiveInfoReq) (out *pb.DelChannelLiveInfoResp, err error) {
	out = &pb.DelChannelLiveInfoResp{}

	chLiveInfo, err := s.cacheClient.GetChannelLiveInfo(in.Uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo GetChannelLiveInfo failed in:%v err:%v", in, err)
	}

	chStatus, err := s.cacheClient.GetChannelLiveStatus(in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo GetChannelLiveStatus failed in:%v err:%v", in, err)
	}

	//更新权限过期周期
	err = s.mysqlStore.DelChannelLiveInfo(ctx, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo err:%v", err)
		return out, err
	}

	channelID := in.ChannelId
	if channelID == 0 {
		channelID = chLiveInfo.ChannelId
	}
	s.cacheClient.DelChannelLiveInfo(in.Uid, channelID)

	// 操作记录，直接起协程
	go s.addAnchorOperRecord(in.GetUid(), channelID, chLiveInfo.GetTagId(), uint32(pb.OperateType_DelAnchor), in.GetOperUser())

	//设置没有权限标志
	s.cacheClient.SetNoAuthFlag(1, AuthFlagKey(pb.EnumIDType_USER_ID, in.Uid), AuthFlagKey(pb.EnumIDType_CHANNEL_ID, in.ChannelId))

	serr := s.anchorContractClient.SetContractLiveActorFlag(ctx, in.Uid, 1)
	if serr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo SetContractLiveActorFlag fail. err:%v", err)
	}

	contractResp, serr := s.anchorContractGoCli.GetUserContract(ctx, in.GetUid(), in.GetUid())
	if serr != nil {
		// retry
		contractResp, serr = s.anchorContractGoCli.GetUserContract(ctx, in.GetUid(), in.GetUid())
		if serr != nil {
			log.ErrorWithCtx(ctx, "DelChannelLiveInfo GetUserContract fail. %+v err:%v", in, serr)
		}
	}

	if serr == nil {
		_, serr = s.anchorContractGoCli.ReclaimAnchorIdentity(ctx, in.GetUid(), &anchorcontract_go2.ReclaimAnchorIdentityReq{
			Uid:            in.GetUid(),
			GuildId:        contractResp.GetContract().GetGuildId(),
			AnchorIdentity: uint32(anchorcontract_go2.SIGN_ANCHOR_IDENTITY_SIGN_ANCHOR_IDENTITY_RADIO_LIVE),
			Handler:        in.GetReason(),
		})
		if serr != nil {
			log.ErrorWithCtx(ctx, "DelChannelLiveInfo ReclaimAnchorIdentity fail. %+v err:%v", in, serr)
		}
	}

	msg := fmt.Sprintf("由于【%s】，已暂时回收您的听听权限。", in.Reason)
	s.SendTTMsg(in.Uid, msg, "", "")

	//设置心跳值，过期，logic层推送结束状态
	if chStatus.Status != pb.EnumChannelLiveStatus_CLOSE {
		s.cacheClient.UpdateChannelLiveHeartBeat(in.Uid, 10000)

		//s.apiCli.KickoutChannelMember(ctx, in.Uid, []uint32{in.Uid}, channelID, 1, 1, "听听权限被回收")
		_, kerr := s.channelBaseApiCli.KickOutChannelMember(ctx, &channel_base_api.KickOutChannelMemberReq{
			ChannelId:      channelID,
			OpUid:          in.Uid,
			BanEnterSecond: 1,
			TargetUidList:  []uint32{in.Uid},
			KickType:       1,
			KickText:       "听听权限被回收",
			OpSource:       "channel-live-mgr",
		})
		if kerr != nil {
			log.WarnWithCtx(ctx, "kickout channel member failed. err:%v, uid:%d, cid:%d", kerr, in.Uid, channelID)
		}

		log.DebugWithCtx(ctx, "DelChannelLiveInfo set heart beat uid:%v", in.Uid)
	}

	// 回收推荐库信息
	serr = s.enterCli.DeletePrepareChannel(ctx, []uint32{in.GetChannelId()})
	if serr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo DeletePrepareChannel failed in:%v err:%v", in, err)
	}

	// 重置粉丝团一些信息
	_, serr = s.liveFansClient.ResetFansGroup(ctx, &liveFansPb.ResetFansGroupReq{AnchorUid: in.GetUid()})
	if serr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo ResetFansGroup failed in:%v err:%v", in, err)
	}

	permissionPushMsg := &pbLogic.ChannelLivePermissionPush{
		AnchorUid: in.Uid,
		ChannelId: channelID,
		Ty:        0,
	}

	pushBin, _ := proto.Marshal(permissionPushMsg)

	pushMsg := &gaPush.PushMessage{
		Cmd:     uint32(gaPush.PushMessage_CHANNEL_LIVE_OPEN_PERMISSION),
		Content: pushBin,
	}

	pushByte, _ := proto.Marshal(pushMsg)

	noiffycation := &pushPb.CompositiveNotification{
		Sequence:           in.Uid,
		TerminalTypeList:   []uint32{protocol.MobileAndroidTT, protocol.MobileIPhoneTT},
		TerminalTypePolicy: PushNotification.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPb.ProxyNotification{
			Type:       uint32(pushPb.ProxyNotification_PUSH),
			Payload:    pushByte,
			Policy:     pushPb.ProxyNotification_DEFAULT,
			ExpireTime: 800,
		},
	}
	perr := s.pushClient.PushToUsers(ctx, []uint32{in.Uid}, noiffycation)

	if perr != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo PushToUsers err:%v", perr)
	}

	err = s.apiClient.FollowOrUnFollowYuYinFuWuHao(ctx, in.Uid, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "DelChannelLiveInfo FollowOrUnFollowYuYinFuWuHao err:%v", err)
	}

	log.DebugWithCtx(ctx, "DelChannelLiveInfo PushToUsers success uid:%v", in.Uid)

	return out, nil
}

func AuthFlagKey(ty pb.EnumIDType, id uint32) string {
	return fmt.Sprintf("%v_%v", ty, id)
}

// 获取直播权限
func (s *ChannelLiveMgrServer) GetChannelLiveInfo(ctx context.Context, in *pb.GetChannelLiveInfoReq) (out *pb.GetChannelLiveInfoResp, err error) {
	log.DebugWithCtx(ctx, "GetChannelLiveInfo in(%+v)", in)

	// 内存缓存key
	ekey := fmt.Sprintf("get_channel_live_info_%v", in.Uid)
	if in.GetUid() == 0 {
		// 房间id查询
		ekey = fmt.Sprintf("get_channel_live_info_ch_%d", in.GetChannelId())
	}

	eVal, ok := easyCache.Get(ekey)
	if ok && eVal != nil && !in.Expire {
		resp, ok := eVal.(*pb.GetChannelLiveInfoResp)
		if ok && resp != nil {
			log.DebugWithCtx(ctx, "GetChannelLiveInfo mem cache in:%v out:%v", in, resp)
			var err error = nil
			if resp.ChannelLiveInfo.ChannelId == 0 {
				err = protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
			}
			return resp, err
		}
	}

	out = &pb.GetChannelLiveInfoResp{
		ChannelLiveInfo: &pb.ChannelLiveInfo{},
	}

	nowTs := uint32(time.Now().Unix())
	var id = in.Uid
	ty := pb.EnumIDType_USER_ID
	if in.Uid == 0 {
		id = in.ChannelId
		ty = pb.EnumIDType_CHANNEL_ID
	}

	//没权限标志
	noAuthKey := AuthFlagKey(ty, id)
	if s.cacheClient.GetNoAuthFlag(noAuthKey) && !in.Expire {
		easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)

		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
	}

	chLiveInfo, err := s.cacheClient.GetChannelLiveInfo(id, ty)
	if err == nil {
		if !in.Expire && chLiveInfo.EndTime <= nowTs {
			easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)

			return out, nil
		}

		if chLiveInfo.TagId == 0 {
			resp, serr := s.enterCli.GetChannelTag(ctx, in.Uid, &entertainmentPB.GetChannelTagReq{
				ChannelId: &chLiveInfo.ChannelId,
			})

			if serr != nil {
				log.ErrorWithCtx(ctx, "GetChannelLiveInfo GetChannelTag serr:%v in:%v", serr, in)
			} else {
				chLiveInfo.TagId = *resp.TagInfo.TagId
				s.cacheClient.SetChannelLiveInfo(in.Uid, chLiveInfo)
			}
		}

		log.DebugWithCtx(ctx, "GetChannelLiveInfo redis cache in:%v out:%+v", in, out)

		out.ChannelLiveInfo = chLiveInfo
		easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)
		return out, nil
	} else {
		log.ErrorWithCtx(ctx, "GetChannelLiveInfo failed in:%v err:%v", in, err)
	}

	chLiveInfo = &pb.ChannelLiveInfo{}
	if in.GetUid() != 0 {
		err = s.mysqlStore.GetChannelLiveInfo(ctx, chLiveInfo, in.Uid)
	} else {
		err = s.mysqlStore.GetChannelLiveInfoByCid(ctx, chLiveInfo, in.GetChannelId())
	}
	if err != nil {
		//清掉没有权限标志
		if strings.Contains(err.Error(), "no rows") {
			s.cacheClient.SetNoAuthFlag(1, noAuthKey)
		} else {
			log.ErrorWithCtx(ctx, "GetChannelLiveInfo fail err:%v in:%v", err, in)
		}
		easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)
		//没有权限的不写入缓存，避免缓存过大
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotAuthority, "没有听听权限")
	}

	if chLiveInfo.TagId == 0 {
		resp, serr := s.enterCli.GetChannelTag(ctx, in.Uid, &entertainmentPB.GetChannelTagReq{
			ChannelId: &chLiveInfo.ChannelId,
		})

		if serr != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveInfo GetChannelTag serr:%v", serr)
		} else {
			chLiveInfo.TagId = *resp.TagInfo.TagId
		}
	}

	err = s.cacheClient.SetChannelLiveInfo(in.Uid, chLiveInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveInfo SetChannelLiveInfo in:%v err:%v", in, err)
	}

	if !in.Expire && chLiveInfo.EndTime <= nowTs {
		return out, nil
	}

	out.ChannelLiveInfo = chLiveInfo

	easyCache.Set(ekey, out, conf.GetLiveCacheTs()*100)

	log.InfoWithCtx(ctx, "GetChannelLiveInfo req:%v rsp:%v", in, out)
	return out, nil
}

// 搜索主播
func (s *ChannelLiveMgrServer) SearchAnchor(ctx context.Context, in *pb.SearchAnchorReq) (out *pb.SearchAnchorResp, err error) {

	log.DebugWithCtx(ctx, "SearchAnchorX in(%+v)", in)

	out = &pb.SearchAnchorResp{
		AnchorInfo: &pb.AnchorInfo{},
	}

	chInfo, err := s.cacheClient.GetChannelLiveInfo(in.Uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "SearchAnchorX GetChannelLiveInfo err:%v in:%v", err, in)
		return out, err
	}

	out.AnchorInfo.Uid = chInfo.Uid
	out.AnchorInfo.ChannelId = chInfo.ChannelId
	out.AnchorInfo.ChannelLiveStatus = 0   //TODO
	out.AnchorInfo.ChannelLivePkStatus = 0 //TODO

	if chInfo.ChannelId > 0 {
		chStatus, err := s.cacheClient.GetChannelLiveStatus(chInfo.Uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "SearchAnchorX GetChannelLiveStatus err:%v in:%v", err, in)
		} else {
			out.AnchorInfo.ChannelLiveStatus = uint32(chStatus.Status)
		}
	}

	pkstate := pk.PkState{
		ChannelID_A: chInfo.ChannelId,
	}
	state, err := pkstate.GetPkStage()
	if err == nil {
		out.AnchorInfo.ChannelLivePkStatus = uint32(state)
	}

	log.DebugWithCtx(ctx, "SearchAnchorX out:%v in:%v", out, in)

	return out, nil
}

func (s *ChannelLiveMgrServer) ChannelLiveFinish(ctx context.Context, in *pb.SetChannelLiveStatusReq, out *pb.SetChannelLiveStatusResp) {
	//从心跳队列清理
	s.cacheClient.DelChannelLiveHeartBeat(in.Uid)

	chLiveInfo := s.cacheClient.GetChannelLiveData(in.ChannelId)

	// 添加机器人观众数
	subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*200)
	defer cancel()

	robotCnt, err := s.robotCli.GetChannelRobotSize(subCtx, in.Uid, in.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus Failed to GetChannelRobotSize err(%s) in.Uid(%d) in.ChannelId(%d)", err.Error(), in.Uid, in.ChannelId)
	}
	chLiveInfo.AudienceCnt += robotCnt
	// 添加机器人数量到每天的观众记录
	serr := s.cacheClient.SetDayRobotAudienceCnt(in.Uid, robotCnt)
	if serr != nil && robotCnt != 0 {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus Failed to SetDayRobotAudienceCnt err(%s) in.Uid(%d) robotCnt(%d)", serr.Error(), in.Uid, robotCnt)
	}

	addFans := uint32(0)
	if in.FansCnt > uint32(out.ChannelLiveStatus.FansCnt) {
		addFans = in.FansCnt - uint32(out.ChannelLiveStatus.FansCnt)
	}

	addGroupFans := uint32(0)
	if in.GroupFansCnt > uint32(out.ChannelLiveStatus.GroupFansCnt) {
		addGroupFans = in.GroupFansCnt - uint32(out.ChannelLiveStatus.GroupFansCnt)
	}

	//记录每场数据到MYSQL
	serr = s.mysqlStore.AddChannelLiveRecordWhenFinish(ctx, in.ChannelLiveId, uint32(chLiveInfo.GiftValue), addFans, addGroupFans,
		chLiveInfo.AudienceCnt, chLiveInfo.SendGiftAudienceCnt, uint32(chLiveInfo.AnchorGiftValue), uint32(chLiveInfo.KnightValue),
		uint32(chLiveInfo.GameGiftValue), uint32(chLiveInfo.GameTs))
	if serr != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus AddChannelLiveRecordWhenFinish serr:%v", serr)
	}

	ts := time.Unix(int64(chLiveInfo.BeginTime), 0).Format("2006-01-02 15:04:05")
	s.mysqlStore.UpdateAnchorTotalData(in.Uid, in.ChannelId, uint32(chLiveInfo.GiftValue), ts)

	s.cacheClient.RecordChannelLiveValue(in.Uid, uint32(chLiveInfo.BeginTime), uint32(chLiveInfo.GiftValue))
}

// 主播设置直播状态
func (s *ChannelLiveMgrServer) SetChannelLiveStatus(ctx context.Context, in *pb.SetChannelLiveStatusReq) (out *pb.SetChannelLiveStatusResp, err error) {
	log.DebugWithCtx(ctx, "SetChannelLiveStatus in:%v", in)

	out = &pb.SetChannelLiveStatusResp{}

	//直播基本信息，不容易变动的部分
	channelLiveStatus := &pb.ChannelLiveStatus{
		Uid:            in.Uid,
		Account:        in.Account,
		Nickname:       in.Nick,
		Sex:            in.Sex,
		ChannelId:      in.ChannelId,
		ChannelLiveId:  in.ChannelLiveId,
		ChannelMicList: []*pb.PkMicSpace{},
		Status:         in.Status,
		FansCnt:        int64(in.FansCnt),
		GroupFansCnt:   int64(in.GroupFansCnt),
		BeginTime:      uint32(time.Now().Unix()),
	}

	pkState := pk.PkState{ChannelID_A: in.ChannelId}
	pkInfo, err := pkState.GetPkInfo()
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetPkInfo failed in:%v err:%v", in, err)
		return
	}

	if pkInfo.TChannelID != 0 && in.Status == pb.EnumChannelLiveStatus_CLOSE {
		tmpPkStage, _ := pkState.GetPkStage()
		if tmpPkStage < pbLogic.EnumChannelLivePKStatus_PUNISH && tmpPkStage != pbLogic.EnumChannelLivePKStatus_IDLE {
			log.ErrorWithCtx(ctx, "SetChannelLiveStatus is pk in:%v pkInfo:%v", in, pkInfo)
			return out, protocol.NewExactServerError(nil, status.ErrChannelLiveCloseLimit, "正在PK，不可以离开房间喔")
		}
	}

	oldStatus := pb.EnumChannelLiveStatus_CLOSE
	oldLiveStatus, err := s.cacheClient.GetChannelLiveStatus(in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus GetChannelLiveStatus in:%v err:%v", in, err)
		return
	}

	if err == nil && (in.ChannelLiveId > 0 && oldLiveStatus.ChannelLiveId > in.ChannelLiveId) {
		in.ChannelLiveId = oldLiveStatus.ChannelLiveId //修复客户端传旧版channelliveID
	}

	channelLiveId := in.ChannelLiveId
	var anchorType uint32
	if err == nil && oldLiveStatus != nil && oldLiveStatus.ChannelLiveId == in.ChannelLiveId {
		oldStatus = oldLiveStatus.Status
		channelLiveStatus.BeginTime = oldLiveStatus.BeginTime
		channelLiveStatus.FansCnt = oldLiveStatus.FansCnt
		channelLiveStatus.GroupFansCnt = oldLiveStatus.GroupFansCnt
		anchorType = oldLiveStatus.GetAnchorType()
		if in.GetStatus() != pb.EnumChannelLiveStatus_CLOSE {
			channelLiveStatus.AnchorType = oldLiveStatus.GetAnchorType()
			channelLiveStatus.VirtualLiveInfo = oldLiveStatus.GetVirtualLiveInfo()
		}

	}

	//生成新的channelliveID
	if in.Status == pb.EnumChannelLiveStatus_OPEN {
		if oldStatus == pb.EnumChannelLiveStatus_CLOSE {
			channelLiveId, err = s.mgr.GenChannelLiveID(ctx)
			if err != nil {
				log.ErrorWithCtx(ctx, "SetChannelLiveStatus GenChannelLiveID err:%v channelLiveId:%v", err, channelLiveId)
				return out, err
			}
			//添加开播记录
			go func() {
				subCtx, cancel := context.WithTimeout(context.Background(), time.Second)
				defer cancel()

				err := s.mysqlStore.AddChannelLiveRecord(subCtx, in.Uid, in.ChannelId, in.GetAnchorType(), channelLiveId)
				if err != nil {
					log.Errorf("SetChannelLiveStatus AddChannelLiveRecord failed in:%v err:%v", in, err)
				}
			}()
			//清理直播数据
			s.cacheClient.OnChannelLiveFinish(in.ChannelId)

			anchorType = in.GetAnchorType()
			channelLiveStatus.AnchorType = in.GetAnchorType()
			channelLiveStatus.VirtualLiveInfo = &pb.VirtualLiveInfo{
				ScreenType: in.GetVirtualLiveInfo().GetScreenType(),
			}
		}
		go func() {
			err := s.cacheClient.UpdateChannelLiveHeartBeat(in.Uid, 0)
			if err != nil {
				log.Errorf("SetChannelLiveStatus UpdateChannelLiveHeartBeat failed in:%v err:%v", in, err)
			}
		}()
	}

	channelLiveStatus.ChannelLiveId = channelLiveId
	s.cacheClient.SetChannelLiveStatus(in.Uid, in.ChannelId, channelLiveStatus, channelLiveId != in.ChannelLiveId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus  failed in:%v err:%v", in, err)
	}

	//匹配状态的恢复或暂停，matchFlag只有结束直播的时候删掉
	var matchValue uint32 = 0
	if in.Status == pb.EnumChannelLiveStatus_OPEN {
		matchValue, _ = s.mgr.GetPKMatchValue(in.Uid, conf.GetMatchType(uint32(time.Now().Unix())))
	}
	channelLiveStatus.PkMatchState = match.UpdateMatchState(s.cacheClient, in.Uid, in.ChannelId, matchValue, in.Status)

	go func() {
		subCtx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()

		s.mysqlStore.AddOperLog(subCtx, in.Uid, in.ChannelId, uint32(in.Status), channelLiveId)
	}()

	out = &pb.SetChannelLiveStatusResp{ChannelLiveStatus: channelLiveStatus}
	//数据上报
	if (in.Status == pb.EnumChannelLiveStatus_CLOSE || in.Status == pb.EnumChannelLiveStatus_PAUSE) && oldStatus == pb.EnumChannelLiveStatus_OPEN {
		go func() {
			s.mgr.DataCenterReport("************", in.ChannelId, map[string]interface{}{
				"uid":       in.Uid,
				"channelId": in.ChannelId,
				"liveId":    channelLiveId,
			})
		}()
	}

	//直播结束，开始统计直播数据
	if in.Status == pb.EnumChannelLiveStatus_CLOSE {
		s.ChannelLiveFinish(ctx, in, out)
	}

	if in.Status == pb.EnumChannelLiveStatus_PAUSE || in.Status == pb.EnumChannelLiveStatus_OPEN {
		statePushMsg := &pbLogic.PkChannelLiveStatusPushMsg{
			ChannelId: in.ChannelId,
			Status:    pbLogic.EnumChannelLiveStatus(in.Status),
		}
		statePushMsgBin, _ := proto.Marshal(statePushMsg)
		s.mgr.PushChannelMsg(ctx, statePushMsgBin, 124, []uint32{pkInfo.TChannelID})
		log.DebugWithCtx(ctx, "SetChannelLiveStatus push statePushMsg:%v", statePushMsg)
	}

	if in.OrigStatus == pb.EnumChannelLiveStatus_OPEN {
		s.redisClient.Del(fmt.Sprintf("ZSET_QUEUE_MIC_APPLY_%v", in.ChannelId))
	}

	err = s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
		ChannelLiveStatus: pbLogic.EnumChannelLiveStatus(in.Status),
		AnchorUid:         in.Uid,
		ChannelId:         in.ChannelId,
		ChannelLiveId:     int64(channelLiveId),
		Ty:                pbLogic.ChannelLiveKafkaEventType_ChannelLiveType,
		CreateTime:        time.Now().Unix(),
		AnchorType:        anchorType,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveStatus ProduceChannelLiveEvent err:%v", err)
	}

	log.InfoWithCtx(ctx, "SetChannelLiveStatus out:%v in:%v oldLiveStatus:%v", out, in, oldLiveStatus)
	return out, nil
}

// 主播心跳，没有PK的情况下用户监控直播中主播的在线状态
func (s *ChannelLiveMgrServer) ChannelLiveHeartbeat(ctx context.Context, in *pb.ChannelLiveHeartbeatReq) (out *pb.ChannelLiveHeartbeatResp, err error) {
	out = &pb.ChannelLiveHeartbeatResp{}

	log.DebugWithCtx(ctx, "ChannelLiveHeartbeatS in:%v", in)

	/*	currChLiveStatus, err := s.cacheClient.GetChannelLiveStatus(in.Uid)

		if err != nil {
			log.ErrorWithCtx(ctx,"ChannelLiveHeartbeatSX GetChannelLiveStatus err:%v in:%v", err, in)
			return out, err
		}*/

	/*	out.IsChange = false
		out.ChannelStatus = &pb.ChannelLiveStatus{
			Uid:            in.Uid,
			Account:        in.Account,
			ChannelId:      in.ChannelId,
			ChannelLiveId:  in.ChannelLiveId,
			ChannelMicList: make([]*pb.PkMicSpace, 0),
			Status:         currChLiveStatus.Status,
		}*/

	//定时更新数据
	nowTs := time.Now().Unix()
	lastHeartTs := GetHeartBeat(in.ChannelId)

	if (nowTs - lastHeartTs) <= 2 {
		return out, nil
	}

	err = s.cacheClient.UpdateChannelLiveHeartBeat(in.Uid, 0)
	if err != nil {
		log.ErrorWithCtx(ctx, "ChannelLiveHeartbeatSX UpdateChannelLiveHeartBeat err:%v in:%v", err, in)
	}

	if (nowTs - lastHeartTs) >= 30 {
		SetHeartBeat(in.ChannelId, nowTs)
		chLiveInfo := s.cacheClient.GetChannelLiveData(in.ChannelId)

		go func() {

			sta, err := s.cacheClient.GetChannelLiveStatus(in.Uid)
			if nil == err && sta.ChannelLiveId > in.ChannelLiveId { //修复客户端传旧版channelliveID
				in.ChannelLiveId = sta.ChannelLiveId
			}

			s.mysqlStore.AddChannelLiveRecordWhenFinish(context.Background(), in.ChannelLiveId, uint32(chLiveInfo.GiftValue), 0, 0,
				chLiveInfo.AudienceCnt, chLiveInfo.SendGiftAudienceCnt, uint32(chLiveInfo.AnchorGiftValue), uint32(chLiveInfo.KnightValue),
				uint32(chLiveInfo.GameGiftValue), uint32(chLiveInfo.GameTs))
		}()
	}

	log.DebugWithCtx(ctx, "ChannelLiveHeartbeatS out:%v in:%v", out, in)

	return out, nil
}

// 包括PK信息
func (s *ChannelLiveMgrServer) GetChannelLiveStatus(ctx context.Context, in *pb.GetChannelLiveStatusReq) (out *pb.GetChannelLiveStatusResp, err error) {

	log.DebugWithCtx(ctx, "GetChannelLiveStatus in:%v", in)

	//先读取缓存
	ekey := fmt.Sprintf("get_channel_live_status_%v", in.Uid)
	if !in.IgnoreMemCache {
		eVal, ok := easyCache.Get(ekey)
		if ok && eVal != nil {
			resp := eVal.(*pb.GetChannelLiveStatusResp)
			if resp != nil {
				log.DebugWithCtx(ctx, "GetChannelLiveStatus cache out:%v", resp)
				return resp, nil
			}
		}
	}

	out = &pb.GetChannelLiveStatusResp{}

	chLiveStatus, err := s.cacheClient.GetChannelLiveStatus(in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveStatus fail err:%v", err)
	}

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
		ChannelID_B: 0,
	}

	pkstatus, _ := pkstate.GetPkStage()
	chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus(pkstatus)
	matchFlag := match.GetMatchFlag(s.cacheClient, in.Uid)
	chLiveStatus.PkMatchState = matchFlag

	opCidMap := s.cacheClient.BatchGetOpPkChannelID([]uint32{in.GetChannelId()})
	if opCidMap != nil && opCidMap[in.GetChannelId()] != 0 {
		chLiveStatus.PkChannelId = opCidMap[in.GetChannelId()]
	}

	// 查询多人pk状态
	if chLiveStatus.PkStatus == pb.EnumChannelLivePKStatus_IDLE {
		resp, err := s.channeLivePkCli.BatCheckIsInMultiPk(ctx, &channellivepkPB.BatCheckIsInMultiPkReq{
			UidList: []uint32{in.GetUid()},
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveStatus BatCheckIsInMultiPk fail in:%v err:%v", in, err)
		}

		if resp.GetMapIdIspk()[in.GetUid()] {
			chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus_BEGIN
		}
	}
	out.ChannelLiveInfo = &pb.ChannelLiveStatusInfo{
		ChannelLiveStatus: chLiveStatus,
	}

	//缓存
	easyCache.Set(ekey, out, conf.GetLiveCacheTs())

	log.DebugWithCtx(ctx, "GetChannelLiveStatus out:%v", out)

	return out, nil
}

func (s *ChannelLiveMgrServer) BatchGetChannelLiveStatusSimple(ctx context.Context, in *pb.BatchGetChannelLiveStatusSimpleReq) (out *pb.BatchGetChannelLiveStatusSimpleResp, err error) {
	out = &pb.BatchGetChannelLiveStatusSimpleResp{ChannelLiveStatusList: make([]*pb.ChannelLiveStatusSimple, 0)}

	if len(in.ChannelList) > 0 {
		channelLiveStatusList, err := s.cacheClient.GetChannelLiveStatusSimple(in.ChannelList, true)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatusSimple GetChannelLiveStatusSimple failed len:%d err:%v in:%v", len(in.GetChannelList()), err, in)
			return out, protocol.NewExactServerError(nil, status.ErrRepositoryFailed)
		}

		livePkResp, err := s.channeLivePkCli.BatCheckIsInMultiPk(ctx, &channellivepkPB.BatCheckIsInMultiPkReq{
			CidList:      in.GetChannelList(),
			IsNeedPkInfo: true,
		})
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatusSimple BatCheckIsInMultiPk failed len:%d err:%v in:%v", len(in.GetChannelList()), err, in)
			return out, err
		}

		opCidMap := s.cacheClient.BatchGetOpPkChannelID(in.ChannelList)
		for _, c := range channelLiveStatusList {
			c.PkChannelIdList = make([]uint32, 0)
			c.PkChannelId = opCidMap[c.ChannelId]
			if c.PkChannelId != 0 {
				log.DebugWithCtx(ctx, "BatchGetChannelLiveStatusSimple opCidMap:%v", opCidMap)
				c.PkChannelIdList = append(c.PkChannelIdList, c.PkChannelId)
			}
			if c.GetPkChannelId() != 0 {
				c.PkType = uint32(pb.EChannelPkType_ENUM_PK_TYPE_SINGLE)
			}
			multiPkSimpleInfo := livePkResp.GetMapCidInfo()[c.GetChannelId()]
			if c.GetPkChannelId() == 0 && livePkResp.GetMapIdIspk()[c.GetChannelId()] && multiPkSimpleInfo != nil {
				c.PkType = func(pkType uint32) uint32 {
					switch channellivepkPB.MultiPkType(pkType) {
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_UNSPECIFIED:
						// 连麦阶段还没选择模式，默认是1v1
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_SINGLE:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_TEAM:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_2V2)
					default:
						return 0
					}
				}(multiPkSimpleInfo.GetPkType())
				// 在多人pk
				for _, cid := range multiPkSimpleInfo.GetChannelIdList() {
					if cid != c.GetChannelId() {
						// 随便取一个对手
						c.PkChannelId = cid
						break
					}
				}
				for _, cid := range multiPkSimpleInfo.GetChannelIdList() {
					if cid != c.GetChannelId() {
						c.PkChannelIdList = append(c.PkChannelIdList, cid)
					}
				}
			}
			out.ChannelLiveStatusList = append(out.ChannelLiveStatusList, c)
		}

	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatusSimple end in:%v out:%v", in, out)
	return out, err
}

// 批量取直播状态
func (s *ChannelLiveMgrServer) BatchGetChannelLiveStatus(ctx context.Context, in *pb.BatchGetChannelLiveStatusReq) (out *pb.BatchGetChannelLiveStatusResp, err error) {
	out = &pb.BatchGetChannelLiveStatusResp{
		ChannelLiveInfoList: make([]*pb.ChannelLiveStatusInfo, 0),
	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatus in:%v", in)

	if len(in.UidList) == 0 && len(in.ChannelList) == 0 {
		return out, nil
	}

	//不支持channelID查询
	if len(in.ChannelList) > 0 || len(in.UidList) > 128 {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatus invalid para %v %v", len(in.UidList), len(in.ChannelList))
		return out, errors.New("invalid para")
	}

	//先筛选出有直播权限的UID列表
	mapNoAuthUid := make(map[uint32]bool)
	ableUidList, lackUidList := make([]uint32, 0, 128), make([]uint32, 0, 128)
	for _, uid := range in.UidList {
		//AuthFlagKey(pb.EnumIDType_USER_ID, uid) 缓存没有权限的UID列表
		v, ok := easyCacheFlag.Get(AuthFlagKey(pb.EnumIDType_USER_ID, uid))
		if ok && v != nil {
			mapNoAuthUid[uid] = true
		} else {
			lackUidList = append(lackUidList, uid)
		}
	}

	//不在内存缓存的查Redis
	authKey := make([]string, 0, len(lackUidList))
	for _, uid := range lackUidList {
		authKey = append(authKey, AuthFlagKey(pb.EnumIDType_USER_ID, uid))
	}

	items, err := s.cacheClient.RedisFlagClient.MGet(authKey...).Result()
	if nil == err && items != nil {
		for index, uid := range lackUidList {
			if index > len(items) {
				continue
			}
			if s, ok := items[index].(string); ok {
				if s == "1" { //缓存没有权限列表
					easyCacheFlag.Set(AuthFlagKey(pb.EnumIDType_USER_ID, uid), s, conf.GetLiveCacheTs()*100)
					mapNoAuthUid[uid] = true
				}
			}
		}
	}

	for _, uid := range in.UidList {
		if mapNoAuthUid[uid] {
			continue
		}
		ableUidList = append(ableUidList, uid)
	}

	mapChannelLiveStatus, err := s.cacheClient.BatchGetChannelLiveStatus(ableUidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatus BatchGetChannelLiveStatus failed in:%v err:%v",
			in, err)
	}

	cidList := make([]uint32, 0)
	for _, liveStatus := range mapChannelLiveStatus {
		cidList = append(cidList, liveStatus.GetChannelId())
	}

	livePkResp, err := s.channeLivePkCli.BatCheckIsInMultiPk(ctx, &channellivepkPB.BatCheckIsInMultiPkReq{
		CidList:      cidList,
		IsNeedPkInfo: true,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetChannelLiveStatus BatCheckIsInMultiPk fail in:%v err:%v", in, err)
	}

	for _, uid := range in.UidList {
		chLiveStatus, ok := mapChannelLiveStatus[uid]
		if !ok {
			continue
		}

		chLiveStatusInfo := &pb.ChannelLiveStatusInfo{}
		if chLiveStatus.Status != pb.EnumChannelLiveStatus_CLOSE {
			pkstate := pk.PkState{ChannelID_A: chLiveStatus.ChannelId}
			pks, _ := pkstate.GetPkStage()
			chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus(pks)
			if chLiveStatus.GetPkStatus() != pb.EnumChannelLivePKStatus_IDLE {
				chLiveStatus.PkType = uint32(pb.EChannelPkType_ENUM_PK_TYPE_SINGLE)
			}

			if chLiveStatus.GetPkStatus() == pb.EnumChannelLivePKStatus_IDLE && livePkResp.GetMapIdIspk()[chLiveStatus.GetChannelId()] {
				chLiveStatus.PkStatus = pb.EnumChannelLivePKStatus_BEGIN
				multiPkSimpleInfo := livePkResp.GetMapCidInfo()[chLiveStatus.GetChannelId()]
				chLiveStatus.PkType = func(pkType uint32) uint32 {
					switch channellivepkPB.MultiPkType(pkType) {
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_UNSPECIFIED:
						// 连麦阶段还没选择模式，默认是1v1
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_SINGLE:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_1V1)
					case channellivepkPB.MultiPkType_MULTI_PK_TYPE_TEAM:
						return uint32(pb.EChannelPkType_ENUM_PK_TYPE_MULTI_2V2)
					default:
						return 0
					}
				}(multiPkSimpleInfo.GetPkType())
			}
		}

		chLiveStatusInfo.ChannelLiveStatus = chLiveStatus
		out.ChannelLiveInfoList = append(out.ChannelLiveInfoList, chLiveStatusInfo)
	}

	log.DebugWithCtx(ctx, "BatchGetChannelLiveStatus in:%v out:%v", in, out)
	return out, nil
}

// 获取我的道具
func (s *ChannelLiveMgrServer) GetMyToolList(ctx context.Context, in *pb.GetMyToolListReq) (out *pb.GetMyToolListResp, err error) {
	out = &pb.GetMyToolListResp{
		Items: make([]*pb.ToolItem, 0),
	}

	log.DebugWithCtx(ctx, "GetMyToolList in:%v", in)

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
		ChannelID_B: 0,
	}

	pkinfo, serr := pkstate.GetPkInfo()
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetMyToolList GetPkInfo in:%v err:%v", in, serr)
	}

	if serr != nil {
		return out, nil
	}

	if pkinfo.TChannelID == 0 {
		log.DebugWithCtx(ctx, "GetMyToolList in:%v TChannelID==0", in)
		return out, nil
	}

	items, err := s.cacheClient.GetUserItemList(in.ChannelId, pkinfo.FBeginTime, in.Uid)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetMyToolList GetUserItemList in:%v err:%v", in, err)
	}

	out.Items = append(out.Items, items...)

	log.DebugWithCtx(ctx, "GetMyToolList in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveMgrServer) GetItemConfig(ctx context.Context, in *pb.GetItemConfigReq) (out *pb.GetItemConfigResp, err error) {
	out = &pb.GetItemConfigResp{
		ItemConfList: make([]*pb.ItemConfig, 0),
	}

	bAll := in.ItemIdList == nil || len(in.ItemIdList) == 0
	mapItem := make(map[string]bool)
	if !bAll {
		for _, itemId := range in.ItemIdList {
			mapItem[itemId] = true
		}
	}
	//TODO fill
	for _, item := range s.sc.ItemConfigList {
		if !(bAll || mapItem[item.ItemId]) {
			continue
		}
		itemConf := &pb.ItemConfig{
			ItemId:          item.ItemId,
			Desc:            item.Desc,
			Icon:            item.Icon,
			EffectUrl:       item.EffectUrl,
			TargetEffectUrl: item.TargetEffectUrl,
			Msg:             item.UseMsg,
			TargetMsg:       item.TargetMsg,
			GainMsg:         item.GainMsg,
			Ty:              pb.ItemType(item.Type),
			Value:           item.Value,
			Name:            item.Name,
			Version:         0,
			MilestoneList:   nil,
		}
		if itemConf.Ty == pb.ItemType_Effect_Type {
			itemConf.MilestoneList = make([]*pb.EffectItemMilestone, 0)
			map2per := s.sc.GetCnt2Dec()
			for cnt, per := range map2per {
				itemConf.MilestoneList = append(itemConf.MilestoneList, &pb.EffectItemMilestone{
					Count:   cnt,
					Percent: per,
				})
			}
		}
		out.ItemConfList = append(out.ItemConfList, itemConf)
	}

	log.DebugWithCtx(ctx, "GetItemConfig out:%v", out)

	return out, nil
}

// PK送礼列表
func (s *ChannelLiveMgrServer) GetChanneLivePkRankUser(ctx context.Context, in *pb.GetChanneLivePkRankUserReq) (out *pb.GetChanneLivePkRankUserResp, err error) {
	out = &pb.GetChanneLivePkRankUserResp{
		UserList: make([]*pb.SendGiftUserInfo, 0),
	}

	log.DebugWithCtx(ctx, "GetChanneLivePkRankUser in:%v", in)

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
		ChannelID_B: 0,
	}

	pkInfo, _ := pkstate.GetPkInfo()
	pkstate.ChannelID_B = pkInfo.TChannelID
	firstStrUidInfo, cid := pkstate.GetFirstShootUid()
	firstUidInfo := s.mgr.ParseStrUidInfo(cid, firstStrUidInfo)
	if cid != in.ChannelId {
		firstUidInfo.Uid = 0
	}

	ranks, serr := pkstate.GetPkRank(int64(pkInfo.FBeginTime), int64(in.Off), int64(in.Cnt))
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChanneLivePkRankUser serr:%v", serr)
		return out, serr
	}

	out.AnchorUid = pkInfo.MUID
	out.ChannelId = in.ChannelId

	for _, k := range ranks {
		userInfo := s.mgr.ParseStrUidInfo(in.GetChannelId(), k.StrUidInfo)
		if userInfo.GetUid() != 0 {
			out.UserList = append(out.UserList, &pb.SendGiftUserInfo{
				Uid:       userInfo.GetUid(),
				Score:     k.Score,
				FirstKill: firstUidInfo.GetUid() == userInfo.GetUid() && firstUidInfo.GetAccount() == userInfo.GetAccount(),
				UkwInfo:   userInfo,
			})
		}
	}

	log.DebugWithCtx(ctx, "GetChanneLivePkRankUser in:%v out:%v", in, out)

	return out, nil
}

// 发起PK申请
func (s *ChannelLiveMgrServer) ApplyPk(ctx context.Context, in *pb.ApplyPkReq) (out *pb.ApplyPkResp, err error) {
	out = &pb.ApplyPkResp{}

	log.InfoWithCtx(ctx, "ApplyPk in:%v", in)

	if err := s.mgr.CheckIsPkCntLimit(in.Uid, true); nil != err {
		log.ErrorWithCtx(ctx, "ApplyPk uid req:%v", in)
		return out, err //protocol.NewExactServerError(nil, status.ErrChannelLivePkCntLimit, "每日20:00～22:00期间只允许连麦PK两次哦～")
	}

	if err := s.mgr.CheckIsPkCntLimit(in.TargetUid, false); nil != err {
		log.ErrorWithCtx(ctx, "ApplyPk target req:%v", in)
		return out, err //protocol.NewExactServerError(nil, status.ErrChannelLivePkCntLimit, "该主播已到达20:00～22:00期间的连麦限额了哦，不支持再连麦PK了")
	}

	nowTs := uint32(time.Now().Unix())
	pkInfoList, err := s.cacheClient.GetAnchorAppointPkInfoList(in.GetUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		return out, err
	}

	targetPkInfoList, err := s.cacheClient.GetAnchorAppointPkInfoList(in.GetTargetUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		return out, err
	}

	for _, pkInfo := range pkInfoList {
		log.DebugWithCtx(ctx, "ApplyPk in:%v Info:%v ts:%d", in, pkInfo, nowTs)
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			isExist, err := s.cacheClient.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "ApplyPk CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
				continue
			}
			if isExist {
				log.ErrorWithCtx(ctx, "ApplyPk anchor is in appoint ok in:%v pkInfo:%v", in, pkInfo)
				return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk)
			}
		}
	}

	for _, pkInfo := range targetPkInfoList {
		log.DebugWithCtx(ctx, "ApplyPk in:%v Info:%v ts:%d", in, pkInfo, nowTs)
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			isExist, err := s.cacheClient.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "ApplyPk CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
				continue
			}
			if isExist {
				log.ErrorWithCtx(ctx, "ApplyPk anchor is in appoint ok in:%v pkInfo:%v", in, pkInfo)
				return out, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoAcceptPk)
			}

		}
	}

	targetChLiveStatus, err := s.cacheClient.GetChannelLiveStatus(in.TargetUid)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetChannelLiveStatus fail err:%v in:%v", err, in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	if targetChLiveStatus.ChannelId == 0 {
		log.ErrorWithCtx(ctx, "ApplyPk ChannelId==0 in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	if targetChLiveStatus.Status == pb.EnumChannelLiveStatus_CLOSE {
		log.ErrorWithCtx(ctx, "ApplyPk Status==close in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveNotOpen)
	}

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
		ChannelID_B: 0,
	}

	pkinfo, err := pkstate.GetPkInfo()
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetPkInfo fail err:%v in:%v", err, in)
		return out, err
	}

	if pkinfo.TChannelID != 0 {
		log.ErrorWithCtx(ctx, "ApplyPk pking in:%v cid:%v", in, pkinfo.TChannelID)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "正在PK")
	}

	targetPkState := pk.PkState{ChannelID_A: in.TargetChannelId}
	targetPkInfo, _ := targetPkState.GetPkInfo()
	if targetPkInfo.TChannelID != 0 {
		log.ErrorWithCtx(ctx, "ApplyPk pking in:%v cid:%v", in, targetPkInfo.TChannelID)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkIng, "对方正在PK")
	}

	applyKey := fmt.Sprintf("apply_pk_%v_%v", in.Uid, in.TargetUid)

	//如果是PK活动榜中的主播，限制PK申请时间
	if conf.IsPkActivityAnchor(in.Uid) {
		if !s.cacheClient.CheckQPSLimit(applyKey) {
			log.ErrorWithCtx(ctx, "ApplyPk CheckQPSLimic in:%v", in)
			return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkRepeatedApply, "PK申请频率太频繁")
		}
	}

	applys, err := s.cacheClient.GetApplyList(in.TargetChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk GetApplyList failed in:%v err:%v", in, err)
	}

	for _, apply := range applys {
		if apply.Uid == in.Uid {
			log.ErrorWithCtx(ctx, "ApplyPk repeated in:%v", in)
			return out, protocol.NewExactServerError(nil, status.ErrChannelLivePkRepeatedApply, "不能重复发起PK申请")
		}
	}

	applyId := s.cacheClient.IncrKey("global_channel_live_apply_pk_key", 0)

	ts := time.Now().Unix()
	err = s.cacheClient.ApplyPk(in.Uid, in.ChannelId, uint32(in.ChannelLiveId), in.TargetChannelId, applyId, ts)
	if err != nil {
		log.ErrorWithCtx(ctx, "ApplyPk cache repeated in:%v", in)
		return out, err
	}

	s.pkApplyProd.ProdPkApplyEvent(&pbLogic.PkApplyKafkaEvent{
		ApplyUid:  in.Uid,
		TargetUid: in.TargetUid,
		EventTy:   pbLogic.EnumApply_apply,
		ApplyTime: uint32(ts),
		ApplyId:   applyId,
	})

	log.DebugWithCtx(ctx, "ApplyPkS in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveMgrServer) StartPk(ctx context.Context, uida, uidb, channelIda, channelIdb, scoreA, scoreB uint32, source int) (uint32, error) {

	//log.DebugWithCtx(ctx,"StartPk uida:%v uidb:%v channelIda:%v channelIdb:%v source:%v", uida, uidb, channelIda, channelIdb, source)

	objPKstateA := &pk.PkState{
		ChannelID_A: channelIda, //in.ChannelId,
	}

	isPKing := objPKstateA.IsPKing()
	if isPKing {
		//log.ErrorWithCtx(ctx,"StartPkSX user is pking uid:%v", uida)
		match.UpdateMatchState(s.cacheClient, uida, channelIda, 0, pb.EnumChannelLiveStatus_CLOSE)
		return 0, fmt.Errorf("user is pking %v", uida)
	}

	objPKstateB := &pk.PkState{
		ChannelID_A: channelIdb,
	}
	isPKing = objPKstateB.IsPKing()
	if isPKing {
		//log.ErrorWithCtx(ctx,"StartPkSX user is pking uid:%v", uidb)
		match.UpdateMatchState(s.cacheClient, uidb, channelIdb, 0, pb.EnumChannelLiveStatus_CLOSE)
		return 0, fmt.Errorf("user is pking %v", uidb)
	}

	objPKstate := &pk.PkState{
		ChannelID_A: channelIda, //in.ChannelId,
		UID_A:       uida,       //in.Uid,
		ChannelID_B: channelIdb, //in.ApplyChannelId,
		UID_B:       uidb,       //in.ApplyUid,
		MatchSource: int32(source),
	}

	beginTs, perr := objPKstate.StartPK()
	pkBeginTime := uint32(beginTs)
	if perr != nil {
		log.ErrorWithCtx(ctx, "StartPkSX StartPK err:%v", perr)
		return 0, perr
	} else {
		//添加PK记录
		subCtx, cancel := context.WithTimeout(ctx, time.Millisecond*300)
		defer cancel()

		s.mysqlStore.AddChannelLivePkRecored(subCtx, uida, channelIda, uidb, channelIdb, 0, 0, "", "")
	}

	//加到定时队列 , 阶段时间
	triggerTime := uint32(time.Now().Unix()) + conf.Sec2State[uint32(pb.EnumChannelLivePKStatus_BEGIN)]
	s.cacheClient.AddTaskToTick(&cache.TickTask{
		AUid:        uidb,
		BUid:        uida,
		AChannelId:  channelIdb,
		BChannelId:  channelIda,
		Status:      uint32(pbLogic.EnumChannelLivePKStatus_BEGIN),
		BeginTs:     uint32(beginTs),
		Source:      uint32(source),
		MatchSource: int32(source),
	}, triggerTime)

	pkStatusPush := s.mgr.FillStartPkInfo(uida, uidb, channelIda, channelIdb, uint32(beginTs))

	//PK开始广播到两个房间
	pkStatusPushBin, _ := proto.Marshal(pkStatusPush)
	s.mgr.PushChannelMsg(ctx, pkStatusPushBin, uint32(channelpb.ChannelMsgType_CHANNEL_LIVE_PK_STATUS_MGS), []uint32{channelIda, channelIdb})

	perr = s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
		ChannelLiveStatus: pbLogic.EnumChannelLiveStatus_OPEN,
		AnchorUid:         uida,
		ChannelId:         channelIda,
		OppAnchorUid:      uidb,
		OppChannelId:      channelIdb,
		ChannelPkStatus:   pbLogic.EnumChannelLivePKStatus_BEGIN,
		ApplyId:           int64(uidb),
		Ty:                pbLogic.ChannelLiveKafkaEventType_ChannelLivePkType,
		CreateTime:        time.Now().Unix(),
		MatchModel:        pbLogic.ChannelLivePKMatchType(source),
		MatchType:         int64(source),
	})

	if perr != nil {
		log.ErrorWithCtx(ctx, "StartPk ProduceChannelLiveEvent perr:%v", perr)
	}

	log.DebugWithCtx(ctx, "StartPk PushChannelMsg pkStatusPush:%v", pkStatusPush)

	//清除PK匹配
	match.UpdateMatchState(s.cacheClient, uida, channelIda, 0, pb.EnumChannelLiveStatus_CLOSE)
	match.UpdateMatchState(s.cacheClient, uidb, channelIdb, 0, pb.EnumChannelLiveStatus_CLOSE)

	//匹配来的
	if source >= 0 {
		s.mysqlStore.AddMatchLog(ctx, int64(uida), int64(pb.EnumPkMatch_PKM_Match_Success), int64(source), int64(scoreA), int64(uidb), int64(scoreB))
	}
	return pkBeginTime, nil
}

func (s *ChannelLiveMgrServer) HandlerApply(ctx context.Context, in *pb.HandlerApplyReq) (out *pb.HandlerApplyResp, err error) {
	out = &pb.HandlerApplyResp{}

	log.DebugWithCtx(ctx, "HandlerApplyS in:%v", in)

	//删掉旧申请的PK，无论这个是接受还是拒绝都删掉
	ok, apply, err := s.cacheClient.DelApply(in.ChannelId, in.ApplyChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerApplySX DelApply err:%v in:%v", err, in)
		return out, err
	}

	var applyId int64 = 0
	var applyTime uint32 = 0

	if apply != nil {
		applyId = apply.ApplyID
		applyTime = apply.ApplyTime
	}

	go func() {
		s.pkApplyProd.ProdPkApplyEvent(&pbLogic.PkApplyKafkaEvent{
			ApplyUid:  in.ApplyUid,
			TargetUid: in.Uid,
			EventTy:   pbLogic.EnumApply(in.Oper),
			ApplyId:   applyId,
			ApplyTime: applyTime,
		})
	}()

	if in.Oper != pb.EnumApply_accept {
		//除了接受操作，其他delete后直接返回
		return out, nil
	}

	if !ok {
		log.ErrorWithCtx(ctx, "HandlerApplySX apply timeout in:%v", in)
		return out, protocol.NewExactServerError(nil, status.ErrChannelLiveIdInvalid, "PK申请已过期")
	}

	out.PkBeginTime, err = s.StartPk(ctx, in.Uid, in.ApplyUid, in.ChannelId, in.ApplyChannelId, 0, 0, -1)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandlerApplySX StartPk err:%v in:%v", err, in)
	}

	log.DebugWithCtx(ctx, "HandlerApplyS in:%v out:%v", in, out)

	return out, nil
}

func (s *ChannelLiveMgrServer) CancelPKApply(ctx context.Context, in *pb.CancelPKApplyReq) (out *pb.CancelPKApplyResp, err error) {
	out = &pb.CancelPKApplyResp{}

	log.InfoWithCtx(ctx, "CancelPKApply in:%v", in)

	_, apply, _ := s.cacheClient.DelApply(in.ChannelId, in.ApplyChannelId)

	var applyId int64 = 0
	var applyTime uint32 = 0

	if apply != nil {
		applyId = apply.ApplyID
		applyTime = apply.ApplyTime
	}

	s.pkApplyProd.ProdPkApplyEvent(&pbLogic.PkApplyKafkaEvent{
		ApplyUid:  in.ApplyUid,
		TargetUid: in.Uid,
		EventTy:   pbLogic.EnumApply_cancel,
		ApplyId:   applyId,
		ApplyTime: applyTime,
	})

	return out, nil
}

func (s *ChannelLiveMgrServer) GetApplyList(ctx context.Context, in *pb.GetApplyListReq) (out *pb.GetApplyListResp, err error) {
	out = &pb.GetApplyListResp{
		ApplyList: make([]*pb.Apply, 0),
	}

	applyList, err := s.cacheClient.GetApplyList(in.ChannelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetApplyList err:%v", err)
	}

	for _, apply := range applyList {
		out.ApplyList = append(out.ApplyList, &pb.Apply{
			ApplyUid:       apply.Uid,
			ApplyChannelId: apply.ChannelId,
			ApplyTime:      apply.ApplyTime,
		})
	}

	return out, nil
}

// 设置PK状态
func (s *ChannelLiveMgrServer) SetPkStatus(ctx context.Context, in *pb.SetPkStatusReq) (out *pb.SetPkStatusResp, err error) {
	out = &pb.SetPkStatusResp{}

	log.DebugWithCtx(ctx, "SetPkStatus in:%v", in)

	pkstate := pk.PkState{
		ChannelID_A: in.ChannelId,
	}
	pkinfoA, err := pkstate.GetPkInfo()
	if err == nil {
		out.TargetChannelId = pkinfoA.TChannelID
	}

	if pkinfoA.TChannelID != 0 {
		pkstate.ChannelID_B = pkinfoA.TChannelID
		pkstateB := pk.PkState{
			ChannelID_A: pkinfoA.TChannelID,
		}
		pkinfoB, _ := pkstateB.GetPkInfo()

		if pkinfoB.TChannelID == in.ChannelId {
			pkstate.FinishPK()
		}
	}

	out.BeginTime = pkinfoA.BeginTime
	out.TargetChannelId = pkinfoA.TChannelID

	perr := s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
		ChannelLiveStatus: pbLogic.EnumChannelLiveStatus_OPEN,
		AnchorUid:         in.Uid,
		ChannelId:         in.ChannelId,
		OppAnchorUid:      pkinfoA.TUID,
		OppChannelId:      pkinfoA.TChannelID,
		ChannelPkStatus:   pbLogic.EnumChannelLivePKStatus_IDLE,
		Ty:                pbLogic.ChannelLiveKafkaEventType_ChannelLivePkType,
		CreateTime:        time.Now().Unix(),
		MatchType:         int64(pkinfoA.MatchSource),
		MatchModel:        pbLogic.ChannelLivePKMatchType(pkinfoA.MatchSource),
	})

	if perr != nil {
		log.ErrorWithCtx(ctx, "StartPk ProduceChannelLiveEvent perr:%v", perr)
	}

	log.DebugWithCtx(ctx, "SetPkStatus in:%v out:%v", in, out)

	return out, nil
}

// 直播送礼榜
func (s *ChannelLiveMgrServer) GetChannelLiveRankUser(ctx context.Context, in *pb.GetChannelLiveRankUserReq) (out *pb.GetChannelLiveRankUserResp, err error) {
	out = &pb.GetChannelLiveRankUserResp{
		UserList: make([]*pb.SendGiftUserInfo, 0),
	}

	ekey := fmt.Sprintf("get_channel_live_rank_user_%v", in.ChannelId)
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.GetChannelLiveRankUserResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	rankUser, _ := s.cacheClient.GetSendGiftRank(in.ChannelId)

	for _, rank := range rankUser {
		sendGiftUser := &pb.SendGiftUserInfo{
			Uid:   rank.Uid,
			Score: rank.Score,
		}
		out.UserList = append(out.UserList, sendGiftUser)
	}

	easyCache.Set(ekey, out, conf.GetLiveCacheTs())

	log.DebugWithCtx(ctx, "GetChannelLiveRankUser out:%v", out)

	return out, nil
}

type WatchRankStu struct {
	Uid   uint32
	Score uint32
}

type WatchRankStus []WatchRankStu

func (w WatchRankStus) Len() int {
	return len(w)
}

func (w WatchRankStus) Less(i, j int) bool {
	return w[i].Score > w[j].Score
}

func (w WatchRankStus) Swap(i, j int) {
	w[i], w[j] = w[j], w[i]
}

// 直播观看榜
func (s *ChannelLiveMgrServer) GetChannelLiveWatchTimeRankUser(ctx context.Context, in *pb.GetChannelLiveWatchTimeRankUserReq) (out *pb.GetChannelLiveWatchTimeRankUserResp, err error) {
	out = &pb.GetChannelLiveWatchTimeRankUserResp{
		UserList: make([]*pb.WatchUserInfo, 0),
	}

	log.DebugWithCtx(ctx, "GetChannelLiveWatchTimeRankUser in:%v", in)

	watchRank, err := s.cacheClient.GetChannelLiveWatchRank(in.ChannelId, WatchAudieCountLimit)

	if err != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveWatchTimeRankUser GetChannelLiveWatchRank err:%v", err)
		return out, err
	}

	ww := WatchRankStus{}

	for _, user := range watchRank {
		ww = append(ww, WatchRankStu{
			Uid:   user.Uid,
			Score: user.Score,
		})
	}

	sort.Sort(ww)

	for index, w := range ww {
		if index >= WatchAudieCountLimit {
			break
		}

		out.UserList = append(out.UserList, &pb.WatchUserInfo{
			Uid:     w.Uid,
			Account: "",
			Score:   w.Score,
		})
	}

	log.DebugWithCtx(ctx, "GetChannelLiveWatchTimeRankUser out:%v", out)

	return out, nil
}

// 单场直播数据统计
func (s *ChannelLiveMgrServer) GetChannelLiveData(ctx context.Context, in *pb.GetChannelLiveDataReq) (out *pb.GetChannelLiveDataResp, err error) {
	log.DebugWithCtx(ctx, "GetChannelLiveData req(%+v)", in)
	out = &pb.GetChannelLiveDataResp{}

	channelId := in.ChannelId
	if channelId == 0 {
		chInfo, err := s.cacheClient.GetChannelLiveInfo(in.Uid, 0)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetChannelLiveData GetChannelLiveInfo err:%v", err)
			return out, err
		}
		channelId = chInfo.ChannelId
	}

	ekey := fmt.Sprintf("get_channel_live_data_%v", channelId)
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.GetChannelLiveDataResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	chData := s.cacheClient.GetChannelLiveData(channelId)
	// 添加机器人观众数
	robotCnt, serverErr := s.robotCli.GetChannelRobotSize(ctx, in.Uid, in.ChannelId)
	if serverErr != nil {
		log.ErrorWithCtx(ctx, "Failed to GetChannelRobotSize err(%s) in.Uid(%d) in.ChannelId(%d)", serverErr.Error(), in.Uid, in.ChannelId)
	}
	chData.AudienceCnt += robotCnt

	out.LiveData = &pb.ChannelLiveData{
		AudienceCnt:         chData.AudienceCnt,
		LiveGiftValue:       uint32(chData.GiftValue),
		AnchorGiftValue:     uint32(chData.AnchorGiftValue),
		SendGiftAudienceCnt: chData.SendGiftAudienceCnt,
		BeginTime:           uint32(chData.BeginTime),
		EndTime:             uint32(chData.EndTime),
		KnightValue:         uint32(chData.KnightValue),
		GameFee:             uint32(chData.GameGiftValue),
	}

	easyCache.Set(ekey, out, conf.GetLiveCacheTs()*2)

	log.InfoWithCtx(ctx, "GetChannelLiveData req(%+v) resp(%+v)", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) GetChannelLiveTOPN(ctx context.Context, in *pb.GetChannelLiveTOPNReq) (out *pb.GetChannelLiveTOPNResp, err error) {
	out = &pb.GetChannelLiveTOPNResp{
		LiveData: make([]*pb.ChannelLiveData, 0),
	}

	records, serr := s.mysqlStore.GetChannelLiveRecords(ctx, in.Uid, in.Off, in.Count)
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetChannelLiveTOPN GetChannelLiveRecords serr:%v", serr)
	}

	for _, re := range records {
		var virtualTs uint32
		var virtualFee uint32
		if re.AnchorType == uint32(pb.AnchorType_Anchor_Type_Virtual) && re.EndTime > re.BeginTime {
			virtualTs = re.EndTime - re.BeginTime
		}
		if re.AnchorType == uint32(pb.AnchorType_Anchor_Type_Virtual) {
			virtualFee = re.Score
		}

		out.LiveData = append(out.LiveData, &pb.ChannelLiveData{
			LiveTime:            re.EndTime - re.BeginTime,
			AudienceCnt:         re.Audiences,
			LiveGiftValue:       re.Score,
			AnchorGiftValue:     re.AnchorGiftValue,
			SendGiftAudienceCnt: re.SendGiftUserCnt,
			AddFans:             re.AddFans,
			AddGroupFans:        re.AddGroupFans,
			BeginTime:           re.BeginTime,
			EndTime:             re.EndTime,
			KnightValue:         re.KnightValue,
			GameFee:             re.GameFee,
			GameTs:              re.GameTs,
			VirtualFee:          virtualFee,
			VirtualTs:           virtualTs,
		})
	}

	return out, nil
}

// 主播积分
func (s *ChannelLiveMgrServer) AddChannelLiveAnchorScore(ctx context.Context, in *pb.AddChannelLiveAnchorScoreReq) (out *pb.AddChannelLiveAnchorScoreResp, err error) {
	out = &pb.AddChannelLiveAnchorScoreResp{}

	log.InfoWithCtx(ctx, "AddChannelLiveAnchorScore in %+v", ctx, in)

	finalScore, err := s.mysqlStore.AddAnchorScore(ctx, in.GetUid(), in.GetScore(), in.GetOrderId(), in.GetSourceType(), in.GetOutsideTime(), in.GetMissionAwardDetail())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddChannelLiveAnchorScore fail. req:%+v, err:%v", in, err)
		return out, err
	}
	out.FinalScore = finalScore

	return out, nil
}

func (s *ChannelLiveMgrServer) GetChannelLiveAnchorScore(ctx context.Context, in *pb.GetChannelLiveAnchorScoreReq) (out *pb.GetChannelLiveAnchorScoreResp, err error) {
	out = &pb.GetChannelLiveAnchorScoreResp{}

	score, err := s.mysqlStore.GetAnchorScore(ctx, in.Uid)

	if err != nil {
		return out, err
	}

	out.Score = score
	return out, nil
}

func (s *ChannelLiveMgrServer) BatchGetAllChannelLive(ctx context.Context, in *pb.BatchGetAllChannelLiveReq) (out *pb.BatchGetAllChannelLiveResp, err error) {
	out = &pb.BatchGetAllChannelLiveResp{}

	log.DebugWithCtx(ctx, "BatchGetAllChannelLive in:%v", in)

	ekey := "batch_get_all_channel_live"
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.BatchGetAllChannelLiveResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	statList, err := s.cacheClient.GetAllChannelLiveStatus()
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAllChannelLiveStatus failed in:%v err:%v", in, err)
		return out, err
	}

	for _, sta := range statList {
		out.ChannelList = append(out.ChannelList, sta.ChannelId)
		out.LiveChannelList = append(out.LiveChannelList, sta)
	}

	easyCache.Set(ekey, out, conf.GetLiveCacheTs()*50)

	return out, nil
}

func (s *ChannelLiveMgrServer) ShutDown() {
	s.stop <- true
	s.knightKfkSub.Close()
	s.ykwKfkSub.Close()
	s.extGameKfkSub.Close()
	s.channelKfkSub.Close()
	s.micKfkSub.Close()
	s.presentKfkSub.Close()
	s.channelLiveProd.Close()
	s.pkApplyProd.Close()
}

func (s *ChannelLiveMgrServer) SendTTMsg(uid uint32, content, hlight, url string) error {
	msg := new(apiPB.ImMsg)
	msg.ImType = &apiPB.ImType{
		SenderType:   uint32(apiPB.IM_SENDER_TYPE_IM_SENDER_NORMAL),
		ReceiverType: uint32(apiPB.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}
	msg.FromUid = 10000 // TT语音助手
	msg.ToIdList = []uint32{uid}
	msg.ImContent = &apiPB.ImContent{}
	msg.ImContent.TextHlUrl = &apiPB.ImTextWithHighlightUrl{
		Content:    content,
		Hightlight: hlight,
		Url:        url,
	}
	msg.ImType.ContentType = uint32(apiPB.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	msg.Platform = apiPB.Platform_UNSPECIFIED
	msg.AppPlatform = "all"

	err := s.apiClient.SendImMsg(s.ctx, uid, protocol.TT, []*apiPB.ImMsg{msg}, true)
	if err != nil {
		log.Errorf("ChannelLiveMgrServer SendTTMsg get fail uid:%d err:%v", uid, err)
	}

	totalPre, ok := notify.NotifySync(uid, notify.ImMsgV2)

	log.Infof("ChannelLiveMgrServer SendTTMsg get fail uid:%d totalPre:%v ok:%v", uid, totalPre, ok)

	return nil
}

func (s *ChannelLiveMgrServer) BatchGetGroupFansGiftValue(ctx context.Context, req *pb.BatchGetGroupFansGiftValueReq) (*pb.BatchGetGroupFansGiftValueResp, error) {
	log.DebugWithCtx(ctx, "BatchGetGroupFansGiftValue req(%+v)", req)
	resp := &pb.BatchGetGroupFansGiftValueResp{}

	ekey := fmt.Sprintf("batch_get_group_Fans_gift_value_%v", req.AnchorUid)
	v, ok := easyCache.Get(ekey)
	if ok && v != nil {
		resp, ok := v.(*pb.BatchGetGroupFansGiftValueResp)
		if ok && resp != nil {
			return resp, nil
		}
	}

	valueList, err := s.cacheClient.BatchGetGiftValue(req.AnchorUid, req.UidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "Failed to BatchGetGroupFansGiftValue err(%s) anchorUid(%d) uidList(%v)", err, req.AnchorUid, req.UidList)
	}

	resp.GiftValueList = valueList

	if len(valueList) > 128 {
		easyCache.Set(ekey, resp, 120)
	}

	log.DebugWithCtx(ctx, "BatchGetGroupFansGiftValue resp(%+v)", resp)
	return resp, nil
}

func (s *ChannelLiveMgrServer) GetUserPushCnt(ctx context.Context, req *pb.GetUserPushCntReq) (*pb.GetUserPushCntResp, error) {
	resp := &pb.GetUserPushCntResp{
		Result: false,
	}

	log.DebugWithCtx(ctx, "GetUserPushCnt req:%v", req)

	nowTs := time.Now()
	resp.ValidUidList = s.cacheClient.GetValidPushUidList(nowTs, req.UidList, req.AnchorUid)

	log.DebugWithCtx(ctx, "GetUserPushCnt req(%+v) resp(%v)", req, resp)

	return resp, nil
}

func (s *ChannelLiveMgrServer) GetPkInfo(ctx context.Context, req *pb.GetPkInfoReq) (*pb.GetPkInfoResp, error) {
	resp := &pb.GetPkInfoResp{
		APkInfo:      &pb.PkSingleInfo{},
		BPkInfo:      &pb.PkSingleInfo{},
		PkCommonInfo: &pb.PkCommonInfo{},
	}

	log.DebugWithCtx(ctx, "GetPkInfo in:%v", req)

	apkstate := pk.PkState{
		ChannelID_A: req.ChannelId,
		ChannelID_B: 0,
	}
	apkinfo, serr := apkstate.GetPkInfo()

	if serr != nil {
		log.ErrorWithCtx(ctx, "GetPkInfo req:%v serr:%v", req, serr)
		return resp, serr
	}

	if apkinfo.TChannelID == 0 {
		log.ErrorWithCtx(ctx, "GetPkInfo req:%v TChannelID empty", req)
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLiveIdInvalid)
	}
	apkstate.ChannelID_B = apkinfo.TChannelID

	bpkstate := pk.PkState{
		ChannelID_A: apkinfo.TChannelID,
		ChannelID_B: 0,
	}

	bpkinfo, serr := bpkstate.GetPkInfo()
	if serr != nil {
		log.ErrorWithCtx(ctx, "GetPkInfo req:%v serr:%v", req, serr)
		return resp, serr
	}

	if bpkinfo.TChannelID != req.ChannelId {
		log.ErrorWithCtx(ctx, "GetPkInfo pkstate confusion %v %v", bpkinfo.TChannelID, req.ChannelId)
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLiveIdInvalid)
	}

	passSec := uint32(time.Now().Unix()) - bpkinfo.BeginTime
	state := conf.GetGConfig().GetState(int(passSec))

	extraConf := conf.GetExtraTimeConf()

	firstStrUidInfo, cid := apkstate.GetFirstShootUid()
	firstUidInfo := s.mgr.ParseStrUidInfo(cid, firstStrUidInfo)
	commonInfo := &pb.PkCommonInfo{
		BeginTime:        bpkinfo.BeginTime,
		PkStatus:         pb.EnumChannelLivePKStatus(state),
		MicList:          make(map[uint32]*pb.PkMicSpace),
		FirstKillUid:     firstUidInfo.GetUid(),
		FirstKillCid:     cid,
		IsExtraTime:      bpkinfo.IsExtra,
		PkExtraTimeRule:  extraConf.Rule,
		ExtraLeftTime:    uint32(extraConf.LeftTs),
		IsOpenExtraTime:  bpkinfo.IsOpenExtra,
		FirstKillUwkinfo: firstUidInfo,
	}

	resp.PkCommonInfo = commonInfo
	aInfo := &pb.PkSingleInfo{
		Uid:           bpkinfo.TUID,
		ChannelId:     bpkinfo.TChannelID,
		ChannelLiveId: 0, //TODO
		PkScore:       apkinfo.PKScore,
		EffectCnt:     apkinfo.EffectCnt,
		MicFlag:       pb.ChannelLiveOpponentMicFlag(apkinfo.MicFlag),
	}
	aMicList, gerr := apkstate.GetMicList()
	if gerr == nil {
		for _, mic := range aMicList {
			commonInfo.MicList[mic.Uid] = &pb.PkMicSpace{
				Uid:                  mic.Uid,
				Account:              "",
				Nick:                 "",
				MicId:                0,
				VoiceId:              mic.VoiceID,
				ChannelVideoClientId: mic.VideoID,
			}
		}
	}

	bInfo := &pb.PkSingleInfo{
		Uid:           apkinfo.TUID,
		ChannelId:     apkinfo.TChannelID,
		ChannelLiveId: 0,
		PkScore:       bpkinfo.PKScore,
		EffectCnt:     bpkinfo.EffectCnt,
		MicFlag:       pb.ChannelLiveOpponentMicFlag(bpkinfo.MicFlag),
	}

	bMicList, gerr := bpkstate.GetMicList()
	if gerr == nil {
		for _, mic := range bMicList {
			commonInfo.MicList[mic.Uid] = &pb.PkMicSpace{
				Uid:                  mic.Uid,
				Account:              "",
				Nick:                 "",
				MicId:                0,
				VoiceId:              mic.VoiceID,
				ChannelVideoClientId: mic.VideoID,
			}
		}
	}

	resp.APkInfo = aInfo
	resp.BPkInfo = bInfo

	log.DebugWithCtx(ctx, "GetPkInfo req:%v resp:%v", req, resp)

	return resp, nil
}

// 麦位信息变化处理
func (s *ChannelLiveMgrServer) ReportClientIDChange(ctx context.Context, req *pb.ReportClientIDChangeReq) (*pb.ReportClientIDChangeResp, error) {
	resp := &pb.ReportClientIDChangeResp{MicList: make([]*pb.PkMicSpace, 0)}

	log.DebugWithCtx(ctx, "ReportClientIDChange in:%v", req)

	pkstate := &pk.PkState{
		ChannelID_A: req.ChannelId,
	}

	mapMic, _ := pkstate.OnVoiceIDChange(&pk.MicInfo{
		Uid:       req.Uid,
		ChannelID: req.ChannelId,
		VoiceID:   req.ClientId,
		VideoID:   req.GetChannelVideoClientId(),
	})

	pkInfo, err := pkstate.GetPkInfo()
	if err == nil && pkInfo.TChannelID > 0 {
		go func() { //推送
			s.mgr.PushMicInfo(req.ChannelId, pkInfo.TChannelID, mapMic)
		}()
	}

	log.InfoWithCtx(ctx, "ReportClientIDChange end req:%v pkInfo:%v resp:%v", req, pkInfo, resp)

	return resp, nil
}

// PK的时候屏蔽对面主播声音
func (s *ChannelLiveMgrServer) SetChannelLiveOpponentMicFlag(ctx context.Context, req *pb.SetChannelLiveOpponentMicFlagReq) (*pb.SetChannelLiveOpponentMicFlagResp, error) {
	resp := &pb.SetChannelLiveOpponentMicFlagResp{}

	pkstate := pk.PkState{
		ChannelID_A: req.ChannelId,
	}

	pkinfo, err := pkstate.GetPkInfo()
	if err != nil {
		return resp, err
	}

	err = pkstate.SetMicFlag(uint32(req.OptMicFlag))
	if err != nil {
		log.ErrorWithCtx(ctx, "SetChannelLiveOpponentMicFlag SetMicFlag err:%v", err)
		return resp, err
	}

	resp.TargetChannelId = pkinfo.TChannelID

	return resp, nil
}

func (s *ChannelLiveMgrServer) StartPkMatch(ctx context.Context, req *pb.StartPkMatchReq) (*pb.StartPkMatchResp, error) {
	resp := &pb.StartPkMatchResp{}

	log.DebugWithCtx(ctx, "StartPkMatchS req:%v", req)

	// 先判断是否处于指定pk赛事期间
	nowTs := uint32(time.Now().Unix())
	pkInfoList, err := s.cacheClient.GetAnchorAppointPkInfoList(req.GetUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetAnchorAppointPkInfoList failed in:%v err:%v", req, err)
		return resp, err
	}

	for _, pkInfo := range pkInfoList {
		log.DebugWithCtx(ctx, "StartPkMatch req:%v anchorPkInfo:%v ts:%d", req, pkInfo, nowTs)
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			isExist, err := s.cacheClient.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "StartPkMatch CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", req, pkInfo, err)
				continue
			}
			if isExist {
				log.ErrorWithCtx(ctx, "StartPkMatch anchor is in appoint ok req:%v anchorPkInfo:%v", req, pkInfo)
				return resp, protocol.NewExactServerError(nil, status.ErrChannelLiveAppointPkIngNoLaunchPk)
			}
		}
	}

	if err := s.mgr.CheckIsPkCntLimit(req.Uid, true); nil != err {
		log.ErrorWithCtx(ctx, "StartPkMatch uid:%v", req.Uid)
		return resp, err //protocol.NewExactServerError(nil, status.ErrChannelLivePkCntLimit, "每日20:00～22:00期间只允许连麦PK两次哦～")
	}

	//先清理掉之前的匹配状态
	match.UpdateMatchState(s.cacheClient, req.Uid, req.ChannelId, 0, pb.EnumChannelLiveStatus_CLOSE)

	matchTy := conf.GetMatchType(uint32(time.Now().Unix()))
	if matchTy != req.MatchType {
		log.ErrorWithCtx(ctx, "StartPkMatchSX invalid match type req:%v", req)
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLivePkMatchInvalidTy, "匹配类型跟服务端不一致")
	}

	matcher := match.GetMatcher(matchTy)
	if matcher == nil {
		log.ErrorWithCtx(ctx, "StartPkMatchSX GetMatcher match empty")
		return resp, protocol.NewExactServerError(nil, status.ErrChannelLivePkMatchInvalidTy, "没有匹配类型")
	}

	var pkValue, _ = s.mgr.GetPKMatchValue(req.Uid, matchTy)
	matcher.Add(s.cacheClient, req.Uid, req.ChannelId, pkValue)

	s.mysqlStore.AddMatchLog(ctx, int64(req.Uid), int64(pb.EnumPkMatch_PKM_Matching), int64(matchTy), int64(pkValue), 0, 0)

	log.DebugWithCtx(ctx, "StartPkMatchS resp:%v matchTy:%v pkValue:%v", resp, matchTy, pkValue)

	return resp, nil
}

func (s *ChannelLiveMgrServer) CancelPkMatch(ctx context.Context, req *pb.CancelPkMatchReq) (*pb.CancelPkMatchResp, error) {
	resp := &pb.CancelPkMatchResp{}

	match.UpdateMatchState(s.cacheClient, req.Uid, req.ChannelId, 0, pb.EnumChannelLiveStatus_CLOSE)

	s.mysqlStore.AddMatchLog(ctx, int64(req.Uid), int64(pb.EnumPkMatch_PKM_Match_Close), 0, 0, 0, 0)

	return resp, nil
}

func (s *ChannelLiveMgrServer) GetPKMatchInfo(ctx context.Context, req *pb.GetPKMatchInfoReq) (*pb.GetPKMatchInfoResp, error) {
	resp := &pb.GetPKMatchInfoResp{}

	log.DebugWithCtx(ctx, "GetPKMatchInfo req:%v", req)

	matchTy := conf.GetMatchType(uint32(time.Now().Unix()))
	matchValue, levelName := s.mgr.GetPKMatchValue(req.Uid, matchTy)

	resp.CptInfo = &pb.PkCompetitionInfo{
		Level:     matchValue,
		LevelName: fmt.Sprintf("我的段位:%v%v分", levelName, matchValue),
	}
	resp.PkMatchTy = matchTy
	resp.PkLimitInfo = s.mgr.GetPkLimitInfo()

	log.DebugWithCtx(ctx, "GetPKMatchInfo resp:%v", resp)

	return resp, nil
}

func (s *ChannelLiveMgrServer) BatGetChannelLiveInfo(ctx context.Context, req *pb.BatGetChannelLiveInfoReq) (*pb.BatGetChannelLiveInfoResp, error) {
	resp := &pb.BatGetChannelLiveInfoResp{}

	if len(req.GetUidList()) > 100 {
		return resp, errors.New("一次批量数量不能超过100")
	}

	// 先读缓存
	uid2Info, err := s.cacheClient.GetAnchorByUidList(req.GetUidList()...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo GetAnchorByUidList failed req:%v err:%v", req, err)
		return resp, err
	}

	noCacheList := make([]uint32, 0)
	for _, uid := range req.GetUidList() {
		if _, ok := uid2Info[uid]; !ok {
			noCacheList = append(noCacheList, uid)
		}
	}

	if len(noCacheList) != 0 {
		log.DebugWithCtx(ctx, "BatGetChannelLiveInfo get from db req:%v list:%v", req, noCacheList)

		liveInfoList, err := s.mysqlStore.GetChannelLiveInfoByUidList(ctx, noCacheList)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo GetChannelLiveInfoByUidList failed req:%v err:%v", req, err)
			return resp, err
		}

		if len(liveInfoList) != 0 {
			noCacheCidList := make([]uint32, 0)
			for _, info := range liveInfoList {
				noCacheCidList = append(noCacheCidList, info.ChannelID)
			}

			tagResp, err := s.enterCli.BatchGetChannelTag(ctx, 0, &entertainmentPB.BatchGetChannelTagReq{
				ChannelIdList: noCacheCidList,
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo BatchGetChannelTag failed req:%v err:%v", req, err)
				return resp, err
			}

			mapTag := make(map[uint32]uint32)
			if len(noCacheCidList) == len(tagResp.GetChannelTagList()) {
				for i, info := range tagResp.GetChannelTagList() {
					mapTag[noCacheCidList[i]] = info.GetTagId()
				}
			} else {
				log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo tag resp err req:%v list:%v", req, noCacheCidList)
			}

			for _, info := range liveInfoList {
				pbInfo := &pb.ChannelLiveInfo{
					Uid:        info.Uid,
					ChannelId:  info.ChannelID,
					BeginTime:  info.BeginTime,
					EndTime:    info.EndTime,
					CreateTime: info.CreateTime,
					OperName:   info.OperUser,
					TagId:      mapTag[info.ChannelID],
					Authority:  info.Authority,
				}
				uid2Info[info.Uid] = pbInfo

				// 设置缓存
				err = s.cacheClient.SetChannelLiveInfo(info.Uid, pbInfo)
				if err != nil {
					log.ErrorWithCtx(ctx, "BatGetChannelLiveInfo SetChannelLiveInfo failed req:%v info:%v err:%v", req, req, pbInfo, err)
				}
			}
		}

	}

	for _, info := range uid2Info {
		resp.InfoList = append(resp.InfoList, info)
	}

	log.DebugWithCtx(ctx, "BatGetChannelLiveInfo end req:%v resp:%v", req, resp)
	return resp, nil
}
