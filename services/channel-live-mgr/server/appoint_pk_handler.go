package server

import (
	"context"
	"database/sql"
	"time"

	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	accountPB "golang.52tt.com/protocol/services/account"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	pbLogic "golang.52tt.com/protocol/services/channellivemgr/logic"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"golang.52tt.com/services/channel-live-mgr/metrics"
)

// AppointPkHandler 指定PK接口处理器
// 统一管理所有指定PK相关的gRPC接口
type AppointPkHandler struct {
	server *ChannelLiveMgrServer
}

// NewAppointPkHandler 创建指定PK处理器
func NewAppointPkHandler(server *ChannelLiveMgrServer) *AppointPkHandler {
	return &AppointPkHandler{
		server: server,
	}
}

// ==================== 管理接口 (运营后台使用) ====================

// AddAppointPkInfo 添加指定PK信息
func (h *AppointPkHandler) AddAppointPkInfo(ctx context.Context, in *pb.AddAppointPkInfoReq) (*pb.AddAppointPkInfoResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("AddAppointPkInfo", time.Since(start))
	}()

	out := &pb.AddAppointPkInfoResp{}

	_, pkTs := conf.GetAppointPkTsConf()
	if pkTs == 0 {
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("add", "config_error")
		return out, protocol.NewExactServerError(nil, -2, "指定pk每场pk时间为0")
	}

	nowTs := uint32(time.Now().Unix())
	log.DebugWithCtx(ctx, "AddAppointPkInfo begin in:%v pkTs:%d nowTs:%d", in, pkTs, nowTs)

	// 验证时间冲突
	err := h.server.checkAppointPkTs(ctx, in.GetInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "AddAppointPkInfo checkAppointPkTs in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("add", "time_conflict")
		return out, err
	}

	// 数据库事务操作
	err = h.server.mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		appointId, sqlErr := h.server.mysqlStore.AddAppointPkInfo(ctx, tx, in.GetInfo().GetUid(),
			in.GetInfo().GetBeginTs(), in.GetInfo().GetEndTs(), nowTs, in.GetInfo().GetOperator())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "AddAppointPkInfo mysqlStore.AddAppointPkInfo failed in:%v err:%v", in, sqlErr)
			return sqlErr
		}

		for _, rivalInfo := range in.GetInfo().GetRivalList() {
			sqlErr = h.server.mysqlStore.AddAppointPkRivalInfo(ctx, tx, appointId, rivalInfo.GetUid(),
				rivalInfo.GetPkBeginTs(), nowTs)
			if sqlErr != nil {
				log.ErrorWithCtx(ctx, "AddAppointPkInfo mysqlStore.AddAppointPkRivalInfo failed in:%v err:%v", in, sqlErr)
				return sqlErr
			}
		}
		return nil
	})

	if err != nil {
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("add", "database_error")
		return out, err
	}

	metrics.GlobalAppointPkMetrics.RecordAppointPkSuccess("add")
	log.DebugWithCtx(ctx, "AddAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

// UpdateAppointPkInfo 更新指定PK信息
func (h *AppointPkHandler) UpdateAppointPkInfo(ctx context.Context, in *pb.UpdateAppointPkInfoReq) (*pb.UpdateAppointPkInfoResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("UpdateAppointPkInfo", time.Since(start))
	}()

	out := &pb.UpdateAppointPkInfoResp{}

	_, pkTs := conf.GetAppointPkTsConf()
	if pkTs == 0 {
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("update", "config_error")
		return out, protocol.NewExactServerError(nil, -2, "指定pk每场pk时间为0")
	}

	nowTs := uint32(time.Now().Unix())

	// 检查是否已发送推送
	mapId2PkInfo, err := h.server.cacheClient.BatchGetAppointPkInfo([]uint32{in.GetInfo().GetAppointId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo BatchGetAppointPkInfo failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordCacheError("appoint_pk", "update", "batch_get_failed")
		return out, err
	}

	pkInfo, err := h.server.mysqlStore.GetAppointPkInfoById(ctx, in.GetInfo().GetAppointId())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo GetAppointPkInfoById failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordQueryError("UpdateAppointPkInfo", "appoint_pk_info", "get_by_id_failed")
		return out, err
	}

	// 检查更新时间限制
	if pkInfo.BeginTs <= (nowTs+60) && pkInfo.EndTs >= nowTs {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo unable update in:%v", in)
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("update", "time_limit")
		return out, protocol.NewExactServerError(nil, -2, "不能进行更新操作，离开始时间少于1分钟")
	}

	// 验证时间冲突
	err = h.server.checkAppointPkTs(ctx, in.GetInfo())
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateAppointPkInfo checkAppointPkTs in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("update", "time_conflict")
		return out, err
	}

	// 数据库事务操作
	err = h.server.mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		sqlErr := h.server.mysqlStore.UpdateAppointPkInfo(ctx, tx, in.GetInfo().GetAppointId(),
			in.GetInfo().GetUid(), in.GetInfo().GetBeginTs(), in.GetInfo().GetEndTs(), nowTs, in.GetInfo().GetOperator())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "UpdateAppointPkInfo mysqlStore.UpdateAppointPkInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}

		sqlErr = h.server.mysqlStore.DelAppointPkRivalInfo(ctx, tx, in.GetInfo().GetAppointId())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "UpdateAppointPkInfo mysqlStore.DelAppointPkRivalInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}

		for _, rivalInfo := range in.GetInfo().GetRivalList() {
			sqlErr = h.server.mysqlStore.AddAppointPkRivalInfo(ctx, tx, in.GetInfo().GetAppointId(),
				rivalInfo.GetUid(), rivalInfo.GetPkBeginTs(), nowTs)
			if sqlErr != nil {
				log.ErrorWithCtx(ctx, "UpdateAppointPkInfo mysqlStore.AddAppointPkRivalInfo failed in:%v sqlErr:%v", in, sqlErr)
				return sqlErr
			}
		}
		return nil
	})

	if err != nil {
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("update", "database_error")
		return out, err
	}

	// 如果已发送推送，需要重新发送
	if _, exists := mapId2PkInfo[in.GetInfo().GetAppointId()]; exists {
		// TODO: 重新发送推送逻辑
		log.InfoWithCtx(ctx, "UpdateAppointPkInfo need resend push notification for appointId:%d", in.GetInfo().GetAppointId())
	}

	metrics.GlobalAppointPkMetrics.RecordAppointPkSuccess("update")
	log.DebugWithCtx(ctx, "UpdateAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

// DelAppointPkInfo 删除指定PK信息
func (h *AppointPkHandler) DelAppointPkInfo(ctx context.Context, in *pb.DelAppointPkInfoReq) (*pb.DelAppointPkInfoResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("DelAppointPkInfo", time.Since(start))
	}()

	out := &pb.DelAppointPkInfoResp{}

	// 检查是否已发送推送
	mapId2PkInfo, err := h.server.cacheClient.BatchGetAppointPkInfo([]uint32{in.GetAppointId()})
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAppointPkInfo BatchGetAppointPkInfo failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordCacheError("appoint_pk", "delete", "batch_get_failed")
		return out, err
	}

	nowTs := uint32(time.Now().Unix())
	pkInfo, err := h.server.mysqlStore.GetAppointPkInfoById(ctx, in.GetAppointId())
	if err != nil {
		log.ErrorWithCtx(ctx, "DelAppointPkInfo GetAppointPkInfoById failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordQueryError("DelAppointPkInfo", "appoint_pk_info", "get_by_id_failed")
		return out, err
	}

	// 检查删除时间限制
	if pkInfo.BeginTs <= (nowTs+60) && pkInfo.EndTs >= nowTs {
		log.ErrorWithCtx(ctx, "DelAppointPkInfo unable delete in:%v", in)
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("delete", "time_limit")
		return out, protocol.NewExactServerError(nil, -2, "不能进行删除操作，离开始时间少于1分钟")
	}

	// 数据库事务操作
	err = h.server.mysqlStore.Transaction(ctx, func(tx *sql.Tx) error {
		sqlErr := h.server.mysqlStore.DelAppointPkInfo(ctx, tx, in.GetAppointId())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "DelAppointPkInfo mysqlStore.DelAppointPkInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}

		sqlErr = h.server.mysqlStore.DelAppointPkRivalInfo(ctx, tx, in.GetAppointId())
		if sqlErr != nil {
			log.ErrorWithCtx(ctx, "DelAppointPkInfo mysqlStore.DelAppointPkRivalInfo failed in:%v sqlErr:%v", in, sqlErr)
			return sqlErr
		}
		return nil
	})

	if err != nil {
		metrics.GlobalAppointPkMetrics.RecordAppointPkFailure("delete", "database_error")
		return out, err
	}

	// 如果已发送推送，需要发送取消通知
	if _, exists := mapId2PkInfo[in.GetAppointId()]; exists {
		// TODO: 发送取消推送逻辑
		log.InfoWithCtx(ctx, "DelAppointPkInfo need send cancel notification for appointId:%d", in.GetAppointId())
	}

	metrics.GlobalAppointPkMetrics.RecordAppointPkSuccess("delete")
	log.DebugWithCtx(ctx, "DelAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

// GetAppointPkInfoList 获取指定PK信息列表
func (h *AppointPkHandler) GetAppointPkInfoList(ctx context.Context, in *pb.GetAppointPkInfoListReq) (*pb.GetAppointPkInfoListResp, error) {
	start := time.Now()
	defer func() {
		metrics.GlobalAppointPkMetrics.RecordTimerDuration("GetAppointPkInfoList", time.Since(start))
	}()

	out := &pb.GetAppointPkInfoListResp{}

	log.DebugWithCtx(ctx, "GetAppointPkInfoList begin in:%v", in)

	// 使用优化后的批量查询方法，避免N+1查询问题
	pkInfoListWithRivals, err := h.server.mysqlStore.GetAppointPkInfoListOptimized(ctx, in.GetUid(), in.GetBeginTs(), in.GetEndTs(),
		in.GetPage()*in.GetPageSize(), in.GetPageSize(), pb.GetAppointPkInfoListReq_QueryType(in.GetQueryType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfoList GetAppointPkInfoListOptimized failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordQueryError("GetAppointPkInfoList", "appoint_pk_info", "optimized_query_failed")
		return out, err
	}

	// 获取总数
	totalCnt, err := h.server.mysqlStore.GetAppointPkInfoTotalCnt(ctx, in.GetUid(), in.GetBeginTs(), in.GetEndTs(),
		pb.GetAppointPkInfoListReq_QueryType(in.GetQueryType()))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfoList GetAppointPkInfoTotalCnt failed in:%v err:%v", in, err)
		metrics.GlobalAppointPkMetrics.RecordQueryError("GetAppointPkInfoList", "appoint_pk_info", "total_count_failed")
		return out, err
	}

	log.DebugWithCtx(ctx, "GetAppointPkInfoList pkInfoListWithRivals count:%d", len(pkInfoListWithRivals))

	// 直接使用优化后的数据结构，无需额外查询
	for _, pkInfoWithRivals := range pkInfoListWithRivals {
		rivalList := make([]*pb.PkRivalInfo, 0)
		for _, rival := range pkInfoWithRivals.Rivals {
			tmpInfo := &pb.PkRivalInfo{
				Uid:       rival.Uid,
				PkBeginTs: rival.BeginTs,
			}
			rivalList = append(rivalList, tmpInfo)
		}

		tmpInfo := &pb.AppointPkInfo{
			AppointId: pkInfoWithRivals.AppointId,
			Uid:       pkInfoWithRivals.Uid,
			BeginTs:   pkInfoWithRivals.BeginTs,
			EndTs:     pkInfoWithRivals.EndTs,
			RivalList: rivalList,
			UpdateTs:  pkInfoWithRivals.UpdateTs,
			Operator:  pkInfoWithRivals.Operator,
		}

		out.InfoList = append(out.InfoList, tmpInfo)
	}

	out.TotalCnt = totalCnt
	out.NextPage = in.GetPage() + 1
	if uint32(len(pkInfoListWithRivals)) < in.GetPageSize() {
		out.NextPage = 0
	}

	metrics.GlobalAppointPkMetrics.RecordQueryTotal("GetAppointPkInfoList", "appoint_pk_info", "success")
	log.DebugWithCtx(ctx, "GetAppointPkInfoList end in:%v out:%v", in, out)
	return out, nil
}
