package server

import (
	"context"
	"fmt"
	"github.com/go-redis/redis"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	pbLogic "golang.52tt.com/protocol/app/channel-live-logic"
	accountPB "golang.52tt.com/protocol/services/accountsvr"
	aapb "golang.52tt.com/protocol/services/apicenter/apiserver"
	pb "golang.52tt.com/protocol/services/channellivemgr"
	publicPB "golang.52tt.com/protocol/services/publicsvr"
	"golang.52tt.com/services/channel-live-mgr/cache"
	"golang.52tt.com/services/channel-live-mgr/conf"
	"golang.52tt.com/services/channel-live-mgr/mysql"
	"reflect"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	NeedProcAppointPkQueueCnt = 2
	NeedProcAppointPk         = "NeedProcAppointPk"

	WaitingAppointPkQueueCnt = 1
	WaitingAppointPk         = "WaitingAppointPk"

	WaitingAppointPkPushQueueCnt = 1
	WaitingAppointPkPush         = "WaitingAppointPkPush"
)

var mapValid2EventType = map[bool]pbLogic.AppointPkEventType{
	true:  pbLogic.AppointPkEventType_AppointPkWin,
	false: pbLogic.AppointPkEventType_AppointPkAbstain,
}

func (s *ChannelLiveMgrServer) StartAppointPkTimer() {
	go s.TimerHandle(60*time.Second, 0, s.TimerLoadNeedProcAppointPkFromMysql)

	for i := 0; i < NeedProcAppointPkQueueCnt; i++ {
		go s.TimerHandle(1*time.Second, i, s.TimerHandleNeedProcAppointPk)
	}

	for i := 0; i < WaitingAppointPkQueueCnt; i++ {
		go s.TimerHandle(2*time.Second, i, s.TimerHandleWaitingAppointPk)
	}

	for i := 0; i < WaitingAppointPkPushQueueCnt; i++ {
		go s.TimerHandle(2*time.Second, i, s.TimerHandleWaitingAppointPkPush)
	}
}

func (s *ChannelLiveMgrServer) TimerHandle(t time.Duration, idx int, handleFunc func(int)) {
	ticker := time.NewTicker(t)
	for {
		select {
		case <-s.stop:
			return
		case <-ticker.C:
			s.handleWithPanicCatch(idx, handleFunc)
		}
	}
}

func (s *ChannelLiveMgrServer) handleWithPanicCatch(idx int, handleFunc func(int)) {
	defer func() {
		if err := recover(); err != nil {
			funcName := runtime.FuncForPC(reflect.ValueOf(handleFunc).Pointer()).Name()
			log.Errorf("handleWithPanicCatch panic func:%s err:%v", funcName, err)
		}
	}()

	handleFunc(idx)
}

func (s *ChannelLiveMgrServer) getAnchorPushRivalMsg(ctx context.Context, anchorUid uint32, pkRivalList []*mysql.AppointPkRivalInfo) (map[uint32]string, error) {
	mapUid2RivalMsg := make(map[uint32]string, 0)

	uidList := []uint32{anchorUid}
	for _, rival := range pkRivalList {
		uidList = append(uidList, rival.Uid)
	}

	mapUid2User, err := s.accountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "getAnchorPushRivalMsg GetUsersMap failed list:%v err:%v", uidList, err)
		return mapUid2RivalMsg, err
	}

	myRivalMsg := ""
	for _, rival := range pkRivalList {
		if user, ok := mapUid2User[rival.Uid]; ok {
			tm := time.Unix(int64(rival.BeginTs), 0)
			myRivalMsg += fmt.Sprintf("[%02d:%02d 对战 %s]", tm.Hour(), tm.Minute(), user.GetNickname())
		}
	}
	mapUid2RivalMsg[anchorUid] = myRivalMsg

	for _, rival := range pkRivalList {
		if user, ok := mapUid2User[anchorUid]; ok {
			tm := time.Unix(int64(rival.BeginTs), 0)
			rivalMsg := fmt.Sprintf("[%02d:%02d 对战 %s]", tm.Hour(), tm.Minute(), user.GetNickname())
			mapUid2RivalMsg[rival.Uid] = rivalMsg
		}
	}

	return mapUid2RivalMsg, nil
}

func (s *ChannelLiveMgrServer) TimerLoadNeedProcAppointPkFromMysql(idx int) {
	getLock := s.cacheClient.GetAppointPkLock("LoadNeedProcFromMysql", 300)
	if !getLock {
		log.Infof("TimerLoadNeedProcAppointPkFromMysql get lock failed")
		return
	}

	defer func() {
		err := s.cacheClient.ReleaseAppointPkLock("LoadNeedProcFromMysql")
		if err != nil {
			log.Errorf("TimerLoadNeedProcAppointPkFromMysql ReleaseAppointPkLock failed err:%v", err)
		}
	}()

	ctx := context.Background()
	nowTm := time.Now()

	// 使用优化后的批量查询方法，一次性获取所有数据
	pkInfoListWithRivals, err := s.mysqlStore.GetNeedProcAppointPkListOptimized(ctx, uint32(nowTm.Unix()), uint32(nowTm.Unix()+3600))
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql GetNeedProcAppointPkListOptimized tm:%v err:%v", nowTm, err)
		return
	}

	log.InfoWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql begin tm:%v size:%v", nowTm, len(pkInfoListWithRivals))

	for _, pkInfoWithRivals := range pkInfoListWithRivals {
		isAlreadyProc, err := s.cacheClient.CheckAppointPkInfoIsExist(pkInfoWithRivals.AppointId)
		if err != nil || isAlreadyProc {
			log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql pkInfo already proc or get cache failed info:%v err:%v", pkInfoWithRivals, err)
			continue
		}

		if len(pkInfoWithRivals.Rivals) <= 0 {
			log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql no pk rival info info:%v len:%v", pkInfoWithRivals, len(pkInfoWithRivals.Rivals))
			continue
		}

		sort.Slice(pkRivalList, func(i, j int) bool { return pkRivalList[i].BeginTs <= pkRivalList[j].BeginTs })

		// 把最早pk开始时间放到处理队列中
		err = s.cacheClient.AddNeedProcAppointPk(pkInfo.AppointId, pkRivalList[0].BeginTs, pkInfo.AppointId%NeedProcAppointPkQueueCnt)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql AddNeedProcAppointPk failed info:%v err:%v", pkInfo, err)
			continue
		}

		// 设置主播指定pk信息
		err = s.setAnchorAppointPkInfoCache(pkInfo, pkRivalList)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql setAnchorAppointPkInfoCache failed info:%v err:%v", pkInfo, err)
			continue
		}

		err = s.setAppointPkInfoCache(pkInfo, pkRivalList)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql setAppointPkInfoCache failed info:%v err:%v", pkInfo, err)
			continue
		}

		// 判断本场pk是否已经推送
		pushCnt, err := s.cacheClient.IncrAppointPkFlag(pkInfo.AppointId)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql IncrAppointPkFlag failed info:%v err:%v", pkInfo, err)
			continue
		}

		if pushCnt == 1 {
			mapUid2RivalMsg, err := s.getAnchorPushRivalMsg(ctx, pkInfo.Uid, pkRivalList)
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql getAnchorPushRivalMsg info:%v err:%v", pkInfo, err)
				continue
			}

			for uid, msg := range mapUid2RivalMsg {
				log.DebugWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql uid:%d msg:%s", uid, msg)
				tm := time.Unix(int64(pkInfo.BeginTs), 0)
				pushMsg := fmt.Sprintf("【重要赛事通知】你将在%02d月%02d日有PK赛事，具体安排为%s"+
					"，请务必在规定时间内处于开启听听且不可处于暂时离开状态，请积极应战，争夺殊荣。", tm.Month(), tm.Day(), msg)
				s.PushAnchorFuWuHaoMsg(ctx, uid, pushMsg, false)
			}
		}
	}

	log.InfoWithCtx(ctx, "TimerLoadNeedProcAppointPkFromMysql end cost:%v size:%v", time.Since(nowTm), len(pkInfoList))
}

func (s *ChannelLiveMgrServer) setAppointPkInfoCache(pkInfo *mysql.AppointPkInfo, rivalList []*mysql.AppointPkRivalInfo) error {
	log.Infof("AddAppointPkInfoCache begin pkInfo:%v rival:%v", pkInfo, rivalList)

	list := make([]*pb.PkRivalInfo, 0)

	for _, rival := range rivalList {
		tmpInfo := &pb.PkRivalInfo{
			Uid:       rival.Uid,
			PkBeginTs: rival.BeginTs,
		}
		list = append(list, tmpInfo)
	}

	tmpInfo := &pb.AppointPkInfo{
		AppointId: pkInfo.AppointId,
		Uid:       pkInfo.Uid,
		BeginTs:   pkInfo.BeginTs,
		EndTs:     pkInfo.EndTs,
		RivalList: list,
		UpdateTs:  pkInfo.UpdateTs,
		Operator:  pkInfo.Operator,
	}

	nowTs := uint32(time.Now().Unix())
	expireTs := pkInfo.EndTs - nowTs + 7*24*3600
	err := s.cacheClient.AddAppointPkInfo(tmpInfo, expireTs)
	if err != nil {
		log.Errorf("AddAppointPkInfoCache cacheClient.AddAppointPkInfo failed info:%v err:%v", tmpInfo, err)
		return err
	}

	log.Infof("AddAppointPkInfoCache end pkInfo:%v rival:%v", pkInfo, rivalList)

	return nil
}

func (s ChannelLiveMgrServer) setAnchorAppointPkInfoCache(pkInfo *mysql.AppointPkInfo, rivalList []*mysql.AppointPkRivalInfo) error {
	log.Infof("setAnchorAppointPkInfoCache begin info:%v rival:%v", pkInfo, rivalList)

	anchorPkInfoList := make([]*cache.AnchorPkInfo, 0)

	for _, rival := range rivalList {
		tmpInfo := &cache.AnchorPkInfo{
			AppointId: pkInfo.AppointId,
			Uid:       rival.Uid,
			BeginTs:   pkInfo.BeginTs,
			EndTs:     pkInfo.EndTs,
		}
		anchorPkInfoList = append(anchorPkInfoList, tmpInfo)
	}

	tmpInfo := &cache.AnchorPkInfo{
		AppointId: pkInfo.AppointId,
		Uid:       pkInfo.Uid,
		BeginTs:   pkInfo.BeginTs,
		EndTs:     pkInfo.EndTs,
	}
	anchorPkInfoList = append(anchorPkInfoList, tmpInfo)

	for _, anchorPkInfo := range anchorPkInfoList {
		err := s.cacheClient.SetAnchorAppointPkInfo(anchorPkInfo)
		if err != nil {
			log.Errorf("setAnchorAppointPkInfoCache SetAnchorAppointPkInfo failed info:%v err:%v", anchorPkInfo, err)
			return err
		}

		nowTs := uint32(time.Now().Unix())
		// 清除用户过期的pk场次信息
		err = s.cacheClient.DelAnchorAppointPkInfoByScore(anchorPkInfo.Uid, 0, nowTs-5)
		if err != nil {
			log.Errorf("setAnchorAppointPkInfoCache DelAnchorAppointPkInfoByScore failed info:%v err:%v", anchorPkInfo, err)
		}
	}

	log.Infof("setAnchorAppointPkInfoCache end pkInfo:%v rival:%v", pkInfo, rivalList)

	return nil
}

func (s *ChannelLiveMgrServer) TimerHandleNeedProcAppointPk(idx int) {
	key := fmt.Sprintf("%s_%d", NeedProcAppointPk, idx)
	getLock := s.cacheClient.GetAppointPkLock(key, 300)
	if !getLock {
		//log.InfoWithCtx(ctx,"TimerHandleNeedProcAppointPk get lock failed")
		return
	}

	defer func() {
		err := s.cacheClient.ReleaseAppointPkLock(key)
		if err != nil {
			log.Errorf("TimerHandleNeedProcAppointPk ReleaseAppointPkLock failed lockKey:%s err:%v", key, err)
		}
	}()

	nowTm := time.Now()
	ctx := context.Background()

	mapId2BeginTs, err := s.cacheClient.GetNeedProcAppointPkList(0, uint32(nowTm.Unix()), uint32(idx))
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk GetNeedProcAppointPkList failed tm:%v idx:%d key:%s err:%v", nowTm, idx, key, err)
		return
	}

	needProcIdList := make([]uint32, 0)
	for id := range mapId2BeginTs {
		needProcIdList = append(needProcIdList, id)
	}

	log.InfoWithCtx(ctx, "TimerHandleNeedProcAppointPk begin tm:%v idx:%d key:%s size:%v", nowTm, idx, key, len(needProcIdList))

	mapId2PkInfo, err := s.cacheClient.BatchGetAppointPkInfo(needProcIdList)
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk BatchGetAppointPkInfo failed err:%v", err)
		return
	}

	for _, id := range needProcIdList {
		if pkInfo, ok := mapId2PkInfo[id]; ok {
			if beginTs, isOk := mapId2BeginTs[id]; isOk {
				log.DebugWithCtx(ctx, "TimerHandleNeedProcAppointPk mapId2BeginTs id:%d beginTs:%d", id, beginTs)
				var nextBeginTs uint32 = 0
				var rivalUid uint32 = 0
				tmpNowTs := uint32(time.Now().Unix())

				sort.Slice(pkInfo.GetRivalList(), func(i, j int) bool { return pkInfo.GetRivalList()[i].PkBeginTs <= pkInfo.GetRivalList()[j].PkBeginTs })

				for index, rivalInfo := range pkInfo.GetRivalList() {
					log.DebugWithCtx(ctx, "TimerHandleNeedProcAppointPk rival:%d beginTs:%d, index:%d", rivalInfo.Uid, rivalInfo.PkBeginTs, index)
					if beginTs == rivalInfo.PkBeginTs {
						rivalUid = rivalInfo.Uid
						if index != len(pkInfo.GetRivalList())-1 {
							nextBeginTs = pkInfo.GetRivalList()[index+1].PkBeginTs
						}
						break
					}
				}

				if nextBeginTs == 0 || pkInfo.GetEndTs() < tmpNowTs {
					// 没有下一个对手，直接删除队列定时, 或者赛事时间段已经结束
					err := s.cacheClient.DelNeedProcAppointPk(pkInfo.GetAppointId(), pkInfo.GetAppointId()%NeedProcAppointPkQueueCnt)
					if err != nil {
						log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk DelNeedProcAppointPk failed info:%v err:%v", pkInfo, err)
						continue
					}
				} else {
					// 有下一个对手， 更新队列时间
					err := s.cacheClient.AddNeedProcAppointPk(pkInfo.GetAppointId(), nextBeginTs, pkInfo.GetAppointId()%NeedProcAppointPkQueueCnt)
					if err != nil {
						log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk AddNeedProcAppointPk failed info:%v err:%v", pkInfo, err)
						continue
					}
				}

				countDownTs, _ := conf.GetAppointPkTsConf()
				if countDownTs == 0 {
					log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk count down ts is zero info:%v ts:%d", pkInfo, beginTs)
					continue
				}

				if rivalUid == 0 || tmpNowTs > countDownTs+beginTs {
					log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk no rival uid or expire rivalUid:%d info:%v ts:%d", rivalUid, pkInfo, beginTs)
					continue
				}

				// 判断pk双方是处于有效pk状态
				myIsValid, otherIsValid, _, _ := s.getAppointPkResByLiveStatus(ctx, pkInfo.GetUid(), rivalUid)

				log.DebugWithCtx(ctx, "TimerHandleNeedProcAppointPk %d %d myValid:%v otherValid:%v", pkInfo.GetUid(), rivalUid, myIsValid, otherIsValid)
				// 都无效，不处理
				if !myIsValid && !otherIsValid {
					continue
				}

				// 避免过期的赛事配置在后台重新启用或者更新，删除上一次应战标志
				err := s.cacheClient.DelAcceptAppointPkFlag(pkInfo.GetUid(), rivalUid, pkInfo.GetAppointId())
				if err != nil {
					log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk DelAcceptAppointPkFlag failed info:%v err:%v", pkInfo, err)
					continue
				}

				uidList := []uint32{rivalUid, pkInfo.Uid}
				mapUid2User, err := s.accountCli.GetUsersMap(ctx, uidList)
				if err != nil {
					log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk GetUsersMap failed info:%v uid:%d err:%v", pkInfo, rivalUid, err)
					continue
				}

				countDownEndTs := beginTs + countDownTs

				cacheErr := s.cacheClient.AddWaitingAppointPk(pkInfo.GetAppointId(), pkInfo.GetUid(), rivalUid, countDownEndTs, pkInfo.GetAppointId()%WaitingAppointPkQueueCnt)
				if cacheErr != nil {
					log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk AddWaitingAppointPk failed info:%v err:%v", pkInfo, cacheErr)
					continue
				}

				cacheErr = s.cacheClient.AddWaitingAppointPk(pkInfo.GetAppointId(), rivalUid, pkInfo.GetUid(), countDownEndTs, pkInfo.GetAppointId()%WaitingAppointPkQueueCnt)
				if cacheErr != nil {
					log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk AddWaitingAppointPk failed info:%v err:%v", pkInfo, cacheErr)
					continue
				}

				cacheErr = s.cacheClient.AddAppointPkDownEndTs(pkInfo.GetUid(), rivalUid, pkInfo.GetAppointId(), countDownEndTs, countDownTs)
				if cacheErr != nil {
					log.ErrorWithCtx(ctx, "TimerHandleNeedProcAppointPk AddAppointPkDownEndTs failed info:%v err:%v", pkInfo, cacheErr)
					continue
				}

				if otherIsValid {
					if user, ok := mapUid2User[pkInfo.GetUid()]; ok {
						s.HandleAppointPush(ctx, pbLogic.AppointPkEventType_AppointPkInvite, rivalUid, user, countDownEndTs, countDownTs, true)
					}
				}

				if myIsValid {
					if user, ok := mapUid2User[rivalUid]; ok {
						s.HandleAppointPush(ctx, pbLogic.AppointPkEventType_AppointPkInvite, pkInfo.GetUid(), user, countDownEndTs, countDownTs, true)
					}
				}

				log.InfoWithCtx(ctx, "appoint pk invite success info:%v beginTs:%d rival:%d nextBeginTs:d", pkInfo, beginTs, rivalUid, nextBeginTs)

			}
		}
	}

	log.InfoWithCtx(ctx, "TimerHandleNeedProcAppointPk end cost:%v idx:%d key:%s size:%v", time.Since(nowTm), idx, key, len(needProcIdList))
}

func (s *ChannelLiveMgrServer) TimerHandleWaitingAppointPk(idx int) {
	key := fmt.Sprintf("%s_%d", WaitingAppointPk, idx)
	getLock := s.cacheClient.GetAppointPkLock(key, 300)
	if !getLock {
		//log.InfoWithCtx(ctx,"TimerHandleWaitingAppointPk get lock failed")
		return
	}

	defer func() {
		err := s.cacheClient.ReleaseAppointPkLock(key)
		if err != nil {
			log.Errorf("TimerHandleWaitingAppointPk ReleaseAppointPkLock failed lockKey:%s err:%v", key, err)
		}
	}()

	nowTm := time.Now()
	ctx := context.Background()

	strMemberList, err := s.cacheClient.GetWaitingAppointPkList(0, uint32(nowTm.Unix()), uint32(idx))
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPk GetWaitingAppointPkList failed tm:%v idx:%d key:%s err:%v", nowTm, idx, key, err)
		return
	}

	log.InfoWithCtx(ctx, "TimerHandleWaitingAppointPk tm:%v size:%v", nowTm, len(strMemberList))

	mapId2IsProc := make(map[uint32]bool, 0)

	for _, strMember := range strMemberList {
		strList := strings.Split(strMember, "_")

		if len(strList) != 3 {
			continue
		}

		appointId, _ := strconv.Atoi(strList[0])
		myUid, _ := strconv.Atoi(strList[1])
		otherUid, _ := strconv.Atoi(strList[2])
		uAppointId := uint32(appointId)
		uMyUid := uint32(myUid)
		uOtherUid := uint32(otherUid)

		if _, ok := mapId2IsProc[uAppointId]; ok {
			log.DebugWithCtx(ctx, "TimerHandleWaitingAppointPk is already proc uAppointId:%d", uAppointId)
			continue
		}

		// 判断对手是否已经应战
		isExist, err := s.cacheClient.CheckWaitingAppointPkIsExist(uAppointId, uOtherUid, uMyUid, uint32(idx))
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPk CheckWaitingAppointPkIsExist failed %d %d %d err:%v", uAppointId, uMyUid, uOtherUid, err)
			continue
		}

		uidList := []uint32{uMyUid, uOtherUid}
		mapUid2User, err := s.accountCli.GetUsersMap(ctx, uidList)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPk GetUsersMap failed %d %d %d err:%v", uAppointId, uMyUid, uOtherUid, err)
			continue
		}

		log.DebugWithCtx(ctx, "TimerHandleWaitingAppointPk uAppointId:%d uOtherUid:%d uMyUid:%d isExist:%v idx:%v", uAppointId, uOtherUid, uMyUid, isExist, idx)

		// 对方已经应战
		if !isExist {
			if myUser, ok := mapUid2User[uMyUid]; ok {
				s.HandleAppointPush(ctx, pbLogic.AppointPkEventType_AppointPkWin, uOtherUid, myUser, 0, 0, false)
			}
		} else {
			if myUser, ok := mapUid2User[uMyUid]; ok {
				s.HandleAppointPush(ctx, pbLogic.AppointPkEventType_AppointPkAbstain, uOtherUid, myUser, 0, 0, false)
			}
		}

		if otherUser, ok := mapUid2User[uOtherUid]; ok {
			s.HandleAppointPush(ctx, pbLogic.AppointPkEventType_AppointPkAbstain, uMyUid, otherUser, 0, 0, false)
		}

		mapId2IsProc[uAppointId] = true

		// 从定时器中删除
		if isExist {
			s.cacheClient.DelWaitingAppointPk(uAppointId, uOtherUid, uMyUid, uint32(idx))
		}

		s.cacheClient.DelWaitingAppointPk(uAppointId, uMyUid, uOtherUid, uint32(idx))

		if !isExist {
			// 对方已经应战，有一方胜利。需要发kafka事件
			anchorResp, err := s.GetAnchorByUidList(ctx, &pb.GetAnchorByUidListReq{
				UidList: []uint32{uMyUid, uOtherUid},
			})
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPk GetAnchorByUidList failed %d %d err:%v", uMyUid, uOtherUid, err)
				continue
			}

			if len(anchorResp.GetAnchorList()) != 2 {
				log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPk no anchor info %d %d list:%v", uMyUid, uOtherUid, anchorResp.GetAnchorList())
				continue
			}

			mapUid2ChannelId := make(map[uint32]uint32, 0)
			for _, anchor := range anchorResp.GetAnchorList() {
				mapUid2ChannelId[anchor.GetUid()] = anchor.GetChannelId()
			}

			s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
				AnchorUid:       uMyUid,
				ChannelId:       mapUid2ChannelId[uMyUid],
				OppAnchorUid:    uOtherUid,
				OppChannelId:    mapUid2ChannelId[uOtherUid],
				ChannelPkStatus: pbLogic.EnumChannelLivePKStatus_FINISH,
				WinUid:          uOtherUid,
				WinChannelId:    mapUid2ChannelId[uOtherUid],
				Ty:              pbLogic.ChannelLiveKafkaEventType_ChannelLivePkType,
				MatchModel:      pbLogic.ChannelLivePKMatchType_CPK_Match_Appoint,
				CreateTime:      time.Now().Unix(),
				MatchType:       int64(pbLogic.ChannelLivePKMatchType_CPK_Match_Appoint),
			})
		}
	}

	log.InfoWithCtx(ctx, "TimerHandleWaitingAppointPk end ts:%v size:%v", time.Since(nowTm), len(strMemberList))
}

func (s *ChannelLiveMgrServer) TimerHandleWaitingAppointPkPush(idx int) {
	key := fmt.Sprintf("%s_%d", WaitingAppointPkPush, idx)
	getLock := s.cacheClient.GetAppointPkLock(key, 60)
	if !getLock {
		//log.InfoWithCtx(ctx,"TimerHandleWaitingAppointPkPush get lock failed")
		return
	}

	defer func() {
		err := s.cacheClient.ReleaseAppointPkLock(key)
		if err != nil {
			log.Errorf("TimerHandleWaitingAppointPkPush ReleaseAppointPkLock failed lockKey:%s err:%v", key, err)
		}
	}()

	nowTm := time.Now()
	ctx := context.Background()

	strMemberList, err := s.cacheClient.GetWaitingAppointPkPushList(0, uint32(nowTm.Unix()), uint32(idx))
	if err != nil {
		log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPkPush GetWaitingAppointPkPushList failed tm:%v idx:%d key:%s err:%v", nowTm, key, key, err)
		return
	}

	log.InfoWithCtx(ctx, "TimerHandleWaitingAppointPkPush tm:%v size:%v", nowTm, len(strMemberList))

	for _, strMember := range strMemberList {
		strList := strings.Split(strMember, "_")

		if len(strList) != 2 {
			continue
		}

		myUid, _ := strconv.Atoi(strList[0])
		otherUid, _ := strconv.Atoi(strList[1])
		uMyUid := uint32(myUid)
		uOtherUid := uint32(otherUid)

		pushEvent, err := s.cacheClient.GetAnchorAppointPkPushEvent(uMyUid)
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPkPush GetAnchorAppointPkPushEvent failed uid:%u err:%v", uMyUid, err)
			continue
		}

		if pushEvent.GetEventType() != uint32(pb.AppointPkEventType_InValid) {
			otherUser, err := s.accountCli.GetUser(ctx, pushEvent.GetOtherUid())
			if err != nil {
				log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPkPush GetUser failed uid:%d err:%v", uOtherUid, err)
				continue
			}

			s.HandleAppointPush(ctx, pbLogic.AppointPkEventType(pushEvent.GetEventType()), pushEvent.GetMyUid(), otherUser, pushEvent.GetCountDownEndTime(), pushEvent.GetCountDownTime(), false)
		}

		s.cacheClient.DelWaitingAppointPkPush(uMyUid, uOtherUid, uMyUid%WaitingAppointPkPushQueueCnt)
	}

	countDownTs, _ := conf.GetAppointPkTsConf()
	if countDownTs != 0 {
		err := s.cacheClient.DelWaitingAppointPkPushByScore(0, uint32(nowTm.Unix())-countDownTs, uint32(idx))
		if err != nil {
			log.ErrorWithCtx(ctx, "TimerHandleWaitingAppointPkPush DelWaitingAppointPkPushByScore failed err:%v idx:%d", err, idx)
		}
	}

	log.InfoWithCtx(ctx, "TimerHandleWaitingAppointPkPush end ts:%v size:%v", time.Since(nowTm), len(strMemberList))
}

func (s *ChannelLiveMgrServer) HandleAppointPush(ctx context.Context, eventType pbLogic.AppointPkEventType, myUid uint32, otherInfo *accountPB.UserResp, countDownEndTs, countDownTs uint32, isNeedRecord bool) error {
	pushEvent := &pbLogic.AppointPkEvent{
		EventType:        uint32(eventType),
		MyUid:            myUid,
		OtherUid:         otherInfo.GetUid(),
		OtherAccount:     otherInfo.GetUsername(),
		OtherNickname:    otherInfo.GetNickname(),
		CountDownEndTime: countDownEndTs,
		OtherSex:         otherInfo.GetSex(),
		CountDownTime:    countDownTs,
	}

	pushData, err := proto.Marshal(pushEvent)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleAppointPush Marshal failed info:%v err:%v", pushEvent, err)
		return err
	}

	err = s.mgr.PushUserChannelMsg(ctx, pushData, []uint32{myUid})
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleAppointPush PushUserChannelMsg failed info:%v err:%v", pushEvent, err)
		return err
	}

	// 进房不需要重新获取，就不记录了
	if !isNeedRecord {
		log.InfoWithCtx(ctx, "HandleAppointPush no need record push info:%v", pushEvent)
		return nil
	}

	// 存到cache，主播进房获取
	var expireTs uint32 = 0
	nowTs := uint32(time.Now().Unix())
	if countDownEndTs >= nowTs {
		expireTs = countDownEndTs - nowTs
	}

	pushEvInfo := &pb.AppointPkEvent{
		EventType:        pushEvent.GetEventType(),
		MyUid:            pushEvent.GetMyUid(),
		OtherUid:         pushEvent.GetOtherUid(),
		OtherAccount:     pushEvent.GetOtherAccount(),
		OtherNickname:    pushEvent.GetOtherNickname(),
		CountDownEndTime: pushEvent.GetCountDownEndTime(),
		OtherSex:         pushEvent.GetOtherSex(),
		CountDownTime:    pushEvent.GetCountDownTime(),
	}

	err = s.cacheClient.AddAnchorAppointPkPushEvent(pushEvInfo, expireTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleAppointPush AddAnchorAppointPkPushEvent failed info:%v err:%v", pushEvent, err)
	}

	// 客户端未确定收到推送，需要重新推送
	err = s.cacheClient.AddWaitingAppointPkPush(myUid, pushEvent.GetOtherUid(), nowTs+5, myUid%WaitingAppointPkPushQueueCnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "HandleAppointPush AddWaitingAppointPkPush failed info:%v err:%v", pushEvent, err)
	}

	log.InfoWithCtx(ctx, "HandleAppointPush end info:%v", pushEvent)
	return nil
}

func (s *ChannelLiveMgrServer) PushAnchorFuWuHaoMsg(ctx context.Context, anchorUid uint32, content string, isHighLightText bool) error {
	imType := &aapb.ImType{
		SenderType:   uint32(aapb.IM_SENDER_TYPE_IM_SENDER_PUBLIC_ACCOUNT),
		ReceiverType: uint32(aapb.IM_RECEIVER_TYPE_IM_RECEIVER_USER),
	}

	publicReq := &publicPB.GetPublicAccountByBindedIdReq{
		Type:     uint32(publicPB.PublicAccountType_SYSTEM),
		BindedId: 90004, // 主播服务号绑定id
	}
	publicRsp, err := s.publicCli.GetPublicAccountByBindedId(ctx, 0, publicReq)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushAnchorFuWuHaoMsg fail to publicClient.GetPublicAccountByBindedId err:%s, anchorUid:%d, content:%s", err.Error(), anchorUid, content)
		return err
	}

	toIDList := []uint32{
		anchorUid,
	}

	imContent := &aapb.ImContent{}
	if isHighLightText {
		imContent.TextHlUrl = &aapb.ImTextWithHighlightUrl{
			Content:    content,
			Hightlight: "点击跳转>",
			Url:        "tt://m.52tt.com/chat?account=tt216681919&target_nick=TT-叶叶",
		}
		imType.ContentType = uint32(aapb.IM_CONTENT_TYPE_IM_CONTENT_TEXT_WITH_HL_URL)
	} else {
		imContent.TextNormal = &aapb.ImTextNormal{
			Content: content,
		}
		imType.ContentType = uint32(aapb.IM_CONTENT_TYPE_IM_CONTENT_TEXT)
	}

	msg := &aapb.ImMsg{
		ImType:    imType,
		FromUid:   publicRsp.GetPublicAccount().GetPublicId(),
		ToIdList:  toIDList,
		ImContent: imContent,
		Platform:  aapb.Platform_UNSPECIFIED,
	}
	msgList := []*aapb.ImMsg{
		msg,
	}
	if err := s.apiCli.SendImMsg(ctx, 0, protocol.TT, msgList, true); err != nil {
		log.ErrorWithCtx(ctx, "PushAnchorFuWuHaoMsg fail to sendPublicIm.SendImMsg err:%s, anchorUid:%d, content:%s", err.Error(), anchorUid, content)
	}

	log.DebugWithCtx(ctx, "PushAnchorFuWuHaoMsg anchorUid:%d, content:%s", anchorUid, content)

	return nil
}

func (s *ChannelLiveMgrServer) GetAppointPkInfo(ctx context.Context, in *pb.GetAppointPkInfoReq) (*pb.GetAppointPkInfoResp, error) {
	out := &pb.GetAppointPkInfoResp{}

	log.DebugWithCtx(ctx, "GetAppointPkInfo begin in:%v", in)

	pkPushEv, err := s.cacheClient.GetAnchorAppointPkPushEvent(in.GetUid())
	if err != nil && err != redis.Nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfo GetAnchorAppointPkPushEvent failed in:%v err:%v", in, err)
		return out, err
	}

	out.PushEventInfo = pkPushEv

	log.DebugWithCtx(ctx, "GetAppointPkInfo end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) ConfirmAppointPkPush(ctx context.Context, in *pb.ConfirmAppointPkPushReq) (*pb.ConfirmAppointPkPushResp, error) {
	out := &pb.ConfirmAppointPkPushResp{}

	log.DebugWithCtx(ctx, "ConfirmAppointPkPush begin in:%v", in)

	err := s.cacheClient.DelWaitingAppointPkPush(in.GetMyUid(), in.GetOtherUid(), in.GetMyUid()%WaitingAppointPkPushQueueCnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "ConfirmAppointPkPush DelWaitingAppointPk failed in:%v err:%v", in, err)
		return out, err
	}

	log.DebugWithCtx(ctx, "ConfirmAppointPkPush end in:%v out:%v", in, out)
	return out, nil
}

func (s *ChannelLiveMgrServer) getAppointPkResByLiveStatus(ctx context.Context, myUid, otherUId uint32) (bool, bool, uint32, uint32) {
	log.DebugWithCtx(ctx, "getAppointPkResByLiveStatus begin %d %d", myUid, otherUId)

	uidList := []uint32{myUid, otherUId}

	liveInfoResp, err := s.BatchGetChannelLiveStatus(ctx, &pb.BatchGetChannelLiveStatusReq{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "getAppointPkResByLiveStatus BatchGetChannelLiveStatus failed in:%v err:%v", uidList, err)
		return false, false, 0, 0
	}

	mapUid2LiveInfo := make(map[uint32]*pb.ChannelLiveStatusInfo, 0)
	for _, liveInfo := range liveInfoResp.GetChannelLiveInfoList() {
		mapUid2LiveInfo[liveInfo.GetChannelLiveStatus().GetUid()] = liveInfo
	}

	if myLiveInfo, okA := mapUid2LiveInfo[myUid]; okA {
		if otherLiveInfo, okB := mapUid2LiveInfo[otherUId]; okB {
			myStatus := myLiveInfo.GetChannelLiveStatus().GetStatus()
			otherStatus := otherLiveInfo.GetChannelLiveStatus().GetStatus()

			if (myStatus == pb.EnumChannelLiveStatus_OPEN || myStatus == pb.EnumChannelLiveStatus_CONTINUE) &&
				(otherStatus == pb.EnumChannelLiveStatus_OPEN || otherStatus == pb.EnumChannelLiveStatus_CONTINUE) {
				return true, true, myLiveInfo.GetChannelLiveStatus().GetChannelId(), otherLiveInfo.GetChannelLiveStatus().GetChannelId()
			}

			if (myStatus == pb.EnumChannelLiveStatus_OPEN || myStatus == pb.EnumChannelLiveStatus_CONTINUE) &&
				(otherStatus == pb.EnumChannelLiveStatus_CLOSE || otherStatus == pb.EnumChannelLiveStatus_PAUSE) {
				return true, false, myLiveInfo.GetChannelLiveStatus().GetChannelId(), otherLiveInfo.GetChannelLiveStatus().GetChannelId()
			}

			if (myStatus == pb.EnumChannelLiveStatus_CLOSE || myStatus == pb.EnumChannelLiveStatus_PAUSE) &&
				(otherStatus == pb.EnumChannelLiveStatus_OPEN || otherStatus == pb.EnumChannelLiveStatus_CONTINUE) {
				return false, true, myLiveInfo.GetChannelLiveStatus().GetChannelId(), otherLiveInfo.GetChannelLiveStatus().GetChannelId()
			}

			return false, false, myLiveInfo.GetChannelLiveStatus().GetChannelId(), otherLiveInfo.GetChannelLiveStatus().GetChannelId()
		}
	}

	return false, false, 0, 0
}

func (s *ChannelLiveMgrServer) AcceptAppointPk(ctx context.Context, in *pb.AcceptAppointPkReq) (*pb.AcceptAppointPkResp, error) {
	out := &pb.AcceptAppointPkResp{}

	log.DebugWithCtx(ctx, "AcceptAppointPk begin in:%v", in)

	nowTs := uint32(time.Now().Unix())
	myPkInfoList, err := s.cacheClient.GetAnchorAppointPkInfoList(in.GetMyUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		return out, err
	}

	otherPkInfoList, err := s.cacheClient.GetAnchorAppointPkInfoList(in.GetOtherUid(), nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetAnchorAppointPkInfoList failed in:%v err:%v", in, err)
		return out, err
	}

	var myAppointId uint32 = 0
	var otherAppointId uint32 = 0

	for _, pkInfo := range myPkInfoList {
		log.DebugWithCtx(ctx, "AcceptAppointPk in:%v Info:%v ts:%d", in, pkInfo, nowTs)
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			isExist, err := s.cacheClient.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "AcceptAppointPk CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
				continue
			}
			if isExist {
				myAppointId = pkInfo.AppointId
				break
			}
		}
	}

	for _, pkInfo := range otherPkInfoList {
		log.DebugWithCtx(ctx, "AcceptAppointPk in:%v Info:%v ts:%d", in, pkInfo, nowTs)
		if nowTs >= pkInfo.BeginTs && nowTs <= pkInfo.EndTs {
			isExist, err := s.cacheClient.CheckAppointPkInfoIsExist(pkInfo.AppointId)
			if err != nil {
				log.ErrorWithCtx(ctx, "AcceptAppointPk CheckAppointPkInfoIsExist failed in:%v Info:%v err:%v", in, pkInfo, err)
				continue
			}
			if isExist {
				otherAppointId = pkInfo.AppointId
				break
			}
		}
	}

	isSameAppointPk := false
	var appointId uint32 = 0
	if myAppointId != 0 && myAppointId == otherAppointId {
		isSameAppointPk = true
		appointId = myAppointId
	}

	if !isSameAppointPk {
		log.InfoWithCtx(ctx, "AcceptAppointPk no same appoint pk in:%v", in)
		return out, nil
	}

	mapId2AppointPk, err := s.cacheClient.BatchGetAppointPkInfo([]uint32{appointId})
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk BatchGetAppointPkInfo failed in:%v err:%v", in, err)
		return out, err
	}

	appointPkInfo := &pb.AppointPkInfo{}
	if pkInfo, ok := mapId2AppointPk[appointId]; ok {
		appointPkInfo = pkInfo
	} else {
		log.InfoWithCtx(ctx, "AcceptAppointPk no appoint pk info in:%v info:%v", in, appointPkInfo)
		return out, nil
	}

	rivalUid := in.GetOtherUid() // 本次pk的对手id
	// 确定本次pk的对手id，设置指定pk的应战标志需要用到
	if appointPkInfo.GetUid() != in.GetMyUid() {
		rivalUid = in.GetMyUid()
	}

	downEndTs, err := s.cacheClient.GetAppointPkDownEndTs(appointPkInfo.GetUid(), rivalUid, appointId)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetAppointPkDownEndTs failed in:%v err:%v", in, err)
		return out, err
	}

	isExist, err := s.cacheClient.DelWaitingAppointPk(appointId, in.GetMyUid(), in.GetOtherUid(), appointId%WaitingAppointPkQueueCnt)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk DelWaitingAppointPk failed in:%v err:%v", in, err)
		return out, err
	}

	var newCnt uint32
	// 等待处理pk标志存在，才需要自增，避免多次操作
	if isExist {
		newCnt, err = s.cacheClient.IncrAcceptAppointPkFlag(appointPkInfo.GetUid(), rivalUid, appointId)
		if err != nil {
			// 失败就把等待处理pk再重新加到队列
			nowTs := uint32(time.Now().Unix())
			var expireTs uint32 = 0
			if downEndTs >= nowTs {
				expireTs = downEndTs - nowTs
			}
			s.cacheClient.AddWaitingAppointPk(appointId, in.GetMyUid(), in.GetOtherUid(), expireTs, appointId%WaitingAppointPkQueueCnt)

			log.ErrorWithCtx(ctx, "AcceptAppointPk IncrAcceptAppointPkFlag failed in:%v err:%v", in, err)
			return out, err
		}
	} else {
		// 已经处理过
		newCnt = 1
	}

	uidList := []uint32{in.GetMyUid(), in.GetOtherUid()}
	mapUid2User, err := s.accountCli.GetUsersMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "AcceptAppointPk GetUsersMap failed in:%v err:%v", in, err)
		return out, err
	}

	// 两方都确定应战，开始pk
	if newCnt == 2 {
		// 判断pk双方是处于有效pk状态
		myIsValid, otherIsValid, myChannelId, otherChannelId := s.getAppointPkResByLiveStatus(ctx, in.GetMyUid(), in.GetOtherUid())

		if myIsValid && otherIsValid {
			_, err := s.StartPk(ctx, in.GetMyUid(), in.GetOtherUid(), myChannelId, otherChannelId, 0, 0, int(pb.ChannelLivePKMatchType_CPK_Match_Appoint))
			if err != nil {
				log.ErrorWithCtx(ctx, "AcceptAppointPk StartPk failed in:%v err:%v", in, err)
				return out, err
			}

			return out, nil
		}

		if !myIsValid && !otherIsValid {
			return out, nil
		}

		if otherUser, ok := mapUid2User[in.GetOtherUid()]; ok {
			s.HandleAppointPush(ctx, mapValid2EventType[myIsValid], in.GetMyUid(), otherUser, 0, 0, false)
		}

		if myUser, ok := mapUid2User[in.GetMyUid()]; ok {
			s.HandleAppointPush(ctx, mapValid2EventType[otherIsValid], in.GetOtherUid(), myUser, 0, 0, false)
		}

		winUid := in.GetMyUid()
		winChannelId := myChannelId
		if !myIsValid {
			winUid = in.GetOtherUid()
			winChannelId = otherChannelId
		}

		s.channelLiveProd.ProduceChannelLiveEvent(&pbLogic.ChannelLiveKafkaEvent{
			AnchorUid:       in.GetMyUid(),
			ChannelId:       myChannelId,
			OppAnchorUid:    in.GetOtherUid(),
			OppChannelId:    otherChannelId,
			ChannelPkStatus: pbLogic.EnumChannelLivePKStatus_FINISH,
			WinUid:          winUid,
			WinChannelId:    winChannelId,
			Ty:              pbLogic.ChannelLiveKafkaEventType_ChannelLivePkType,
			MatchModel:      pbLogic.ChannelLivePKMatchType_CPK_Match_Appoint,
			CreateTime:      time.Now().Unix(),
			MatchType:       int64(pbLogic.ChannelLivePKMatchType_CPK_Match_Appoint),
		})

		return out, nil

	} else if newCnt == 1 {
		pushEvent, err := s.cacheClient.GetAnchorAppointPkPushEvent(in.GetMyUid())
		if err != nil && err != redis.Nil {
			log.ErrorWithCtx(ctx, "AcceptAppointPk GetAnchorAppointPkPushEvent failed in:%v err:%v", in, err)
			return out, err
		}

		if pushEvent.GetEventType() != uint32(pbLogic.AppointPkEventType_AppointPkWaiting) {
			downTs, _ := conf.GetAppointPkTsConf()
			if otherUser, ok := mapUid2User[in.GetOtherUid()]; ok {
				err = s.HandleAppointPush(ctx, pbLogic.AppointPkEventType_AppointPkWaiting, in.GetMyUid(), otherUser, downEndTs, downTs, true)
				if err != nil {
					log.ErrorWithCtx(ctx, "AcceptAppointPk HandleAppointPush failed in:%v err:%v", in, err)
					return out, err
				}
			}
		}
	}

	log.InfoWithCtx(ctx, "AcceptAppointPk end in:%v out:%v", in, out)

	return out, nil
}
