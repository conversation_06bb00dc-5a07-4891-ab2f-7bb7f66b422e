package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

// ContestPkConfigRecord 赛事PK配置数据库记录
type ContestPkConfigRecord struct {
	ID               uint64 `db:"id"`
	ContestID        string `db:"contest_id"`
	ContestName      string `db:"contest_name"`
	ActivityService  string `db:"activity_service"`
	AnchorUidA       uint64 `db:"anchor_uid_a"`
	AnchorUidB       uint64 `db:"anchor_uid_b"`
	ContestBeginTime uint64 `db:"contest_begin_time"`
	ContestEndTime   uint64 `db:"contest_end_time"`
	PkBeginTime      uint64 `db:"pk_begin_time"`
	PkEndTime        uint64 `db:"pk_end_time"`
	AllowExtraTime   bool   `db:"allow_extra_time"`
	Status           uint32 `db:"status"`
	WinnerUID        uint64 `db:"winner_uid"`
	FinalScoreA      uint32 `db:"final_score_a"`
	FinalScoreB      uint32 `db:"final_score_b"`
	Operator         string `db:"operator"`
	CreateTime       uint64 `db:"create_time"`
	UpdateTime       uint64 `db:"update_time"`
}

// CreateContestPkConfig 创建赛事PK配置
func (s *Store) CreateContestPkConfig(ctx context.Context, tx *sql.Tx, config *pb.ContestPkConfig) error {
	query := `
		INSERT INTO contest_pk_config (
			contest_id, contest_name, activity_service, anchor_uid_a, anchor_uid_b,
			contest_begin_time, contest_end_time, pk_begin_time, pk_end_time,
			allow_extra_time, status, operator, create_time, update_time
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	nowTs := uint64(time.Now().Unix())
	args := []interface{}{
		config.ContestId,
		config.ContestName,
		config.ActivityService,
		config.AnchorUidA,
		config.AnchorUidB,
		config.ContestBeginTime,
		config.ContestEndTime,
		config.PkBeginTime,
		config.PkEndTime,
		config.AllowExtraTime,
		uint32(config.Status),
		config.Operator,
		nowTs,
		nowTs,
	}

	var err error
	if tx != nil {
		_, err = tx.ExecContext(ctx, query, args...)
	} else {
		_, err = s.db.ExecContext(ctx, query, args...)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	log.InfoWithCtx(ctx, "CreateContestPkConfig success contestId:%s", config.ContestId)
	return nil
}

// UpdateContestPkConfig 更新赛事PK配置
func (s *Store) UpdateContestPkConfig(ctx context.Context, tx *sql.Tx, config *pb.ContestPkConfig) error {
	query := `
		UPDATE contest_pk_config SET
			contest_name = ?, activity_service = ?, anchor_uid_a = ?, anchor_uid_b = ?,
			contest_begin_time = ?, contest_end_time = ?, pk_begin_time = ?, pk_end_time = ?,
			allow_extra_time = ?, status = ?, winner_uid = ?, final_score_a = ?, final_score_b = ?,
			operator = ?, update_time = ?
		WHERE contest_id = ?
	`

	nowTs := uint64(time.Now().Unix())
	args := []interface{}{
		config.ContestName,
		config.ActivityService,
		config.AnchorUidA,
		config.AnchorUidB,
		config.ContestBeginTime,
		config.ContestEndTime,
		config.PkBeginTime,
		config.PkEndTime,
		config.AllowExtraTime,
		uint32(config.Status),
		config.WinnerUid,
		config.FinalScoreA,
		config.FinalScoreB,
		config.Operator,
		nowTs,
		config.ContestId,
	}

	var err error
	var result sql.Result
	if tx != nil {
		result, err = tx.ExecContext(ctx, query, args...)
	} else {
		result, err = s.db.ExecContext(ctx, query, args...)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPkConfig failed contestId:%s err:%v", config.ContestId, err)
		return err
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("contest pk config not found: %s", config.ContestId)
	}

	log.InfoWithCtx(ctx, "UpdateContestPkConfig success contestId:%s", config.ContestId)
	return nil
}

// GetContestPkConfig 获取赛事PK配置
func (s *Store) GetContestPkConfig(ctx context.Context, contestId string) (*pb.ContestPkConfig, error) {
	query := `
		SELECT contest_id, contest_name, activity_service, anchor_uid_a, anchor_uid_b,
			   contest_begin_time, contest_end_time, pk_begin_time, pk_end_time,
			   allow_extra_time, status, winner_uid, final_score_a, final_score_b,
			   operator, create_time, update_time
		FROM contest_pk_config
		WHERE contest_id = ?
	`

	var record ContestPkConfigRecord
	err := s.db.GetContext(ctx, &record, query, contestId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("contest pk config not found: %s", contestId)
		}
		log.ErrorWithCtx(ctx, "GetContestPkConfig failed contestId:%s err:%v", contestId, err)
		return nil, err
	}

	return s.convertRecordToConfig(&record), nil
}

// BatchGetContestPkConfig 批量获取赛事PK配置
func (s *Store) BatchGetContestPkConfig(ctx context.Context, req *pb.BatchGetContestPkConfigReq) ([]*pb.ContestPkConfig, uint32, error) {
	whereConditions := []string{}
	args := []interface{}{}

	// 构建查询条件
	if len(req.ContestIdList) > 0 {
		placeholders := strings.Repeat("?,", len(req.ContestIdList))
		placeholders = placeholders[:len(placeholders)-1] // 移除最后一个逗号
		whereConditions = append(whereConditions, fmt.Sprintf("contest_id IN (%s)", placeholders))
		for _, id := range req.ContestIdList {
			args = append(args, id)
		}
	}

	if req.ActivityService != "" {
		whereConditions = append(whereConditions, "activity_service = ?")
		args = append(args, req.ActivityService)
	}

	if req.TimeBegin > 0 && req.TimeEnd > 0 {
		whereConditions = append(whereConditions, "pk_begin_time >= ? AND pk_end_time <= ?")
		args = append(args, req.TimeBegin, req.TimeEnd)
	}

	if req.Status != pb.ContestPkStatus_CONTEST_PK_PENDING {
		whereConditions = append(whereConditions, "status = ?")
		args = append(args, uint32(req.Status))
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM contest_pk_config %s", whereClause)
	var totalCount uint32
	err := s.db.GetContext(ctx, &totalCount, countQuery, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkConfig count failed err:%v", err)
		return nil, 0, err
	}

	// 分页查询
	page := req.Page
	if page == 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize == 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`
		SELECT contest_id, contest_name, activity_service, anchor_uid_a, anchor_uid_b,
			   contest_begin_time, contest_end_time, pk_begin_time, pk_end_time,
			   allow_extra_time, status, winner_uid, final_score_a, final_score_b,
			   operator, create_time, update_time
		FROM contest_pk_config %s
		ORDER BY create_time DESC
		LIMIT ? OFFSET ?
	`, whereClause)

	args = append(args, pageSize, offset)

	var records []ContestPkConfigRecord
	err = s.db.SelectContext(ctx, &records, dataQuery, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkConfig select failed err:%v", err)
		return nil, 0, err
	}

	configs := make([]*pb.ContestPkConfig, 0, len(records))
	for _, record := range records {
		configs = append(configs, s.convertRecordToConfig(&record))
	}

	return configs, totalCount, nil
}

// GetContestPkConfigsByTimeRange 根据时间范围获取赛事PK配置
func (s *Store) GetContestPkConfigsByTimeRange(ctx context.Context, beginTime, endTime uint64, status pb.ContestPkStatus) ([]*pb.ContestPkConfig, error) {
	query := `
		SELECT contest_id, contest_name, activity_service, anchor_uid_a, anchor_uid_b,
			   contest_begin_time, contest_end_time, pk_begin_time, pk_end_time,
			   allow_extra_time, status, winner_uid, final_score_a, final_score_b,
			   operator, create_time, update_time
		FROM contest_pk_config
		WHERE pk_begin_time >= ? AND pk_begin_time <= ? AND status = ?
		ORDER BY pk_begin_time ASC
	`

	var records []ContestPkConfigRecord
	err := s.db.SelectContext(ctx, &records, query, beginTime, endTime, uint32(status))
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkConfigsByTimeRange failed beginTime:%d endTime:%d status:%d err:%v", beginTime, endTime, status, err)
		return nil, err
	}

	configs := make([]*pb.ContestPkConfig, 0, len(records))
	for _, record := range records {
		configs = append(configs, s.convertRecordToConfig(&record))
	}

	return configs, nil
}

// CheckAnchorContestPkConflict 检查主播是否有赛事PK时间冲突
func (s *Store) CheckAnchorContestPkConflict(ctx context.Context, anchorUidA, anchorUidB uint64, beginTime, endTime uint64, excludeContestId string) (bool, error) {
	query := `
		SELECT COUNT(*) FROM contest_pk_config
		WHERE ((anchor_uid_a = ? OR anchor_uid_b = ?) OR (anchor_uid_a = ? OR anchor_uid_b = ?))
		  AND ((pk_begin_time <= ? AND pk_end_time >= ?) OR (pk_begin_time <= ? AND pk_end_time >= ?))
		  AND status IN (?, ?)
		  AND contest_id != ?
	`

	var count int
	err := s.db.GetContext(ctx, &count, query,
		anchorUidA, anchorUidA, anchorUidB, anchorUidB,
		beginTime, beginTime, endTime, endTime,
		uint32(pb.ContestPkStatus_CONTEST_PK_PENDING), uint32(pb.ContestPkStatus_CONTEST_PK_RUNNING),
		excludeContestId)

	if err != nil {
		log.ErrorWithCtx(ctx, "CheckAnchorContestPkConflict failed anchorUidA:%d anchorUidB:%d err:%v", anchorUidA, anchorUidB, err)
		return false, err
	}

	return count > 0, nil
}

// convertRecordToConfig 将数据库记录转换为配置对象
func (s *Store) convertRecordToConfig(record *ContestPkConfigRecord) *pb.ContestPkConfig {
	return &pb.ContestPkConfig{
		ContestId:        record.ContestID,
		ContestName:      record.ContestName,
		ActivityService:  record.ActivityService,
		AnchorUidA:       record.AnchorUidA,
		AnchorUidB:       record.AnchorUidB,
		ContestBeginTime: record.ContestBeginTime,
		ContestEndTime:   record.ContestEndTime,
		PkBeginTime:      record.PkBeginTime,
		PkEndTime:        record.PkEndTime,
		AllowExtraTime:   record.AllowExtraTime,
		Status:           pb.ContestPkStatus(record.Status),
		WinnerUid:        record.WinnerUID,
		FinalScoreA:      record.FinalScoreA,
		FinalScoreB:      record.FinalScoreB,
		Operator:         record.Operator,
		CreateTime:       record.CreateTime,
		UpdateTime:       record.UpdateTime,
	}
}
