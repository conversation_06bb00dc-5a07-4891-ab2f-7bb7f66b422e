package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

// ContestPkResultRecord 赛事PK结果数据库记录
type ContestPkResultRecord struct {
	ID              uint64 `db:"id"`
	ContestID       string `db:"contest_id"`
	WinnerUID       uint64 `db:"winner_uid"`
	FinalScoreA     uint32 `db:"final_score_a"`
	FinalScoreB     uint32 `db:"final_score_b"`
	ActualStartTime uint64 `db:"actual_start_time"`
	ActualEndTime   uint64 `db:"actual_end_time"`
	IsExtraTime     bool   `db:"is_extra_time"`
	PkDuration      uint32 `db:"pk_duration"`
	ResultStatus    uint32 `db:"result_status"`
	CreateTime      uint64 `db:"create_time"`
	UpdateTime      uint64 `db:"update_time"`
}

// CreateContestPkResult 创建赛事PK结果
func (s *Store) CreateContestPkResult(ctx context.Context, tx *sql.Tx, result *pb.ContestPkResult) error {
	query := `
		INSERT INTO contest_pk_result (
			contest_id, winner_uid, final_score_a, final_score_b,
			actual_start_time, actual_end_time, is_extra_time, pk_duration,
			result_status, create_time, update_time
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	nowTs := uint64(time.Now().Unix())
	args := []interface{}{
		result.ContestId,
		result.WinnerUid,
		result.FinalScoreA,
		result.FinalScoreB,
		result.ActualStartTime,
		result.ActualEndTime,
		result.IsExtraTime,
		result.PkDuration,
		uint32(result.ResultStatus),
		nowTs,
		nowTs,
	}

	var err error
	if tx != nil {
		_, err = tx.ExecContext(ctx, query, args...)
	} else {
		_, err = s.db.ExecContext(ctx, query, args...)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "CreateContestPkResult failed contestId:%s err:%v", result.ContestId, err)
		return err
	}

	log.InfoWithCtx(ctx, "CreateContestPkResult success contestId:%s", result.ContestId)
	return nil
}

// UpdateContestPkResult 更新赛事PK结果
func (s *Store) UpdateContestPkResult(ctx context.Context, tx *sql.Tx, result *pb.ContestPkResult) error {
	query := `
		UPDATE contest_pk_result SET
			winner_uid = ?, final_score_a = ?, final_score_b = ?,
			actual_start_time = ?, actual_end_time = ?, is_extra_time = ?,
			pk_duration = ?, result_status = ?, update_time = ?
		WHERE contest_id = ?
	`

	nowTs := uint64(time.Now().Unix())
	args := []interface{}{
		result.WinnerUid,
		result.FinalScoreA,
		result.FinalScoreB,
		result.ActualStartTime,
		result.ActualEndTime,
		result.IsExtraTime,
		result.PkDuration,
		uint32(result.ResultStatus),
		nowTs,
		result.ContestId,
	}

	var err error
	var sqlResult sql.Result
	if tx != nil {
		sqlResult, err = tx.ExecContext(ctx, query, args...)
	} else {
		sqlResult, err = s.db.ExecContext(ctx, query, args...)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateContestPkResult failed contestId:%s err:%v", result.ContestId, err)
		return err
	}

	rowsAffected, _ := sqlResult.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("contest pk result not found: %s", result.ContestId)
	}

	log.InfoWithCtx(ctx, "UpdateContestPkResult success contestId:%s", result.ContestId)
	return nil
}

// GetContestPkResult 获取赛事PK结果
func (s *Store) GetContestPkResult(ctx context.Context, contestId string) (*pb.ContestPkResult, error) {
	query := `
		SELECT contest_id, winner_uid, final_score_a, final_score_b,
			   actual_start_time, actual_end_time, is_extra_time, pk_duration,
			   result_status, create_time, update_time
		FROM contest_pk_result
		WHERE contest_id = ?
	`

	var record ContestPkResultRecord
	err := s.db.GetContext(ctx, &record, query, contestId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("contest pk result not found: %s", contestId)
		}
		log.ErrorWithCtx(ctx, "GetContestPkResult failed contestId:%s err:%v", contestId, err)
		return nil, err
	}

	return s.convertResultRecordToProto(&record), nil
}

// BatchGetContestPkResult 批量获取赛事PK结果
func (s *Store) BatchGetContestPkResult(ctx context.Context, contestIds []string) ([]*pb.ContestPkResult, error) {
	if len(contestIds) == 0 {
		return []*pb.ContestPkResult{}, nil
	}

	query := `
		SELECT contest_id, winner_uid, final_score_a, final_score_b,
			   actual_start_time, actual_end_time, is_extra_time, pk_duration,
			   result_status, create_time, update_time
		FROM contest_pk_result
		WHERE contest_id IN (?` + strings.Repeat(",?", len(contestIds)-1) + `)`

	args := make([]interface{}, len(contestIds))
	for i, id := range contestIds {
		args[i] = id
	}

	var records []ContestPkResultRecord
	err := s.db.SelectContext(ctx, &records, query, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetContestPkResult failed err:%v", err)
		return nil, err
	}

	results := make([]*pb.ContestPkResult, 0, len(records))
	for _, record := range records {
		results = append(results, s.convertResultRecordToProto(&record))
	}

	return results, nil
}

// GetContestPkResultsByTimeRange 根据时间范围获取赛事PK结果
func (s *Store) GetContestPkResultsByTimeRange(ctx context.Context, beginTime, endTime uint64, status pb.ContestPkResultStatus, limit int) ([]*pb.ContestPkResult, error) {
	query := `
		SELECT contest_id, winner_uid, final_score_a, final_score_b,
			   actual_start_time, actual_end_time, is_extra_time, pk_duration,
			   result_status, create_time, update_time
		FROM contest_pk_result
		WHERE actual_end_time >= ? AND actual_end_time <= ? AND result_status = ?
		ORDER BY actual_end_time DESC
		LIMIT ?
	`

	var records []ContestPkResultRecord
	err := s.db.SelectContext(ctx, &records, query, beginTime, endTime, uint32(status), limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkResultsByTimeRange failed beginTime:%d endTime:%d status:%d err:%v", beginTime, endTime, status, err)
		return nil, err
	}

	results := make([]*pb.ContestPkResult, 0, len(records))
	for _, record := range records {
		results = append(results, s.convertResultRecordToProto(&record))
	}

	return results, nil
}

// DeleteContestPkResult 删除赛事PK结果
func (s *Store) DeleteContestPkResult(ctx context.Context, tx *sql.Tx, contestId string) error {
	query := `DELETE FROM contest_pk_result WHERE contest_id = ?`

	var err error
	var result sql.Result
	if tx != nil {
		result, err = tx.ExecContext(ctx, query, contestId)
	} else {
		result, err = s.db.ExecContext(ctx, query, contestId)
	}

	if err != nil {
		log.ErrorWithCtx(ctx, "DeleteContestPkResult failed contestId:%s err:%v", contestId, err)
		return err
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		log.WarnWithCtx(ctx, "DeleteContestPkResult no rows affected contestId:%s", contestId)
	}

	log.InfoWithCtx(ctx, "DeleteContestPkResult success contestId:%s", contestId)
	return nil
}

// GetContestPkResultByWinner 根据获胜者查询赛事PK结果
func (s *Store) GetContestPkResultByWinner(ctx context.Context, winnerUid uint64, beginTime, endTime uint64, limit int) ([]*pb.ContestPkResult, error) {
	query := `
		SELECT contest_id, winner_uid, final_score_a, final_score_b,
			   actual_start_time, actual_end_time, is_extra_time, pk_duration,
			   result_status, create_time, update_time
		FROM contest_pk_result
		WHERE winner_uid = ? AND actual_end_time >= ? AND actual_end_time <= ?
		ORDER BY actual_end_time DESC
		LIMIT ?
	`

	var records []ContestPkResultRecord
	err := s.db.SelectContext(ctx, &records, query, winnerUid, beginTime, endTime, limit)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetContestPkResultByWinner failed winnerUid:%d err:%v", winnerUid, err)
		return nil, err
	}

	results := make([]*pb.ContestPkResult, 0, len(records))
	for _, record := range records {
		results = append(results, s.convertResultRecordToProto(&record))
	}

	return results, nil
}

// UpsertContestPkResult 插入或更新赛事PK结果
func (s *Store) UpsertContestPkResult(ctx context.Context, tx *sql.Tx, result *pb.ContestPkResult) error {
	// 先尝试获取现有记录
	existing, err := s.GetContestPkResult(ctx, result.ContestId)
	if err != nil {
		// 记录不存在，创建新记录
		return s.CreateContestPkResult(ctx, tx, result)
	}

	// 记录存在，更新记录
	result.CreateTime = existing.CreateTime // 保持原创建时间
	return s.UpdateContestPkResult(ctx, tx, result)
}

// convertResultRecordToProto 将数据库记录转换为Proto对象
func (s *Store) convertResultRecordToProto(record *ContestPkResultRecord) *pb.ContestPkResult {
	return &pb.ContestPkResult{
		ContestId:       record.ContestID,
		WinnerUid:       record.WinnerUID,
		FinalScoreA:     record.FinalScoreA,
		FinalScoreB:     record.FinalScoreB,
		ActualStartTime: record.ActualStartTime,
		ActualEndTime:   record.ActualEndTime,
		IsExtraTime:     record.IsExtraTime,
		PkDuration:      record.PkDuration,
		ResultStatus:    pb.ContestPkResultStatus(record.ResultStatus),
		CreateTime:      record.CreateTime,
		UpdateTime:      record.UpdateTime,
	}
}
