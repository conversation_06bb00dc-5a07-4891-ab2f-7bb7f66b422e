package mysql

import (
	"context"
	"fmt"
	"strings"
	"time"

	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/channellivemgr"
)

// AppointPkInfoWithRivals 包含对手信息的指定PK信息
type AppointPkInfoWithRivals struct {
	AppointPkInfo
	Rivals []*AppointPkRivalInfo `db:"-"` // 不映射到数据库字段
}

// GetAppointPkInfoListOptimized 优化后的批量查询方法
// 解决N+1查询问题，使用JOIN一次性获取所有数据
func (s *Store) GetAppointPkInfoListOptimized(ctx context.Context, uid, beginTs, endTs, offset, limit uint32, queryType pb.GetAppointPkInfoListReq_QueryType) ([]*AppointPkInfoWithRivals, error) {
	infoList := make([]*AppointPkInfoWithRivals, 0)

	// 构建WHERE条件
	whereConditions := make([]string, 0)
	args := make([]interface{}, 0)

	if uid != 0 {
		whereConditions = append(whereConditions, "a.uid = ?")
		args = append(args, uid)
	}

	switch queryType {
	case pb.GetAppointPkInfoListReq_All:
		// 查询所有
	case pb.GetAppointPkInfoListReq_Future:
		whereConditions = append(whereConditions, "a.begin_ts >= ?")
		args = append(args, beginTs)
	case pb.GetAppointPkInfoListReq_Past:
		whereConditions = append(whereConditions, "a.end_ts <= ?")
		args = append(args, endTs)
	case pb.GetAppointPkInfoListReq_Range:
		if beginTs > 0 {
			whereConditions = append(whereConditions, "a.begin_ts >= ?")
			args = append(args, beginTs)
		}
		if endTs > 0 {
			whereConditions = append(whereConditions, "a.end_ts <= ?")
			args = append(args, endTs)
		}
	}

	whereClause := ""
	if len(whereConditions) > 0 {
		whereClause = "WHERE " + strings.Join(whereConditions, " AND ")
	}

	// 使用LEFT JOIN一次性获取所有数据
	sql := fmt.Sprintf(`
		SELECT 
			a.appoint_id, a.uid, a.begin_ts, a.end_ts, a.update_ts, a.operator,
			r.uid as rival_uid, r.begin_ts as rival_begin_ts, r.update_ts as rival_update_ts
		FROM %s a 
		LEFT JOIN %s r ON a.appoint_id = r.appoint_id
		%s
		ORDER BY a.appoint_id DESC, r.begin_ts ASC
		LIMIT ? OFFSET ?`,
		tblAppointPkInfo, tblAppointPkRivalInfo, whereClause)

	args = append(args, limit, offset)

	log.DebugWithCtx(ctx, "GetAppointPkInfoListOptimized SQL: %s, args: %v", sql, args)

	// 执行查询
	rows, err := s.db.QueryContext(ctx, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetAppointPkInfoListOptimized query failed: %v", err)
		return infoList, err
	}
	defer rows.Close()

	// 解析结果，处理一对多关系
	appointPkMap := make(map[uint32]*AppointPkInfoWithRivals)

	for rows.Next() {
		var appointId, uid, beginTs, endTs, updateTs uint32
		var operator string
		var rivalUid, rivalBeginTs, rivalUpdateTs *uint32

		err := rows.Scan(
			&appointId, &uid, &beginTs, &endTs, &updateTs, &operator,
			&rivalUid, &rivalBeginTs, &rivalUpdateTs,
		)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetAppointPkInfoListOptimized scan failed: %v", err)
			continue
		}

		// 如果是新的appointId，创建新的记录
		if _, exists := appointPkMap[appointId]; !exists {
			appointPkMap[appointId] = &AppointPkInfoWithRivals{
				AppointPkInfo: AppointPkInfo{
					AppointId: appointId,
					Uid:       uid,
					BeginTs:   beginTs,
					EndTs:     endTs,
					UpdateTs:  updateTs,
					Operator:  operator,
				},
				Rivals: make([]*AppointPkRivalInfo, 0),
			}
		}

		// 如果有对手信息，添加到对手列表
		if rivalUid != nil && rivalBeginTs != nil {
			rival := &AppointPkRivalInfo{
				AppointId: appointId,
				Uid:       *rivalUid,
				BeginTs:   *rivalBeginTs,
				UpdateTs:  *rivalUpdateTs,
			}
			appointPkMap[appointId].Rivals = append(appointPkMap[appointId].Rivals, rival)
		}
	}

	// 转换为切片并保持排序
	for _, info := range appointPkMap {
		infoList = append(infoList, info)
	}

	log.DebugWithCtx(ctx, "GetAppointPkInfoListOptimized result count: %d", len(infoList))
	return infoList, nil
}

// BatchGetAppointPkInfoWithRivals 批量获取指定PK信息及其对手信息
// 优化版本，避免N+1查询
func (s *Store) BatchGetAppointPkInfoWithRivals(ctx context.Context, appointIds []uint32) (map[uint32]*AppointPkInfoWithRivals, error) {
	result := make(map[uint32]*AppointPkInfoWithRivals)

	if len(appointIds) == 0 {
		return result, nil
	}

	// 构建IN查询的占位符
	placeholders := make([]string, len(appointIds))
	args := make([]interface{}, len(appointIds))
	for i, id := range appointIds {
		placeholders[i] = "?"
		args[i] = id
	}
	inClause := strings.Join(placeholders, ",")

	// 使用LEFT JOIN一次性获取所有数据
	sql := fmt.Sprintf(`
		SELECT 
			a.appoint_id, a.uid, a.begin_ts, a.end_ts, a.update_ts, a.operator,
			r.uid as rival_uid, r.begin_ts as rival_begin_ts, r.update_ts as rival_update_ts
		FROM %s a 
		LEFT JOIN %s r ON a.appoint_id = r.appoint_id
		WHERE a.appoint_id IN (%s)
		ORDER BY a.appoint_id, r.begin_ts ASC`,
		tblAppointPkInfo, tblAppointPkRivalInfo, inClause)

	log.DebugWithCtx(ctx, "BatchGetAppointPkInfoWithRivals SQL: %s, appointIds: %v", sql, appointIds)

	rows, err := s.db.QueryContext(ctx, sql, args...)
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetAppointPkInfoWithRivals query failed: %v", err)
		return result, err
	}
	defer rows.Close()

	// 解析结果
	for rows.Next() {
		var appointId, uid, beginTs, endTs, updateTs uint32
		var operator string
		var rivalUid, rivalBeginTs, rivalUpdateTs *uint32

		err := rows.Scan(
			&appointId, &uid, &beginTs, &endTs, &updateTs, &operator,
			&rivalUid, &rivalBeginTs, &rivalUpdateTs,
		)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchGetAppointPkInfoWithRivals scan failed: %v", err)
			continue
		}

		// 如果是新的appointId，创建新的记录
		if _, exists := result[appointId]; !exists {
			result[appointId] = &AppointPkInfoWithRivals{
				AppointPkInfo: AppointPkInfo{
					AppointId: appointId,
					Uid:       uid,
					BeginTs:   beginTs,
					EndTs:     endTs,
					UpdateTs:  updateTs,
					Operator:  operator,
				},
				Rivals: make([]*AppointPkRivalInfo, 0),
			}
		}

		// 如果有对手信息，添加到对手列表
		if rivalUid != nil && rivalBeginTs != nil {
			rival := &AppointPkRivalInfo{
				AppointId: appointId,
				Uid:       *rivalUid,
				BeginTs:   *rivalBeginTs,
				UpdateTs:  *rivalUpdateTs,
			}
			result[appointId].Rivals = append(result[appointId].Rivals, rival)
		}
	}

	log.DebugWithCtx(ctx, "BatchGetAppointPkInfoWithRivals result count: %d", len(result))
	return result, nil
}

// GetNeedProcAppointPkListOptimized 优化后的获取需要处理的指定PK列表
// 使用索引优化查询性能
func (s *Store) GetNeedProcAppointPkListOptimized(ctx context.Context, nowTs, futureTs uint32) ([]*AppointPkInfoWithRivals, error) {
	infoList := make([]*AppointPkInfoWithRivals, 0)

	// 使用优化后的索引查询
	sql := fmt.Sprintf(`
		SELECT 
			a.appoint_id, a.uid, a.begin_ts, a.end_ts, a.update_ts, a.operator,
			r.uid as rival_uid, r.begin_ts as rival_begin_ts, r.update_ts as rival_update_ts
		FROM %s a 
		LEFT JOIN %s r ON a.appoint_id = r.appoint_id
		WHERE a.begin_ts <= ? AND a.end_ts >= ?
		ORDER BY a.begin_ts ASC, a.appoint_id ASC, r.begin_ts ASC`,
		tblAppointPkInfo, tblAppointPkRivalInfo)

	log.DebugWithCtx(ctx, "GetNeedProcAppointPkListOptimized SQL: %s, nowTs: %d, futureTs: %d", sql, nowTs, futureTs)

	rows, err := s.db.QueryContext(ctx, sql, futureTs, nowTs)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetNeedProcAppointPkListOptimized query failed: %v", err)
		return infoList, err
	}
	defer rows.Close()

	// 解析结果，处理一对多关系
	appointPkMap := make(map[uint32]*AppointPkInfoWithRivals)
	appointIdOrder := make([]uint32, 0) // 保持顺序

	for rows.Next() {
		var appointId, uid, beginTs, endTs, updateTs uint32
		var operator string
		var rivalUid, rivalBeginTs, rivalUpdateTs *uint32

		err := rows.Scan(
			&appointId, &uid, &beginTs, &endTs, &updateTs, &operator,
			&rivalUid, &rivalBeginTs, &rivalUpdateTs,
		)
		if err != nil {
			log.ErrorWithCtx(ctx, "GetNeedProcAppointPkListOptimized scan failed: %v", err)
			continue
		}

		// 如果是新的appointId，创建新的记录
		if _, exists := appointPkMap[appointId]; !exists {
			appointPkMap[appointId] = &AppointPkInfoWithRivals{
				AppointPkInfo: AppointPkInfo{
					AppointId: appointId,
					Uid:       uid,
					BeginTs:   beginTs,
					EndTs:     endTs,
					UpdateTs:  updateTs,
					Operator:  operator,
				},
				Rivals: make([]*AppointPkRivalInfo, 0),
			}
			appointIdOrder = append(appointIdOrder, appointId)
		}

		// 如果有对手信息，添加到对手列表
		if rivalUid != nil && rivalBeginTs != nil {
			rival := &AppointPkRivalInfo{
				AppointId: appointId,
				Uid:       *rivalUid,
				BeginTs:   *rivalBeginTs,
				UpdateTs:  *rivalUpdateTs,
			}
			appointPkMap[appointId].Rivals = append(appointPkMap[appointId].Rivals, rival)
		}
	}

	// 按顺序转换为切片
	for _, appointId := range appointIdOrder {
		if info, exists := appointPkMap[appointId]; exists {
			infoList = append(infoList, info)
		}
	}

	log.DebugWithCtx(ctx, "GetNeedProcAppointPkListOptimized result count: %d", len(infoList))
	return infoList, nil
}
