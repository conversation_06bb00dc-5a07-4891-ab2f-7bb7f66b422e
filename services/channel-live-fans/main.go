package main

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"os"

	"golang.52tt.com/services/channel-live-fans/server"

	"google.golang.org/grpc"

	"golang.52tt.com/pkg/config"
	grpcEx "golang.52tt.com/pkg/foundation/grpc/server"
	"golang.52tt.com/protocol/services/channellivefans"

	_ "golang.52tt.com/pkg/hub/tyr/compatible/server" // 兼容tyr公共库
)

func main() {

	flag := grpcEx.ParseServerFlags(os.Args)
	fmt.Println(os.Args[0])

	var (
		svr *server.ChannelLiveFansServer
		err error
	)
	initializer := func(ctx context.Context, s *grpc.Server, sc *config.ServerConfig) error {
		kafka.InitEventLinkSubWithGrpcSvr(s)

		svr, err = server.NewChannelLiveFansServer(ctx, sc.Configer)
		if err != nil {
			return err
		}

		channellivefans.RegisterChannelLiveFansServer(s, svr)
		return nil
	}

	closer := func(ctx context.Context, s *grpc.Server) error {
		if svr != nil {
			svr.ShutDown()
		}
		return nil
	}

	s := grpcEx.NewServer(
		flag,
		grpcEx.WithGRPCServerOptions(grpc.UnaryInterceptor(grpcEx.StatusCodeUnaryInterceptor)),
		grpcEx.WithGRPCServerInitializer(initializer),
		grpcEx.WithGRPCServerCloser(closer),
		grpcEx.WithDefaultConfig("channel-live-fans.json", grpcEx.AdapterJSON),
	)

	s.Serve()
	//

}

//
//
