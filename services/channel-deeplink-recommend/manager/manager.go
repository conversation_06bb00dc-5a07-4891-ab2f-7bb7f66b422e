package manager

import (
	"context"
	"gitlab.ttyuyin.com/golang/gudetama/oss/datacenter"
	"golang.52tt.com/clients/account"
	channellivemgr "golang.52tt.com/clients/channel-live-mgr"
	entertainment "golang.52tt.com/clients/entertainmentrecommendback"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channel-deeplink-recommend"
	"golang.52tt.com/services/channel-deeplink-recommend/cache"
	"golang.52tt.com/services/channel-deeplink-recommend/conf"
	"math/rand"
	"time"
)

// 相关排名默认从 1 开始

const maxCacheChannelCnt = 250 // 本地缓存默认缓存前N个房间

const defaultRecommendWayRankBegin = 16
const defaultRecommendWayMinCnt = 30
const defaultRecommendWayMaxCnt = 50

const recommendChannelMinCnt = 5 // 最小推荐数，若小于该值则走兜底逻辑

type ChannelDeeplinkRecommendMgr struct {
	shutDown         chan interface{}
	sc               *conf.ServiceConfigT
	cache            *cache.ChannelDeeplinkRecommendCache
	localCacheMgr    *LocalCacheManager
	accountCli       *account.Client
	entertainmentCli *entertainment.Client
	liveMgrCli       *channellivemgr.Client
}

func NewChannelDeeplinkRecommendMgr(sc *conf.ServiceConfigT, cache *cache.ChannelDeeplinkRecommendCache) *ChannelDeeplinkRecommendMgr {
	accountCli, _ := account.NewClient()
	liveMgrCli, _ := channellivemgr.NewClient()

	mgr := &ChannelDeeplinkRecommendMgr{
		sc:               sc,
		shutDown:         make(chan interface{}),
		cache:            cache,
		localCacheMgr:    newLocalCacheManager(),
		accountCli:       accountCli,
		entertainmentCli: entertainment.NewClient(),
		liveMgrCli:       liveMgrCli,
	}

	mgr.timer(mgr.updateLivingRecommendChannel, 3*time.Minute)
	mgr.timer(mgr.checkIfAddDiversionChannelList, time.Second)

	// 随机种子
	rand.Seed(time.Now().UnixNano())

	return mgr
}

func (m *ChannelDeeplinkRecommendMgr) RecommendChannel(ctx context.Context, in *pb.GetRecommendChannelByRulesReq) uint32 {
	cid := uint32(0)
	isDefaultRecommend := false

	if in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_LivingChannelMemCnt) {
		cid, isDefaultRecommend = m.recommendChannelCommon(ctx, in)

	} else if in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_DiversionConf) ||
		in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_DiversionConfWithRate) {

		cid, isDefaultRecommend = m.recommendChannelFromDiversionConf(ctx, in)
	}

	t := uint32(0)
	if isDefaultRecommend {
		t = 1
	}

	DataCenterReport(in.GetUid(), cid, t, "")
	return cid
}

func (m *ChannelDeeplinkRecommendMgr) AddRecommendDiversionConf(ctx context.Context, in *pb.AddRecommendDiversionConfReq) (*pb.AddRecommendDiversionConfResp, error) {
	out := &pb.AddRecommendDiversionConfResp{}
	list := make([]*cache.ChannelDiversionRate, 0, len(in.GetDiversionConf()))

	for _, c := range in.GetDiversionConf() {
		if c.GetChannelId() == 0 {
			continue
		}

		list = append(list, &cache.ChannelDiversionRate{Cid: c.GetChannelId(), Rate: c.GetRate()})
	}

	if len(list) == 0 {
		log.ErrorWithCtx(ctx, "AddRecommendDiversionConf fail. conf list err")
		return out, protocol.NewServerError(status.ErrBadRequest)
	}

	idx, err := m.cache.ResetChannelDiversion(list)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddRecommendDiversionConf fail to ResetChannelDiversion. in:%+v, err:%v", in, err)
		return out, err
	}

	out.ConfIdx = idx

	log.InfoWithCtx(ctx, "AddRecommendDiversionConf list:%+v", list)
	return out, nil
}

func (m *ChannelDeeplinkRecommendMgr) recommendChannelCommon(ctx context.Context, in *pb.GetRecommendChannelByRulesReq) (uint32, bool) {

	log.DebugfWithCtx(ctx, "recommendChannelCommon in:%+v", in)

	matchChannelList := make([]uint32, 0)
	wantSex, notLimit := m.getMatchSex(ctx, in.GetUid(), in.GetSexMode())
	channelList := m.localCacheMgr.getLivingChannelRecommendList()

	for _, info := range channelList {
		// 筛选性别符合的
		if !notLimit && info.AnchorSex != wantSex {
			continue
		}

		// 筛选sub_tag_id符合的
		if len(in.GetSubTagIdList()) > 0 {
			for _, tagId := range in.GetSubTagIdList() {
				if tagId == info.TagId {
					matchChannelList = append(matchChannelList, info.ChannelId)
					break
				}
			}
		} else {
			matchChannelList = append(matchChannelList, info.ChannelId)
		}
	}

	begin, end := in.GetBeginRank(), in.GetEndRank()
	listLen := uint32(len(matchChannelList))
	matchCnt := uint32(0)

	if listLen >= begin {
		if listLen > end {
			matchCnt = end - begin + 1
		} else {
			matchCnt = listLen - begin + 1
		}
	}

	if matchCnt < recommendChannelMinCnt {
		return m.defaultRecommendWay(ctx, channelList, in), true
	}

	r := uint32(rand.Int31n(int32(matchCnt))) + begin
	if r <= 0 {
		log.ErrorWithCtx(ctx, "recommendChannelCommon fail. r <= 0 will panic")
		return 0, false
	}

	return matchChannelList[r-1], false
}

// 从分流配置列表中推荐房间
func (m *ChannelDeeplinkRecommendMgr) recommendChannelFromDiversionConf(ctx context.Context, in *pb.GetRecommendChannelByRulesReq) (uint32, bool) {

	log.DebugfWithCtx(ctx, "recommendChannelFromDiversionConf in:%+v", in)

	info := m.localCacheMgr.getDiversionChannelRecommendList(in.GetDivisionConfIdx())

	if len(info.ChannelList) < recommendChannelMinCnt {
		channelList := m.localCacheMgr.getLivingChannelRecommendList()
		return m.defaultRecommendWay(ctx, channelList, in), true
	}

	if in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_DiversionConf) {
		r := rand.Int31n(int32(len(info.ChannelList)))
		return info.ChannelList[r].ChannelId, false

	} else if in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_DiversionConfWithRate) {
		if info.TotalRate == 0 {
			return 0, false
		}

		r := rand.Int31n(int32(info.TotalRate)) + 1
		tmpRate := uint32(0)

		for _, c := range info.ChannelList {
			tmpRate += c.Rate
			if uint32(r) <= tmpRate {
				return c.ChannelId, false
			}
		}
	}

	return 0, false
}

// 兜底策略
func (m *ChannelDeeplinkRecommendMgr) defaultRecommendWay(ctx context.Context, channelList []*LivingRecommendChannel, in *pb.GetRecommendChannelByRulesReq) uint32 {
	if in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_LivingChannelMemCnt) {
		return m.defaultRecommendChannelWithTagIdLimit(in.GetUid(), channelList, in.GetSubTagIdList())

	} else if in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_DiversionConf) ||
		in.GetDiversionMode() == uint32(pb.GetRecommendChannelByRulesReq_DiversionConfWithRate) {

		return m.defaultRecommendChannelCommon(in.GetUid(), channelList)
	}

	return 0
}

func (m *ChannelDeeplinkRecommendMgr) defaultRecommendChannelWithTagIdLimit(uid uint32, channelList []*LivingRecommendChannel, subTagIdList []uint32) uint32 {

	log.Debugf("defaultRecommendChannelWithTagIdLimit uid:%d", uid)

	if len(subTagIdList) == 0 {
		return m.defaultRecommendChannelCommon(uid, channelList)
	}

	matchChannelList := make([]uint32, 0)
	for _, info := range channelList {
		// 筛选tag_id符合的
		for _, tagId := range subTagIdList {
			if tagId == info.TagId {
				matchChannelList = append(matchChannelList, info.ChannelId)
				break
			}
		}
	}

	matchCnt := len(matchChannelList)
	if matchCnt < defaultRecommendWayMinCnt || matchCnt < defaultRecommendWayRankBegin {
		return m.defaultRecommendChannelCommon(uid, channelList)
	}

	if matchCnt > defaultRecommendWayMaxCnt {
		matchCnt = defaultRecommendWayMaxCnt
	}

	r := rand.Int31n(int32(matchCnt-defaultRecommendWayRankBegin+1)) + defaultRecommendWayRankBegin
	if r <= 0 {
		log.Errorf("defaultRecommendChannelWithTagIdLimit fail. r <= 0 will panic")
		return 0
	}

	return matchChannelList[r-1]
}

func (m *ChannelDeeplinkRecommendMgr) defaultRecommendChannelCommon(uid uint32, channelList []*LivingRecommendChannel) uint32 {

	log.Debugf("defaultRecommendChannelCommon uid:%d", uid)

	matchCnt := len(channelList)
	if matchCnt == 0 {
		return 0
	}

	if matchCnt < defaultRecommendWayMinCnt || matchCnt < defaultRecommendWayRankBegin {
		r := rand.Int31n(int32(matchCnt))
		return channelList[r].ChannelId

	} else {
		if matchCnt > defaultRecommendWayMaxCnt {
			matchCnt = defaultRecommendWayMaxCnt
		}

		r := rand.Int31n(int32(matchCnt-defaultRecommendWayRankBegin+1)) + defaultRecommendWayRankBegin
		if r <= 0 {
			log.Errorf("defaultRecommendChannelCommon fail. r <= 0 will panic")
			return 0
		}

		return channelList[r-1].ChannelId
	}
}

func (m *ChannelDeeplinkRecommendMgr) timer(handle func() error, d time.Duration) {
	_ = handle()

	go func() {
		for {
			select {
			case <-m.shutDown:
				return
			case <-time.After(d):
				_ = handle()
			}
		}
	}()
}

func (m *ChannelDeeplinkRecommendMgr) updateLivingRecommendChannel() error {
	_ = m.updateRecommendChannelList()
	_ = m.updateDiversionChannelList()
	return nil
}

func (m *ChannelDeeplinkRecommendMgr) updateRecommendChannelList() error {
	channelList, err := m.cache.RangeChannelMemCntTopN(YuYinTagId, maxCacheChannelCnt)
	if err != nil {
		log.Errorf("updateRecommendChannelList fail to RangeChannelMemCntTopN. err:%v", err)
		return err
	}

	mapTagId, err := m.cache.BatchGetChannelTagId(channelList)
	if err != nil {
		log.Errorf("updateRecommendChannelList fail to BatchGetChannelTagId. err:%v", err)
		return err
	}

	mapSex, err := m.cache.BatchGetChannelAnchorSex(channelList)
	if err != nil {
		log.Errorf("updateRecommendChannelList fail to BatchGetChannelAnchorSex. err:%v", err)
		return err
	}

	updateList := make([]*LivingRecommendChannel, 0, len(channelList))
	for _, cid := range channelList {
		tagId, ok := mapTagId[cid]
		if !ok {
			continue
		}

		sex, ok := mapSex[cid]
		if !ok {
			continue
		}

		updateList = append(updateList, &LivingRecommendChannel{
			ChannelId: cid,
			TagId:     tagId,
			AnchorSex: sex,
		})
	}

	m.localCacheMgr.updateLivingChannelRecommendList(updateList)
	return nil
}

func (m *ChannelDeeplinkRecommendMgr) checkIfAddDiversionChannelList() error {
	currVersion, err := m.cache.GetChannelDiversionVersion()
	if err != nil {
		log.Errorf("checkIfAddDiversionChannelList fail to GetChannelDiversionVersion. err:%v", err)
		return err
	}

	if currVersion == m.localCacheMgr.getDiversionVersion() {
		return nil
	}

	err = m.updateDiversionChannelListByIdx(currVersion)
	if err != nil {
		log.Errorf("checkIfAddDiversionChannelList fail to updateDiversionChannelListByIdx. err:%v", err)
		return err
	}

	log.Infof("checkIfAddDiversionChannelList add success")
	return nil
}

func (m *ChannelDeeplinkRecommendMgr) updateDiversionChannelListByIdx(idx uint32) error {
	diversionList, err := m.cache.GetChannelDiversion(cache.GetDiversionIdx(idx))
	if err != nil {
		log.Errorf("updateDiversionChannelListByIdx fail to GetChannelDiversion. err:%v", err)
		return err
	}

	cidList := make([]uint32, 0, len(diversionList))
	for _, info := range diversionList {
		cidList = append(cidList, info.Cid)
	}

	mapLivingChannel, err := m.cache.BatchGetLivingChannel(cidList)
	if err != nil {
		log.Errorf("updateDiversionChannelListByIdx fail to BatchGetLivingChannel. err:%v", err)
		return err
	}

	updateList := make([]*DiversionRecommendChannel, 0, len(diversionList))
	totalRate := uint32(0)
	for _, info := range diversionList {

		// 只缓存在开播的房间
		if living, ok := mapLivingChannel[info.Cid]; !ok || !living {
			continue
		}

		if info.Rate == 0 {
			info.Rate = 1
		}

		totalRate += info.Rate
		updateList = append(updateList, &DiversionRecommendChannel{
			ChannelId: info.Cid,
			Rate:      info.Rate,
		})
	}

	m.localCacheMgr.updateDiversionChannelRecommendList(&DiversionInfo{ChannelList: updateList, TotalRate: totalRate}, idx, false)
	log.Debugf("updateDiversionChannelListByIdx idx:%v, list:%+v, total_rate:%d", idx, updateList, totalRate)

	return nil
}

func (m *ChannelDeeplinkRecommendMgr) updateDiversionChannelList() error {
	for i := uint32(0); i < cache.DiversionCnt; i++ {
		err := m.updateDiversionChannelListByIdx(i)
		if err != nil {
			log.Errorf("updateDiversionChannelList fail to updateDiversionChannelListByIdx. err:%v", err)
			return err
		}
		/*diversionList, err := m.cache.GetChannelDiversion(cache.GetDiversionIdx(i))
		if err != nil {
			log.Errorf("updateDiversionChannelList fail to GetChannelDiversion. err:%v", err)
			return err
		}

		cidList := make([]uint32, 0, len(diversionList))
		for _, info := range diversionList {
			cidList = append(cidList, info.Cid)
		}

		mapLivingChannel, err := m.cache.BatchGetLivingChannel(cidList)
		if err != nil {
			log.Errorf("updateDiversionChannelList fail to BatchGetLivingChannel. err:%v", err)
			return err
		}

		updateList := make([]*DiversionRecommendChannel, 0, len(diversionList))
		totalRate := uint32(0)
		for _, info := range diversionList {

			// 只缓存在开播的房间
			if living, ok := mapLivingChannel[info.Cid]; !ok || !living {
				continue
			}

			if info.Rate == 0 {
				info.Rate = 1
			}

			totalRate += info.Rate
			updateList = append(updateList, &DiversionRecommendChannel{
				ChannelId: info.Cid,
				Rate:      info.Rate,
			})
		}

		m.localCacheMgr.updateDiversionChannelRecommendList(&DiversionInfo{ChannelList: updateList, TotalRate: totalRate}, i, false)
		log.Debugf("updateDiversionChannelList idx:%v, list:%+v, total_rate:%d", i, updateList, totalRate)*/
	}
	return nil
}

func (m *ChannelDeeplinkRecommendMgr) getMatchSex(ctx context.Context, uid, sexMode uint32) (uint32, bool) {

	switch pb.GetRecommendChannelByRulesReq_SexMode(sexMode) {
	case pb.GetRecommendChannelByRulesReq_NotLimit:
		return 0, true

	case pb.GetRecommendChannelByRulesReq_OppositeSex:
		user, serr := m.accountCli.GetUserByUid(ctx, uid)
		if serr != nil {
			log.ErrorWithCtx(ctx, "getMatchSex fail to GetUserByUid. uid:%d, sexMode:%d, err:%v", uid, sexMode, serr)
			return 0, true
		}

		if user.GetSex() == account.Female {
			return uint32(account.Male), false
		} else {
			return uint32(account.Female), false
		}

	case pb.GetRecommendChannelByRulesReq_SameSex:
		user, serr := m.accountCli.GetUserByUid(ctx, uid)
		if serr != nil {
			log.ErrorWithCtx(ctx, "getMatchSex fail to GetUserByUid. uid:%d, sexMode:%d, err:%v", uid, sexMode, serr)
			return 0, true
		}

		if user.GetSex() == account.Male {
			return uint32(account.Male), false
		} else {
			return uint32(account.Female), false
		}

	case pb.GetRecommendChannelByRulesReq_Female:
		return uint32(account.Female), false

	case pb.GetRecommendChannelByRulesReq_Male:
		return uint32(account.Male), false
	}

	return 0, true
}

// 主播任务数据上报
func DataCenterReport(uid, channelId, t uint32, link string) {
	datacenter.StdReportKV("************", map[string]interface{}{
		"uid":        uid,
		"link":       link,
		"channelId":  channelId,
		"type":       t,
		"totalDate":  time.Now().Format("2006-01-02 15:04:05"),
		"createTime": time.Now().Format("2006-01-02 15:04:05"),
	})

	log.Infof("DataCenterReport uid:%d, cid:%d, t:%d, link:%s", uid, channelId, t, link)
}

func (m *ChannelDeeplinkRecommendMgr) ShutDown() {
	close(m.shutDown)
	m.liveMgrCli.Close()
	m.accountCli.Close()
	m.entertainmentCli.Close()
}
