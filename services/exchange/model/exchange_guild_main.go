package model

import (
	"github.com/jinzhu/gorm"
	"time"
)

type ExchangeGuildMain struct {
	MasterUid uint32 `gorm:"primary_key;auto_increment:false"`
	Company string `gorm:"not null;type:varchar(1024)"`
	CompanyCode string `gorm:"not null;type:varchar(1024)"`
	Legal<PERSON>erson string `gorm:"not null;type:varchar(200)"`
	PlaceCount uint32 `gorm:"not null"`
	CreateAt uint32 `gorm:"not null;"`
	UpdateAt uint32 `gorm:"not null"`
	OaAccountId string `gorm:"not null;type:varchar(200);default:''"`
	BusinessLicense    string `gorm:"not null;default:'';type:varchar(1024)"` // 营业执照电子版
	CompanyAccountName string `gorm:"not null;default:'';type:varchar(1024)"` // 公司开户名称
	BankAccount        string `gorm:"not null;default:'';type:varchar(1024)"` // 银行账户
	BankBranch         string `gorm:"not null;default:'';type:varchar(1024)"` // 开户支行
	BankBasicAccount   string `gorm:"not null;default:'';type:varchar(1024)"` // 基本存款账户信息表电子版
	GeneralTaxpayer    string `gorm:"not null;default:'';type:varchar(1024)"` // 一般纳税人
	InvoiceType int32 `gorm:"not null;default:0"`                             // 发票类型
	ContactName        string `gorm:"not null;default:'';type:varchar(255)"`  // 联系人
	ContactPhone       string `gorm:"not null;default:'';type:varchar(255)"`  // 联系电话
	ContactAddr        string `gorm:"not null;default:'';type:varchar(1024)"` // 联系地址
	ContactEmail       string `gorm:"not null;default:'';type:varchar(1024)"` // 联系email
	IDCard string `gorm:"not null;default:'';type:varchar(200)"`              // 法人身份证号码
}

func (exchangeGuildMain *ExchangeGuildMain) TableName() string {
	return "exchange_guild_main"
}

type ExchangeGuildMainDB struct {
	ExchangeGuildMain
	Db *gorm.DB
}

func (xdb *ExchangeGuildMainDB)AutoMigrate() error {
	return xdb.Db.AutoMigrate(&xdb.ExchangeGuildMain).Error
}

func (xdb *ExchangeGuildMainDB) GetList(offset uint32, limit uint32, list *[]ExchangeGuildMain) error {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Offset(offset).Limit(limit).Find(list).Error
}

func (xdb *ExchangeGuildMainDB) GetCount() (count uint32) {
	xdb.Db.Model(&xdb.ExchangeGuildMain).Count(&count)
	return
}

func (xdb *ExchangeGuildMainDB) Insert() error {
	xdb.ExchangeGuildMain.CreateAt = uint32(time.Now().Unix())
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Create(&xdb.ExchangeGuildMain).Error
}

func (xdb *ExchangeGuildMainDB) Save() error {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Save(&xdb.ExchangeGuildMain).Error
}

func (xdb *ExchangeGuildMainDB) Delete() error {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Delete(&xdb.ExchangeGuildMain).Error
}

func (xdb *ExchangeGuildMainDB) GetOne(masterUid uint32) {
	xdb.Db.Model(&xdb.ExchangeGuildMain).Where("master_uid=?", masterUid).First(&xdb.ExchangeGuildMain)
}

func (xdb *ExchangeGuildMainDB) GetHasPlaceCount(offset uint32, limit uint32, list *[]ExchangeGuildMain) error  {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Where("place_count>0").Offset(offset).Limit(limit).Find(list).Error
}

func (xdb *ExchangeGuildMainDB) GetHasPlaceCountCount() (count uint32)  {
	xdb.Db.Model(&xdb.ExchangeGuildMain).Where("place_count>0").Count(&count)
	return
}

func (xdb *ExchangeGuildMainDB) UpdatePlaceCount(masterUid uint32, placeCount uint32) error {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Where("master_uid=?", masterUid).Updates(map[string]interface{}{"place_count": placeCount, "update_at":time.Now().Unix()}).Error
}

func (xdb *ExchangeGuildMainDB) Update(masterUid uint32, company string, companyCode string, legalPerson string) error {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Where("master_uid=?", masterUid).Updates(map[string]interface{}{"company": company, "company_code": companyCode, "legal_person": legalPerson}).Error
}

func (xdb *ExchangeGuildMainDB) GetAll() *[]ExchangeGuildMain {
	list := &[]ExchangeGuildMain{}
	xdb.Db.Model(&xdb.ExchangeGuildMain).Find(list)
	return list
}

func (xdb *ExchangeGuildMainDB) UpdateAccountId(masterUid uint32, oaAccountId string) error {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Where("master_uid=?", masterUid).Updates(map[string]interface{}{"oa_account_id": oaAccountId, "update_at":time.Now().Unix()}).Error
}

func (xdb *ExchangeGuildMainDB) UpdateContract(masterUid uint32, contactName string, contactPhone string, contactAddr string, contactEmail string) error {
	return xdb.Db.Model(&xdb.ExchangeGuildMain).Where("master_uid=?", masterUid).Updates(map[string]interface{}{
		"contact_name": contactName, "contact_phone": contactPhone, "contact_addr": contactAddr, "contact_email": contactEmail, "update_at": time.Now().Unix()}).Error
}
