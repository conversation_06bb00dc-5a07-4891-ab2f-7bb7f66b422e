package config

import (
"encoding/json"
"fmt"
"golang.52tt.com/pkg/config"
"golang.52tt.com/pkg/log"
"io/ioutil"
)

type ServiceConfigT struct {
    MysqlConfig    *config.MysqlConfig `json:"mysql_config"`
}

func (sc *ServiceConfigT) Parse(configFile string) (err error) {
    defer func() {
        if e := recover(); e != nil {
            err = fmt.Errorf("Failed to parse config: %v \n", e)
        }
    }()

    data, err := ioutil.ReadFile(configFile)
    if err != nil {
        return err
    }
    err = json.Unmarshal(data, &sc)
    if err != nil {
        return err
    }

    log.Infof("ServiceConfigT:MysqlConfig:%+v\n", sc.MysqlConfig)
    return
}

func (sc *ServiceConfigT) GetMysqlConfig() *config.MysqlConfig {
    return sc.MysqlConfig
}



