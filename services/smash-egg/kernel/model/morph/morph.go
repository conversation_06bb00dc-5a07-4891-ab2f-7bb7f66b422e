package morph

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/pkg/protocol"
	public_notice "golang.52tt.com/protocol/services/public-notice"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	ab_control "golang.52tt.com/services/smash-egg/ab-control"
	"golang.52tt.com/services/smash-egg/kernel/model/activity_conf"
	"math"
	"sync"
	"sync/atomic"
	"time"

	"github.com/go-redis/redis"
	"github.com/jmoiron/sqlx"
	pushService "golang.52tt.com/clients/push-notification/v2"
	ga "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/services/smash-egg/kernel/entity"
	"golang.52tt.com/services/smash-egg/kernel/model/setting"
	runCfg "golang.52tt.com/services/smash-egg/kernel/server/config"
)

const (
	timeLayoutStandard = "2006-01-02 15:04:05"
)

type Morph struct {
	cache     *Cache
	store     *Store
	setting   *setting.Setting
	actCfgMgr *activity_conf.SmashActivityMgr
	abControl *ab_control.ABControl

	morphHits uint32
	notMorph  int32

	pushClient      *pushService.Client
	publicNoticeCli public_notice.PublicNoticeClient
	stop            chan interface{}
	wg              sync.WaitGroup
}

func NewMorph(redisClient *redis.Client, dbClient *sqlx.DB, setting *setting.Setting, pushClient *pushService.Client,
	publicNoticeCli public_notice.PublicNoticeClient, activityMgr *activity_conf.SmashActivityMgr) *Morph {
	s := &Morph{}
	s.cache = NewCache(redisClient)
	s.store = &Store{db: dbClient}
	s.setting = setting
	s.actCfgMgr = activityMgr
	s.pushClient = pushClient
	s.publicNoticeCli = publicNoticeCli
	s.abControl, _ = ab_control.NewABControl()
	s.stop = make(chan interface{})

	err := s.store.CreateMorphRecordTable()
	if err != nil {
		log.Errorf("CreateMorphRecordTable fail. err:%v", err)
	}

	return s
}

func (s *Morph) Start() error {
	//变身状态检测
	go func() {
		var (
			morph bool
			err   error
		)

		s.wg.Add(1)

		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Second):
				//变身冻结5个同步周期
				if atomic.LoadInt32(&s.notMorph) < 5 {
					break
				}

				morph, err = s.morphCheck()
				if err != nil {
					log.Errorf("morphCheck fail: %s", err.Error())
				} else {
					log.Debugf("morphCheck >> %v", morph)
				}
			}
		}
	}()

	// 同步变身状态与击打值
	go func() {
		var (
			err   error
			morph bool
		)

		s.wg.Add(1)

		for {
			select {
			case <-s.stop:
				s.wg.Done()
				return
			case <-time.After(time.Second * 3):
				//转转状态同步以及数据维护
				morph, _, err = s.morphSync()
				if err != nil {
					log.Errorf("morphCheck fail : %s", err.Error())
					break
				} else {
					log.Debugf("[stable]morphSync >> morph=%v, notMorph=%d", morph, atomic.LoadInt32(&s.notMorph))
				}

				//转转播报
				if morph {
					atomic.StoreInt32(&s.notMorph, 0)
				} else {
					atomic.AddInt32(&s.notMorph, 1)
				}
			}
		}
	}()
	return nil
}

func (s *Morph) Stop() {
	close(s.stop)
	s.wg.Wait()
}

func (s *Morph) GetMorphHits() uint32 {
	return atomic.LoadUint32(&s.morphHits)
}

func (s *Morph) AddMorphHits(t time.Time, hits uint32) (err error) {
	return s.addMorphHits(t, hits)
}

func (s *Morph) Morphing() (ok bool, end time.Time, err error) {
	return s.cache.Morphing()
}

func (s *Morph) calcVirtualHits(hits, speedStep, speedUp uint32, maxSpeed float32) uint32 {
	const maxUint32 = float64(^uint32(0))

	if 0 == speedUp {
		return hits
	}

	virtualHits := float64(speedStep) * (1 + math.Min(float64(hits)/float64(speedUp), float64(maxSpeed)))
	if virtualHits > maxUint32 {
		return speedStep
	} else {
		if hits > uint32(virtualHits) {
			return hits
		}
		return uint32(virtualHits)
	}
}

func (s *Morph) getMorphHits(cfg *entity.SystemConfig) (hits uint32, err error) {
	var start time.Time
	start, err = s.cache.GetNextMorphCountingTime()
	if nil != err {
		return
	}

	var data map[string]uint32
	data, err = s.cache.GetNextMorphHits()
	if nil != err {
		return
	}

	var count int
	if start.IsZero() {
		count = 1
	} else {
		count = int(time.Since(start) / time.Minute)
	}

	for k, v := range data {
		t := s.calcVirtualHits(v, cfg.SpeedStep, cfg.SpeedUp, cfg.MaxSpeed)

		log.Debugf("[stable]calcVirtualHits %s >> hit=%d, step=%d, up=%d, maxIndex=%.2f, vir=%d", k, v, cfg.SpeedStep, cfg.SpeedUp, cfg.MaxSpeed, t)

		hits += t
		count--
	}

	if count > 0 {
		hits += uint32(count) * cfg.SpeedStep
	}
	return
}

func (s *Morph) addMorphHits(t time.Time, hits uint32) (err error) {
	var start time.Time
	start, err = s.cache.GetNextMorphCountingTime()
	if nil != err {
		return
	}

	var part string
	if start.IsZero() {
		part = "default"
	} else {
		if t.Before(start) {
			return
		} else {
			part = fmt.Sprintf("part-%d", (t.Unix()-start.Unix())/60+1)
		}
	}

	var total uint32
	total, err = s.cache.AddNextMorphHits(part, hits)
	if nil != err {
		return
	}

	log.Debugf("[stable]AddNextMorphHits >> %s - %d at %s, %s", part, total, start.Format(timeLayoutStandard), t.Format(timeLayoutStandard))
	return
}

func (s *Morph) morphCheck() (morph bool, err error) {
	var cfg entity.SystemConfig
	err = s.setting.GetSystemConfig(&cfg)
	if nil != err {
		log.Errorf("get system config failed: %+v", err)
		return
	}

	if (runCfg.StatusEnable != cfg.Status && runCfg.StatusTesting != cfg.Status) || 0 == cfg.MorphHits {
		return
	}

	var currentHits uint32
	currentHits, err = s.getMorphHits(&cfg)
	if nil != err {
		log.Errorf("get current hits failed: %+v", err)
		return
	}

	atomic.StoreUint32(&s.morphHits, currentHits)
	readyHits := uint32(0.9 * float32(cfg.MorphHits))

	log.Debugf("[stable]current hits=%+v, morph ready hits=%+v, morphing hits=%+v", currentHits, readyHits, cfg.MorphHits)
	if 0 == currentHits || currentHits < readyHits {
		return
	}

	now := time.Now()
	actCfg, err := s.actCfgMgr.GetSmashActivityConfWithCache(context.Background(), false)
	if err != nil {
		log.Errorf("morphCheck get smash activity config failed: %+v", err)
		return
	}

	if actCfg == nil ||
		now.Unix() < actCfg.GetBeginTime() || now.Unix() > actCfg.GetEndTime() {
		return
	}

	currTheme := runCfg.ThemeName

	if currentHits >= cfg.MorphHits {
		dur := time.Second * time.Duration(cfg.MorphDuration)
		end := now.Add(dur)

		var ok bool
		ok, err = s.cache.MarkMorph(end, dur)
		if !ok {
			if err != nil {
				log.Errorf("start morph failed:%+v", err)
			}
		} else {
			//通知变身
			news := &ga.SmashEggBreakingNews{
				CurrentHits:     0,
				MorphHits:       cfg.MorphHits,
				MorphFlag:       uint32(ga.SmashEggBreakingNews_START),
				MorphEndTime:    uint32(end.Unix()),
				CurActivityType: uint32(ab_control.GetCurrActivityType(currTheme)),
				IconUrl:         "",
				Content:         fmt.Sprintf("%s已变身，立即打开转起来，赢豪礼！", actCfg.GetActivityName()),
			}
			s.MorphNotify(news)

			morph = true
		}
		return
	}

	if currentHits >= readyHits {
		ok, err := s.cache.MarkReadyToMorph(now)
		if !ok {
			if err != nil {
				log.Errorf("morph ready failed: %+v", err)
			}
		} else {
			//通知准备变身
			news := &ga.SmashEggBreakingNews{
				CurrentHits:     currentHits,
				MorphHits:       cfg.MorphHits,
				MorphFlag:       uint32(ga.SmashEggBreakingNews_READY),
				MorphEndTime:    0,
				CurActivityType: uint32(ab_control.GetCurrActivityType(currTheme)),
				IconUrl:         "",
				Content:         fmt.Sprintf("%s即将变身，立即打开等待大奖的来临吧！", actCfg.GetActivityName()),
			}
			s.MorphNotify(news)
		}
	}
	return
}

func (s *Morph) MorphNotify(news *ga.SmashEggBreakingNews) {
	// 旧版本推送
	err := s.pushMorphNotifyNews(uint32(ga.PushMessage_SMASH_EGGS_BREAKING_EVENT), news)
	if nil != err {
		log.Errorf("MorphNotify pushMorphNotifyNews failed, news:%v, err:%+v", news, err)
	}

	// 新版本推送
	err = s.pushMorphNotifyNews(uint32(ga.PushMessage_SMASH_EGGS_BREAKING_EVENT_V2), news)
	if nil != err {
		log.Errorf("MorphNotify pushMorphNotifyNews failed, news:%v, err:%+v", news, err)
	}
}

func (s *Morph) pushMorphNotifyNews(cmd uint32, msg *ga.SmashEggBreakingNews) error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	channelMsgBin, err := msg.Marshal()
	if err != nil {
		log.ErrorWithCtx(ctx, "pushMorphNotifyNews marshal err: %s, %+v", err.Error(), msg)
		return err
	}

	seq := uint32(time.Now().Unix())
	pushMessage := &ga.PushMessage{
		Cmd:     cmd,
		Content: channelMsgBin,
		SeqId:   seq,
	}
	pushMessageBytes, e := pushMessage.Marshal()
	if e != nil {
		log.ErrorWithCtx(ctx, "pushMorphNotifyNews Marshal  err: %v", e)
		return e
	}

	notification := &pushPB.CompositiveNotification{
		Sequence: seq,
		TerminalTypeList: []uint32{
			protocol.MobileAndroidTT,
			protocol.MobileIPhoneTT,
		},
		TerminalTypePolicy: pushService.DefaultPolicy,
		AppId:              uint32(protocol.TT),
		ProxyNotification: &pushPB.ProxyNotification{
			Type:       uint32(pushPB.ProxyNotification_PUSH),
			Payload:    pushMessageBytes,
			Policy:     pushPB.ProxyNotification_DEFAULT,
			ExpireTime: 60,
			PushLabel:  fmt.Sprintf("CMD_%d", cmd),
		},
	}

	err = s.pushClient.PushMulticast(ctx, 1, "1@channel", []uint32{}, notification)
	if nil != err {
		log.ErrorWithCtx(ctx, "pushMorphNotifyNews msg failed:%v", err)
		return err
	}

	log.InfoWithCtx(ctx, "pushMorphNotifyNews cmd:%d, seq:%d, %+v", cmd, seq, msg)
	return nil
}

func (s *Morph) morphSync() (morph bool, end time.Time, err error) {
	morph, end, err = s.cache.Morphing()
	if err != nil {
		log.Errorf("get morph failed: %+v", err)
		return
	}

	if !morph {
		return
	}

	atomic.StoreUint32(&s.morphHits, 0)

	//充值变身统计数据
	var ok bool
	_, err = s.cache.SetNextMorphCountingTime(end)
	if nil != err {
		log.Errorf("set next morph counting time failed: %+v", err)
		return
	}

	ok, err = s.cache.ResetNextMorphHits()
	if nil != err {
		log.Errorf("reset next morph hits failed: %+v", err)
		return
	}

	if ok {
		log.Debugf("[stable]hits is rested in morphing at %s", end.Format("15:04:05"))
	}

	//删除准备变身
	err = s.cache.UnmarkReadyToMorph()
	if nil != err {
		log.Errorf("remove morph ready failed: %+v", err)
		return
	}

	//记录变身
	{
		var cfg entity.SystemConfig
		err = s.setting.GetSystemConfig(&cfg)
		if nil != err {
			log.Errorf("get system config failed: %+v", err)
			return
		}

		var dur time.Duration
		dur, err = time.ParseDuration(fmt.Sprintf("-%ds", cfg.MorphDuration))
		if err != nil {
			log.Errorf("ParseDuration failed:%v", err)
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
		defer cancel()
		err = s.store.InsertMorphRecord(ctx, end.Add(dur), cfg.MorphDuration)
		if err != nil {
			log.Errorf("InsertMorphRecord failed: %v", err)
			return
		}
	}
	return
}
