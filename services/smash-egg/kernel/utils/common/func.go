package common

import (
	"fmt"
	"time"
)

func TheBeginningOfHour(t time.Time) time.Time {
	var d time.Duration
	d, _ = time.ParseDuration(fmt.Sprintf("-%dm%ds", t.Minute(), t.Second()))
	return t.Add(d)
}

func TheEndingOfHour(t time.Time) time.Time {
	var d time.Duration
	d, _ = time.ParseDuration(fmt.Sprintf("%dm%ds", 59-t.Minute(), 59-t.Second()))
	return t.Add(d)
}

func TheBeginningOfDay(t time.Time) time.Time {
	var d time.Duration
	d, _ = time.ParseDuration(fmt.Sprintf("-%dh%dm%ds", t.Hour(), t.Minute(), t.Second()))
	return t.Add(d)
}

func TheEndingOfDay(t time.Time) time.Time {
	var d time.Duration
	d, _ = time.ParseDuration(fmt.Sprintf("%dh%dm%ds", 23-t.Hour(), 59-t.<PERSON>ute(), 59-t.Second()))
	return t.Add(d)
}

func TheHourAgo(t time.Time, c int) time.Time {
	var d time.Duration
	d, _ = time.ParseDuration(fmt.Sprintf("-%dh", c))
	return t.Add(d)
}

func TheDayAgo(t time.Time, c int) time.Time {
	var d time.Duration
	d, _ = time.ParseDuration(fmt.Sprintf("-%dh", c*24))
	return t.Add(d)
}

func TheMonthAgo(t time.Time, c int) time.Time {
	var d time.Duration
	d, _ = time.ParseDuration(fmt.Sprintf("-%dh", c*24))
	return t.Add(d)
}

