package cache

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/store/redis"
	"golang.52tt.com/services/pia/mgr"
)

type DramaListCache struct {
	redisCli redis.CmdableV2
}

func NewDramaListCache(redisCli redis.CmdableV2) mgr.IDramaRankListCache {
	return &DramaListCache{
		redisCli: redisCli,
	}
}

func (c *DramaListCache) Fetch(ctx context.Context, key string, target interface{}) error {

	targetStr, err := c.redisCli.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return mgr.ErrCacheNotFound
		}
		log.ErrorWithCtx(ctx, "DramaListCache failed to fetch, key:%s, err:%v", key, err)
		return err
	}
	err = json.Unmarshal([]byte(targetStr), target)
	if err != nil {
		log.ErrorWithCtx(ctx, "DramaListCache failed to Unmarshal, targetStr:%s, err:%v", targetStr, err)
		return err
	}
	return nil
}

func (c *DramaListCache) Save(ctx context.Context, key string, target interface{}, expireTs time.Duration) error {
	b, err := json.Marshal(target)
	if err != nil {
		log.ErrorWithCtx(ctx, "Fetch failed to Marshal, target:%v, err:%v", err)
		return err
	}
	err = c.redisCli.Set(ctx, key, string(b), expireTs).Err()
	if err != nil {
		log.ErrorWithCtx(ctx, "DramaListCache failed to Save, key:%s, target:%v, err:%v", key, target, err)
		return err
	}
	return nil
}
