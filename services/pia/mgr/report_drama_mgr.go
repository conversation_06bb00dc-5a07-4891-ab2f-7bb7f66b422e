package mgr

import (
	"context"
	"errors"
	"fmt"
	"go.mongodb.org/mongo-driver/bson"
	im_api "golang.52tt.com/clients/im-api"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/pia"
	"strconv"
	"strings"
	"sync"
	"time"
)

var (
	NotFoundError = errors.New("查询不到数据")
)

type ReportDramaRecordProcessResult string

var (
	ReportDramaRecordProcessResultAccept = ReportDramaRecordProcessResult("通过")
	ReportDramaRecordProcessResultReject = ReportDramaRecordProcessResult("不通过")
)

type ReportRequest struct {
	dramaID         uint32    // 剧本id
	uid             uint32    // 用户id
	userNickname    string    // 用户昵称
	userTTID        string    // 用户ttid
	reason          []string  // 举报原因
	evidenceImgList []string  // 证据图片列表
	reportTime      time.Time // 举报时间
	description     string    // 举报详情描述
}

func NewReportRequest(dramaID, uid uint32, userTTID, userNickname, description, evidenceImgList string, reason []uint32, reportTimestamp int64) *ReportRequest {
	r := &ReportRequest{
		dramaID:      dramaID,
		uid:          uid,
		userTTID:     userTTID,
		userNickname: userNickname,
		reportTime:   time.Unix(reportTimestamp, 0),
		description:  description,
		reason:       make([]string, len(reason)),
	}
	if len(evidenceImgList) > 0 {
		// 切割图片
		split := strings.Split(evidenceImgList, "|")
		if len(split) > 0 {
			r.evidenceImgList = split
		}
	}
	for i := range reason {
		switch reason[i] {
		case 0:
			r.reason[i] = "政治敏感(政治话题等)"
		case 1:
			r.reason[i] = "色情低俗(低俗信息等)"
		case 2:
			r.reason[i] = "违法行为(诈骗、涉赌、涉毒等)"
		case 3:
			r.reason[i] = "营销广告(骚扰/游戏/垃圾广告等)"
		case 4:
			r.reason[i] = "诋毁谩骂(人身攻击、地域攻击等)"
		case 5:
			r.reason[i] = "侵权行为(泄露隐私等)"
		case 6:
			r.reason[i] = "其他"
		case 7:
			r.reason[i] = "涉及成年人不良诱导/未成年人不当行为"
		default:
			// do nothing
		}
	}
	return r
}

type ReportDramaRecord struct {
	ID                 string                         `bson:"_id" json:"id"`                                    // id
	DramaId            uint32                         `bson:"drama_id" json:"drama_id"`                         // 剧本id
	DramaOriginalId    uint32                         `bson:"drama_original_id" json:"drama_original_id"`       // 原始剧本id
	DramaTitle         string                         `bson:"drama_title" json:"drama_title"`                   // 剧本标题
	Uid                uint32                         `bson:"uid" json:"uid"`                                   // 用户id
	UserNickname       string                         `bson:"user_nickname" json:"user_nickname"`               // 用户昵称
	UserTTID           string                         `bson:"user_ttid" json:"user_ttid"`                       // 用户ttid
	Reason             []string                       `bson:"reason" json:"reason"`                             // 举报原因
	EvidenceImgList    []string                       `bson:"evidence_img_list" json:"evidence_img_list"`       // 证据图片列表
	ReportTime         time.Time                      `bson:"report_time" json:"report_time"`                   // 举报时间
	Description        string                         `bson:"description" json:"description"`                   // 举报详情描述
	IsDeleted          bool                           `bson:"is_deleted" json:"is_deleted"`                     // 是否删除
	IsReport           bool                           `bson:"is_report" json:"is_report"`                       // 是否上报了
	IsAlreadyProcessed bool                           `bson:"is_already_processed" json:"is_already_processed"` // 是否处理
	ProcessTime        time.Time                      `bson:"process_time" json:"process_time"`                 // 处理时间
	ProcessResult      ReportDramaRecordProcessResult `bson:"process_result" json:"process_result"`             // 处理结果
	IsAlreadyNotify    bool                           `bson:"is_already_notify" json:"is_already_notify"`       // 是否通知
}

type ReportDramaRecordStore interface {
	// FetchById 根据id获取举报记录，如果不存在则返回 NotFoundError
	FetchById(ctx context.Context, id string) (*ReportDramaRecord, error)
	// FetchList 根据条件获取举报记录列表
	FetchList(ctx context.Context, m bson.M) ([]*ReportDramaRecord, error)
	//Save 保存举报记录
	Save(ctx context.Context, req *ReportDramaRecord) error
	//Update 更新
	Update(ctx context.Context, req *ReportDramaRecord) error
}

type ReportDramaMgr struct {
	dramaStore             IDramaV2
	reportDramaRecordStore ReportDramaRecordStore
	idService              IDService
	dramaWebApi            DramaWebApi
	imCli                  im_api.IClient
}

func NewReportDramaMgr(
	dramaStore IDramaV2,
	reportDramaRecordStore ReportDramaRecordStore,
	dramaWebApi DramaWebApi,
	idService IDService,
	imCli im_api.IClient) *ReportDramaMgr {
	return &ReportDramaMgr{
		dramaStore:             dramaStore,
		reportDramaRecordStore: reportDramaRecordStore,
		dramaWebApi:            dramaWebApi,
		idService:              idService,
		imCli:                  imCli,
	}
}

//Report 举报
func (r *ReportDramaMgr) Report(ctx context.Context, req *ReportRequest) error {
	// 查询剧本的信息
	dramaInfo, err := r.dramaStore.GetDramaByID(ctx, req.dramaID, WithDramaInfoReadMask().MaskOriginId().MaskTitle())
	if err != nil {
		log.ErrorWithCtx(ctx, "[剧本举报]查询剧本的信息出错, err:%v, dramaID: %d", err, req.dramaID)
		return err
	}
	// 生成举报记录
	record := &ReportDramaRecord{
		ID:              fmt.Sprintf("%d_%d_%d", req.uid, req.dramaID, r.idService.GenerateID()),
		DramaId:         req.dramaID,
		DramaOriginalId: dramaInfo.OriginID,
		DramaTitle:      dramaInfo.Title,
		Uid:             req.uid,
		UserNickname:    req.userNickname,
		UserTTID:        req.userTTID,
		Reason:          req.reason,
		EvidenceImgList: req.evidenceImgList,
		ReportTime:      req.reportTime,
		Description:     req.description,
	}
	// 保存举报记录
	err = r.reportDramaRecordStore.Save(ctx, record)
	if err != nil {
		log.ErrorWithCtx(ctx, "[剧本举报]保存举报记录失败, err:%v, record: %v", err, record)
		return err
	}
	// 发送IM消息
	_, err = r.imCli.SimpleSendTTAssistantText(ctx, record.Uid, fmt.Sprintf("亲爱的用户,我们已收到您对剧本（ID：%d）《%s》的举报,我们将立即调查处理,感谢您与我们一起维护平台绿色健康的网络环境!", record.DramaOriginalId, record.DramaTitle), "", "")
	if err != nil {
		log.ErrorWithCtx(ctx, "[剧本举报]发送举报成功通知失败, err:%v, record: %v", err, record)
	}
	return nil
}

//UploadReportInfo 上传举报信息到网站
func (r *ReportDramaMgr) UploadReportInfo(ctx context.Context) error {
	// 查询没有上报的举报记录
	list, err := r.reportDramaRecordStore.FetchList(ctx, bson.M{
		"is_deleted": false,
		"is_report":  false,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "[上传举报信息到网站]查询没有上报的举报记录失败, err:%v", err)
		return err
	}
	// 并行上报
	wg := sync.WaitGroup{}
	for _, info := range list {
		wg.Add(1)
		go func(reportRecordInfo *ReportDramaRecord) {
			defer wg.Done()
			// 调用网站的接口上报
			err := r.dramaWebApi.ReportDrama(ctx, &ReportDramaReq{
				ReportUserId:   reportRecordInfo.UserTTID,
				ReportUserName: reportRecordInfo.UserNickname,
				DramaId:        strconv.Itoa(int(reportRecordInfo.DramaOriginalId)),
				ReasonList:     reportRecordInfo.Reason,
				ReasonDetail:   reportRecordInfo.Description,
				EvidenceUrl:    reportRecordInfo.EvidenceImgList,
				BusinessId:     reportRecordInfo.ID,
			})
			if err != nil && !errors.Is(err, ErrAlreadyReport) {
				// 上报失败且错误不是已经上报过了
				log.ErrorWithCtx(ctx, "[上传举报信息到网站]上报失败, err:%v, reportRecordInfo: %v", err, reportRecordInfo)
				return
			}
			// 调用成功，则更新举报记录的is_report为true
			reportRecordInfo.IsReport = true
			err = r.reportDramaRecordStore.Update(ctx, reportRecordInfo)
			if err != nil {
				log.ErrorWithCtx(ctx, "[上传举报信息到网站]更新举报记录的is_report为true失败, err:%v, reportRecordInfo: %v", err, reportRecordInfo)
				return
			}
		}(info)
	}
	wg.Wait()
	return nil
}

//HandleReportDramaResult 处理网站返回的举报结果
func (r *ReportDramaMgr) HandleReportDramaResult(ctx context.Context, req *pb.PiaHandleDramaReportResultReq) (*pb.PiaHandleDramaReportResultResp, error) {
	// 获取举报记录
	reportRecordInfo, err := r.reportDramaRecordStore.FetchById(ctx, req.Id)
	if err != nil {
		// 如果举报记录不存在，则不处理
		if errors.Is(err, NotFoundError) {
			log.InfoWithCtx(ctx, "[处理网站返回的举报结果]举报记录不存在, req: %v", req)
			return &pb.PiaHandleDramaReportResultResp{}, nil
		}
		log.ErrorWithCtx(ctx, "[处理网站返回的举报结果]获取举报记录失败, err:%v, req: %v", err, req)
		return nil, err
	}
	// 判断举报结果
	if reportRecordInfo.IsAlreadyProcessed {
		// 如果已经处理过了，则不处理
		return &pb.PiaHandleDramaReportResultResp{}, nil
	}
	// 更新举报记录的is_already_processed为true
	reportRecordInfo.IsAlreadyProcessed = true
	reportRecordInfo.ProcessTime = time.Now()
	if req.GetIsAccept() {
		reportRecordInfo.ProcessResult = ReportDramaRecordProcessResultAccept
	} else {
		reportRecordInfo.ProcessResult = ReportDramaRecordProcessResultReject
	}
	err = r.reportDramaRecordStore.Update(ctx, reportRecordInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "[处理网站返回的举报结果]更新举报记录的is_already_processed为true失败, err:%v, req: %v", err, req)
		return nil, err
	}
	return &pb.PiaHandleDramaReportResultResp{}, nil
}

//SendHandleResultToTT 发送处理结果到TT助手
func (r *ReportDramaMgr) SendHandleResultToTT(ctx context.Context) error {
	// 查询还未发送到TT助手的举报记录
	list, err := r.reportDramaRecordStore.FetchList(ctx, bson.M{
		"is_deleted":           false,
		"is_report":            true,
		"is_already_processed": true,
		"is_already_notify":    false,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "[发送处理结果到TT助手]查询还未发送到TT助手的举报记录失败, err:%v", err)
		return err
	}
	// 并行处理
	wg := sync.WaitGroup{}
	for _, info := range list {
		wg.Add(1)
		go func(record *ReportDramaRecord) {
			defer wg.Done()
			log.DebugWithCtx(ctx, "[发送处理结果到TT助手]开始发送, record: %v", record)
			// 更新举报记录的is_already_notify为true
			record.IsAlreadyNotify = true
			err = r.reportDramaRecordStore.Update(ctx, record)
			if err != nil {
				log.ErrorWithCtx(ctx, "[发送处理结果到TT助手]更新举报记录的is_already_notify为true失败, err:%v, record: %v", err, record)
				return
			}
			// 如果更新成功了，再发送到TT助手，保证业务方的需求0次或者1次
			var sendErr error
			if record.ProcessResult == ReportDramaRecordProcessResultAccept {
				_, sendErr = r.imCli.SimpleSendTTAssistantText(ctx, record.Uid, fmt.Sprintf("您举报的剧本（ID：%d）《%s》经核实情况属实，现已处理", record.DramaOriginalId, record.DramaTitle), "", "")
			} else {
				_, sendErr = r.imCli.SimpleSendTTAssistantText(ctx, record.Uid, fmt.Sprintf("您举报的剧本（ID：%d）《%s》经核实举报不成功", record.DramaOriginalId, record.DramaTitle), "", "")
			}
			if sendErr != nil {
				log.ErrorWithCtx(ctx, "[发送处理结果到TT助手]发送到TT助手失败, err:%v, record: %v", sendErr, record)
			}
		}(info)
	}
	wg.Wait()
	return nil
}
