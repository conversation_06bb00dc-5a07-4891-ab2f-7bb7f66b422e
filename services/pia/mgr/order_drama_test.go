package mgr

import (
	"context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"golang.52tt.com/clients/mocks/account"
	"golang.52tt.com/clients/mocks/channel"
	pb "golang.52tt.com/protocol/services/pia"
	"golang.52tt.com/services/pia/conf"
	"testing"
)

type orderDramaTestData struct {
	name      string
	wantData  *pb.OrderDramaResp
	wantError error
	initFunc  func()
	req       *pb.OrderDramaReq
	ctx       context.Context
}

func TestOrderDramaMgr_OrderDrama(t *testing.T) {
	t.Skip()
	controller := gomock.NewController(t)
	orderDramaListRepo := NewMockOrderDramaListRepo(controller)
	eventBus := NewMockEventBus(controller)
	pushService := NewMockPushService(controller)
	accountCli := account.NewMockIClient(controller)
	//ukwCli := youknowwho.NewMockIClient(controller)
	iDrama := NewMockIDramaV2(controller)
	lockService := NewMockLockService(controller)
	orderDramaService := NewMockOrderDramaService(controller)
	timeService := NewMockTimeService(controller)
	convertor := NewMockConvertor(controller)
	channelCli := channel.NewMockIClient(controller)
	idService := NewMockIDService(controller)
	mockIConfig := conf.NewMockIConfig(controller)
	eventBus.EXPECT().Subscribe(gomock.Any(), gomock.Any()).AnyTimes()
	eventBus.EXPECT().Publish(gomock.Any(), gomock.Any()).AnyTimes()
	orderDramaMgr := NewOrderDramaMgr(
		mockIConfig,
		idService,
		orderDramaListRepo,
		eventBus,
		pushService,
		accountCli,
		iDrama,
		orderDramaService,
		lockService,
		timeService,
		nil,
		channelCli,
		convertor,
	)
	testTable := []orderDramaTestData{}

	for _, item := range testTable {
		t.Run(item.name, func(t *testing.T) {
			item.initFunc()
			resp, err := orderDramaMgr.OrderDrama(item.ctx, item.req)
			assert.Equal(t, item.wantError, err)
			if item.wantError == nil {
				assert.NotNil(t, resp.GetOrderList())
				assert.Equal(t, item.wantData.OrderList.ChannelId, resp.GetOrderList().GetChannelId())
				wantList := item.wantData.OrderList.List
				resultList := resp.OrderList.List
				for i := range wantList {
					//assert.Equal(t, wantList[i].DramaId, resultList[i].DramaId)
					//assert.Equal(t, wantList[i].DramaTitle, resultList[i].DramaTitle)
					//assert.Equal(t, wantList[i].DramaType, resultList[i].DramaType)
					//assert.Equal(t, wantList[i].DramaTags, resultList[i].DramaTags)
					//assert.Equal(t, wantList[i].DramaSummary, resultList[i].DramaSummary)
					//assert.Equal(t, wantList[i].DramaAvatar, resultList[i].DramaAvatar)
					//assert.Equal(t, wantList[i].DramaAuthor, resultList[i].DramaAuthor)
					assert.Equal(t, wantList[i].UserId, resultList[i].UserId)
					assert.Equal(t, wantList[i].UserName, resultList[i].UserName)
					assert.Equal(t, wantList[i].UserAvatar, resultList[i].UserAvatar)
					assert.Equal(t, wantList[i].UserSex, resultList[i].UserSex)
					assert.Equal(t, wantList[i].IndexId, resultList[i].IndexId)
				}
			}
		})
	}

}

func TestOrderDramaMgr_GetOrderDramaList(t *testing.T) {
}

func TestOrderDramaMgr_DeleteDrama(t *testing.T) {
}

func TestOrderDramaMgr_HandleMsg(t *testing.T) {
}
