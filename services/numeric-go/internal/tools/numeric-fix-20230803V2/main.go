package main

import (
	"context"
	"encoding/binary"
	"fmt"
	"github.com/go-redis/redis"
	"github.com/jinzhu/gorm"
	"golang.52tt.com/clients/account"
	missionTL "golang.52tt.com/clients/missiontimeline"
	pushclient "golang.52tt.com/clients/push-notification/v2"
	"golang.52tt.com/clients/seqgen/v2"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	tracing "golang.52tt.com/pkg/tracing/jaeger"
	tlsvr "golang.52tt.com/protocol/services/missiontimelinesvr"
	pb "golang.52tt.com/protocol/services/numeric-go"
	pushPB "golang.52tt.com/protocol/services/push-notification/v2"
	"golang.52tt.com/services/numeric-go/internal/cache"
	"google.golang.org/grpc"
	"strconv"
	"sync/atomic"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

var (
	mysqlDb, mysqlGift *gorm.DB
	cacheClient        *cache.NumericGoCache
	accountCli         *account.Client
)

var seqGenCli *seqgen.Client
var pushCli *pushclient.Client
var missionTLCli *missionTL.Client

func main() {
	var err error
	mysqlConfig := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		UserName:     "godman",
		Password:     "thegodofman",
		Database:     "gift",
		Charset:      "utf8",
		Protocol:     "tcp",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}

	mysqlConfigGift := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		UserName:     "godman",
		Password:     "thegodofman",
		Database:     "gift",
		Charset:      "utf8",
		Protocol:     "tcp",
		PingInterval: 300,
		MaxIdleConns: 25,
		MaxOpenConns: 50,
	}

	redisClient := &redis.Client{}
	redisNew := &redis.Client{}
	redisLimit := &redis.Client{}

	redisNew = redis.NewClient(&redis.Options{
		Network:  "tcp",
		Addr:     "************:6379",
		PoolSize: 2,
		DB:       0,
	})
	redisClient = redis.NewClient(&redis.Options{
		Network:  "tcp",
		Addr:     "************:6379",
		PoolSize: 2,
		DB:       0,
	})
	mysqlDb, err = gorm.Open("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}
	mysqlGift, err = gorm.Open("mysql", mysqlConfigGift.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}
	mysqlDb = mysqlDb.Debug()
	redisTracer := tracing.Init("numeric-go_redis")

	cacheClient = cache.NewNumericGoCacheV2(redisClient, redisNew, redisLimit, redisTracer)

	accountCli, err = account.NewClient(grpc.WithBlock())
	if err != nil {
		fmt.Println(err)
		return
	}
	seqGenCli, err = seqgen.NewClient()
	if err != nil {
		fmt.Println(err)
		return
	}
	pushCli, err = pushclient.NewClient()
	if err != nil {
		fmt.Println(err)
		return
	}
	missionTLCli = missionTL.NewClient()

	t := time.Now()

	table := fmt.Sprintf("personal_rich_charm_log_%s", t.Format("200601"))

	// 所有问题期间产生流水的UID
	uidList := make([]*PersonalRichCharmLog, 0)
	err = mysqlDb.Table(table).
		Select("distinct uid").
		Where("create_time >= ?", "2023-08-03 11:00").
		Where("create_time < ?", "2023-08-03 14:15").
		Where("type = 2").
		Where("order_id != ''").
		Order("uid asc").
		Find(&uidList).Error
	if err != nil {
		panic(err)
	}

	fmt.Println("uidList len:", len(uidList))

	n := 0

	for _, u := range uidList {
		uid := u.Uid

		var isLock bool
		ret := redisClient.SIsMember(cache.UserRichSwitchKey, strconv.Itoa(int(uid)))
		if ret.Err() != nil {
			if ret.Err() != redis.Nil {
				fmt.Printf("GetRichSwitch SIsMember err %s\n", ret.Err())
			}
		} else {
			isLock = ret.Val()
		}

		if isLock {
			// 锁定财富值的昨晚处理了
			continue
		}

		if uid == 53866749 || uid == 318335770 || uid == 958756 {
			// 53866749 已处理
			continue
		}

		// 当前财富值
		beforeNumeric, _ := getNumeric(uid)

		// uid期间的魅力值变更
		sum := new(PersonalRichCharmLog)
		err = mysqlDb.Table(table).
			Select("sum(add_value) as add_value").
			Where("uid = ?", uid).
			Where("create_time >= ?", "2023-08-03 11:00").
			Where("create_time < ?", "2023-08-03 14:15").
			Where("type = 2").
			Where("order_id != ''").
			First(&sum).Error
		if err != nil {
			if err != gorm.ErrRecordNotFound {
				fmt.Println("err: ", err)
				continue
			}
		}

		if sum.AddValue == 0 {
			continue
		}

		n = n + 1
		fmt.Println("uid:", uid, "sum:", sum.AddValue, "n:", n)

		if sum.AddValue > int64(beforeNumeric.Rich) {
			fmt.Println("rich over", uid, "sum:", sum.AddValue, "before:", beforeNumeric.Rich)
			sum.AddValue = int64(beforeNumeric.Rich)
		}

		tbl := fmt.Sprintf("personal_rich_and_charm_%02d", GetTableIndex(uid))
		err = mysqlDb.Table(tbl).
			Exec(fmt.Sprintf("update %s set rich = rich - %d, charm = charm + %d where uid = %d limit 1", tbl, sum.AddValue, sum.AddValue, uid)).Error

		if err != nil {
			fmt.Println("update err: ", err)
			continue
		}

		// 最新财富值
		numeric, _ := getNumeric(uid)

		if err = mysqlDb.Table(table).
			Create(&PersonalRichCharmLog{
				Uid:         uid,
				Type:        uint32(pb.NumericT_Rich),
				BeforeValue: beforeNumeric.Rich,
				AddValue:    -sum.AddValue,
				CreateTime:  time.Now(),
				OrderID:     "Fix_20230803",
			}).Error; err != nil {
			fmt.Println("err: ", err)
		}
		if err = mysqlDb.Table(table).
			Create(&PersonalRichCharmLog{
				Uid:         uid,
				Type:        uint32(pb.NumericT_Charm),
				BeforeValue: beforeNumeric.Charm,
				AddValue:    +sum.AddValue,
				CreateTime:  time.Now(),
				OrderID:     "Fix_20230803",
			}).Error; err != nil {
			fmt.Println("err: ", err)
		}

		err = cacheClient.DelPersonalNumeric(context.Background(), uid)
		if err != nil {
			fmt.Println(err)
			return
		}

		bgCtx := context.Background()

		// sync
		syncMissionNumeric(bgCtx, uid, numeric.Rich, numeric.Charm)
		// notify
		_ = notifyClient(bgCtx, []uint32{uid}, 6)
	}
}

type PersonalRichAndCharm struct {
	UID   uint32 `gorm:"primaryKey;column:uid" json:"uid"` // uid
	Rich  uint64 `gorm:"column:rich" json:"rich"`          // 财富值
	Charm uint64 `gorm:"column:charm" json:"charm"`        // 魅力值
}

func GetTableIndex(uid uint32) uint32 {
	return uid % 100
}

type PersonalRichCharmLog struct {
	Id          uint32    `db:"id" json:"id"`
	Uid         uint32    `db:"uid" json:"uid"`                                // uid
	Type        uint32    `db:"type" json:"type"`                              // 类型 1财富 2魅力
	BeforeValue uint64    `db:"before_value" json:"before_value"`              // 处理之前的值
	AddValue    int64     `db:"add_value" json:"add_value"`                    // 新增的值
	CreateTime  time.Time `db:"autoCreateTime;create_time" json:"create_time"` // 时间
	OrderID     string    `db:"order_id" json:"order_id"`                      // 订单号
}

type UserPresentSendDetail struct {
	Uid     uint32
	ToUid   uint32
	OrderId string
	AddRich uint64
}

func getNumeric(uid uint32) (*PersonalRichAndCharm, error) {
	numeric := new(PersonalRichAndCharm)
	err := mysqlDb.Table(fmt.Sprintf("personal_rich_and_charm_%02d", GetTableIndex(uid))).
		Where("uid = ?", uid).First(&numeric).Error
	if err != nil {
		fmt.Println("err: ", err)
		return nil, err
	}
	return numeric, nil
}

func syncMissionNumeric(ctx context.Context, uid uint32, rich, charm uint64) {
	seq, err := seqGenCli.GenerateSequence(ctx, uid, seqgen.NamespaceUser, seqgen.KeyGrowth, 1)
	if err != nil {
		log.ErrorWithCtx(ctx, "syncMissionNumeric GenerateSequence err: %s", err.Error())
		return
	}

	numMsg := &tlsvr.NumericInfoMessage{
		// 留意这里的uint32可能不够了
		Charm: uint32(charm),
		Rich:  uint32(rich),
	}

	log.DebugWithCtx(ctx, "syncMissionNumeric NumericChanged uid:%d, seq:%d, numMsg:%+v", uid, seq, numMsg)

	err = missionTLCli.NumericChanged(ctx, uid, uint32(seq), numMsg)
	if err != nil {
		log.ErrorWithCtx(ctx, "syncMissionNumeric NumericChanged err: %s", err.Error())
		return
	}
}

func notifyClient(ctx context.Context, uidList []uint32, syncType uint32) (err error) {
	var b [4]byte
	binary.BigEndian.PutUint32(b[:], syncType) // Network Order
	sequence := uint32(time.Now().UnixNano())
	seq := atomic.AddUint32(&sequence, 1)
	err = pushCli.PushToUsers(ctx, uidList, &pushPB.CompositiveNotification{
		Sequence:           seq,
		TerminalTypePolicy: pushclient.DefaultPolicy,
		AppId:              0,
		ProxyNotification: &pushPB.ProxyNotification{
			Type:    uint32(pushPB.ProxyNotification_NOTIFY),
			Payload: b[:],
		},
	})

	if err != nil {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d err=%s", uidList, len(uidList), syncType, seq, err.Error())
	} else {
		log.DebugWithCtx(ctx, "NotifySyncX - users=%v(%d) type=%v seq=%d", uidList, len(uidList), syncType, seq)

		fmt.Printf("NotifySyncX - users=%v \n", uidList)
	}

	return err
}
