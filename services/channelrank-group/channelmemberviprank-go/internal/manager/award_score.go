package manager

import (
	"context"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/pkg/protocol"
	channelGA "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/channelmemberviprank-go"
	"golang.52tt.com/services/channelrank-group/channelmemberviprank-go/internal/cache"
	"golang.52tt.com/services/channelrank-group/channelmemberviprank-go/internal/store"
	"golang.52tt.com/services/channelrank-group/common"
	"golang.52tt.com/services/channelrank-group/common/dyconf"
	"strconv"
	"sync"
	"time"
)

// MaxAwardScore 奖励分最大值 1亿
const MaxAwardScore = 1e8

// AwardScoreIndex 奖励分配置索引
type AwardScoreIndex struct {
	PresentAwardScore struct {
		CfgItems []*PresentAwardScoreIndex
	}
	TopNAwardScore struct {
		CfgItems []*TopNAwardScoreIndex
	}
}

type PresentAwardScoreIndex struct {
	PresentIds map[uint32]struct{}
	Cid        map[uint32]struct{}
	Uid        map[uint32]struct{}
	StartTime  time.Time
	EndTime    time.Time
	Count      uint32
	Total      uint32
	AwardScore uint32
	AllUser    bool
}

func (p *PresentAwardScoreIndex) HasPresentAwardScore(presentId, uid, cid uint32, t time.Time) (isExists bool, count, total, award uint32, diemType channelGA.MultiDimChannelRankType) {
	if p == nil {
		return false, 0, 0, 0, channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_UNSPECIFIED
	}
	isInRange := !p.StartTime.After(t) && !p.EndTime.Before(t)
	_, presentIdExists := p.PresentIds[presentId]

	if presentIdExists && p.AllUser && isInRange {
		return presentIdExists, p.Count, p.Total, p.AwardScore, channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_MEMBER
	}

	_, uidExists := p.Uid[uid]
	_, cidExists := p.Cid[cid]

	// 用户优先
	if presentIdExists && uidExists && isInRange {
		return presentIdExists, p.Count, p.Total, p.AwardScore, channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_MEMBER
	}

	if presentIdExists && cidExists && isInRange {
		return presentIdExists, p.Count, p.Total, p.AwardScore, channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_UNSPECIFIED
	}

	return false, 0, 0, 0, channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_UNSPECIFIED
}

type TopNAwardScoreIndex struct {
	StartTime           time.Time
	EndTime             time.Time
	DiemType            channelGA.MultiDimChannelRankType
	TagId               uint32
	TopNAwardScoreItems map[uint32]uint32 // topN -> awardScore
}

func (ti *TopNAwardScoreIndex) HasTopNAwardScore(diemType channelGA.MultiDimChannelRankType, tagId, top uint32, t time.Time) (award uint32) {
	isInRange := !ti.StartTime.After(t) && !ti.EndTime.Before(t)

	if isInRange && ti.DiemType == diemType && ti.TagId == tagId {
		awardScore, ok := ti.TopNAwardScoreItems[top]
		if ok {
			return awardScore
		}
	}
	return 0
}

func NewAwardScoreIndex(ctx context.Context, presentConf []dyconf.PresentAwardScore, topNConf []dyconf.TopNAwardScore) *AwardScoreIndex {
	log.DebugWithCtx(ctx, "InitAwardScoreIndex start, presentConf:%+v, topNConf:%+v", presentConf, topNConf)
	var index AwardScoreIndex
	for _, confItem := range presentConf {
		indexItem := PresentAwardScoreIndex{
			StartTime:  time.Unix(confItem.StartTime, 0),
			EndTime:    time.Unix(confItem.EndTime, 0),
			Count:      confItem.Count,
			Total:      confItem.Total,
			AwardScore: confItem.AwardScore,
			Cid:        make(map[uint32]struct{}),
			Uid:        make(map[uint32]struct{}),
			PresentIds: make(map[uint32]struct{}),
			AllUser:    confItem.AllUser,
		}
		if indexItem.StartTime.IsZero() || indexItem.EndTime.IsZero() {
			log.ErrorWithCtx(ctx, "InitAwardScoreIndex time invalid, indexItem:%+v", indexItem)
			continue
		}
		if indexItem.StartTime.After(indexItem.EndTime) || time.Now().After(indexItem.EndTime) {
			log.ErrorWithCtx(ctx, "InitAwardScoreIndex time invalid, indexItem:%+v", indexItem)
			continue
		}
		if (indexItem.Count == 0 && indexItem.Total == 0) || indexItem.AwardScore == 0 || len(confItem.PresentId) == 0 || (len(confItem.Cid) == 0 && len(confItem.Uid) == 0) {
			log.ErrorWithCtx(ctx, "InitAwardScoreIndex param invalid, indexItem:%+v", indexItem)
			continue
		}
		if indexItem.AwardScore >= MaxAwardScore {
			log.ErrorWithCtx(ctx, "InitAwardScoreIndex awardScore too large, indexItem:%+v", indexItem)
			continue
		}
		for _, presentId := range confItem.PresentId {
			indexItem.PresentIds[presentId] = struct{}{}
		}
		for _, cid := range confItem.Cid {
			indexItem.Cid[cid] = struct{}{}
		}
		for _, uid := range confItem.Uid {
			indexItem.Uid[uid] = struct{}{}
		}
		index.PresentAwardScore.CfgItems = append(index.PresentAwardScore.CfgItems, &indexItem)
	}

	for _, confItem := range topNConf {
		indexItem := TopNAwardScoreIndex{
			StartTime:           time.Unix(confItem.StartTime, 0),
			EndTime:             time.Unix(confItem.EndTime, 0),
			TopNAwardScoreItems: make(map[uint32]uint32),
			DiemType:            channelGA.MultiDimChannelRankType(confItem.DiemType),
			TagId:               confItem.TagId,
		}
		if indexItem.StartTime.IsZero() || indexItem.EndTime.IsZero() {
			log.ErrorWithCtx(ctx, "InitAwardScoreIndex time invalid, indexItem:%+v", indexItem)
			continue
		}
		if indexItem.StartTime.After(indexItem.EndTime) || time.Now().After(indexItem.EndTime) {
			log.ErrorWithCtx(ctx, "InitAwardScoreIndex time invalid, indexItem:%+v", indexItem)
			continue
		}
		for _, v := range confItem.TopNAwardScoreItems {
			if v.AwardScore == 0 || v.TopN == 0 {
				log.ErrorWithCtx(ctx, "InitAwardScoreIndex top param invalid, indexItem:%+v", indexItem)
				continue
			}
			if v.AwardScore >= MaxAwardScore {
				log.ErrorWithCtx(ctx, "InitAwardScoreIndex awardScore too large, indexItem:%+v", indexItem)
				continue
			}
			indexItem.TopNAwardScoreItems[v.TopN] = v.AwardScore
		}
		index.TopNAwardScore.CfgItems = append(index.TopNAwardScore.CfgItems, &indexItem)
	}

	log.InfoWithCtx(ctx, "InitAwardScoreIndex init index:%+v", index)
	return &index
}

func (a *AwardScoreIndex) GetPresentAwardScoreIndex() []*PresentAwardScoreIndex {
	return a.PresentAwardScore.CfgItems
}

func (a *AwardScoreIndex) GetTopNAwardScoreIndex() []*TopNAwardScoreIndex {
	return a.TopNAwardScore.CfgItems
}

func (m *ChannelMemberVipRankGoMgr) SyncAwardScoreIndex(ctx context.Context) {
	lock := sync.RWMutex{}
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			awardScoreIndexNew := NewAwardScoreIndex(ctx, m.Dyconfig.GetPresentAwardScoreConf(), m.Dyconfig.GetTopNAwardScoreConf())
			lock.Lock()
			m.awardScoreIndex = awardScoreIndexNew
			lock.Unlock()
		}
	}
}

// CreateHourRankAwardScore 创建奖励分发放任务
func (m *ChannelMemberVipRankGoMgr) CreateHourRankAwardScore(ctx context.Context, in *pb.CreateHourRankAwardScoreReq) (*pb.CreateHourRankAwardScoreResp, error) {
	out := &pb.CreateHourRankAwardScoreResp{}
	log.InfoWithCtx(ctx, "CreateHourRankAwardScore start req:%+v", in)
	now := time.Now()
	destType, objId := in.GetDestType(), in.GetObjId()
	if destType == 0 || objId == 0 || in.GetScore() == 0 || in.GetOrderId() == "" {
		log.ErrorWithCtx(ctx, "CreateHourRankAwardScore param invalid, destType:%d, objId:%d, score:%d, orderId:%s", destType, objId, in.GetScore(), in.GetOrderId())
		return out, protocol.NewExactServerError(0, status.ErrRequestParamInvalid)
	}
	if in.GetScore() >= MaxAwardScore {
		log.ErrorWithCtx(ctx, "CreateHourRankAwardScore score too large, score:%d", in.GetScore())
		return out, protocol.NewExactServerError(0, status.ErrRequestParamInvalid, "奖励分太大")
	}
	hours := make([]time.Time, 0)
	if in.GetActiveTime() == 0 {
		if in.GetStartTime() == 0 || in.GetEndTime() == 0 {
			log.ErrorWithCtx(ctx, "CreateHourRankAwardScore time empty, startTime:%d, endTime:%d", in.GetStartTime(), in.GetEndTime())
			return out, protocol.NewExactServerError(0, status.ErrRequestParamInvalid, "发放时间错误1")
		}
		startTime := time.Unix(in.GetStartTime(), 0)
		endTime := time.Unix(in.GetEndTime(), 0)
		if startTime.IsZero() || endTime.IsZero() || startTime.After(endTime) {
			log.ErrorWithCtx(ctx, "CreateHourRankAwardScore time error, startTime:%d, endTime:%d", in.GetStartTime(), in.GetEndTime())
			return out, protocol.NewExactServerError(0, status.ErrRequestParamInvalid, "发放时间错误2")
		}
		// spit by hours
		for t := startTime; !t.After(endTime); t = t.Add(time.Hour) {
			hours = append(hours, t.Truncate(time.Hour))
		}
	} else {
		activeTime := time.Unix(in.GetActiveTime(), 0)
		if activeTime.IsZero() || activeTime.After(now) {
			log.ErrorWithCtx(ctx, "CreateHourRankAwardScore activeTime error, activeTime:%d", in.GetActiveTime())
			return out, protocol.NewExactServerError(0, status.ErrRequestParamInvalid, "发放时间错误3")
		}
		hours = append(hours, activeTime.Truncate(time.Hour))
	}
	if len(hours) > 720 {
		log.ErrorWithCtx(ctx, "CreateHourRankAwardScore hours too many, len:%d, startTime:%d, endTime:%d", len(hours), in.GetStartTime(), in.GetEndTime())
		return out, protocol.NewExactServerError(0, status.ErrRequestParamInvalid, "请缩小时间范围")
	}

	log.InfoWithCtx(ctx, "CreateHourRankAwardScore spit hours:%+v", hours)

	list := make([]*store.HourRankAwardScore, 0)
	for _, h := range hours {
		orderId := fmt.Sprintf("%s_%s", in.GetOrderId(), h.Format("010215")) // orderId + hour
		list = append(list, &store.HourRankAwardScore{
			DestType:   destType,
			ObjID:      objId,
			Score:      in.GetScore(),
			ActiveTime: h,
			OrderID:    orderId,
			Status:     0,
			CreateTime: now,
		})
	}

	err := m.Store.BatchCreateHourRankAwardScores(list)
	if err != nil {
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			log.WarnWithCtx(ctx, "CreateHourRankAwardScore duplicate key, objId:%d, score:%d, orderId:%s, err:%+v", objId, in.GetScore(), in.GetOrderId(), err)
			return out, protocol.NewExactServerError(0, status.ErrChannelRankOrderIdRepeat)
		}
		log.ErrorWithCtx(ctx, "CreateHourRankAwardScore db error", err)
		return out, err
	}

	return out, nil
}

// GeneralAwardScoreHandler 处理发放普通奖励分
func (m *ChannelMemberVipRankGoMgr) GeneralAwardScoreHandler(ctx context.Context) {
	now := time.Now()
	list, err := m.Store.GetCurrentHourRankAwardScoreWaitingList(now)
	if err != nil {
		log.ErrorWithCtx(ctx, "GeneralAwardScoreHandler GetCurrentHourRankAwardScoreWaitingList err:%v", err)
		return
	}

	log.InfoWithCtx(ctx, "GeneralAwardScoreHandler awardScore Task len:%d", len(list))

	for _, v := range list {
		activeTime := v.ActiveTime.Truncate(time.Hour)
		if activeTime.After(now) {
			continue
		}
		if !IsSameHour(now, activeTime) {
			log.WarnWithCtx(ctx, "GeneralAwardScoreHandler not same hour, now:%v, activeTime:%v", now, v.ActiveTime)
			continue
		}
		orderId := fmt.Sprintf("%s_%s", "HAS_", v.OrderID)

		o := &store.AwardScoreOrder{
			DestType:   v.DestType,
			Score:      v.Score,
			OrderId:    orderId,
			SourceType: pb.AwardScoreSourceType_AwardScoreSourceTypeGrant,
			ActiveTime: now,
		}

		log.InfoWithCtx(ctx, "GeneralAwardScoreHandler awardScore Task:%+v, orderInfo:%+v", v, o)

		switch v.DestType {
		case pb.CreateHourRankAwardScoreReq_DestTypeChannel:
			o.ChannelId = v.ObjID
			channelInfo, sErr := m.ChannelCli.GetChannelSimpleInfo(ctx, 0, o.ChannelId)
			if sErr != nil {
				log.Errorf("GeneralAwardScoreHandler GetChannelSimpleInfo err %v", sErr)
				continue
			}
			o.ChannelType = channelInfo.GetChannelType()
			if o.ChannelType == uint32(channelGA.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
				o.Uid = channelInfo.GetBindId()
			}
			if err = m.Store.UpdateHourRankAwardScoreStatus(v.ID, func() error {
				return m.RecordChannelAwardScore(ctx, o, "")
			}); err != nil {
				log.ErrorWithCtx(ctx, "GeneralAwardScoreHandler UpdateHourRankAwardScoreStatus err:%v", err)
			}
		case pb.CreateHourRankAwardScoreReq_DestTypeMember:
			o.Uid = v.ObjID
			if err = m.Store.UpdateHourRankAwardScoreStatus(v.ID, func() error {
				return m.RecordMemberAwardScore(ctx, o, "")
			}); err != nil {
				log.ErrorWithCtx(ctx, "GeneralAwardScoreHandler UpdateHourRankAwardScoreStatus err:%v", err)
			}
		}
	}
}

func IsSameHour(t1, t2 time.Time) bool {
	return t1.Year() == t2.Year() && t1.Month() == t2.Month() && t1.Day() == t2.Day() && t1.Hour() == t2.Hour()
}

func (m *ChannelMemberVipRankGoMgr) PresentAwardScoreHandler(ctx context.Context, orderInfo *store.OrderInfo) {
	sendTm := time.Unix(int64(orderInfo.BoughtTime), 0)
	hour := uint32(sendTm.Hour())
	cid := orderInfo.ChannelId
	uid := orderInfo.ToUid
	presentId := orderInfo.PresentId
	count := orderInfo.Count
	score := orderInfo.TotalPrice

	presentAwardScoreIndex := m.awardScoreIndex.GetPresentAwardScoreIndex()
	for _, presentItem := range presentAwardScoreIndex {
		isHitPresent, sumCount, sumTotal, awardScore, diemType := presentItem.HasPresentAwardScore(presentId, uid, cid, sendTm)
		if !isHitPresent {
			continue
		}
		// 累加礼物计数
		newCount, newScore, err := m.Cache.IncrHourRankPresentAwardScoreCount(hour, presentId, count, score, uid, cid, diemType)
		if err != nil {
			log.ErrorWithCtx(ctx, "PresentAwardScoreHandler IncrHourRankPresentAwardScoreCount err %v, orderInfo=%+v", err, orderInfo)
			return
		}

		log.InfoWithCtx(ctx, "PresentAwardScoreHandler isHitPresent and incr count, presentId:%d, uid:%d, cid:%d, score:%d, newCount:%d, sumCount:%d, newScore:%d, sumTotal:%d", presentId, uid, cid, score, newCount, sumCount, newScore, sumTotal)

		// 计数达标，发放奖励分
		if (newCount > 0 && sumCount > 0 && newCount >= sumCount) ||
			(newScore > 0 && sumTotal > 0 && newScore >= sumTotal) {

			log.InfoWithCtx(ctx, "PresentAwardScoreHandler isHitPresent and give award start, awardScore:%d, uid:%d, cid:%d, presentId:%d, diemType:%d", awardScore, uid, cid, presentId, diemType)

			channelInfo, sErr := m.ChannelCli.GetChannelSimpleInfo(ctx, 0, cid)
			if sErr != nil {
				log.Errorf("PresentAwardScoreHandler GetChannelSimpleInfo err %v, orderInfo=%+v", sErr, orderInfo)
				return
			}
			// 语音房加上UID，便于后面检查新星榜
			if cid > 0 && channelInfo.GetChannelType() == uint32(channelGA.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
				uid = channelInfo.GetBindId()
			}
			o := &store.AwardScoreOrder{
				Uid:         uid,
				ChannelId:   cid,
				Score:       awardScore,
				OrderId:     fmt.Sprintf("HAS_%d_%d_%d_%d_%s", uid, cid, diemType, presentId, time.Now().Format("20060102150405")),
				SourceType:  pb.AwardScoreSourceType_AwardScoreSourceTypePresent,
				Detail:      strconv.Itoa(int(presentId)),
				ActiveTime:  sendTm,
				TagId:       0,
				ChannelType: channelInfo.GetChannelType(),
			}

			if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_MEMBER {
				o.DestType = pb.CreateHourRankAwardScoreReq_DestTypeMember
				lockKey := cache.GenHourPresentAwardScoreMemberLockKey(hour, presentId, uid)
				if err = m.RecordMemberAwardScore(ctx, o, lockKey); err != nil {
					log.ErrorWithCtx(ctx, "PresentAwardScoreHandler RecordMemberAwardScore err:%v, orderInfo=%+v", err, orderInfo)
				}
			} else {
				o.DestType = pb.CreateHourRankAwardScoreReq_DestTypeChannel
				lockKey := cache.GenHourPresentAwardScoreChannelLockKey(hour, presentId, cid)
				if err = m.RecordChannelAwardScore(ctx, o, lockKey); err != nil {
					log.ErrorWithCtx(ctx, "PresentAwardScoreHandler RecordChannelAwardScore err:%v, orderInfo=%+v", err, orderInfo)
				}
			}
		}
	}
}

// TopNAwardScoreHandler 处理发放topN奖励分
func (m *ChannelMemberVipRankGoMgr) TopNAwardScoreHandler(ctx context.Context, diemType channelGA.MultiDimChannelRankType, tagId, rank, objId uint32) {
	var uid, cid, cType uint32
	if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_MEMBER {
		uid = objId
	} else {
		cid = objId
		channelInfo, sErr := m.ChannelCli.GetChannelSimpleInfo(ctx, 0, cid)
		if sErr != nil {
			log.Errorf("TopNAwardScoreHandler GetChannelSimpleInfo err %v", sErr)
			return
		}
		if channelInfo.GetChannelType() == uint32(channelGA.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
			uid = channelInfo.GetBindId()
		}
		cType = channelInfo.GetChannelType()
	}

	topNAwardScoreIndex := m.awardScoreIndex.GetTopNAwardScoreIndex()
	for _, topNItem := range topNAwardScoreIndex {
		awardScore := topNItem.HasTopNAwardScore(diemType, tagId, rank, time.Now())
		if awardScore == 0 {
			continue
		}

		log.InfoWithCtx(ctx, "TopNAwardScoreHandler awardScore:%d, uid:%d, cid:%d, rank:%d, cType:%d, diemType:%d, tagId:%d", awardScore, uid, cid, rank, cType, diemType, tagId)

		o := &store.AwardScoreOrder{
			OrderId:     fmt.Sprintf("HAS_%d_%d_%d_%d_%s", uid, cid, diemType, rank, time.Now().Format("20060102150405")),
			SourceType:  pb.AwardScoreSourceType_AwardScoreSourceTypeTopN,
			Score:       awardScore,
			ActiveTime:  time.Now(),
			ChannelId:   cid,
			ChannelType: cType,
			Uid:         uid,
			TagId:       tagId,
			Detail:      strconv.Itoa(int(rank)),
		}

		if diemType == channelGA.MultiDimChannelRankType_MULTI_DIM_CHANNEL_RANK_TYPE_MEMBER {
			o.DestType = pb.CreateHourRankAwardScoreReq_DestTypeMember
			if err := m.RecordMemberAwardScore(ctx, o, ""); err != nil {
				log.ErrorWithCtx(ctx, "TopNAwardScoreHandler RecordMemberAwardScore err:%v", err)
			}
		} else {
			o.DestType = pb.CreateHourRankAwardScoreReq_DestTypeChannel
			if err := m.RecordChannelAwardScore(ctx, o, ""); err != nil {
				log.ErrorWithCtx(ctx, "TopNAwardScoreHandler RecordChannelAwardScore err:%v", err)
			}
		}
	}
}

func (m *ChannelMemberVipRankGoMgr) HandleAwardScoreTimer() {
	ctx := context.Background()
	m.GeneralAwardScoreHandler(ctx)
}

// GetMemberGuildTags 获取签约成员签约公会旗下娱乐厅标签
func (m *ChannelMemberVipRankGoMgr) GetMemberGuildTags(ctx context.Context, uid uint32) ([]uint32, uint32, error) {
	// 签约公会
	contractResp, sErr := m.AnchorContractCli.GetUserContractCacheInfo(ctx, 0, uid)
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetMemberGuildTags GetUserContractCacheInfo err:%v", sErr)
		return nil, 0, sErr
	}
	guildId := contractResp.GetContract().GetGuildId()
	if guildId == 0 {
		return nil, 0, nil
	}
	// 公会旗下房间
	channelIds, sErr := m.ChannelGuildCli.GetChannelGuildList(ctx, guildId, uint32(channelGA.ChannelType_GUILD_PUBLIC_FUN_CHANNEL_TYPE))
	if sErr != nil {
		log.ErrorWithCtx(ctx, "GetMemberGuildTags GetChannelGuildList err:%v", sErr)
		return nil, guildId, sErr
	}
	tags := make([]uint32, 0)
	tagsMap := make(map[uint32]struct{})
	for _, cid := range channelIds {
		tagId := m.Cache.GetTagIdByCid(cid)
		if tagId >= 3000 && tagId <= 4000 {
			tagId = 3000
		}
		if common.IsMemRankSupportChannelTag(tagId) {
			tagsMap[tagId] = struct{}{}
		}
	}
	for tagId := range tagsMap {
		tags = append(tags, tagId)
	}
	return tags, guildId, nil
}

// RecordChannelAwardScore 记录房间榜奖励分
func (m *ChannelMemberVipRankGoMgr) RecordChannelAwardScore(ctx context.Context, orderInfo *store.AwardScoreOrder, lockKey string) error {
	hour := uint32(orderInfo.ActiveTime.Hour())
	cid := orderInfo.ChannelId
	var tagId uint32
	if orderInfo.SourceType == pb.AwardScoreSourceType_AwardScoreSourceTypeTopN {
		tagId = orderInfo.TagId
	} else {
		tagId = m.Cache.GetTagIdByCid(cid)
		if tagId >= 3000 && tagId <= 4000 {
			tagId = 3000
		}
	}
	orderInfo.TagId = tagId
	isNewStar := m.IsNewStarAnchor(orderInfo.Uid)

	log.InfoWithCtx(ctx, "RecordChannelAwardScore start orderInfo:%+v, tagId:%d, isNewStar:%v", orderInfo, tagId, isNewStar)

	err := m.Store.RecordAwardScoreOrder(orderInfo)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordChannelAwardScore Store.RecordChannelHourRankOrder err %v, orderInfo=%+v", err, orderInfo)
		if mysqlErr, ok := err.(*mysql.MySQLError); ok && mysqlErr.Number == 1062 {
			log.ErrorWithCtx(ctx, "RecordChannelAwardScore order id Duplicate orderId=%q", orderInfo.OrderId)
			return nil
		}
		_ = m.reporter.SendError(fmt.Sprintf("小时榜奖励分写入mysql失败 err:%s orderId:%s", err, orderInfo.OrderId))
		return err
	}

	err = m.Cache.AddHourRankChannelAwardScore(cid, hour, orderInfo.Score, tagId, isNewStar, lockKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordChannelAwardScore Cache.AddHourRankChannelAwardScore err %v, orderInfo=%+v", err, orderInfo)
		_ = m.reporter.SendError(fmt.Sprintf("小时榜奖励分写入redis失败 err:%s orderInfos=%+v", err, orderInfo))
		return err
	}
	return nil
}

// RecordMemberAwardScore 记录成员榜奖励分
func (m *ChannelMemberVipRankGoMgr) RecordMemberAwardScore(ctx context.Context, orderInfo *store.AwardScoreOrder, lockKey string) error {
	hour := uint32(orderInfo.ActiveTime.Hour())
	uid := orderInfo.Uid
	orderInfos := make([]*store.AwardScoreOrder, 0)
	tagLids := make([]uint32, 0)
	var err error

	// 非总榜时，加上总榜
	if orderInfo.TagId > 0 {
		sumRankOrderInfo := *orderInfo
		sumRankOrderInfo.TagId = 0
		sumRankOrderInfo.OrderId = fmt.Sprintf("%s_%d", orderInfo.OrderId, 0)
		orderInfos = append(orderInfos, &sumRankOrderInfo)
	}

	if orderInfo.SourceType == pb.AwardScoreSourceType_AwardScoreSourceTypeTopN {
		// 注意：TopN 奖励分，只加指定的榜单及总榜，指定总榜时不需要显示指定tag
		if orderInfo.TagId > 0 {
			tagLids = append(tagLids, orderInfo.TagId)
		}
		// 加上指定品类榜
		orderInfo.OrderId = fmt.Sprintf("%s_%d", orderInfo.OrderId, orderInfo.TagId)
		orderInfos = append(orderInfos, orderInfo)
	} else {
		// 加上签约公会旗下娱乐厅所有品类榜
		var guildId uint32
		tagLids, guildId, err = m.GetMemberGuildTags(ctx, uid)
		if err != nil {
			log.ErrorWithCtx(ctx, "RecordMemberAwardScore GetMemberGuildTags err:%v, uid:%d, guildId:%d", err, uid, guildId)
			return err
		}
		if len(tagLids) == 0 {
			log.WarnWithCtx(ctx, "RecordMemberAwardScore tagIds empty, uid:%d, guildId:%d", uid, guildId)
			return nil
		}
		for _, tagId := range tagLids {
			newOrderInfo := *orderInfo
			newOrderInfo.TagId = tagId
			newOrderInfo.OrderId = fmt.Sprintf("%s_%d", orderInfo.OrderId, tagId)
			orderInfos = append(orderInfos, &newOrderInfo)
		}
	}

	log.InfoWithCtx(ctx, "RecordMemberAwardScore start orderInfos:%+v, tagLids:%+v, lockKey:%s", orderInfos, tagLids, lockKey)

	err = m.Store.BatchRecordAwardScoreOrder(orderInfos)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordMemberAwardScore Store.BatchRecordHourRankOrder err %v, orderInfos=%+v", err, orderInfo)
		_ = m.reporter.SendError(fmt.Sprintf("小时榜奖励分写入mysql失败 err:%s orderInfos=%+v", err, orderInfo))
		return err
	}

	err = m.Cache.AddHourRankMemberAwardScore(uid, hour, orderInfo.Score, tagLids, lockKey)
	if err != nil {
		log.ErrorWithCtx(ctx, "RecordMemberAwardScore Cache.AddHourRankMemberAwardScore err %v, orderInfo=%+v", err, orderInfo)
		_ = m.reporter.SendError(fmt.Sprintf("小时榜奖励分写入redis失败 err:%s orderInfos=%+v", err, orderInfo))
		return err
	}
	return nil
}
