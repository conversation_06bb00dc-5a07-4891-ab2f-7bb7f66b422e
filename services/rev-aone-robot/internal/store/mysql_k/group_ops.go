package mysql_k

import (
	"context"
	"database/sql"

	"golang.52tt.com/services/rev-aone-robot/internal/common"
)

/*
CREATE TABLE groupinfo (
	`id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '序号ID',
	`group_name` VARCHAR(50) NOT NULL COMMENT  'Lark群名称',
    `lark_chat_id` VARCHAR(120) NOT NULL COMMENT  'Lark群会话ID',
	PRIMARY KEY (`id`)
)ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '群组信息';
*/

func (d *AoneKSQLDao) AddLarkGroup(ctx context.Context, group common.LarkChatGroup) error {
	_, err := d.dbProvider.Exec(ctx,
		"INSERT INTO groupinfo (group_name, lark_chat_id)"+
			" VALUES(?, ?)",
		group.Name, group.LarkChatID)
	return err
}
func (d *AoneKSQLDao) GetLarkGroupByName(ctx context.Context, name string) (error, *common.LarkChatGroup) {
	var group common.LarkChatGroup
	err := d.dbProvider.QueryOne(ctx, &group,
		"FROM groupinfo WHERE group_name = ?", name)

	if err == sql.ErrNoRows {
		return nil, nil // 群组不存在
	} else if err != nil {
		return err, nil // 数据库查询错误，返回 nil 用户和错误
	}
	return nil, &group
}

func (d *AoneKSQLDao) GetLarkGroupByID(ctx context.Context, ID string) (error, *common.LarkChatGroup) {
	var group common.LarkChatGroup

	err := d.dbProvider.QueryOne(ctx, &group,
		"FROM groupinfo WHERE id = ?", ID)
	if err == sql.ErrNoRows {
		return nil, nil // 群组不存在
	} else if err != nil {
		return err, nil // 数据库查询错误，返回 nil 用户和错误
	}
	return nil, &group
}

func (d *AoneKSQLDao) GetAllLarkGroup(ctx context.Context) (error, []*common.LarkChatGroup) {
	var groups []*common.LarkChatGroup
	err := d.dbProvider.Query(ctx, &groups,
		"FROM groupinfo")
	if err != nil {
		return err, nil // 数据库查询错误，返回 nil 用户和错误
	}
	return nil, groups
}
