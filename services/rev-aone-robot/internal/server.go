package internal

import (
    "context"
    "fmt"
    "net/http"

    larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
    larkhttp "github.com/larksuite/oapi-sdk-go/v3/core/httpserverext"
    larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
    "golang.52tt.com/pkg/log"
    "golang.52tt.com/protocol/services/demo/echo"
    "golang.52tt.com/services/rev-aone-robot/internal/config"
    "golang.52tt.com/services/rev-aone-robot/internal/hook_event/feishu"
    git_event "golang.52tt.com/services/rev-aone-robot/internal/hook_event/gitlab"
    "golang.52tt.com/services/rev-aone-robot/internal/store"
)

// https://gitlab.ttyuyin.com/help/user/project/integrations/webhook_events.md

type WebHookServer struct {
    GitEvent *git_event.GitHookEventMgr
	FeishuEvent *feishu.LarkEvent

    startResultChan chan error
}

func NewWebHookServer(botConf *config.AoneRobotConfig) *WebHookServer {

    dbMgr, err := store.NewAoneStoreMgr(botConf.DbConf)
    if err != nil {
        log.Errorf("NewAoneStoreMgr failed: %v", err)
        return nil
    }

    // 创建一个gitlab hook 事件处理器
    g := git_event.NewGitHookEvent(botConf.GitRestApi, botConf.Lark, dbMgr)
	// 创建一个飞书事件处理器
    f := feishu.NewEvent(botConf.Lark, botConf.GitRestApi, dbMgr)

	return &WebHookServer{
		GitEvent:    g,
		FeishuEvent: f,
	}

}

// 启动web服务
func (s *WebHookServer) Start(config *config.WebHookConfig) error {

	http.HandleFunc("/ttrev/webhook/git", s.GitEvent.OnEvent)

	http.HandleFunc("/ttrev/webhook/lark/event", larkhttp.NewEventHandlerFunc(s.FeishuEvent.LarkEventHandler,
        larkevent.WithLogLevel(larkcore.LogLevelInfo)))

	//http.HandleFunc("/webhook/lark/card", larkhttp.NewCardActionHandlerFunc(cardHandler,
    //	larkevent.WithLogLevel(larkcore.LogLevelInfo)))

	fmt.Println("Listening begin")

    s.startResultChan = make(chan error)

    // 启动 goroutine
    go func() {
        resultErr := s.interStart(config)
        s.startResultChan <- resultErr // 将结果发送到通道
    }()

    go s.checkServeState()

    return nil
}

func (s *WebHookServer) interStart(config *config.WebHookConfig) error {
    return http.ListenAndServe(config.ListenAddr, nil)
}

// 检查hook listen 的服务状态
func (s *WebHookServer) checkServeState() {

    // 等待启动结果
    select {
    case result := <-s.startResultChan:
        if result != nil {
            fmt.Println("WebHookServer finished ")
            log.Errorf("http ListenAndServe finished: %v", result)

            s.ShutDown()
        }
    }
}

func (s *WebHookServer) ShutDown() {

}

func (s *WebHookServer) Echo(ctx context.Context, req *echo.StringMessage) (*echo.StringMessage, error) {
	return req, nil
}
