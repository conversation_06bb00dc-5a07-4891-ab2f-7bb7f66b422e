package anti_corruption_layer

//go:generate quicksilver-cli test interface ../anti-corruption-layer
//go:generate mockgen -destination=../mocks/anti_corruption_layer.go -package=mocks golang.52tt.com/services/channel-wedding/internal/model/anti-corruption-layer IACLayer

import (
	"context"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	channel_msg_api "gitlab.ttyuyin.com/bizFund/bizFund/pkg/channel-msg-api"
	channelMic "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic"
	micMiddle "gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channel_mic_middle"
	"gitlab.ttyuyin.com/gengo/bizplatform-ttinfra/tt/bizplatform/ttinfra/channelol_go"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	"golang.52tt.com/clients/account"
	award_center "golang.52tt.com/clients/award-center"
	backpackBase "golang.52tt.com/clients/backpack-base"
	backpackSender "golang.52tt.com/clients/backpack-sender"
	"golang.52tt.com/clients/channel"
	channelIm "golang.52tt.com/clients/channelim"
	imApi "golang.52tt.com/clients/im-api"
	presend_middleware_client "golang.52tt.com/clients/present-middleware"
	publicnotice "golang.52tt.com/clients/public-notice"
	push "golang.52tt.com/clients/push-notification/v2"
	user_online "golang.52tt.com/clients/user-online"
	userProfile "golang.52tt.com/clients/user-profile-api"
	"golang.52tt.com/pkg/deal_token"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/app/channel_wedding_logic"
	pushPb "golang.52tt.com/protocol/app/push"
	"golang.52tt.com/protocol/app/virtual_image_logic"
	backpackSenderPB "golang.52tt.com/protocol/services/backpacksender"
	channel_wedding "golang.52tt.com/protocol/services/channel-wedding"
	channel_wedding_conf "golang.52tt.com/protocol/services/channel-wedding-conf"
	channel_wedding_minigame "golang.52tt.com/protocol/services/channel-wedding-minigame"
	channel_wedding_plan "golang.52tt.com/protocol/services/channel-wedding-plan"
	"golang.52tt.com/protocol/services/channelim"
	fellow_level_award "golang.52tt.com/protocol/services/fellow-level-award"
	fellow_svr "golang.52tt.com/protocol/services/fellow-svr"
	present_middlewarePb "golang.52tt.com/protocol/services/present-middleware"
	publicNoticePb "golang.52tt.com/protocol/services/public-notice"
	virtual_image_resource "golang.52tt.com/protocol/services/virtual-image-resource"
	virtual_image_user "golang.52tt.com/protocol/services/virtual-image-user"
	"golang.52tt.com/services/channel-wedding/internal/conf"
	"golang.52tt.com/services/channel-wedding/internal/model/comm"
	butils "golang.52tt.com/services/risk-control/backpack-sender/utils"
	"time"
)

type ACLayer struct {
	shutDown             chan struct{}
	bc                   conf.IBusinessConfManager
	imApiCli             imApi.IClient
	channelMsgApi        channel_msg_api.IClient
	userProfileCli       userProfile.IClient
	channelMicCli        channelMic.ChannelMicClient
	virtualImageUser     virtual_image_user.VirtualImageUserClient
	virtualImageResource virtual_image_resource.VirtualImageResourceClient
	micMiddleCli         micMiddle.ChannelMicMiddleClient
	weddingMiniGameCli   channel_wedding_minigame.ChannelWeddingMinigameClient
	weddingCfgCli        channel_wedding_conf.ChannelWeddingConfClient
	weddingPlanCli       channel_wedding_plan.ChannelWeddingPlanClient
	publicNoticeCli      publicnotice.IClient
	channelCli           channel.IClient
	pushCli              push.IClient
	backpackSenderCli    backpackSender.IClient
	backpackCli          backpackBase.IClient
	channelOlCli         channelol_go.ChannelolGoClient

	awardCenter         award_center.IClient
	fellowSvrCli        fellow_svr.FellowSvrClient
	fellowLeverAwardCli fellow_level_award.FellowLevelAwardClient
	presentMiddle       presend_middleware_client.IClient
	channelImCli        channelIm.IClient
	userOlCli           user_online.IClient
}

type SendPresentReq struct {
	FromUid, FromCid, ToUid, GiftId, Price, Amount uint32
	OrderId, DealToken                             string
	AwardTs                                        int64
}

// NewMgr 活动配置模块
func NewMgr(ctx context.Context, bc conf.IBusinessConfManager) (*ACLayer, error) {
	imApiCli, _ := imApi.NewClient()
	channelMsgApi, _ := channel_msg_api.NewIClient()
	userProfileCli, _ := userProfile.NewClient()
	channelMicCli := channelMic.MustNewClient(ctx)
	virtualImageUser := virtual_image_user.MustNewClient(ctx)
	virtualImageResource := virtual_image_resource.MustNewClient(ctx)
	micMiddleCli := micMiddle.MustNewClient(ctx)
	weddingMiniGameCli := channel_wedding_minigame.MustNewClient(ctx)
	weddingCfgCli := channel_wedding_conf.MustNewClient(ctx)
	weddingPlanCli := channel_wedding_plan.MustNewClient(ctx)
	publicNoticeCli := publicnotice.NewIClient()
	channelCli := channel.NewIClient()
	pushCli, _ := push.NewClient()
	backpackSenderCli, _ := backpackSender.NewClient()
	backpackCli, _ := backpackBase.NewClient()
	channelOlCli, _ := channelol_go.NewClient(ctx)

	awardCenter, _ := award_center.NewClient()
	fellowSvrCli, _ := fellow_svr.NewClient(ctx)
	fellowLeverAwardCli, _ := fellow_level_award.NewClient(ctx)
	presentMiddle := presend_middleware_client.NewIClient()
	channelImCli := channelIm.NewClient()
	userOlCli := user_online.NewIClient()
	m := &ACLayer{
		shutDown:             make(chan struct{}),
		bc:                   bc,
		imApiCli:             imApiCli,
		channelMsgApi:        channelMsgApi,
		userProfileCli:       userProfileCli,
		channelMicCli:        channelMicCli,
		virtualImageUser:     virtualImageUser,
		virtualImageResource: virtualImageResource,
		micMiddleCli:         micMiddleCli,
		weddingMiniGameCli:   weddingMiniGameCli,
		weddingCfgCli:        weddingCfgCli,
		weddingPlanCli:       weddingPlanCli,
		publicNoticeCli:      publicNoticeCli,
		channelCli:           channelCli,
		pushCli:              pushCli,
		backpackSenderCli:    backpackSenderCli,
		backpackCli:          backpackCli,
		channelOlCli:         channelOlCli,

		awardCenter:         awardCenter,
		fellowSvrCli:        fellowSvrCli,
		fellowLeverAwardCli: fellowLeverAwardCli,
		presentMiddle:       presentMiddle,
		channelImCli:        channelImCli,
		userOlCli:           userOlCli,
	}

	return m, nil
}

func (m *ACLayer) Stop() {
	close(m.shutDown)
}

// GetWeddingThemeCfg 获取婚礼主题配置
func (m *ACLayer) GetWeddingThemeCfg(ctx context.Context, themeId uint32) (*channel_wedding_conf.ThemeCfg, error) {
	out, err := m.weddingCfgCli.GetThemeCfg(ctx, &channel_wedding_conf.GetThemeCfgReq{
		ThemeId: themeId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingThemeCfg failed. themeId:%d, err:%v", themeId, err)
		return nil, err
	}

	return out.GetThemeCfg(), nil
}

// GetWeddingPlanInfo 获取婚礼方案信息
func (m *ACLayer) GetWeddingPlanInfo(ctx context.Context, planId uint32) (*channel_wedding_plan.GetSimpleWeddingPlanInfoResponse, error) {
	if planId == 0 {
		return nil, nil
	}
	out, err := m.weddingPlanCli.GetSimpleWeddingPlanInfo(ctx, &channel_wedding_plan.GetSimpleWeddingPlanInfoRequest{
		WeddingPlanId: planId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "GetWeddingPlanInfo failed. planId:%d, err:%v", planId, err)
		return nil, err
	}

	return out, nil
}

// UpdateWeddingPlanStatus 更新婚礼方案状态
func (m *ACLayer) UpdateWeddingPlanStatus(ctx context.Context, planId uint32) error {
	if planId == 0 {
		return nil
	}
	_, err := m.weddingPlanCli.UpdateWeddingPlanStatus(ctx, &channel_wedding_plan.UpdateWeddingPlanStatusRequest{
		WeddingPlanId: planId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "UpdateWeddingPlanStatus failed. planId:%d, err:%v", planId, err)
		return err
	}

	return nil
}

type SceneNotifyMsgReq struct {
	OpUid              uint32
	Cid                uint32
	Level              uint32
	ThemeId            uint32
	SceneId            uint32
	ExtOpt             proto.MessageV1
	HighlightPresentId uint32
	BuyerUid           uint32
	BuyerPresentId     uint32
}

func (m *ACLayer) SendSceneNotifyMsg(ctx context.Context, in *SceneNotifyMsgReq) error {
	if in == nil || in.Cid == 0 || in.ThemeId == 0 {
		return nil
	}

	themeCfg, err := m.GetWeddingThemeCfg(ctx, in.ThemeId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendSceneNotifyMsg failed to GetWeddingThemeCfg. themeId:%d, err:%v", in.ThemeId, err)
		return err
	}

	var sceneCfg *channel_wedding_conf.WeddingSceneCfg
	for _, info := range themeCfg.GetSceneCfgList() {
		if info.GetScene() == in.SceneId {
			sceneCfg = info
		}
	}
	if sceneCfg == nil {
		//return nil
	}

	reservePresent := &channel_wedding_logic.WeddingReservePresent{}
	highlight := &channel_wedding_logic.WeddingSceneHighLightOpt{}
	// 高光时刻增加抛花球送红钻礼物的逻辑
	if in.HighlightPresentId > 0 {
		var presentId uint32
		bgItemCfg, err := m.backpackCli.GetPackageItemCfg(ctx, in.HighlightPresentId)
		if err != nil {
			log.ErrorWithCtx(ctx, "SendSceneNotifyMsg failed to GetPackageItemCfg. opUid:%d, err:%v", in.OpUid, err)
		}
		if bgItemCfg.GetItemCfgList() != nil {
			presentId = bgItemCfg.GetItemCfgList()[0].GetSourceId()
		}
		log.InfoWithCtx(ctx, "SendSceneNotifyMsg. opUid:%v, in:%+v, presentID:%d", in.OpUid, in, presentId)
		if presentId != 0 {
			highlight.PresentId = presentId
			in.ExtOpt = highlight
		}
	} else {
		// 其他场景需要发 BuyerUid>0 需要发预定礼物
		if in.BuyerUid > 0 && in.BuyerPresentId > 0 {
			reservePresent.PresentId = in.BuyerPresentId
			reservePresent.BuyerUid = in.BuyerUid
			log.InfoWithCtx(ctx, "SendSceneNotifyMsg.  in:%+v, reservePresent:%+v", in, reservePresent)
		}
	}

	msgTy := uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_SCENE_NOTIFY_MSG)
	opt := &channel_wedding_logic.WeddingSceneNotifyOpt{
		Cid:            in.Cid,
		ServerTimeMs:   time.Now().UnixMilli(),
		CurrLevel:      in.Level,
		SceneCfg:       fillWeddingSceneCfg(sceneCfg, in.SceneId),
		ExtOpt:         "",
		ReservePresent: reservePresent,
	}

	if in.ExtOpt != nil {
		data, _ := proto.Marshal(in.ExtOpt)
		opt.ExtOptBytes = data
	}

	msg, err := proto.Marshal(opt)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendSceneNotifyMsg marshal err:%v, %+v", err, opt)
		return err
	}

	seqId, _, err := m.channelMsgApi.ReliablePushToChannel(ctx, in.OpUid, in.Cid, msgTy, "场景动画", msg)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendSceneNotifyMsg ReliablePushToChannel failed, %+v, seqId:%v, err:%v", in, seqId, err)
		return err
	}

	log.DebugWithCtx(ctx, "SendSceneNotifyMsg success. in:%+v, opt:%+v", in, opt)
	return nil
}

// SendStageChangeMsg 阶段变更房间广播消息
func (m *ACLayer) SendStageChangeMsg(ctx context.Context, opUid uint32, info *channel_wedding.WeddingInfo) error {
	if info == nil || info.GetCid() == 0 {
		return nil
	}

	uidList := []uint32{info.GetGroom().GetUid(), info.GetBride().GetUid()}
	userMap, err := m.userProfileCli.BatchGetUserProfileV2(ctx, uidList, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendStageChangeMsg failed to BatchGetUserProfileV2. opUid:%d, uidList:%v, err:%v", opUid, uidList, err)
		return err
	}

	opt := &channel_wedding_logic.WeddingStageChangeOpt{
		Cid:          info.GetCid(),
		ServerTimeMs: time.Now().UnixMilli(),
		WeddingInfo: &channel_wedding_logic.WeddingInfo{
			WeddingId:         info.GetWeddingId(),
			StartTime:         info.GetStartTime(),
			EndTime:           info.GetEndTime(),
			ChairGameEntry:    info.GetChairGameEntry(),
			BridesmaidManList: info.GetBridesmaidManList(),
			HappinessValue:    info.GetCurrHappinessValue(),
			CurrLevel:         info.GetCurrLevel(),
			Bride: &channel_wedding_logic.WeddingCpMemInfo{
				UserProfile: userMap[info.GetBride().GetUid()],
			},
			Groom: &channel_wedding_logic.WeddingCpMemInfo{
				UserProfile: userMap[info.GetGroom().GetUid()],
			},
			StageInfo: fillWeddingStageInfo(info.GetStageInfo()),
			ThemeCfg:  fillWeddingRoomThemeCfg(info.GetThemeCfg()),
			WeddingMemorialVideo: &channel_wedding_logic.WeddingMemorialVideo{
				ResourceUrl:  info.GetWeddingMemorialVideo().GetResourceUrl(),
				ResourceMd5:  info.GetWeddingMemorialVideo().GetResourceMd5(),
				ResourceJson: info.GetWeddingMemorialVideo().GetResourceJson(),
				UserPictures: info.GetWeddingMemorialVideo().GetUserPictures(),
			},
			BoneCfg: &channel_wedding_logic.WeddingBoneCfg{
				MaleBoneId:       info.GetBoneCfg().GetMaleBoneId(),
				FemaleBoneId:     info.GetBoneCfg().GetFemaleBoneId(),
				BaseMaleBoneId:   info.GetBoneCfg().GetBaseMaleBoneId(),
				BaseFemaleBoneId: info.GetBoneCfg().GetBaseFemaleBoneId(),
			},
			HappinessConfig: fillHappinessConfigInfo(info.GetHappinessConfig()),
		},
	}

	if info.GetStageInfo().GetCurrStage() == uint32(channel_wedding_logic.WeddingStage_WEDDING_STAGE_WELCOME_GUEST) {
		// 欢迎嘉宾阶段, 需带上新人服装信息用于婚礼预告展示
		uid2ItemList, err := m.GetUserVirtualImageInuseMap(ctx, uidList)
		if err != nil {
			log.WarnWithCtx(ctx, "SendStageChangeMsg failed to GetUserVirtualImageInuseMap. opUid:%d, uidList:%v, err:%v", opUid, uidList, err)
		}

		for uid, itemList := range uid2ItemList {
			clotheIdList := make([]uint32, 0)
			for _, item := range itemList {
				clotheIdList = append(clotheIdList, item.GetCfgId())
			}

			clothesInfo := &channel_wedding_logic.WeddingClothesInfo{
				Uid:           uid,
				ClothesIdList: clotheIdList,
			}

			if uid == info.GetGroom().GetUid() {
				clothesInfo.Orientation = uint32(virtual_image_logic.VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_RIGHT)
			} else {
				clothesInfo.Orientation = uint32(virtual_image_logic.VirtualImageOrientation_VIRTUAL_IMAGE_ORIENTATION_LEFT)
			}

			opt.ClothesInfoList = append(opt.ClothesInfoList, clothesInfo)
		}
	}

	data, e := proto.Marshal(opt)
	if e != nil {
		log.ErrorWithCtx(ctx, "SendStageChangeMsg marshal err:%v, %+v", e, opt)
		return e
	}

    seqId, _, err2 := m.channelMsgApi.ReliablePushToChannel(ctx, opUid, info.GetCid(),
		uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_STAGE_CHANGE_MSG), "婚礼房阶段变更", data)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "SendStageChangeMsg failed to ReliablePushToChannel. err: %s, seqId:%v, opUid:%v, info:%+v", err2.Error(), seqId, opUid, info)
		return err2
	}

	log.DebugWithCtx(ctx, "SendStageChangeMsg success. opUid:%v, opt:%+v", opUid, opt)
	return nil
}

// SendWeddingBridesmaidUpdateMsg 伴郎伴娘变更推送
func (m *ACLayer) SendWeddingBridesmaidUpdateMsg(ctx context.Context, cid uint32, bridesmaidManList []uint32) error {
	if cid == 0 {
		return nil
	}

	opt := &channel_wedding_logic.WeddingBridesmaidUpdateOpt{
		BridesmaidManList: bridesmaidManList,
	}

	data, e := proto.Marshal(opt)
	if e != nil {
		log.ErrorWithCtx(ctx, "SendWeddingBridesmaidUpdateMsg marshal err:%v, %+v", e, opt)
		return e
	}

	err2 := m.SimplePushToChannel(ctx, 0, cid,
		uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_BRIDESMAID_UPDATE_MSG), "伴郎伴娘变更", data)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "SendWeddingBridesmaidUpdateMsg failed to SimplePushToChannel. err: %s, cid:%v, bridesmaidManList:%+v", err2.Error(), cid, bridesmaidManList)
		return err2
	}

	log.DebugWithCtx(ctx, "SendWeddingBridesmaidUpdateMsg success. cid:%v, opt:%+v", cid, opt)
	return nil
}

func fillWeddingRoomThemeCfg(cfg *channel_wedding.WeddingRoomThemeCfg) *channel_wedding_logic.WeddingRoomThemeCfg {
	if cfg == nil {
		return nil
	}

	out := &channel_wedding_logic.WeddingRoomThemeCfg{
		ThemeId:             cfg.GetThemeId(),
		ThemeResource:       cfg.GetThemeResource(),
		ThemeResourceMd5:    cfg.GetThemeResourceMd5(),
		IsFreeTheme:         cfg.GetIsFreeTheme(),
		SceneCfgList:        make([]*channel_wedding_logic.WeddingSceneCfg, 0, len(cfg.GetSceneCfgList())),
		LevelClothesList:    make([]*channel_wedding_logic.WeddingLevelClothes, 0, len(cfg.GetLevelClothesList())),
		LevelBackgroundList: make([]*channel_wedding_logic.WeddingLevelBackgroundCfg, 0, len(cfg.GetLevelBackgroundList())),
		WeddingPreviewResource: &channel_wedding_logic.WeddingResource{
			ResourceUrl:  cfg.GetWeddingPreviewResource().GetResourceUrl(),
			ResourceMd5:  cfg.GetWeddingPreviewResource().GetResourceMd5(),
			CpBoneId:     cfg.GetWeddingPreviewResource().GetCpBoneId(),
			ItemIdList:   cfg.GetWeddingPreviewResource().GetItemIds(),
			BaseCpBoneId: cfg.GetWeddingPreviewResource().GetBaseCpBoneId(),
		},
	}

	for _, v := range cfg.GetSceneCfgList() {
		out.SceneCfgList = append(out.SceneCfgList, &channel_wedding_logic.WeddingSceneCfg{
			Scene:            v.GetScene(),
			SceneResource:    v.GetSceneResource(),
			SceneResourceMd5: v.GetSceneResourceMd5(),
			BoneCfgList:      fillSceneBoneCfgList(v.GetBoneCfgList()),
		})
	}

	for _, v := range cfg.GetLevelClothesList() {
		out.LevelClothesList = append(out.LevelClothesList, &channel_wedding_logic.WeddingLevelClothes{
			Level:             v.GetLevel(),
			GroomClothes:      v.GetGroomClothes(),
			BrideClothes:      v.GetBrideClothes(),
			GroomsmanClothes:  v.GetGroomsmanClothes(),
			BridesmaidClothes: v.GetBridesmaidClothes(),
		})
	}

	for _, v := range cfg.GetLevelBackgroundList() {
		log.Debugf("fillWeddingRoomThemeCfg. GetLevelBackgroundList:%+v", v)

		out.LevelBackgroundList = append(out.LevelBackgroundList, &channel_wedding_logic.WeddingLevelBackgroundCfg{
			Level:                    v.GetLevel(),
			BackgroundPicture:        v.GetBackgroundPicture(),
			BackgroundMp4Url:         v.GetBackgroundMp4Url(),
			SpecialBackgroundMp4Url:  v.GetSpecialBackgroundMp4Url(),
			SpecialBackgroundPicture: v.GetSpecialBackgroundPicture(),
		})
	}

	if cfg.GetChairResCfg() != nil {
		out.ChairResCfg = &channel_wedding_logic.ChairGameResourceCfg{
			ChairPic:            cfg.GetChairResCfg().GetChairPic(),
			SittingPoseFemaleId: cfg.GetChairResCfg().GetSittingPoseFemaleId(),
			SittingPoseMaleId:   cfg.GetChairResCfg().GetSittingPoseMaleId(),
			StandbyFemaleId:     cfg.GetChairResCfg().GetStandbyFemaleId(),
			StandbyMaleId:       cfg.GetChairResCfg().GetStandbyMaleId(),
			FailFemaleIds:       cfg.GetChairResCfg().GetFailFemaleIds(),
			FailMaleIds:         cfg.GetChairResCfg().GetFailMaleIds(),
		}
	}

	return out
}

func fillSceneBoneCfgList(list []*channel_wedding.WeddingSceneBoneCfg) []*channel_wedding_logic.WeddingSceneBoneCfg {
	out := make([]*channel_wedding_logic.WeddingSceneBoneCfg, 0, len(list))
	for _, v := range list {
		out = append(out, &channel_wedding_logic.WeddingSceneBoneCfg{
			Level:         v.GetLevel(),
			SeqIndex:      v.GetSeqIndex(),
			AnimationName: v.GetAnimationName(),
			BoneId:        v.GetBoneId(),
			BaseBoneId:    v.GetBaseBoneId(),
		})
	}
	return out
}

func fillWeddingStageInfo(info *channel_wedding.WeddingStageInfo) *channel_wedding_logic.WeddingStageInfo {
	if info == nil {
		return nil
	}

	out := &channel_wedding_logic.WeddingStageInfo{
		CurrStage:      info.GetCurrStage(),
		SubStage:       info.GetSubStage(),
		StageBeginTime: info.GetStageStartTs(),
		StageEndTime:   info.GetStageEndTs(),
		StageCfgList:   make([]*channel_wedding_logic.WeddingStageCfg, 0),
	}

	for _, v := range info.GetStageCfgList() {
		out.StageCfgList = append(out.StageCfgList, &channel_wedding_logic.WeddingStageCfg{
			Stage:     v.GetStage(),
			SubStage:  v.GetSubStage(),
			StageName: v.GetStageName(),
		})
	}

	return out
}

func fillWeddingSceneCfg(cfg *channel_wedding_conf.WeddingSceneCfg, sceneId uint32) *channel_wedding_logic.WeddingSceneCfg {
	if cfg == nil {
		return &channel_wedding_logic.WeddingSceneCfg{
			Scene: sceneId,
		}
	}

	list := make([]*channel_wedding_logic.WeddingSceneBoneCfg, 0)
	for _, v := range cfg.GetBoneCfgList() {
		list = append(list, &channel_wedding_logic.WeddingSceneBoneCfg{
			Level:         v.Level,
			SeqIndex:      v.SeqIndex,
			AnimationName: v.AnimationName,
			BoneId:        v.BoneId,
			BaseBoneId:    v.GetBaseBoneId(),
		})
	}

	return &channel_wedding_logic.WeddingSceneCfg{
		Scene:            cfg.GetScene(),
		SceneResource:    cfg.GetResource().GetResourceUrl(),
		SceneResourceMd5: cfg.GetResource().GetResourceMd5(),
		BoneCfgList:      list,
	}
}

// SendHappinessChangeMsg 幸福值变更房间广播消息
func (m *ACLayer) SendHappinessChangeMsg(ctx context.Context, uid, cid, happiness, nextLevelTipsValue uint32) error {
	if cid == 0 || happiness == 0 {
		return nil
	}

	opt := &channel_wedding_logic.WeddingHappinessChangeOpt{
		Cid:                cid,
		ServerTimeMs:       time.Now().UnixMilli(),
		CurrHappinessValue: happiness,
		NextLevelTips:      nextLevelTipsValue,
	}

	data, e := proto.Marshal(opt)
	if e != nil {
		log.ErrorWithCtx(ctx, "SendHappinessChangeMsg marshal err:%v, %+v", e, opt)
		return e
	}

	err2 := m.SimplePushToChannel(ctx, uid, cid,
		uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_HAPPINESS_CHANGE_MSG), "婚礼房幸福值变更", data)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "SendHappinessChangeMsg failed to SimplePushToChannel. err: %s, opUid:%v, info:%+v", err2.Error(), uid, opt)
		return err2
	}

	log.DebugWithCtx(ctx, "SendHappinessChangeMsg success. opUid:%v, opt:%+v", uid, opt)
	return nil
}

// SendGroupPhotoPosChangeMsg 发送合照位置变化通知
func (m *ACLayer) SendGroupPhotoPosChangeMsg(ctx context.Context, opUid, cid uint32, micIdList []uint32) error {
	if cid == 0 {
		return nil
	}

	opt := &channel_wedding_logic.WeddingGroupPhotoSeatChangeOpt{
		Cid:      cid,
		SeatList: make([]*channel_wedding_logic.WeddingGroupPhotoSeat, 0),
	}

	for _, micId := range micIdList {
		opt.SeatList = append(opt.SeatList, &channel_wedding_logic.WeddingGroupPhotoSeat{
			MicId: micId,
		})
	}

	data, e := proto.Marshal(opt)
	if e != nil {
		log.ErrorWithCtx(ctx, "SendGroupPhotoPosChangeMsg marshal err:%v, %+v", e, opt)
		return e
	}

	err2 := m.SimplePushToChannel(ctx, opUid, cid,
		uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_GROUP_PHOTO_SEAT_CHANGE_MSG), "合照位置信息变化", data)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "SendGroupPhotoPosChangeMsg failed to SimplePushToChannel. err: %s, opUid:%v, info:%+v",
			err2.Error(), opUid, micIdList)
		return err2
	}

	log.DebugWithCtx(ctx, "SendGroupPhotoPosChangeMsg success. opUid:%v, opt:%+v", opUid, opt)
	return nil
}

// SendGuestEnterRoomMsg 嘉宾进房公屏提醒
func (m *ACLayer) SendGuestEnterRoomMsg(ctx context.Context, opUid, cid, guestUid, guestType uint32) error {
	user, err := m.userProfileCli.GetUserProfileV2(ctx, guestUid, true)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendGuestEnterRoomMsg failed to GetUserProfileV2. opUid:%d, guestUid:%d, err:%v", opUid, guestUid, err)
		return err
	}

	if user.GetPrivilege().GetType() > 0 {
		// 神秘人不处理
		return nil
	}

	opt := &channel_wedding_logic.WeddingGuestEnterRoomOpt{
		Cid:          cid,
		ServerTimeMs: time.Now().UnixMilli(),
		User:         user,
		GuestType:    guestType,
	}

	if guestType == uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_GROOM) {
		opt.PublicTextPrefix = "恭迎新郎"
		opt.PublicTextSuffix = "隆重登场，幸福的他今天将迎娶最美的新娘"

	} else if guestType == uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDE) {
		opt.PublicTextPrefix = "恭迎新娘"
		opt.PublicTextSuffix = "隆重登场，美丽的她今天将嫁给帅气的新郎"

	} else {
		// 伴郎伴娘需要根据性别区分
		if user.GetSex() == uint32(account.Male) {
			opt.GuestType = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDES)
			opt.PublicTextPrefix = "欢迎伴郎"
			opt.PublicTextSuffix = "入场，他不仅是新郎的好哥们，也是我们共同的祝福使者"
		} else {
			opt.GuestType = uint32(channel_wedding_logic.WeddingGuestType_WEDDING_GUEST_TYPE_BRIDESMAID)
			opt.PublicTextPrefix = "欢迎伴娘"
			opt.PublicTextSuffix = "入场，她不仅是新娘的好姐妹，也是我们共同的祝福使者"
		}
	}

	data, e := proto.Marshal(opt)
	if e != nil {
		log.ErrorWithCtx(ctx, "SendGuestEnterRoomMsg marshal err:%v, %+v", e, opt)
		return e
	}

	err2 := m.SimplePushToChannel(ctx, opUid, cid,
		uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_GUEST_ENTER_MSG), "嘉宾进房提醒", data)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "SendGuestEnterRoomMsg failed to SimplePushToChannel. err: %s, opUid:%v, info:%+v",
			err2.Error(), opUid, opt)
		return err2
	}

	log.DebugWithCtx(ctx, "SendGuestEnterRoomMsg success. opUid:%v, opt:%+v", opUid, opt)
	return nil
}

// KickCommUserOutMicSpace 踢普通用户(非嘉宾)下麦
func (m *ACLayer) KickCommUserOutMicSpace(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo) error {
	if weddingInfo == nil || weddingInfo.GetCid() == 0 {
		return nil
	}
	channelId := weddingInfo.GetCid()

	// 1.获取房间内当前麦位列表
	micResp, err := m.channelMicCli.GetMicrList(ctx, &channelMic.GetMicrListReq{
		ChannelId: channelId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "KickOutMicSpace fail to GetMicrList. channelId:%d, err:%v", channelId, err)
		return err
	}

	guestUidMap := make(map[uint32]struct{})
	for _, uid := range weddingInfo.GetBridesmaidManList() {
		guestUidMap[uid] = struct{}{} // 伴郎伴娘
	}

	kickOutList := make([]uint32, 0)
	for _, mic := range micResp.GetAllMicList() {
		if mic.GetMicUid() == 0 {
			// 空麦位不踢
			continue
		}
		if _, ok := guestUidMap[mic.GetMicUid()]; ok &&
			mic.GetMicId() != comm.GroomMicId &&
			mic.GetMicId() != comm.BrideMicId {
			// 不在新人麦的伴郎伴娘不踢
			continue
		}
		if mic.GetMicId() == comm.HostMicId {
			// 主持人不踢
			continue
		}
		if mic.GetMicId() == comm.GroomMicId && mic.GetMicUid() == weddingInfo.GetGroom().GetUid() {
			// 新郎不踢
			continue
		}
		if mic.GetMicId() == comm.BrideMicId && mic.GetMicUid() == weddingInfo.GetBride().GetUid() {
			// 新娘不踢
			continue
		}

		kickOutList = append(kickOutList, mic.GetMicUid())
	}

	if len(kickOutList) == 0 {
		return nil
	}

	_, err = m.micMiddleCli.KickOutMic(ctx, &micMiddle.KickOutMicReq{
		Source:        "channel-wedding",
		OpUid:         0,
		Cid:           channelId,
		TargetUidList: kickOutList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "KickOutMicSpace fail to KickOutMicSpace.kickOutList:%v channelId:%d, err:%v", kickOutList, channelId, err)
		return err
	}

	log.InfoWithCtx(ctx, "KickOutMicSpace success. kickOutList:%v, channelId:%d", kickOutList, channelId)
	return nil
}

// SendLevelChangeMsg 等级变更房间广播消息
func (m *ACLayer) SendLevelChangeMsg(ctx context.Context, weddingInfo *channel_wedding.WeddingInfo, level uint32,
	upgradeCloths map[uint32][]uint32, poseList []*channel_wedding.UserWeddingPose) error {
	opt := &channel_wedding_logic.WeddingLevelChangeOpt{
		Cid:          weddingInfo.GetCid(),
		CurrLevel:    level,
		ServerTimeMs: time.Now().UnixMilli(),
	}

	poseMap := make(map[uint32]*channel_wedding.UserWeddingPose)
	for _, pose := range poseList {
		poseMap[pose.GetUid()] = pose
	}

	var err error
	opt.ClothesInfoList, err = m.genLevelUpgradeClothes(ctx, upgradeCloths, poseMap)
	if err != nil {
		log.WarnWithCtx(ctx, "SendLevelChangeMsg genLevelUpgradeClothes err:%v, %+v", err, opt)
	}

	data, e := proto.Marshal(opt)
	if e != nil {
		log.ErrorWithCtx(ctx, "SendLevelChangeMsg marshal err:%v, %+v", e, opt)
		return e
	}

	err2 := m.SimplePushToChannel(ctx, weddingInfo.GetBride().GetUid(), weddingInfo.GetCid(),
		uint32(channelPB.ChannelMsgType_CHANNEL_WEDDING_LEVEL_CHANGE_MSG), "婚礼房等级变更", data)
	if err2 != nil {
		log.ErrorWithCtx(ctx, "SendLevelChangeMsg failed to SimplePushToChannel. err: %s, opUid:%v, opt:%+v", err2.Error(), weddingInfo.GetBride().GetUid(), opt)
		return err2
	}

	log.DebugWithCtx(ctx, "SendLevelChangeMsg success. opUid:%v, opt:%+v", weddingInfo.GetBride().GetUid(), opt)
	return nil
}

func fillHappinessConfigInfo(info *channel_wedding.HappinessConfigInfo) *channel_wedding_logic.HappinessConfigInfo {
	out := &channel_wedding_logic.HappinessConfigInfo{}
	for _, v := range info.GetConfig() {
		out.Config = append(out.Config, &channel_wedding_logic.HappinessLevelInfo{
			Level:      v.GetLevel(),
			LevelValue: v.GetLevelValue(),
		})
	}
	return out
}

var sitSubCateTy = uint32(virtual_image_resource.VirtualImageResourceSubCategory_VIRTUAL_IMAGE_RESOURCE_SUB_CATEGORY_SITTING_POSE)

// getUseItemListWithPose 获取用户使用的物品列表,拼接站姿
func getUseItemListWithPose(useItems []*virtual_image_user.InuseItemInfo, pose *channel_wedding.UserWeddingPose) []*virtual_image_user.InuseItemInfo {
	itemList := make([]*virtual_image_user.InuseItemInfo, 0, len(useItems))
	for _, item := range useItems {
		if item.GetSubCategory() == sitSubCateTy {
			// 婚礼房不需要坐姿
			continue
		}
		itemList = append(itemList, item)
	}

	if pose.GetPose() > 0 {
		itemList = append(itemList, &virtual_image_user.InuseItemInfo{
			CfgId: pose.GetPose(), SubCategory: sitSubCateTy,
		})
		if pose.GetPoseBoneId() > 0 {
			itemList = append(itemList, &virtual_image_user.InuseItemInfo{
				CfgId: pose.GetPoseBoneId(), SubCategory: 0,
			})
		}

		if pose.GetBasePoseBoneId() > 0 {
			itemList = append(itemList, &virtual_image_user.InuseItemInfo{
				CfgId: pose.GetBasePoseBoneId(), SubCategory: 0,
			})
		}
	}

	return itemList
}

func (m *ACLayer) genLevelUpgradeClothes(ctx context.Context, uid2Clothes map[uint32][]uint32, uid2Pose map[uint32]*channel_wedding.UserWeddingPose) ([]*channel_wedding_logic.WeddingClothesInfo, error) {
	if len(uid2Clothes) == 0 {
		return nil, nil
	}

	infoList := make([]*channel_wedding_logic.WeddingClothesInfo, 0, len(uid2Clothes))
	uidList := make([]uint32, 0, len(uid2Clothes))
	for uid := range uid2Clothes {
		if uid == 0 {
			continue
		}
		uidList = append(uidList, uid)
	}

	uis2UseList, err := m.GetUserVirtualImageInuseMap(ctx, uidList)
	if err != nil {
		log.ErrorWithCtx(ctx, "genLevelUpgradeClothes failed to GetUserVirtualImageInuseMap. uidList:%v, err:%v", uidList, err)
		return nil, err
	}

	allowUseSubCateMap := make(map[uint32]bool)
	sunCateList := m.bc.GetAllowChangeSubCategoryList()
	for _, subCate := range sunCateList {
		allowUseSubCateMap[subCate] = true
	}

	for uid, clothesList := range uid2Clothes {
		list := clothesList
		itemList := getUseItemListWithPose(matchAllowSubCateItemList(allowUseSubCateMap, uis2UseList[uid]), uid2Pose[uid])
		for _, item := range itemList {
			list = append(list, item.GetCfgId())
		}

		infoList = append(infoList, &channel_wedding_logic.WeddingClothesInfo{
			Uid:           uid,
			ClothesIdList: list,
			Orientation:   uid2Pose[uid].GetOrientation(),
		})
	}

	return infoList, nil
}

// EndWeddingChairGame 结束抢椅子游戏
func (m *ACLayer) EndWeddingChairGame(ctx context.Context, cid, weddingId uint32) error {
	_, err := m.weddingMiniGameCli.ForceCloseChairGame(ctx, &channel_wedding_minigame.ForceCloseChairGameRequest{
		ChannelId: cid,
		WeddingId: weddingId,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "EndWeddingChairGame fail to ForceCloseChairGame. channelId:%d, err:%v", cid, err)
		return err
	}
	return nil
}

func (m *ACLayer) PushBreakingNews(ctx context.Context, channelId, newsId uint32, weddingInfo *channel_wedding.WeddingInfo) error {
	userMap, err := m.userProfileCli.BatchGetUserProfileV2(ctx, []uint32{weddingInfo.GetGroom().GetUid(), weddingInfo.GetBride().GetUid()}, false)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushBreakingNews failed to BatchGetUserProfileV2. channelId:%d, err:%v", channelId)
		return err
	}

	cSimpleInfo, err := m.channelCli.GetChannelSimpleInfo(ctx, weddingInfo.GetGroom().GetUid(), channelId)
	if err != nil {
		log.ErrorWithCtx(ctx, "PushBreakingNews.GetChannelSimpleInfo, channelId: %d, weddingInfo:%v:, err: %v", channelId, weddingInfo, err)
		return err
	}

	fromUser := userMap[weddingInfo.GetGroom().GetUid()]
	targetUser := userMap[weddingInfo.GetBride().GetUid()]
	breakingNewsMessage := &publicNoticePb.CommonBreakingNewsV3{
		FromUid: fromUser.GetUid(),
		FromUserInfo: &publicNoticePb.UserInfo{
			Account: fromUser.GetAccount(),
			Nick:    fromUser.GetNickname(),
		},
		TargetUid: targetUser.GetUid(),
		TargetUserInfo: &publicNoticePb.UserInfo{
			Account: targetUser.GetAccount(),
			Nick:    targetUser.GetNickname(),
		},
		ChannelId: channelId,
		ChannelInfo: &publicNoticePb.ChannelInfo{
			ChannelDisplayid: cSimpleInfo.GetDisplayId(),
			ChannelBindid:    cSimpleInfo.GetBindId(),
			ChannelType:      cSimpleInfo.GetChannelType(),
			ChannelName:      cSimpleInfo.GetName(),
		},
		BreakingNewsBaseOpt: &publicNoticePb.CommBreakingNewsBaseOpt{
			TriggerType: uint32(pushPb.CommBreakingNewsBaseOpt_COMMON_RICH_TEXT_NEWS),
		},
	}
	req := &publicNoticePb.PushBreakingNewsReq{
		BreakingCmdType:    publicNoticePb.PushBreakingNewsReq_COMMON_BREAKING_EVENT_V3,
		CommonBreakingNews: breakingNewsMessage,
	}
	req.RichTextNews = &publicNoticePb.RichTextNews{
		NewsId: newsId,
	}

	_, pushErr := m.publicNoticeCli.PushBreakingNews(ctx, req)
	if pushErr != nil {
		log.ErrorWithCtx(ctx, "pushBreakingNews fail to PushMulticast. uid:%d, err:%v", channelId, pushErr)
		return pushErr
	}
	log.InfoWithCtx(ctx, "pushBreakingNews success, uid:%d, BreakingNewsMessage:%v", channelId, req)
	return nil
}

func (m *ACLayer) SendPresentToUser(ctx context.Context, uid, channelId, presentId uint32, weddingId int64) error {
	if uid == 0 || presentId == 0 {
		log.ErrorWithCtx(ctx, "SendPresentToUser. uid:%d, presentId:%d", uid, presentId)
		return nil
	}

	awardBusinessId, awardSecretKey := m.bc.GetAwardBusinessInfo()

	orderId := fmt.Sprintf("%d_%d_%d_%d_%d", awardBusinessId, uid, channelId, weddingId, presentId)
	cipherText := butils.AESEncrypt([]byte(orderId), []byte(awardSecretKey))
	_, err := m.backpackSenderCli.SendBackpackWithRiskControl(ctx, &backpackSenderPB.SendBackpackWithRiskControlReq{
		BusinessId:  awardBusinessId,
		BackpackId:  presentId,
		ReceiveUid:  uid,
		BackpackCnt: 1,
		ServerTime:  time.Now().Unix(),
		OrderId:     orderId,
		SourceAppId: "婚礼房高光时刻奖励",
		Ciphertext:  cipherText,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "SendPresentToUser. uid:%d, presentId:%d, err: %v", uid, presentId, err)
		return err
	}

	log.InfoWithCtx(ctx, "SendPresentToUser success. uid:%d, presentId:%d", uid, presentId)
	return err
}

func (m *ACLayer) GetMyWeddingInfo(ctx context.Context, uid uint32) (*channel_wedding_plan.GetMyWeddingInfoResponse, error) {
	return m.weddingPlanCli.GetMyWeddingInfo(ctx, &channel_wedding_plan.GetMyWeddingInfoRequest{Uid: uid})
}

// SendCommonShelfPresent 发送普通礼物架礼物
func (m *ACLayer) SendCommonShelfPresent(ctx context.Context, in *SendPresentReq) error {
	dk, err := GenDealToken(in.FromUid, in.Price, in.DealToken, in.OrderId, in.OrderId)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendCommonShelfPresent fail to GenDealToken. in:%+v, dealToken:%+v, err:%v", in, dk, err)
		return err
	}

	req := &present_middlewarePb.SendPresentReq{
		SendUid:   in.FromUid,
		BatchType: uint32(present_middlewarePb.PresentBatchSendType_PRESENT_SOURCE_WITH_UID),
		TargetInfo: &present_middlewarePb.PresentTargetInfo{
			Target: &present_middlewarePb.PresentTargetInfo_SingleTarget{
				SingleTarget: &present_middlewarePb.SingleTargetUser{
					Uid:       in.ToUid,
					ItemId:    in.GiftId,
					Count:     in.Amount,
					OrderId:   in.OrderId,
					DealToken: dk,
				}}},
		ChannelId:     in.FromCid, // 收礼方房间
		SendChannelId: in.FromCid, // 送礼方房间
		SendSource:    uint32(present_middlewarePb.PresentSourceType_PRESENT_SOURCE_WEDDING_RESERVE),
		ItemSource:    uint32(present_middlewarePb.PresentSourceType_PRESENT_SOURCE_WEDDING_RESERVE),
		SendMethod:    uint32(present_middlewarePb.PresentSendMethodType_PRESENT_TYPE_ROOM),
		IsOptValid:    true,
		WithPay:       false, // 无需再支付了
		WithPush:      true,
		PushInfo: &present_middlewarePb.PushInfo{
			ChannelPushType:  uint32(present_middlewarePb.PushInfo_Channel_NORMAL),
			PersonalPushType: uint32(present_middlewarePb.PushInfo_Person_NORMAL_SENDER),
			ImMsgType:        uint32(present_middlewarePb.PushInfo_IM_NONE),
		},
		SendTime: in.AwardTs,
	}

	_, err = m.presentMiddle.SendPresent(ctx, req)
	if err != nil {
		log.ErrorWithCtx(ctx, "SendCommonShelfPresent fail to SendPresent. in:%+v, err:%v", in, err)
		return err
	}

	log.InfoWithCtx(ctx, "SendCommonShelfPresent in:%+v", in)
	return nil
}

func GenDealToken(uid, totalPrice uint32, preDealToken, baseOrderId, orderId string) (string, error) {
	newDt := deal_token.NewDealTokenData(baseOrderId, orderId, "channel-wedding", int64(uid), int64(totalPrice))
	outDealToken, err := deal_token.AddDealToken(preDealToken, newDt)
	if err != nil {
		log.Errorf("GenDealToken fail to AddDealToken. uid:%d, newDt:%+v, err:%v", uid, newDt, err)
		return outDealToken, err
	}

	return outDealToken, nil
}

// SendChannelMsg 发送公屏消息
func (m *ACLayer) SendChannelMsg(ctx context.Context, uid, channelId uint32, content string) error {
	msg := &channelim.ChannelCommonMsg{
		Type:        uint32(channelPB.ChannelMsgType_CHANNEL_TEXT_SYS_MSG),
		ToChannelId: channelId,
		Content:     content,
	}
	seqId, _, err := m.channelImCli.SendCommonMessage(ctx, uid, channelId, msg, true, uint32(channelPB.ChannelMsgType_CHANNEL_TEXT_SYS_MSG))
	if err != nil {
		log.ErrorWithCtx(ctx, "SendChannelMsg SendCommonMessage failed uid %d channelId %d err %v", uid, channelId, err)
		return err
	}
	log.InfoWithCtx(ctx, "SendChannelMsg SendCommonMessage success uid %d channelId %d seqId %d", uid, channelId, seqId)
	return nil
}

func (m *ACLayer) CheckUsersInChannel(ctx context.Context, uidList []uint32, channelId uint32) (bool, error) {
	onlineResp, err := m.channelOlCli.BatchGetUserChannelId(ctx, &channelol_go.BatchGetUserChannelIdReq{
		UidList: uidList,
	})
	if err != nil {
		log.ErrorWithCtx(ctx, "CheckUserOnline fail to BatchGetLatestOnlineInfo. uidList: %d, err: %v", uidList, err)
		return false, err
	}

	log.DebugWithCtx(ctx, "CheckUserOnline success. uidList: %d, onlineResp: %+v", uidList, onlineResp.Results)
	for uid, userChannel := range onlineResp.Results {
		if userChannel != channelId {
			log.ErrorWithCtx(ctx, "CheckUserOnline user %d is not in channel %d", uid, channelId)
			return false, nil
		}
	}

	return true, nil
}
