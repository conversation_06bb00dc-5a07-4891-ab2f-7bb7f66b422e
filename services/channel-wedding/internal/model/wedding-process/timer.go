package wedding_process

import (
    "context"
    "fmt"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer"
    "gitlab.ttyuyin.com/avengers/tyr/pkg/cluster/timer/task/tasks"
    "gitlab.ttyuyin.com/tt-infra/tyr/log"
    protocolgrpc "golang.52tt.com/pkg/protocol/grpc"
    pb "golang.52tt.com/protocol/services/channel-wedding"
    "golang.52tt.com/services/channel-wedding/internal/model/comm"
    "golang.52tt.com/services/channel-wedding/internal/model/wedding-process/store"
    "time"
)

func (m *WeddingProcess) startTimer() error {
    var err error
    m.timerD, err = timer.NewTimerD(context.Background(),
        "channel-wedding-process",
        timer.WithV8RedisCmdable(m.cache.GetRedisClient()))
    if err != nil {
        log.Errorf("startTimer NewTimerD err:%v", err)
        return err
    }

    m.timerD.AddLocalIntervalTask(time.Second*5,
        tasks.NewTracingTask(tasks.FuncTask(m.CheckWeddingStart), "CheckWeddingStart", 0))

    m.timerD.AddLocalIntervalTask(time.Millisecond*200,
        tasks.NewTracingTask(tasks.FuncTask(m.CheckFreeWeddingStageSwitch), "CheckFreeWeddingStageSwitch", 0))

    m.timerD.AddLocalIntervalTask(time.Millisecond*500,
        tasks.NewTracingTask(tasks.FuncTask(func(ctx context.Context) {
            m.CheckSendSuit(ctx, 1)
        }), "CheckSendSuit-1", 0))

    m.timerD.AddLocalIntervalTask(time.Millisecond*500,
        tasks.NewTracingTask(tasks.FuncTask(func(ctx context.Context) {
            m.CheckSendSuit(ctx, 2)
        }), "CheckSendSuit-2", 0))

    // test 过渡
    m.timerD.AddLocalIntervalTask(time.Millisecond*500,
        tasks.NewTracingTask(tasks.FuncTask(func(ctx context.Context) {
            m.CheckSendSuit(ctx, 0)
        }), "CheckSendSuit-0", 0))

    m.timerD.Start()
    return nil
}

// CheckWeddingStart 检查婚礼是否开始
func (m *WeddingProcess) CheckWeddingStart(ctx context.Context) {
    now := time.Now()
    list, err := m.store.GetNotStartWeddingScheduleList(ctx, now)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckWeddingStart GetNotStartWeddingScheduleList err:%v", err)
        return
    }

    // 每个房间只取第一个
    cid2Wedding := make(map[uint32]*store.WeddingSchedule)
    for _, v := range list {
        if _, ok := cid2Wedding[v.Cid]; ok {
            continue
        }
        cid2Wedding[v.Cid] = v
    }

    for _, v := range cid2Wedding {
        err = m.weddingStartHandle(ctx, v)
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckWeddingStart weddingStartHandle %+v, err:%v", v, err)
            continue
        }
    }
}

func (m *WeddingProcess) weddingStartHandle(c context.Context, v *store.WeddingSchedule) error {
    ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*5)
    defer cancel()

    key := fmt.Sprintf("weddingStartHandle:%d", v.Cid)
    ok, err := m.cache.Lock(ctx, key, time.Second*5)
    if err != nil {
        log.ErrorWithCtx(ctx, "weddingStartHandle Lock err:%v", err)
        return err
    }
    if !ok {
        return nil
    }
    defer func() {
        _ = m.cache.Unlock(ctx, key)
    }()

    err = m.TryBeginWedding(ctx, 0, v)
    if err != nil {
        log.ErrorWithCtx(ctx, "weddingStartHandle TryBeginWedding %+v, err:%v", v, err)
        return err
    }

    log.DebugWithCtx(ctx, "weddingStartHandle cid:%d", v.Cid)
    return nil
}

// CheckFreeWeddingStageSwitch 检查免费婚礼阶段切换
func (m *WeddingProcess) CheckFreeWeddingStageSwitch(ctx context.Context) {
    now := time.Now()
    expireTs := now.Unix() - int64(m.bc.GetFreeWeddingStageSec())

    cidList, err := m.cache.GetStageExpireCid(ctx, expireTs, 50)
    if err != nil {
        log.ErrorWithCtx(ctx, "CheckFreeWeddingStageSwitch GetStageExpireCid err:%v", err)
        return
    }

    if len(cidList) == 0 {
        return
    }

    for _, cid := range cidList {
        err := m.freeWeddingStageSwitch(ctx, cid)
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckFreeWeddingStageSwitch freeWeddingStageSwitch. cid:%d, err:%v", cid, err)
        }
    }
}

func (m *WeddingProcess) freeWeddingStageSwitch(c context.Context, cid uint32) error {
    ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*5)
    defer cancel()

    key := fmt.Sprintf("freeWeddingStageSwitch:%d", cid)
    ok, err := m.cache.Lock(ctx, key, time.Second*5)
    if err != nil {
        log.ErrorWithCtx(ctx, "freeWeddingStageSwitch Lock err:%v", err)
        return err
    }
    if !ok {
        return nil
    }
    defer func() {
        _ = m.cache.Unlock(ctx, key)
    }()

    info, err := m.GetChannelWeddingInfo(ctx, 0, cid)
    if err != nil {
        log.ErrorWithCtx(ctx, "freeWeddingStageSwitch GetChannelWeddingInfo err:%v", err)
        return err
    }

    if info.GetStageInfo().GetStageEndTs() > time.Now().Unix() {
        return nil
    }

    currStage := info.GetStageInfo().GetCurrStage()
    nextStage := getNextStage(currStage, info.GetStageInfo().GetStageCfgList())

    err = m.SwitchWeddingStage(ctx, 0, cid, nextStage, 0)
    if err != nil {
        log.ErrorWithCtx(ctx, "freeWeddingStageSwitch SwitchWeddingStage err:%v", err)
    }

    log.DebugWithCtx(ctx, "freeWeddingStageSwitch cid:%d, currStage:%d, nextStage:%d", cid, currStage, nextStage)
    return nil
}

func getNextStage(curr uint32, stageList []*pb.WeddingStageCfg) uint32 {
    for i, v := range stageList {
        if v.GetStage() == curr {
            if i+1 < len(stageList) {
                return stageList[i+1].GetStage()
            }
        }
    }
    return 0
}

// CheckSendSuit 检查发放套装
func (m *WeddingProcess) CheckSendSuit(ctx context.Context, themeTy uint32) {
    now := time.Now()
    begin := now.Add(-time.Hour * 6)

    list, err := m.store.GetAwardLogList(ctx, now, store.AwardNotDone, themeTy, begin, now)
    if err != nil {
        log.Errorf("CheckSendSuit GetAwardLogList err:%v", err)
        return
    }

    if begin.Month() != now.Month() {
        lastMonthList, err := m.store.GetAwardLogList(ctx, begin, store.AwardNotDone, themeTy, begin, now)
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckSendSuit GetAwardLogList err:%v", err)
        }
        list = append(list, lastMonthList...)
    }

    for _, v := range list {
        err = m.sendSuitHandle(ctx, v)
        if err != nil {
            log.ErrorWithCtx(ctx, "CheckSendSuit sendSuitHandle err:%v", err)
            continue
        }
    }
}

func (m *WeddingProcess) sendSuitHandle(c context.Context, v *store.AwardLog) error {
    ctx, cancel := protocolgrpc.NewContextWithInfoTimeout(c, time.Second*5)
    defer cancel()

    key := fmt.Sprintf("sendSuitHandle:%s", v.OrderId)
    ok, err := m.cache.Lock(ctx, key, time.Second*5)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendSuitHandle Lock err:%v", err)
        return err
    }
    if !ok {
        return nil
    }
    defer func() {
        _ = m.cache.Unlock(ctx, key)
    }()

    err = m.acLayerMgr.SendVirtualImageSuitWithUse(ctx, v.Uid, &comm.UpgradeSuitInfo{
        Uid:         v.Uid,
        Name:        v.SuitName,
        ClothesList: v.GetSuitItemList(),
        Icon:        v.SuitIcon,
        Duration:    v.AwardDurationSec,
        OrderId:     v.OrderId,
    }, nil)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendSuitHandle SendVirtualImageSuitWithUse err:%v", err)
        return err
    }

    err = m.store.UpdateAwardLogStatus(ctx, v.OrderId, store.AwardDone, v.WeddingCTime)
    if err != nil {
        log.ErrorWithCtx(ctx, "sendSuitHandle UpdateAwardLogStatus err:%v", err)
        return err
    }

    log.DebugWithCtx(ctx, "sendSuitHandle orderId:%s", v.OrderId)
    return nil
}
