package conf

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"sync"
	"time"

	"golang.52tt.com/pkg/files"
	"golang.52tt.com/pkg/log"
)

const (
	configFile                   = "/data/oss/conf-center/tt/channel_online_rank_vip_info.json"
	defaultSettlementHour        = 3
	defaultReloadDelay           = 3
	defaultViewCnt               = 3
	defaultMaxActiveScore        = 5
	defaultAbandonSec      int64 = 5
	defaultStopMigrateTime       = 1685548800

	defaultNewChannelEventPoolSize = 100
)

var pRWMutex = sync.RWMutex{}
var gOnlineRankConf = &ChannelRankDyConfig{}

type ChannelRankDyConfig struct {
	VipInfoList         []*MemberVipInfo `json:"vip_info_list"`
	MaxActiveScore      float64          `json:"max_active_score"`
	EnterAbandonSec     int64            `json:"enter_abandon_sec"`
	LeaveAbandonSec     int64            `json:"leave_abandon_sec"`
	RefreshTop          bool             `json:"refresh_top"`
	ViewCnt             int64            `json:"view_cnt"`
	ReloadDelay         int64            `json:"reload_delay"`
	YKWSettlementHour   int              `json:"ykw_settlement_hour"`
	StopMigrateTime     int64            `json:"stop_migrate_time"`
	FixLastHour         int              `json:"fix_last_hour"`
	CheckOnlineInterval int64            `json:"check_online_interval"`
	FixNotExistMember   bool             `json:"fix_not_exist_member"`

	//v2
	V2Config struct {
		WarnConf struct {
			FeiShuUrl string
			Push      bool
		}

		AsyncJobSwitch   bool
		AsyncJobPoolSize uint32

		CoroutinePool struct {
			PoolSize uint32
			Cap      uint32
		}
		CheckChannelOlMemMinSize uint32

		ChannelEventEnterTimeOutSec uint32
		ChannelEventLeftTimeOutSec  uint32

		PresentEventDelaySec uint32

		BlackUids []uint32

		OnlineRankSwitch struct {
			UseNew          bool
			Rate            uint32
			WhiteUids       []uint32
			WhiteChannelIds []uint32
		}
		WeekRankSwitch struct {
			UseNew          bool
			Rate            uint32
			WhiteUids       []uint32
			WhiteChannelIds []uint32
		}

		CheckChannelOlPreTimeSec     uint32
		CheckChannelOlCount          uint32
		CheckChannelOlMaxMemberCount uint32

		EnableRefreshTop3 bool

		DisableSubChannelEventOld bool
		DisableSubPresentEventOld bool

		// 2024.11 新的进出房消费协程池大小
		NewChannelEventPoolSize uint32
		// kafka worker
		NewChannelEventWorkerSize uint32

		DisableLoadTop3Special bool
	}
}

type MemberVipInfo struct {
	Id     uint32 `json:"id"`
	MinVal uint32 `json:"min_val"`
	Name   string `json:"name"`
}

func (m *MemberVipInfo) GetId() uint32 {
	if m == nil {
		return 0
	}
	return m.Id
}
func (m *MemberVipInfo) GetName() string {
	if m == nil {
		return ""
	}
	return m.Name
}
func (m *MemberVipInfo) GetMinVal() uint32 {
	if nil == m {
		return 0
	}
	return m.MinVal
}

func init() {
	err := load()
	if err != nil {
		//log.Panicf("dyconf init fail. %+v", err)
	}

	watch := files.NewFileModifyWatch(configFile, time.Second)
	go watch.Start(func() {
		load()
	})
}

func load() error {
	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		log.Errorf("load fail %v", err)
		return err
	}
	ctx := context.Background()

	tmpConf := &ChannelRankDyConfig{}
	err = json.Unmarshal(data, &tmpConf)
	if err != nil {
		log.ErrorWithCtx(ctx, "load conf err:%v", err)
		return err
	}
	pRWMutex.Lock()
	gOnlineRankConf = tmpConf
	pRWMutex.Unlock()

	log.InfoWithCtx(ctx, "dyconf change=%+v", gOnlineRankConf)
	log.InfoWithCtx(ctx, "V2Config change=%+v", gOnlineRankConf.V2Config)
	return nil
}

func GetVipInfo(val uint32) (*MemberVipInfo, *MemberVipInfo) {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf == nil {
		return nil, nil
	}

	var sz = len(gOnlineRankConf.VipInfoList)

	if sz == 0 {
		return nil, nil
	}

	currIdx := 0
	for i := 0; i < sz; i++ {
		if val >= gOnlineRankConf.VipInfoList[i].MinVal {
			currIdx = i
		} else {
			break
		}
	}

	if currIdx < sz-1 {
		return gOnlineRankConf.VipInfoList[currIdx], gOnlineRankConf.VipInfoList[currIdx+1]
	}

	return gOnlineRankConf.VipInfoList[currIdx], nil
}

func GetAbandonSec(isEnter bool) int64 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	enterSec, leaveSec := defaultAbandonSec, defaultAbandonSec
	if gOnlineRankConf != nil {
		if gOnlineRankConf.EnterAbandonSec > 0 {
			enterSec = gOnlineRankConf.EnterAbandonSec
		}
		if gOnlineRankConf.LeaveAbandonSec > 0 {
			leaveSec = gOnlineRankConf.LeaveAbandonSec
		}
	}

	if isEnter {
		return enterSec
	}
	return leaveSec
}

func GetMaxActiveScore() float64 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf == nil || gOnlineRankConf.MaxActiveScore == 0 {
		return defaultMaxActiveScore
	}
	return gOnlineRankConf.MaxActiveScore
}

func FixNotExistSwitch() bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf == nil {
		return false
	}
	return gOnlineRankConf.FixNotExistMember
}

func RefreshTopSwitch() bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf == nil {
		return false
	}
	return gOnlineRankConf.RefreshTop
}

func GetViewCnt() int64 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf == nil || gOnlineRankConf.ViewCnt == 0 {
		return defaultViewCnt
	}
	return gOnlineRankConf.ViewCnt
}

func GetReloadDelay() int64 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf == nil || gOnlineRankConf.ReloadDelay == 0 {
		return defaultReloadDelay
	}
	return gOnlineRankConf.ReloadDelay
}

// YKWSettlementHour
func GetYKWSettlementHour() int {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf == nil || gOnlineRankConf.YKWSettlementHour == 0 {
		return defaultSettlementHour
	}
	return gOnlineRankConf.YKWSettlementHour
}

func GetStopMigrateTime() time.Time {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()

	if gOnlineRankConf == nil || gOnlineRankConf.StopMigrateTime == 0 {
		return time.Unix(defaultStopMigrateTime, 0)
	}
	return time.Unix(gOnlineRankConf.StopMigrateTime, 0)
}

func GetFixLastHour() int {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	hours := 24
	if gOnlineRankConf != nil && gOnlineRankConf.FixLastHour > 0 {
		hours = gOnlineRankConf.FixLastHour
	}
	return hours
}

func GetCheckOnlineInterval() int64 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	var interval int64 = 3600 * 6
	if gOnlineRankConf != nil && gOnlineRankConf.CheckOnlineInterval > 0 {
		interval = gOnlineRankConf.CheckOnlineInterval
	}
	return interval
}

func GetEnableRefreshTop3() bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.EnableRefreshTop3
}

func GetDisableSubChannelEventOld() bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.DisableSubChannelEventOld
}

func GetDisableSubPresentEventOld() bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.DisableSubPresentEventOld
}

func GetCheckChannelOlPreTimeSec() uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.CheckChannelOlPreTimeSec
}

func GetCheckChannelOlCount() uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.CheckChannelOlCount
}

func GetCheckChannelOlMaxMemberCount() uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.CheckChannelOlMaxMemberCount
}

func GetOnlineRankSwitch(cid, uid uint32) bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if !gOnlineRankConf.V2Config.OnlineRankSwitch.UseNew {
		return false
	}
	for _, whiteUid := range gOnlineRankConf.V2Config.OnlineRankSwitch.WhiteUids {
		if uid == whiteUid {
			return true
		}
	}
	for _, whiteCid := range gOnlineRankConf.V2Config.OnlineRankSwitch.WhiteChannelIds {
		if cid == whiteCid {
			return true
		}
	}
	return uid%100 < gOnlineRankConf.V2Config.OnlineRankSwitch.Rate
}

func GetWeekRankSwitch(cid, uid uint32) bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if !gOnlineRankConf.V2Config.WeekRankSwitch.UseNew {
		return false
	}
	for _, whiteUid := range gOnlineRankConf.V2Config.WeekRankSwitch.WhiteUids {
		if uid == whiteUid {
			return true
		}
	}
	for _, whiteCid := range gOnlineRankConf.V2Config.WeekRankSwitch.WhiteChannelIds {
		if cid == whiteCid {
			return true
		}
	}
	return uid%100 < gOnlineRankConf.V2Config.WeekRankSwitch.Rate
}

func GetWarnConf() (string, bool) {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.WarnConf.FeiShuUrl, gOnlineRankConf.V2Config.WarnConf.Push
}

func GetAsyncJobConf() (bool, uint32) {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.AsyncJobSwitch, gOnlineRankConf.V2Config.AsyncJobPoolSize
}

func GetPresentEventDelaySec() uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.PresentEventDelaySec
}

func GetChannelEventTimeOut(isEnter bool) uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if isEnter {
		return gOnlineRankConf.V2Config.ChannelEventEnterTimeOutSec
	}
	return gOnlineRankConf.V2Config.ChannelEventLeftTimeOutSec
}

func CheckIsBlockUser(uid uint32) bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	for _, blockUid := range gOnlineRankConf.V2Config.BlackUids {
		if uid == blockUid {
			return true
		}
	}
	return false
}

func GetCoroutinePoolCfg() (poolSize, cap uint32) {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf.V2Config.CoroutinePool.PoolSize == 0 || gOnlineRankConf.V2Config.CoroutinePool.Cap == 0 {
		return 10, 10
	}
	return gOnlineRankConf.V2Config.CoroutinePool.PoolSize, gOnlineRankConf.V2Config.CoroutinePool.Cap
}

func GetCheckChannelOlMemMinSize() uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf.V2Config.CheckChannelOlMemMinSize == 0 {
		return 10
	}
	return gOnlineRankConf.V2Config.CheckChannelOlMemMinSize
}

func GetNewChannelEventPoolSize() uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf.V2Config.NewChannelEventPoolSize == 0 {
		return defaultNewChannelEventPoolSize
	}
	return gOnlineRankConf.V2Config.NewChannelEventPoolSize
}

func GetNewChannelEventWorkSize() uint32 {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	if gOnlineRankConf.V2Config.NewChannelEventWorkerSize == 0 {
		return 1
	}
	return gOnlineRankConf.V2Config.NewChannelEventWorkerSize
}

func IsDisableLoadTop3Special() bool {
	pRWMutex.RLock()
	defer pRWMutex.RUnlock()
	return gOnlineRankConf.V2Config.DisableLoadTop3Special
}
