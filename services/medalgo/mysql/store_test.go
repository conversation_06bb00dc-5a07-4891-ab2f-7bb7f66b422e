package mysql

import (
	"context"
	"fmt"
	"regexp"

	"reflect"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	sqlxmock "github.com/zhashkevych/go-sqlxmock"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	pb "golang.52tt.com/protocol/services/medalgo"
)

var dbRealConnect *sqlx.DB
var isUseRealConnect bool = false
var cfg *config.MysqlConfig

func init() {
	log.SetLevel(log.DebugLevel)

	cfg = &config.MysqlConfig{
		Host:     "**************",
		Port:     3306,
		UserName: "godman",
		Password: "thegodofman",
		Database: "appsvr",
		Charset:  "utf8",
	}

	dbRealConnect, _ = sqlx.Connect("mysql", cfg.ConnectionString())
}

func GetMockMysql() (mock sqlxmock.Sqlmock, db *sqlx.DB) {
	var err error
	db, mock, err = sqlxmock.Newx()
	if err != nil {
		panic(err)
	}

	if isUseRealConnect {
		db = dbRealConnect
	}
	return
}

func TestStore_InsertMedalConfig(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	medalConfig := &pb.PMedal{
		Name:                "Name",
		Desc:                []string{"test1", "test2", "test3"},
		Icon:                "Icon",
		BorderIcon:          "BorderIcon",
		Url:                 "Url",
		ExpireTime:          10,
		BuffExpRate:         20,
		BuffCurrencyRate:    30,
		Title:               "Title",
		Content:             "Content",
		TitleIcon:           "TitleIcon",
		IsAllowSetTaillight: true,
		ZipUrl:              "52tt.com",
	}

	sql := `INSERT INTO medal_config(name, title, content, url, icon, border_icon, title_icon, description_1, description_2, description_3, 
			experience, red_diamonds, allow_taillight, expire_time, zip_url) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)`

	mock.ExpectExec(regexp.QuoteMeta(sql)).WithArgs(medalConfig.GetName(), medalConfig.GetTitle(), medalConfig.GetContent(), medalConfig.GetUrl(),
		medalConfig.GetIcon(), medalConfig.GetBorderIcon(), medalConfig.GetTitleIcon(), medalConfig.Desc[0], medalConfig.Desc[1], medalConfig.Desc[2],
		medalConfig.GetBuffExpRate(), medalConfig.GetBuffCurrencyRate(), medalConfig.GetIsAllowSetTaillight(), medalConfig.GetExpireTime(), medalConfig.GetZipUrl(),
	).WillReturnResult(sqlxmock.NewResult(1, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx         context.Context
		medalConfig *pb.PMedal
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "InsertMedalConfig",
			fields: fields{db: db},
			args: args{
				ctx:         context.TODO(),
				medalConfig: medalConfig,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.InsertMedalConfig(tt.args.ctx, tt.args.medalConfig); (err != nil) != tt.wantErr {
				t.Errorf("Store.InsertMedalConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_QueryMedalConfig(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	sql := "SELECT medal_id,name,title,content,url,icon,border_icon,title_icon,description_1,description_2,description_3,experience,red_diamonds,allow_taillight,expire_time,zip_url FROM medal_config where invalid = 0;"

	medalConfig := &pb.PMedal{
		MedalId:             1,
		Name:                "Name",
		Desc:                []string{"test1", "test2", "d3"},
		Icon:                "Icon",
		BorderIcon:          "BorderIcon",
		Url:                 "Url",
		Title:               "Title",
		Content:             "Content",
		TitleIcon:           "TitleIcon",
		IsAllowSetTaillight: false,
		ZipUrl:              "52tt.com",
	}

	rows := sqlxmock.NewRows([]string{"medal_id", "name", "title", "content", "url", "icon", "border_icon", "title_icon", "description_1", "description_2", "description_3",
		"experience", "red_diamonds", "allow_taillight", "expire_time", "zip_url",
	}).AddRow(1, medalConfig.Name, medalConfig.Title, medalConfig.Content, medalConfig.Url, medalConfig.Icon, medalConfig.BorderIcon, medalConfig.TitleIcon,
		medalConfig.Desc[0], medalConfig.Desc[1], medalConfig.Desc[2], medalConfig.BuffExpRate, 0, 0, 0, medalConfig.ZipUrl)
	mock.ExpectQuery(regexp.QuoteMeta(sql)).WithArgs().WillReturnRows(rows)

	type fields struct {
		db *sqlx.DB
	}
	tests := []struct {
		name    string
		fields  fields
		want    []*pb.PMedal
		want1   map[uint32]bool
		wantErr bool
	}{
		{
			name:    "QueryMedalConfig",
			fields:  fields{db: db},
			want:    []*pb.PMedal{medalConfig},
			want1:   map[uint32]bool{1: false},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			got, got1, err := s.QueryMedalConfig()
			if (err != nil) != tt.wantErr {
				t.Errorf("Store.QueryMedalConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Logf("Store.QueryMedalConfig() got %+v, got1 %+v", got, got1)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Store.QueryMedalConfig() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("Store.QueryMedalConfig() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestStore_UpdateMedalConfig(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	medalConfig := &pb.PMedal{
		MedalId:             1,
		Name:                "Name1",
		Desc:                []string{"test1", "test2", "test3"},
		Icon:                "Icon",
		BorderIcon:          "BorderIcon",
		Url:                 "Url",
		ExpireTime:          10,
		BuffExpRate:         20,
		BuffCurrencyRate:    40,
		Title:               "Title",
		Content:             "Content",
		TitleIcon:           "TitleIcon",
		IsAllowSetTaillight: true,
		ZipUrl:              "52tt.com2",
	}

	sql := `Update medal_config set name=?, title=?, content=?, url=?, icon=?, border_icon=?,title_icon=?, description_1=?, description_2=?, description_3=?,experience=?, 
	red_diamonds=?, allow_taillight=?, expire_time=?, zip_url=? where medal_id =?;`

	mock.ExpectExec(regexp.QuoteMeta(sql)).WithArgs(medalConfig.GetName(), medalConfig.GetTitle(), medalConfig.GetContent(), medalConfig.GetUrl(),
		medalConfig.GetIcon(), medalConfig.GetBorderIcon(), medalConfig.GetTitleIcon(), medalConfig.GetDesc()[0], medalConfig.GetDesc()[1], medalConfig.GetDesc()[2],
		medalConfig.GetBuffExpRate(), medalConfig.GetBuffCurrencyRate(), medalConfig.GetIsAllowSetTaillight(), medalConfig.GetExpireTime(),
		medalConfig.GetZipUrl(), medalConfig.GetMedalId()).WillReturnResult(sqlxmock.NewResult(1, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx         context.Context
		medalConfig *pb.PMedal
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "UpdateMedalConfig",
			fields:  fields{db: db},
			args:    args{ctx: context.TODO(), medalConfig: medalConfig},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.UpdateMedalConfig(tt.args.ctx, tt.args.medalConfig); (err != nil) != tt.wantErr {
				t.Errorf("Store.UpdateMedalConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_GetUpdateTime(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	sql := "SELECT unix_timestamp(op_time) FROM medal_config order by op_time desc limit 1;"
	rows := sqlxmock.NewRows([]string{"op_time"}).AddRow(1)
	mock.ExpectQuery(regexp.QuoteMeta(sql)).WithArgs().WillReturnRows(rows)

	type fields struct {
		db *sqlx.DB
	}
	tests := []struct {
		name           string
		fields         fields
		wantUpdateTime int64
		wantErr        bool
	}{
		{
			name:           "GetUpdateTime",
			fields:         fields{db: db},
			wantUpdateTime: 1,
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			gotUpdateTime, err := s.GetUpdateTime()
			t.Logf("Store.GetUpdateTime() gotUpdateTime %v", gotUpdateTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("Store.GetUpdateTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotUpdateTime != tt.wantUpdateTime {
				t.Errorf("Store.GetUpdateTime() = %v, want %v", gotUpdateTime, tt.wantUpdateTime)
			}
		})
	}
}

func TestStore_InvalidateMedal(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	var medalId uint32 = 1
	sql := `Update medal_config set invalid=1 where medal_id=?`
	mock.ExpectExec(regexp.QuoteMeta(sql)).WithArgs(medalId).WillReturnResult(sqlxmock.NewResult(0, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx     context.Context
		medalId uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "InvalidateMedal",
			fields:  fields{db: db},
			args:    args{ctx: context.TODO(), medalId: medalId},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.InvalidateMedal(tt.args.ctx, tt.args.medalId); (err != nil) != tt.wantErr {
				t.Errorf("Store.InvalidateMedal() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_AwardMedalToUser(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	var (
		uid            uint32 = 2404178
		medalId        uint32 = 1
		effectDuration int64  = 86400
		policy         uint32 = ReWrite
	)

	var expireTimeUpdate = "VALUES(expire_time)"
	sql := `INSERT INTO user_medal(uid, medal_id, award_time, expire_time) VALUES(?, ?, FROM_UNIXTIME(?), FROM_UNiXTIME(?)) ON DUPLICATE KEY UPDATE expire_time=%s`
	var query = fmt.Sprintf(sql, expireTimeUpdate)

	mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs().WillReturnResult(sqlxmock.NewResult(1, 1))

	sql = `INSERT INTO user_medal_%02d(uid, medal_id, award_time, expire_time) VALUES(?, ?, FROM_UNIXTIME(?), FROM_UNiXTIME(?)) ON DUPLICATE KEY UPDATE expire_time=%s`
	query = fmt.Sprintf(sql, uid%100, expireTimeUpdate)
	mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs().WillReturnResult(sqlxmock.NewResult(1, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx            context.Context
		uid            uint32
		medalId        uint32
		effectDuration int64
		policy         uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "AwardMedalToUser",
			fields:  fields{db: db},
			args:    args{ctx: context.TODO(), uid: uid, medalId: medalId, effectDuration: effectDuration, policy: policy},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.AwardMedalToUser(tt.args.ctx, tt.args.uid, tt.args.medalId, tt.args.effectDuration, tt.args.policy); (err != nil) != tt.wantErr {
				t.Errorf("Store.AwardMedalToUser() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_ReduceUserMedal(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	var (
		uid            uint32 = 2404178
		medalId        uint32 = 1
		effectDuration int64  = 86400
	)

	expireTimeUpdate := fmt.Sprintf("IF(expire_time>award_time, FROM_UNIXTIME(UNIX_TIMESTAMP(expire_time)-%d), VALUES(expire_time))", effectDuration)
	var query = fmt.Sprintf("UPDATE user_medal SET expire_time=%s where uid = %d and medal_id = %d", expireTimeUpdate, uid, medalId)
	mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs().WillReturnResult(sqlxmock.NewResult(1, 1))

	query = fmt.Sprintf("UPDATE user_medal_%02d SET expire_time=%s where uid = %d and medal_id = %d", uid%100, expireTimeUpdate, uid, medalId)
	mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs().WillReturnResult(sqlxmock.NewResult(1, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx            context.Context
		uid            uint32
		medalId        uint32
		effectDuration int64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "ReduceUserMedal",
			fields:  fields{db: db},
			args:    args{ctx: context.TODO(), uid: uid, medalId: medalId, effectDuration: effectDuration},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.ReduceUserMedal(tt.args.ctx, tt.args.uid, tt.args.medalId, tt.args.effectDuration); (err != nil) != tt.wantErr {
				t.Errorf("Store.ReduceUserMedal() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_ClearUserTaillightMedals(t *testing.T) {

	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	var (
		uid uint32 = 2404178
	)

	var query = "UPDATE user_medal SET is_taillight = 0 WHERE uid = ?"
	mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs(uid).WillReturnResult(sqlxmock.NewResult(1, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx context.Context
		uid uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "ClearUserTaillightMedals",
			fields:  fields{db: db},
			args:    args{ctx: context.TODO(), uid: uid},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.ClearUserTaillightMedals(tt.args.ctx, tt.args.uid); (err != nil) != tt.wantErr {
				t.Errorf("Store.ClearUserTaillightMedals() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_SetUserTaillightMedals(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	var query = fmt.Sprintf("UPDATE user_medal SET is_taillight = 1 WHERE uid = ? AND medal_id in (%s);", "2404178")
	mock.ExpectExec(regexp.QuoteMeta(query)).WithArgs().WillReturnResult(sqlxmock.NewResult(1, 1))

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		ctx         context.Context
		uid         uint32
		medalIdList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "SetUserTaillightMedals",
			fields:  fields{db: db},
			args:    args{ctx: context.TODO()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.SetUserTaillightMedals(tt.args.ctx, tt.args.uid, tt.args.medalIdList); (err != nil) != tt.wantErr {
				t.Errorf("Store.SetUserTaillightMedals() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestStore_GetUserMedals(t *testing.T) {
	mock, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	sql := `SELECT uid, medal_id, unix_timestamp(award_time) as award_time, unix_timestamp(expire_time) as expire_time, 
	buff_exp, buff_currency, is_taillight FROM user_medal WHERE uid IN(%s) AND expire_time > ?`
	sql = fmt.Sprintf(sql, "2404178")

	model := UserMedal{
		Uid:            2404178,
		MedalId:        1,
		AwardTime:      1,
		ExpireTime:     1,
		BufferExp:      1,
		BufferCurrency: 1,
	}
	rows := sqlxmock.NewRows([]string{"uid", "medal_id", "award_time", "expire_time", "buff_exp", "buff_currency", "is_taillight"}).AddRow(
		model.Uid, model.MedalId, model.AwardTime, model.ExpireTime, model.BufferExp, model.BufferCurrency, 1,
	)
	mock.ExpectQuery(regexp.QuoteMeta(sql)).WithArgs().WillReturnRows(rows)

	var (
		uidList = []uint32{2404178}
		m       = map[uint32][]*UserMedal{}
	)
	m[model.Uid] = []*UserMedal{&model}

	type fields struct {
		db *sqlx.DB
	}
	type args struct {
		uidList []uint32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint32][]*UserMedal
		wantErr bool
	}{
		{
			name:    "GetUserMedals",
			fields:  fields{db: db},
			args:    args{uidList: uidList},
			want:    m,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			got, err := s.GetUserMedals(tt.args.uidList)
			t.Logf("Store.GetUserMedals() %+v", got)
			if (err != nil) != tt.wantErr {
				t.Errorf("Store.GetUserMedals() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				//t.Errorf("Store.GetUserMedals() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStore_CreateMysqlTable(t *testing.T) {

	_, db := GetMockMysql()
	defer func() {
		_ = db.Close()
	}()

	type fields struct {
		db *sqlx.DB
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name:    "NewMysqlV2",
			fields:  fields{db: db},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Store{
				db: tt.fields.db,
			}
			if err := s.CreateMysqlTable(); (err != nil) != tt.wantErr {
				t.Errorf("Store.CreateMysqlTable() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
