// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/services/user-recall/internal/conf (interfaces: ISDyConfigHandler)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockISDyConfigHandler is a mock of ISDyConfigHandler interface.
type MockISDyConfigHandler struct {
	ctrl     *gomock.Controller
	recorder *MockISDyConfigHandlerMockRecorder
}

// MockISDyConfigHandlerMockRecorder is the mock recorder for MockISDyConfigHandler.
type MockISDyConfigHandlerMockRecorder struct {
	mock *MockISDyConfigHandler
}

// NewMockISDyConfigHandler creates a new mock instance.
func NewMockISDyConfigHandler(ctrl *gomock.Controller) *MockISDyConfigHandler {
	mock := &MockISDyConfigHandler{ctrl: ctrl}
	mock.recorder = &MockISDyConfigHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISDyConfigHandler) EXPECT() *MockISDyConfigHandlerMockRecorder {
	return m.recorder
}

// CheckInConsumeRange mocks base method.
func (m *MockISDyConfigHandler) CheckInConsumeRange(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInConsumeRange", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckInConsumeRange indicates an expected call of CheckInConsumeRange.
func (mr *MockISDyConfigHandlerMockRecorder) CheckInConsumeRange(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInConsumeRange", reflect.TypeOf((*MockISDyConfigHandler)(nil).CheckInConsumeRange), arg0)
}

// CheckInInviteBlack mocks base method.
func (m *MockISDyConfigHandler) CheckInInviteBlack(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInInviteBlack", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckInInviteBlack indicates an expected call of CheckInInviteBlack.
func (mr *MockISDyConfigHandlerMockRecorder) CheckInInviteBlack(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInInviteBlack", reflect.TypeOf((*MockISDyConfigHandler)(nil).CheckInInviteBlack), arg0)
}

// CheckInLossBlack mocks base method.
func (m *MockISDyConfigHandler) CheckInLossBlack(arg0 uint32) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInLossBlack", arg0)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckInLossBlack indicates an expected call of CheckInLossBlack.
func (mr *MockISDyConfigHandlerMockRecorder) CheckInLossBlack(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInLossBlack", reflect.TypeOf((*MockISDyConfigHandler)(nil).CheckInLossBlack), arg0)
}

// GetBlindboxVal mocks base method.
func (m *MockISDyConfigHandler) GetBlindboxVal() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlindboxVal")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetBlindboxVal indicates an expected call of GetBlindboxVal.
func (mr *MockISDyConfigHandlerMockRecorder) GetBlindboxVal() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlindboxVal", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetBlindboxVal))
}

// GetDataCenterUrlPrefix mocks base method.
func (m *MockISDyConfigHandler) GetDataCenterUrlPrefix() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDataCenterUrlPrefix")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetDataCenterUrlPrefix indicates an expected call of GetDataCenterUrlPrefix.
func (mr *MockISDyConfigHandlerMockRecorder) GetDataCenterUrlPrefix() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDataCenterUrlPrefix", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetDataCenterUrlPrefix))
}

// GetInviteConf mocks base method.
func (m *MockISDyConfigHandler) GetInviteConf() (string, string, string) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInviteConf")
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(string)
	return ret0, ret1, ret2
}

// GetInviteConf indicates an expected call of GetInviteConf.
func (mr *MockISDyConfigHandlerMockRecorder) GetInviteConf() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInviteConf", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetInviteConf))
}

// GetInviteUrl mocks base method.
func (m *MockISDyConfigHandler) GetInviteUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInviteUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetInviteUrl indicates an expected call of GetInviteUrl.
func (mr *MockISDyConfigHandlerMockRecorder) GetInviteUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInviteUrl", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetInviteUrl))
}

// GetInviterWebUrl mocks base method.
func (m *MockISDyConfigHandler) GetInviterWebUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInviterWebUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetInviterWebUrl indicates an expected call of GetInviterWebUrl.
func (mr *MockISDyConfigHandlerMockRecorder) GetInviterWebUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInviterWebUrl", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetInviterWebUrl))
}

// GetMarktId2TbeanName mocks base method.
func (m *MockISDyConfigHandler) GetMarktId2TbeanName(arg0 uint32) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarktId2TbeanName", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetMarktId2TbeanName indicates an expected call of GetMarktId2TbeanName.
func (mr *MockISDyConfigHandlerMockRecorder) GetMarktId2TbeanName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarktId2TbeanName", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetMarktId2TbeanName), arg0)
}

// GetMaxGiftVal mocks base method.
func (m *MockISDyConfigHandler) GetMaxGiftVal() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxGiftVal")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetMaxGiftVal indicates an expected call of GetMaxGiftVal.
func (mr *MockISDyConfigHandlerMockRecorder) GetMaxGiftVal() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxGiftVal", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetMaxGiftVal))
}

// GetMustBind mocks base method.
func (m *MockISDyConfigHandler) GetMustBind() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMustBind")
	ret0, _ := ret[0].(bool)
	return ret0
}

// GetMustBind indicates an expected call of GetMustBind.
func (mr *MockISDyConfigHandlerMockRecorder) GetMustBind() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMustBind", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetMustBind))
}

// GetRecalledWebUrl mocks base method.
func (m *MockISDyConfigHandler) GetRecalledWebUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecalledWebUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetRecalledWebUrl indicates an expected call of GetRecalledWebUrl.
func (mr *MockISDyConfigHandlerMockRecorder) GetRecalledWebUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecalledWebUrl", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetRecalledWebUrl))
}

// GetShortLinkHost mocks base method.
func (m *MockISDyConfigHandler) GetShortLinkHost() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShortLinkHost")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetShortLinkHost indicates an expected call of GetShortLinkHost.
func (mr *MockISDyConfigHandlerMockRecorder) GetShortLinkHost() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShortLinkHost", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetShortLinkHost))
}

// GetSmsType mocks base method.
func (m *MockISDyConfigHandler) GetSmsType(arg0 uint32) uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSmsType", arg0)
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetSmsType indicates an expected call of GetSmsType.
func (mr *MockISDyConfigHandlerMockRecorder) GetSmsType(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSmsType", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetSmsType), arg0)
}

// GetStageConsume mocks base method.
func (m *MockISDyConfigHandler) GetStageConsume() uint64 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStageConsume")
	ret0, _ := ret[0].(uint64)
	return ret0
}

// GetStageConsume indicates an expected call of GetStageConsume.
func (mr *MockISDyConfigHandlerMockRecorder) GetStageConsume() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStageConsume", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetStageConsume))
}

// GetUserRecallSmsJumpUrl mocks base method.
func (m *MockISDyConfigHandler) GetUserRecallSmsJumpUrl() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRecallSmsJumpUrl")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetUserRecallSmsJumpUrl indicates an expected call of GetUserRecallSmsJumpUrl.
func (mr *MockISDyConfigHandlerMockRecorder) GetUserRecallSmsJumpUrl() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRecallSmsJumpUrl", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetUserRecallSmsJumpUrl))
}

// GetUserRecallSmsType mocks base method.
func (m *MockISDyConfigHandler) GetUserRecallSmsType() uint32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserRecallSmsType")
	ret0, _ := ret[0].(uint32)
	return ret0
}

// GetUserRecallSmsType indicates an expected call of GetUserRecallSmsType.
func (mr *MockISDyConfigHandlerMockRecorder) GetUserRecallSmsType() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserRecallSmsType", reflect.TypeOf((*MockISDyConfigHandler)(nil).GetUserRecallSmsType))
}

// Start mocks base method.
func (m *MockISDyConfigHandler) Start() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start")
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockISDyConfigHandlerMockRecorder) Start() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockISDyConfigHandler)(nil).Start))
}
