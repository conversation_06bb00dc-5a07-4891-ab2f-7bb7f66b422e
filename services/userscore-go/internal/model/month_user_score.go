package model

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/jmoiron/sqlx"
	"golang.52tt.com/pkg/log"
	"time"
)

func (s *Store) getMonthUserScoreTableName(t time.Time) string {
	return fmt.Sprintf("month_user_score_%d%02d", t.Year(), t.Month())
}

func (s *Store) CreateMonthUserScore(addMonth int) error {
	nextMonth := time.Now().AddDate(0, addMonth, 0)
	tableName := s.getMonthUserScoreTableName(nextMonth)
	sqlFormat := "CREATE TABLE IF NOT EXISTS %s (" +
	"uid int(10) unsigned NOT NULL COMMENT '用户id'," +
	"score_type int unsigned NOT NULL DEFAULT '0'," +
	"score int unsigned NOT NULL DEFAULT 0 COMMENT '积分'," +
	"PRIMARY KEY(uid, score_type)" +
	") ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='财务使用的用户月度积分结余表'"
	sqlStr := fmt.Sprintf(sqlFormat, tableName)
	log.Infof(sqlStr)

	_, err := s.db.Exec(sqlStr)
	return err
}

func (s *Store) InsertMonthUserScore(ctx context.Context, uid uint32, scoreType uint32, score uint32, t time.Time) error {
	tableName := s.getMonthUserScoreTableName(t)
	sqlStr := fmt.Sprintf("insert into %s(uid, score_type, score) values(?, ?, ?)", tableName)
	log.InfoWithCtx(ctx, sqlStr)
	_, err := s.db.ExecContext(ctx, sqlStr, uid, scoreType, score)
	return err
}

type ScoreMonthStatementTime struct {
	StatementYearMonth uint32    `db:"statement_year_month" json:"statement_year_month"`
	StatementGenTime   time.Time `db:"statement_gen_time" json:"statement_gen_time"`
}

func (s *Store) GenMonthUserScoreTable() error {
	ctx, cancel := context.WithTimeout(context.Background(), time.Hour)
	defer cancel()
	tNow := time.Now()
	sqlStr := "SELECT statement_year_month, statement_gen_time FROM tbl_score_month_statement_time WHERE statement_year_month = ?"
	tLastMonth := tNow.AddDate(0, -1, 0)
	yearMonth := int(tLastMonth.Year())*100 + int(tLastMonth.Month())
	statementTime := &ScoreMonthStatementTime{}
	err := s.db.GetContext(ctx, statementTime, sqlStr, yearMonth)
	if err != nil && err != sql.ErrNoRows {
		log.ErrorWithCtx(ctx, "GetContext %s err=%v", sqlStr, err)
		return err
	}
	if err != sql.ErrNoRows {
		log.InfoWithCtx(ctx, "tbl_score_month_statement_time exit %d", yearMonth)
		return nil
	}

	return s.Transaction(ctx, func(tx *sqlx.Tx) error {
		sqlStr = "INSERT INTO tbl_score_month_statement_time (statement_year_month,statement_gen_time) VALUES(?, ?)"
		_, err = tx.ExecContext(ctx, sqlStr, yearMonth, tLastMonth)
		if err != nil {
			log.ErrorWithCtx(ctx, "ExecContext %s err=%v", sqlStr, err)
			return err
		}

		monthUserScore := fmt.Sprintf("month_user_score_%d", yearMonth)

		for i := 0; i < 100; i++ {
			sqlStr = fmt.Sprintf("select uid, score_type, score from user_score_%02d where score > 0", i%100)
			scoreDataList := make([]UserScoreAllData, 0)
			err = tx.SelectContext(ctx, &scoreDataList, sqlStr)
			if err != nil {
				log.ErrorWithCtx(ctx, "SelectContext %s err=%v", sqlStr, err)
				return err
			}

			sqlStr = fmt.Sprintf("INSERT INTO %s (uid, score_type, score) VALUES (?, ?, ?)", monthUserScore)
			for _, elem := range scoreDataList {
				_, err = tx.ExecContext(ctx, sqlStr, elem.Uid, elem.ScoreType, elem.Score)
				if err != nil {
					log.ErrorWithCtx(ctx, "ExecContext %s err=%v", sqlStr, err)
					return err
				}
			}
		}

		//修正
		historyTableName := s.getMonthHistoryTableName(tLastMonth)
		sqlStr = fmt.Sprintf("SELECT tblA.log_id as log_id, tblA.uid as uid,tblA.change_score as change_score, tblA.finally_score as finally_score, "+
			"tblA.create_time as create_time, tblA.score_type as score_type FROM %s tblA ,"+
			" (select uid, score_type, min(log_id) as MinLog FROM %s GROUP BY uid, score_type) tblB "+
			"WHERE tblA.uid = tblB.uid AND tblA.score_type = tblB.score_type AND tblA.log_id = tblB.MinLog ORDER BY tblA.uid", historyTableName, historyTableName)
		historyList := make([]UserScoreMonthlyHistory, 0)
		err = tx.SelectContext(ctx, &historyList, sqlStr)
		if err != nil {
			log.ErrorWithCtx(ctx, "SelectContext %s err=%v", sqlStr, err)
			return err
		}

		sqlStr = fmt.Sprintf("REPLACE INTO %s (uid, score_type, score) VALUES (?, ?, ?)", monthUserScore)
		for _, elem := range historyList {
			_, err = tx.ExecContext(ctx, sqlStr, elem.UID, elem.ScoreType, elem.FinallyScore-elem.ChangeScore)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExecContext %s err=%v", sqlStr, err)
				return err
			}
		}

		return nil
	})
}