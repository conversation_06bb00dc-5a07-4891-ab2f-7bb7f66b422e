package event

import (
	"context"
	"fmt"
	"time"

	"gitlab.ttyuyin.com/avengers/tyr/compatible/proto"
	"golang.52tt.com/clients/channel"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/protocol/app"
	channelPB "golang.52tt.com/protocol/app/channel"
	"golang.52tt.com/protocol/services/minToolkit/kafka/pb/kafkapresent"
	"golang.52tt.com/services/channel-live-stats/conf"
	"golang.52tt.com/services/channel-live-stats/manager"
	//"golang.52tt.com/services/ugc/common/event"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka"
	"gitlab.ttyuyin.com/tt-infra/middleware/kafka/subscriber"
)

const topicPresentEvent = "present_event_v2"

type PresentEventSub struct {
	subscriber.Subscriber
	channelCli *channel.Client
	mgr        *manager.ChannelLiveStatsManager
}

func NewPresentEventSub(clientId, groupId string, topics, brokers []string, mgr *manager.ChannelLiveStatsManager) (*PresentEventSub, error) {

	conf := kafka.DefaultConfig()
	conf.ClientID = clientId
	conf.Consumer.Offsets.Initial = kafka.OffsetNewest
	conf.Consumer.Return.Errors = true

	/*
	kafkaSub, err := event.NewKafkaSub(topicPresentEvent, brokers, groupId, topics, conf)
	if err != nil {
		log.Errorf("Failed to create kafka-subscriber %+v", err)
		return nil, err
	}

	kafkaSub.SetIsMetrics()
	 */

	kafkaSub, err := kafka.NewSubscriber(brokers, conf)
	if err != nil {
		return nil, err
	}

	channelCli := channel.NewClient()

	sub := &PresentEventSub{
		Subscriber:   kafkaSub,
		channelCli: channelCli,
		mgr:        mgr,
	}

	err = sub.SubscribeContext(groupId, topics, subscriber.ProcessorContextFunc(sub.handlerEvent))
	if err != nil {
		return nil, err
	}

	//sub.SetMessageProcessor(sub.handlerEvent)
	return sub, nil
}

func (s *PresentEventSub) Close() {
	s.Subscriber.Stop()
}

func (s *PresentEventSub) handlerEvent(ctx context.Context, msg *subscriber.ConsumerMessage) (error, bool) {
	log.Debugf("handlerEvent topic:%s", msg.Topic)

	switch msg.Topic {
	case topicPresentEvent:
		return s.handlerPresentEvent(msg)
	}
	return nil, false
}

func (s *PresentEventSub) handlerPresentEvent(msg *subscriber.ConsumerMessage) (error, bool) {
	presentEvent := &kafkapresent.PresentEvent{}
	err := proto.Unmarshal(msg.Value, presentEvent)
	if err != nil {
		log.Errorf(" handlerPresentEvent Failed to proto.Unmarshal err(%v)", err)
		return err, false
	}

	log.Debugf("handlerPresentEvent %+v", presentEvent)

	//不是T豆礼物，不进行处理
	if presentEvent.GetPriceType() != 2 {
		return nil, true
	}

	// 不是语音直播房, 不进行处理
	if presentEvent.GetChannelType() != uint32(channelPB.ChannelType_RADIO_LIVE_CHANNEL_TYPE) {
		return nil, true
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	channelInfo, err := s.channelCli.GetChannelSimpleInfo(ctx, 0, presentEvent.GetChannelId())
	if err != nil {
		log.Errorf("handlerPresentEvent Failed to GetChannelSimpleInfo presentEvent:%v err(%v)", presentEvent, err)
		// 重试
		return err, true
	}

	// 检查订单是否已处理
	newOrderId := fmt.Sprintf("ls_present_%s", presentEvent.GetOrderId())
	isProc := s.mgr.CheckOrderIsProc(newOrderId)
	if isProc {
		log.Infof("handlerPresentEvent order is already proc orderId:%v", newOrderId)
		return nil, false
	}

	targetUid := presentEvent.GetTargetUid()
	anchorUid := channelInfo.GetBindId()
	channelFee := presentEvent.GetPrice() * presentEvent.GetItemCount()
	anchorIncome := uint32(0)
	gameFee := uint32(0)
	gameChannelFee := uint32(0) // 互动房间流水

	// 是此语音直播房的主播, 互动游戏流水不计入主播收入
	if anchorUid == targetUid && presentEvent.GetTagType() != uint32(app.PresentTagType_PRESENT_TAG_CHANNEL_GAME) {
		anchorIncome = channelFee
	}

	// 互动游戏流水
	if presentEvent.GetTagType() == uint32(app.PresentTagType_PRESENT_TAG_CHANNEL_GAME) && anchorUid == targetUid {
		gameFee = channelFee
	}
	if presentEvent.GetTagType() == uint32(app.PresentTagType_PRESENT_TAG_CHANNEL_GAME) {
		gameChannelFee = channelFee
		log.Infof("handlerPresentEvent PRESENT_TAG_CHANNEL_GAME. orderId=%s cid=%d sendUid=%d targetUid=%d anchorUid=%d gameChannelFee=%d",
			presentEvent.OrderId, presentEvent.ChannelId, presentEvent.Uid, presentEvent.TargetUid, anchorUid, gameChannelFee)
	}

	sendTime := time.Unix(int64(presentEvent.GetSendTime()), 0)
	if conf.TestMod {
		base := time.Date(2021, 8, 9, 20, 15, 0, 0, time.Local)
		sendTime = sendTime.AddDate(0, 0, -19+int(sendTime.Sub(base)/10/time.Minute))
	}

	err = s.mgr.IncrAnchorIncome(ctx, anchorUid, presentEvent.GetUid(), presentEvent.GetChannelId(), channelFee, anchorIncome, 0,
		gameFee, gameChannelFee, sendTime, int64(presentEvent.GetItemSource()), presentEvent.GetIsVirtualLive())
	if err != nil {
		log.Errorf(" handlerPresentEvent Failed to IncrAnchorIncome err(%v)", err)
		return err, false
	}

	if anchorIncome != 0 {
		s.mgr.HandleAnchorChSendPresentEvent(ctx, presentEvent.GetUid(), anchorUid, anchorIncome, sendTime)
	}

	return nil, true
}
