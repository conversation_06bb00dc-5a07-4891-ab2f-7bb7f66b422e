package main

import (
	"fmt"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	"golang.52tt.com/pkg/config"
)

func main() {
	statsMysqlConf := &config.MysqlConfig{
		Host:         "************",
		Port:         3306,
		Protocol:     "tcp",
		Database:     "channel_live_stats",
		UserName:     "godman",
		Password:     "thegodofman",
		Charset:      "utf8",
		PingInterval: 300,
		MaxIdleConns: 1,
		MaxOpenConns: 1,
	}
	mysqlDb, err := gorm.Open("mysql", statsMysqlConf.ConnectionString())
	if err != nil {
		fmt.Printf("Failed to Connect mysql %v", err)
		return
	}

	err = mysqlDb.Exec("update tbl_anchor_daily_live_record set day_live_valid=1,live_valid_minutes=120 where date='2024-12-17' and uid= 366672906").Error
	if err != nil {
		fmt.Printf("err:%v", err)
	}

	err = mysqlDb.Exec("update tbl_anchor_monthly_live_record set day_live_valid_cnt=day_live_valid_cnt+1, live_valid_minutes=live_valid_minutes+120, live_active_cnt=live_active_cnt+1" +
		" where yearmonth='2024-12' and uid=366672906").Error
	if err != nil {
		fmt.Printf("err:%v", err)
	}

}
