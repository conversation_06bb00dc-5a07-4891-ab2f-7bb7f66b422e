package main

import (
	"context"
	"crypto/tls"
	"fmt"
	beego "github.com/astaxie/beego/config"
	"github.com/go-gomail/gomail"
	_ "github.com/go-sql-driver/mysql" // MySQL驱动。
	"github.com/jinzhu/gorm"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/clients/account"
	"golang.52tt.com/pkg/config"
	"golang.52tt.com/pkg/log"
	"golang.52tt.com/services/channel-live-stats/mysql"
	"golang.52tt.com/services/channel-live-stats/tool/channel-live-center-data/datahouse"
	"golang.52tt.com/services/channel-live-stats/tool/channel-live-center-data/urrc"
	"google.golang.org/grpc"
	"os"
	"strconv"
	"sync"
	"time"
)

func InitSqlx(mysqlName string) *gorm.DB {
	cfg, _ := beego.NewConfig("json", "/home/<USER>/lja/channel-live-center-data/live-center-data.json")

	mysqlConfig := new(config.MysqlConfig)
	mysqlConfig.Read(cfg, mysqlName)
	mysqlAddr := mysqlConfig.ConnectionString()
	log.Infof("InitSqlx addr:%v", mysqlAddr)

	mysqlDB, err := gorm.Open("mysql", mysqlConfig.ConnectionString())
	if err != nil {
		log.Errorf("InitSqlx err:%v", err)
		panic(err)
	}

	return mysqlDB
}

func sendMail(filePath, header string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>")
	m.SetHeader("Subject", header)

	m.SetBody("text/html", "数据表在附件")
	m.Attach(filePath) //附件

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func sendMailV2(filePath, header string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", "<EMAIL>", "<EMAIL>", "<EMAIL>",
		"<EMAIL>", "<EMAIL>", "<EMAIL>")
	m.SetHeader("Subject", header)

	m.SetBody("text/html", "数据表在附件")
	m.Attach(filePath) //附件

	d := gomail.NewDialer("mail.52tt.com", 465, "<EMAIL>", "V7.D.sTy@%bDB50t#n.r$A4h+-FnXA")

	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		fmt.Println(err)
		return err
	}
	return nil
}

func main() {
	nowTm := time.Now()

	// 月数据
	if nowTm.Day() == 1 {
		for i := 0; i < 3; i++ {
			monthFirstTm := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local)
			endTm := time.Unix(int64(monthFirstTm.Unix()-1), 0)
			beginTm := time.Date(endTm.Year(), endTm.Month(), 1, 0, 0, 0, 0, time.Local)

			file := xlsx.NewFile()
			sheet, err := file.AddSheet("主播中心月维度数据")
			if err != nil {
				log.Errorf("file.AddSheet failed err:%v", err)
				return
			}

			row := sheet.AddRow()
			slice := []string{"主播uid", "月有效天数", "月有效时长(分钟)", "月主播收礼", "月直播流水", "月付费人数", "月新增关注", "月粉丝团新增", "骑士团流水", "违规次数",
				"直播活跃天", "平台活跃天", "平台活跃时长(分钟)", "新建有效关系链数"}
			row.WriteSlice(&slice, -1)

			isSuccess := ExportLiveCenterMonthData(file, beginTm, endTm, sheet)
			if !isSuccess {
				time.Sleep(1 * time.Minute)
				continue
			}

			path := fmt.Sprintf("主播中心月维度数据%04d%02d.xlsx", beginTm.Year(), beginTm.Month())
			file.Save(path)

			err = sendMailV2(path, "主播中心月维度数据")
			if err != nil {
				log.Errorf("sendMail failed err:%v", err)
			}

			os.Remove(path)
			break
		}
	}

	// 周数据
	if nowTm.Weekday() == time.Monday {
		for i := 0; i < 3; i++ {
			weekFirstTm := time.Date(nowTm.Year(), nowTm.Month(), nowTm.Day(), 0, 0, 0, 0, time.Local)
			beginTm := weekFirstTm.AddDate(0, 0, -7)
			endTm := time.Unix(weekFirstTm.Unix()-1, 0)

			file := xlsx.NewFile()
			sheet, err := file.AddSheet("主播中心周维度数据")
			if err != nil {
				log.Errorf("file.AddSheet failed err:%v", err)
				return
			}

			row := sheet.AddRow()
			slice := []string{"主播uid", "周有效天数", "周有效时长(分钟)", "周主播收礼", "周直播流水", "周付费人数", "周新增关注", "周粉丝团新增", "骑士团流水", "违规次数",
				"直播活跃天", "平台活跃天", "平台活跃时长(分钟)", "新建有效关系链数"}
			row.WriteSlice(&slice, -1)

			isSuccess := ExportLiveCenterWeekData(file, beginTm, endTm, sheet)
			if !isSuccess {
				time.Sleep(1 * time.Minute)
				continue
			}

			path := fmt.Sprintf("主播中心周维度数据(%04d%02d%02d-%04d%02d%02d).xlsx", beginTm.Year(), beginTm.Month(), beginTm.Day(),
				endTm.Year(), endTm.Month(), endTm.Day())
			file.Save(path)

			err = sendMailV2(path, "主播中心周维度数据")
			if err != nil {
				log.Errorf("sendMail failed err:%v", err)
			}

			os.Remove(path)
			break
		}
	}

	// 每天推当月的
	for i := 0; i < 3; i++ {
		monthFirstTm := time.Date(nowTm.Year(), nowTm.Month(), 1, 0, 0, 0, 0, time.Local)
		beginTm := monthFirstTm
		endTm := nowTm

		file := xlsx.NewFile()
		sheet, err := file.AddSheet("主播中心月维度数据")
		if err != nil {
			log.Errorf("file.AddSheet failed err:%v", err)
			return
		}

		row := sheet.AddRow()
		slice := []string{"主播uid", "月有效天数", "月有效时长(分钟)", "月主播收礼", "月直播流水", "月付费人数", "月新增关注", "月粉丝团新增", "骑士团流水", "违规次数",
			"直播活跃天", "平台活跃天", "平台活跃时长(分钟)", "新建有效关系链数"}
		row.WriteSlice(&slice, -1)

		isSuccess := ExportLiveCenterMonthData(file, beginTm, endTm, sheet)
		if !isSuccess {
			// 重试
			time.Sleep(1 * time.Minute)
			continue
		}

		path := fmt.Sprintf("主播中心月维度数据%04d%02d.xlsx", beginTm.Year(), beginTm.Month())
		file.Save(path)

		err = sendMail(path, "主播中心月维度数据")
		if err != nil {
			log.Errorf("sendMail failed err:%v", err)
		}

		os.Remove(path)
		break
	}
}

func batGetUserVio(ctx context.Context, accountCli account.IClient, uidList []uint32, beginTm, endTm time.Time) (map[uint32]string, map[string][]uint32) {
	nowTm := time.Now()
	subCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	mapUid2Alias := make(map[uint32]string)
	mapTid2DateViolationsCnt := make(map[string][]uint32)
	var err error

	uidInfoMap, serverErr := accountCli.BatGetUserByUid(subCtx, uidList...)
	if serverErr != nil {
		log.Errorf("batGetUserVio Failed to BatGetUserByUid uidList:%v err:%v", uidList, serverErr)
		return mapUid2Alias, mapTid2DateViolationsCnt
	}

	anchorAliasList := make([]string, 0)
	for _, v := range uidInfoMap {
		anchorAliasList = append(anchorAliasList, v.GetAlias())
		mapUid2Alias[v.GetUid()] = v.GetAlias()
	}

	_, _, mapTid2DateViolationsCnt, _, err = urrc.GetPractitionerViolationsInfoFromAudit(anchorAliasList, beginTm, endTm)
	if err != nil {
		log.Errorf("batGetUserVio Failed to GetPractitionerViolationsInfoFromAudit failed anchorAliasList:%v %v %v  err:%+v", anchorAliasList, beginTm, beginTm, err)
	}

	log.Infof("batGetUserVio cost:%v", time.Now().Sub(nowTm))
	return mapUid2Alias, mapTid2DateViolationsCnt
}

func ExportLiveCenterMonthData(file *xlsx.File, beginTm, endTm time.Time, sheet *xlsx.Sheet) bool {
	ctx := context.Background()

	liveStatsDb := InitSqlx("liveStatsDb")

	accountCli, err := account.NewClient(grpc.WithBlock())
	if err != nil {
		log.Errorf("ExportLiveCenterMonthData account.NewClient failed err:%v", err)
		return false
	}

	log.Infof("ExportLiveCenterMonthData begin b:%v e:%v", beginTm, endTm)

	mapUid2MonthList := make(map[uint32][]*mysql.AnchorMonthlyLiveRecord, 0)
	offset := 0
	limit := 3000
	for {
		records := make([]*mysql.AnchorMonthlyLiveRecord, 0)

		err := liveStatsDb.Table(mysql.TblAnchorMonthlyRecord).Where("yearmonth = ?", mysql.GetYearMonth(beginTm)).Offset(offset).Limit(limit).Scan(&records).Error
		if err != nil {
			log.Errorf("ExportLiveCenterMonthData liveStatsDb.SelectContext select fail %d %d err:%v", offset, limit, err)
			return false
		}

		for _, record := range records {
			if _, ok := mapUid2MonthList[record.Uid]; ok {
				mapUid2MonthList[record.Uid] = append(mapUid2MonthList[record.Uid], record)
			} else {
				mapUid2MonthList[record.Uid] = make([]*mysql.AnchorMonthlyLiveRecord, 0)
				mapUid2MonthList[record.Uid] = append(mapUid2MonthList[record.Uid], record)
			}
		}

		if len(records) < limit {
			break
		}

		offset = offset + limit
	}

	log.Infof("ExportLiveCenterMonthData mapUid2MonthList:%d", len(mapUid2MonthList))

	mapUid2Alias := make(map[uint32]string)
	mapTid2DateViolationsCnt := make(map[string][]uint32)
	uidList := make([]uint32, 0)
	totalCnt := 0
	for uid, _ := range mapUid2MonthList {
		uidList = append(uidList, uid)
		totalCnt += 1
		//批量获取用户违规
		if len(uidList) >= 50 || totalCnt == len(mapUid2MonthList) {
			tmpMapUid2Alias, tmpMapTid2DateViolationsCnt := batGetUserVio(ctx, accountCli, uidList, beginTm, endTm)
			for k, v := range tmpMapUid2Alias {
				mapUid2Alias[k] = v
			}
			for k, v := range tmpMapTid2DateViolationsCnt {
				mapTid2DateViolationsCnt[k] = v
			}
			uidList = make([]uint32, 0)
		}
	}

	for uid, monthList := range mapUid2MonthList {
		wg := sync.WaitGroup{}

		// 从数仓获取数据
		anchorActiveList := make([]*datahouse.MemberDailyInfo, 0)
		anchorPayChainList := make([]*datahouse.MemberChainDailyInfo, 0)

		wg.Add(1)
		go func() {
			defer wg.Done()
			anchorActiveList, err = datahouse.QueryMemberDailyInfo(ctx, uid, beginTm, endTm)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExportLiveCenterMonthData Failed to QueryMemberDailyInfo failed uid:%d %d %d  err:%+v", uid, beginTm, endTm, err)
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			anchorPayChainList, err = datahouse.QueryMemberChainDailyInfo(ctx, uid, beginTm, endTm)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExportLiveCenterMonthData Failed to QueryMemberChainInfo failed uid:%d %d %d  err:%+v", uid, beginTm, endTm, err)
			}
		}()

		wg.Wait()

		var validDayCnt, liveValidMinutes, anchorIncome, channelFee, knightIncome, liveActiveDay, consumerCnt, followCnt uint32
		var newFansCnt uint32
		for _, monthRecord := range monthList {
			validDayCnt += monthRecord.DayLiveValidCnt
			liveValidMinutes += monthRecord.LiveValidMinutes
			anchorIncome += monthRecord.AnchorIncome
			channelFee += monthRecord.ChannelFee
			consumerCnt += monthRecord.ConsumerCnt
			followCnt += monthRecord.FollowCnt
			newFansCnt += monthRecord.NewFansCnt
			knightIncome += monthRecord.KnightIncome
			liveActiveDay += monthRecord.LiveActiveCnt
		}

		var vioCnt uint32
		for _, cnt := range mapTid2DateViolationsCnt[mapUid2Alias[uid]] {
			vioCnt += cnt
		}

		var platActiveDays uint32
		var platActiveSec uint32
		for _, active := range anchorActiveList {
			platActiveDays += active.LoginDays
			platActiveSec += active.LoginDuration
		}

		var payChainCnt uint32
		for _, pay := range anchorPayChainList {
			payChainCnt += pay.VaildChainCnt
		}

		row := sheet.AddRow()
		cell := row.AddCell()
		cell.Value = strconv.Itoa(int(uid))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(validDayCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(liveValidMinutes))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(anchorIncome))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(channelFee))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(consumerCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(followCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(newFansCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(knightIncome))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(vioCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(liveActiveDay))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(platActiveDays))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(platActiveSec / 60))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(payChainCnt))

		log.Infof("ExportLiveCenterMonthData anchor data uid:%d liveRecord:%v newFansCnt:%d %d %d %d %d %d %d %d %d %d %d ", uid, monthList, newFansCnt, validDayCnt,
			liveValidMinutes, anchorIncome, channelFee, knightIncome, vioCnt, liveActiveDay, platActiveDays, platActiveSec, payChainCnt)

	}

	log.Infof("ExportLiveCenterMonthData end b:%v e:%v", beginTm, endTm)
	return true
}

func ExportLiveCenterWeekData(file *xlsx.File, beginTm, endTm time.Time, sheet *xlsx.Sheet) bool {
	ctx := context.Background()

	liveStatsDb := InitSqlx("liveStatsDb")

	accountCli, err := account.NewClient(grpc.WithBlock())
	if err != nil {
		log.Errorf("ExportLiveCenterWeekData account.NewClient failed err:%v", err)
		return false
	}

	log.Infof("ExportLiveCenterWeekData begin b:%v e:%v", beginTm, endTm)

	mapUid2DayList := make(map[uint32][]*mysql.AnchorDailyLiveRecord, 0)
	offset := 0
	limit := 3000
	for {
		records := make([]*mysql.AnchorDailyLiveRecord, 0)

		err := liveStatsDb.Table(mysql.TblAnchorDailyRecord).Where("date >= ?", mysql.GetDateTime(beginTm)).Where("date <= ?", mysql.GetDateTime(endTm)).
			Offset(offset).Limit(limit).Scan(&records).Error
		if err != nil {
			log.Errorf("ExportLiveCenterWeekData liveStatsDb.SelectContext select fail %d %d err:%v", offset, limit, err)
			return false
		}

		for _, record := range records {
			if _, ok := mapUid2DayList[record.Uid]; ok {
				mapUid2DayList[record.Uid] = append(mapUid2DayList[record.Uid], record)
			} else {
				mapUid2DayList[record.Uid] = make([]*mysql.AnchorDailyLiveRecord, 0)
				mapUid2DayList[record.Uid] = append(mapUid2DayList[record.Uid], record)
			}
		}

		if len(records) < limit {
			break
		}

		offset = offset + limit
	}

	log.Infof("ExportLiveCenterWeekData mapUid2DayList:%d", len(mapUid2DayList))

	mapUid2Alias := make(map[uint32]string)
	mapTid2DateViolationsCnt := make(map[string][]uint32)
	uidList := make([]uint32, 0)
	totalCnt := 0
	for uid, _ := range mapUid2DayList {
		uidList = append(uidList, uid)
		totalCnt += 1
		//批量获取用户违规
		if len(uidList) >= 50 || totalCnt == len(mapUid2DayList) {
			tmpMapUid2Alias, tmpMapTid2DateViolationsCnt := batGetUserVio(ctx, accountCli, uidList, beginTm, endTm)
			for k, v := range tmpMapUid2Alias {
				mapUid2Alias[k] = v
			}
			for k, v := range tmpMapTid2DateViolationsCnt {
				mapTid2DateViolationsCnt[k] = v
			}
			uidList = make([]uint32, 0)
		}
	}

	for uid, dayList := range mapUid2DayList {
		wg := sync.WaitGroup{}

		// 从数仓获取数据
		anchorActiveList := make([]*datahouse.MemberDailyInfo, 0)
		anchorPayChainList := make([]*datahouse.MemberChainDailyInfo, 0)

		wg.Add(1)
		go func() {
			defer wg.Done()
			anchorActiveList, err = datahouse.QueryMemberDailyInfo(ctx, uid, beginTm, endTm)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExportLiveCenterWeekData Failed to QueryMemberDailyInfo failed uid:%d %d %d  err:%+v", uid, beginTm, endTm, err)
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			anchorPayChainList, err = datahouse.QueryMemberChainDailyInfo(ctx, uid, beginTm, endTm)
			if err != nil {
				log.ErrorWithCtx(ctx, "ExportLiveCenterWeekData Failed to QueryMemberChainInfo failed uid:%d %d %d  err:%+v", uid, beginTm, endTm, err)
			}
		}()

		wg.Wait()

		var validDayCnt, liveValidMinutes, anchorIncome, channelFee, knightIncome, liveActiveDay, consumerCnt, followCnt uint32
		var newFansCnt uint32
		for _, dayRecord := range dayList {
			validDayCnt += dayRecord.DayLiveValid
			liveValidMinutes += dayRecord.LiveValidMinutes
			anchorIncome += dayRecord.AnchorIncome
			channelFee += dayRecord.ChannelFee
			consumerCnt += dayRecord.WeekConsumerCnt
			followCnt += dayRecord.WeekFollowCnt
			newFansCnt += dayRecord.NewFansCnt
			knightIncome += dayRecord.KnightIncome
			if dayRecord.LiveValidMinutes >= mysql.LiveActiveAnchorMinutes {
				liveActiveDay += 1
			}

		}

		var vioCnt uint32
		for _, cnt := range mapTid2DateViolationsCnt[mapUid2Alias[uid]] {
			vioCnt += cnt
		}

		var platActiveDays uint32
		var platActiveSec uint32
		for _, active := range anchorActiveList {
			platActiveDays += active.LoginDays
			platActiveSec += active.LoginDuration
		}

		var payChainCnt uint32
		for _, pay := range anchorPayChainList {
			payChainCnt += pay.VaildChainCnt
		}

		row := sheet.AddRow()
		cell := row.AddCell()
		cell.Value = strconv.Itoa(int(uid))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(validDayCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(liveValidMinutes))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(anchorIncome))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(channelFee))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(consumerCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(followCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(newFansCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(knightIncome))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(vioCnt))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(liveActiveDay))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(platActiveDays))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(platActiveSec / 60))
		cell = row.AddCell()
		cell.Value = strconv.Itoa(int(payChainCnt))

		log.Infof("ExportLiveCenterWeekData anchor data uid:%d liveRecord:%v newFansCnt:%d %d %d %d %d %d %d %d %d %d %d ", uid, dayList, newFansCnt, validDayCnt,
			liveValidMinutes, anchorIncome, channelFee, knightIncome, vioCnt, liveActiveDay, platActiveDays, platActiveSec, payChainCnt)

	}

	log.Infof("ExportLiveCenterWeekData end b:%v e:%v", beginTm, endTm)
	return true
}
