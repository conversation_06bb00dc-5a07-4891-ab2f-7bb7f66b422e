package lottery_open

import (
	"github.com/globalsign/mgo"
	"github.com/globalsign/mgo/bson"
	"gitlab.ttyuyin.com/tt-infra/tyr/log"
	pb "golang.52tt.com/protocol/services/channel-lottery"
	"golang.52tt.com/services/channel-lottery/utils/mongo"
	"time"
)

// LotteryOpen 自定义礼物
type LotteryOpen struct {
	Id         uint32   `bson:"_id"`
	ChannelId  uint32   `bson:"channel_id"`
	DisplayId  uint32   `bson:"display_id"`
	BeginTime  int64    `bson:"begin_time"`
	EndTime    int64    `bson:"end_time"`
	UpdateTime int64    `bson:"update_time"`
	Conditions []uint32 `bson:"conditions"`
	Source     uint32   `bson:"source"`
}

const lotteryOpenCollection = "lottery_open"

type Mongo struct {
	mongo.MongoDao
}

func NewMongo(m mongo.MongoDao) *Mongo {
	return &Mongo{m}
}

func (m *Mongo) GetLotteryOpen(id uint32) (*LotteryOpen, error) {
	s, c := m.Connect(lotteryOpenCollection)
	defer s.Close()

	lo := &LotteryOpen{}
	err := c.FindId(id).One(lo)
	if err == mgo.ErrNotFound {
		err = nil
	}
	return lo, err
}

func (m *Mongo) GetLotteryOpenByChannelId(channelId uint32) (*LotteryOpen, error) {
	s, c := m.Connect(lotteryOpenCollection)
	defer s.Close()

	lo := &LotteryOpen{}
	b := bson.M{}
	b["channel_id"] = channelId
	b["begin_time"] = bson.M{
		"$lte": time.Now().Unix(),
	}
	b["end_time"] = bson.M{
		"$gte": time.Now().Unix(),
	}
	err := c.Find(b).One(lo)
	if err == mgo.ErrNotFound {
		err = nil
	}
	return lo, err
}

//func (m *Mongo) SetLotteryOpen(lotteryOpen *LotteryOpen, del bool) (info *mgo.ChangeInfo, err error) {
//	s, c := m.Connect(lotteryOpenCollection)
//	defer s.Close()
//
//	if del {
//		err = c.RemoveId(lotteryOpen.Id)
//		return nil, err
//	}
//
//	timeNow := time.Now().Unix()
//	lotteryOpen.UpdateTime = timeNow
//	if lotteryOpen.Id == 0 {
//		nextId, err := m.GetNextId(lotteryOpenCollection)
//		if err != nil {
//			return info, err
//		} else {
//			lotteryOpen.Id = nextId
//		}
//	} else {
//		tmp := &LotteryOpen{}
//		err = c.FindId(lotteryOpen.Id).One(&tmp)
//		if err != nil {
//			log.Errorf("Fail to FindId id(%d) err(%s)", lotteryOpen.Id, err)
//			err = nil
//		}
//	}
//
//	return c.UpsertId(lotteryOpen.Id, lotteryOpen)
//}

func (m *Mongo) SetLotteryOpenV2(lotteryOpen *LotteryOpen) (info *mgo.ChangeInfo, err error) {
	s, c := m.Connect(lotteryOpenCollection)
	defer s.Close()

	timeNow := time.Now().Unix()
	lotteryOpen.UpdateTime = timeNow
	beginTime := lotteryOpen.BeginTime
	endTime := lotteryOpen.EndTime

	nextId, err := m.GetNextId(lotteryOpenCollection)
	if err != nil {
		return info, err
	} else {
		lotteryOpen.Id = nextId
	}

	filter := bson.M{
		"_id": bson.M{"$ne": lotteryOpen.Id},
		///"display_id": lotteryOpen.DisplayId,
		"channel_id": lotteryOpen.ChannelId,
	}
	if beginTime != 0 && endTime != 0 {
		filter["begin_time"] = bson.M{"$lte": endTime}
		filter["end_time"] = bson.M{"$gte": beginTime}
	}

	if lotteryOpen.Source == uint32(pb.SetLotteryOpenListReq_SetSource_ANCHOR_LEVEL_SETTLE) {
		// 主播等级结算奖励不需要校验时间, 直接插入。lotteryOpen.Id不可能等于
		filter = bson.M{
			"_id": lotteryOpen.Id,
		}
	}

	changeUpdate := bson.M{
		"$setOnInsert": lotteryOpen,
	}

	var res *mgo.ChangeInfo

	res, err = c.Upsert(filter, changeUpdate)
	log.Debugf("lotteryOpen:%+v,changeInfo:%v", lotteryOpen, res)

	return res, err
}

func (m *Mongo) UpdateLotteryOpen(lotteryOpen *LotteryOpen) error {
	s, c := m.Connect(lotteryOpenCollection)
	defer s.Close()

	timeNow := time.Now().Unix()
	lotteryOpen.UpdateTime = timeNow

	err := c.UpdateId(lotteryOpen.Id, lotteryOpen)
	log.Debugf("lotteryOpen:%+v,err:%v", lotteryOpen, err)

	return err
}

func (m *Mongo) DelLotteryOpenByIds(idList []uint32) error {
	s, c := m.Connect(lotteryOpenCollection)
	defer s.Close()

	change, err := c.RemoveAll(bson.M{"_id": bson.M{"$in": idList}})
	if err != nil {
		return err
	}

	log.Debugf("RemoveAll %v OK:%+v", idList, change)
	return nil
}

func (m *Mongo) SearchLotteryOpens(displayId, channelId uint32, offset int, limit int) ([]*LotteryOpen, int, error) {
	s, c := m.Connect(lotteryOpenCollection)
	defer s.Close()

	lotteryOpens := make([]*LotteryOpen, 0)
	b := bson.M{}

	if channelId != 0 {
		b["channel_id"] = channelId
	}

	if displayId != 0 {
		b["display_id"] = displayId // 后续废弃
	}

	err := c.Find(b).Sort("-update_time").Skip(offset).Limit(limit).All(&lotteryOpens)

	count, _ := c.Find(b).Count()
	return lotteryOpens, count, err
}

func (m *Mongo) FindSameLotteryOpen(id, channelId uint32, beginTime, endTime int64) *LotteryOpen {
	s, c := m.Connect(lotteryOpenCollection)
	defer s.Close()

	lo := &LotteryOpen{}
	cond := bson.M{
		"_id": bson.M{
			"$ne": id,
		},
		//"display_id": displayId,
		"channel_id": channelId,
	}
	if beginTime != 0 && endTime != 0 {
		cond["begin_time"] = bson.M{"$lte": endTime}
		cond["end_time"] = bson.M{"$gte": beginTime}
	}
	err := c.Find(cond).One(&lo)
	if err != nil && err != mgo.ErrNotFound {
		log.Errorf("Fail to FindSameLotteryOpen adId(%d) channelId(%d) beginTime(%d) endTime(%d) err(%s)", id, channelId, beginTime, endTime, err)
	}
	return lo
}
