package main

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/jmoiron/sqlx"
	"github.com/tealeg/xlsx"
	"golang.52tt.com/clients/account"
	"google.golang.org/grpc"
	"strconv"
	"strings"
)

var db *sqlx.DB
var accountCli *account.Client
var guildid2ConsultList = make(map[uint32][]uint32)

type ConUidList struct {
	GuildId        uint32   `json:"guild_id"`
	ConsultUidList []uint32 `json:"consult_uid_list"`
}

func init() {
	tmpAccountCli, err := account.NewClient(grpc.WithAuthority("account.52tt.local"), grpc.WithBlock())
	if nil != err {
		fmt.Printf("\n account.NewClient err:%v \n", err)
	}
	accountCli = tmpAccountCli
}

func main() {
	xlsxfile2, err := xlsx.OpenFile("公会签约咨询名单.xlsx")
	if nil != err {
		fmt.Printf("OpenFile err:%v", err)
		return
	}
	for _, sh := range xlsxfile2.Sheets {
		for idx, row := range sh.Rows {
			if idx == 0 {
				continue
			}
			id, err := strconv.ParseUint(row.Cells[1].String(), 10, 32)
			if nil != err {
				//fmt.Printf("\n ParseUint str:%v err:%v \n", row.Cells[1].String(), err)
				continue
			}
			guildId := uint32(id)
			accountArr := strings.Split(row.Cells[4].String(), "\n")

			for idx, c := range accountArr {
				accountArr[idx] = strings.TrimSpace(c)
			}

			fmt.Printf("\nguildId:%v accountArr:%v\n", row.Cells[1].String(), accountArr)

			if _, ok := guildid2ConsultList[guildId]; false == ok {
				guildid2ConsultList[guildId] = make([]uint32, 0)
			}
			users, err := accountCli.BatchQueryUidListByAlias(context.Background(), accountArr)
			if nil != err {
				fmt.Printf("\n BatchQueryUidList guildId:%v err:%v \n", guildId, err)
				continue
			}

			for _, u := range users {
				guildid2ConsultList[guildId] = append(guildid2ConsultList[guildId], u.Uid)
			}
		}
	}

	var cl []*ConUidList
	for guildId, uidList := range guildid2ConsultList {
		if 0 == len(uidList) {
			continue
		}
		cl = append(cl, &ConUidList{
			GuildId:        guildId,
			ConsultUidList: uidList,
		})
	}

	//fmt.Printf("\n guildid2ConsultList:%v \n", guildid2ConsultList)

	str, err := json.Marshal(cl)
	if nil != err {
		fmt.Printf("\n json.Marshal err:%v \n", err)
		return
	}

	fmt.Printf("\n %v \n", string(str))
}
