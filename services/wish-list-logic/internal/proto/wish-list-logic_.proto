syntax = "proto3";

package ga.wishlistlogic;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/wishlistlogic";

message SenderUserInfo {
    UserProfile sender_profile = 1;
    int64 sender_value = 2; //贡献值
}

enum WishGiftType {
  NormalWishGift = 0; // 普通礼物
  LuckWishGift = 1; // 幸运礼物
}

// 心愿单礼物
message WishGiftItem {
  uint32 gift_id = 1;
  WishGiftType gift_type = 2; //礼物类型
  uint32 wish_total = 3; // 许愿总数
  uint32 received_total = 4; // 已完成总数
  uint32 sender_total = 5; // 助力人数
  repeated SenderUserInfo sender_user_list = 6; // 助力榜前三
  SenderUserInfo my_sender_value = 7; //自己数据
}

// 心愿单
message WishItem {
  string wish_id = 1; // 心愿单ID
  string wish_title = 2; // 心愿单标题
  string gratitude_words = 3; // 感谢寄语
  repeated WishGiftItem gift_list = 4; // 心愿礼物列表
  string wish_theme_res = 5; // 心愿单主题资源包
  bool valid = 6; // 当前生效的心愿单
  string wish_theme_res_md5 = 7; // 心愿单主题资源包MD5
  string gratitude_words_color = 8; // 感谢寄语文案颜色
}

message GetAnchorWishListReq {
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2;
    uint32 channel_id = 3;
}

message GetAnchorWishListResp {
    ga.BaseResp base_resp = 1;
    repeated WishItem wish_list = 2;
    bool is_wish_init = 3; // 是否首次开播，is_wish_init==true表示没有设置过，需要弹框
    uint32 continuous_interval = 4; // 连点间隔配置（秒）
}

message SetAnchorWishListReq {
    ga.BaseReq base_req = 1;
    uint32 anchor_uid = 2; // 主播UID
    uint32 channel_id = 3; // 房间ID
    WishItem wish_item = 4;
    uint64 channel_live_id = 5;
}

message SetAnchorWishListResp {
    ga.BaseResp base_resp = 1;
}

enum WishListChangeType {
   ENUM_CONFIG_CHANGE = 0;  //心愿单配置变化（主播编辑），wish_list列表是3个礼物配置，不一定有进度数据和送礼人数据
   ENUM_WISH_ACCOMPLISH  = 1;  //完成某个礼物心愿，wish_list列表是完成心愿的进度和送礼人数据
   ENUM_WISH_PROCESS  = 2;  //某个礼物进度刷新
   ENUM_WISH_ALL_ACCOMPLISH  = 3;  //完成整个心愿单
}

//心愿单变化推送
message WishListChangePushMsg {
    WishListChangeType msg_type = 1;
    uint32 anchor_uid = 2;
    uint32 channel_id = 3;
    WishItem wish_list = 4;
}

// 返回感谢寄语列表
message GetWishGratitudeWordsReq {
  ga.BaseReq base_req = 1;
  uint32 anchor_uid = 2; // 主播UID
  uint32 channel_id = 3; // 房间ID
}
message GetWishGratitudeWordsResp {
  ga.BaseResp base_resp = 1;
  repeated string list = 2; // 感谢寄语
}

//心愿单数据变化kfk
message WishDataKfk{
    uint64 channel_live_id = 1; //直播场次ID
    uint32 anchor_uid = 2; //主播UID
    uint32 channel_id = 3; //房间ID
    WishListChangeType msg_type = 4;
    uint32 total_price = 5; //已经收到的心愿单礼物总价值（T豆）
    uint32 server_time = 6; //发送本消息服务时间
    WishItem wish_item = 7; //心愿单数据，完成心愿单是带所有送礼UID，其他情况只有每个礼物top3UID
}