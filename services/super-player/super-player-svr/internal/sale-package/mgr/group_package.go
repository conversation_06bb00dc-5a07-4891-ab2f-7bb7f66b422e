package mgr

import (
	"context"
	"errors"
	"fmt"
	"gitlab.ttyuyin.com/avengers/tyr/core/log"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/protocol/common/status"
	pb "golang.52tt.com/protocol/services/superplayersvr"
	"golang.52tt.com/services/super-player/super-player-svr/internal/pkg/utils"
	"strings"
	"time"
)

// GroupPackage 分组套餐（数据库存储结构）
type GroupPackage struct {
	Id            uint32    `db:"id"`
	Name          string    `db:"name"`
	Desc          string    `db:"descc"`
	PackageType   uint32    `db:"package_type"`
	SubType       uint32    `db:"sub_type"`
	PackageStatus uint32    `db:"package_status"`
	OriginalPrice float32   `db:"original_price"`
	Price         float32   `db:"price"`
	Value         int32     `db:"value"`
	Days          int32     `db:"days"`
	AutoValue     int32     `db:"auto_value"`
	Operator      string    `db:"operator"`
	IsShowCoupon  bool      `db:"is_show_coupon"`
	Ctime         time.Time `db:"ctime"`
	Mtime         time.Time `db:"mtime"`
}

type MarketInfo struct {
	MarketId  uint32 `db:"market_id"`
	ProductId string `db:"product_id"`
	PackageId uint32 `db:"id"`
}

func (m *Manager) checkPackageUpgrade(ctx context.Context, groupPackage *pb.GroupPackage) error {
	if groupPackage.PackageType != pb.PackageType_ENUM_PACKAGE_TYPE_SVIP || groupPackage.SubType != pb.PackageSubType_PackageSubType_UpgradeSVIP {
		log.ErrorWithCtx(ctx, "checkGroupPackageUpgrade upgrade package type error, groupPackage:%v", groupPackage)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "升级套餐类型错误")
	}
	// 参数校验
	if groupPackage.GetPrice() == 0 {
		log.ErrorWithCtx(ctx, "checkGroupPackageUpgrade failed, price is 0 groupPackage:%v", groupPackage)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "售价不能为0")
	}
	// 优惠券校验
	if groupPackage.GetOriginalPrice() == 0 && groupPackage.GetIsShowCoupon() {
		log.ErrorWithCtx(ctx, "AddPackage original price unable be zero groupPackage:%v", groupPackage)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "勾选优惠券后，原价不能为0")
	}
	if groupPackage.GetOriginalPrice() != 0 && groupPackage.GetOriginalPrice() <= groupPackage.GetPrice() {
		log.ErrorWithCtx(ctx, "AddPackage original price unable less than price groupPackage:%v", groupPackage)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "原价必须大于售价")
	}
	return nil
}

func (m *Manager) checkPackage(ctx context.Context, groupPack *pb.GroupPackage) error {
	if groupPack.GetPrice() == 0 {
		log.ErrorWithCtx(ctx, "checkGroupPackage price unable be zero groupPack:%v", groupPack)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "售价不能为0")
	}

	// 优惠券校验
	if groupPack.GetOriginalPrice() == 0 && groupPack.GetIsShowCoupon() {
		log.ErrorWithCtx(ctx, "checkGroupPackage original price unable be zero groupPack:%v", groupPack)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "勾选优惠券后，原价不能为0")
	}
	if groupPack.GetOriginalPrice() != 0 && groupPack.GetOriginalPrice() <= groupPack.GetPrice() {
		log.ErrorWithCtx(ctx, "checkGroupPackage original price unable less than price groupPack:%v", groupPack)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "原价必须大于售价")
	}

	switch groupPack.SubType {
	case pb.PackageSubType_PackageSubType_AutoRenew:
		if groupPack.Days < 7 {
			log.ErrorWithCtx(ctx, "checkGroupPackage auto package days unable less than 7 groupPack:%v", groupPack)
			return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "自动续费套餐会员天数不能少于7天")
		}
	}
	return nil
}

func (m *Manager) pbGroupPack2dbPack(ctx context.Context, groupPack *pb.GroupPackage) *GroupPackage {
	dbPack := &GroupPackage{
		Name:          groupPack.GetName(),
		Desc:          groupPack.GetDesc(),
		PackageType:   uint32(groupPack.GetPackageType()),
		SubType:       uint32(groupPack.GetSubType()),
		PackageStatus: groupPack.GetPackageStatus(),
		OriginalPrice: groupPack.GetOriginalPrice(),
		Price:         groupPack.GetPrice(),
		Value:         groupPack.Value,
		Days:          groupPack.GetDays(),
		AutoValue:     0,
		Operator:      groupPack.GetOperator(),
		IsShowCoupon:  groupPack.GetIsShowCoupon(),
	}
	if groupPack.SubType == pb.PackageSubType_PackageSubType_AutoRenew {
		dbPack.AutoValue = groupPack.GetAutoValue()
	}
	return dbPack
}

func (m *Manager) checkGroupPackage(ctx context.Context, groupPack *pb.GroupPackage, appstoreProducts map[string]struct{}) (*GroupPackage, []*Package, []*UpgradePackage, error) {
	if len(groupPack.MarketList) == 0 {
		log.ErrorWithCtx(ctx, "AddGroupPackage market list is empty, groupPack:%v", groupPack)
		return nil, nil, nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "应用信息不能为空")
	}
	if groupPack.Days == 0 {
		log.ErrorWithCtx(ctx, "AddGroupPackage days is 0, groupPack:%v", groupPack)
		return nil, nil, nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "天数不能为0")
	}

	isUpgrade := false
	if groupPack.SubType == pb.PackageSubType_PackageSubType_UpgradeAll || groupPack.SubType == pb.PackageSubType_PackageSubType_UpgradeSVIP {
		isUpgrade = true
		err := m.checkPackageUpgrade(ctx, groupPack)
		if err != nil {
			return nil, nil, nil, err
		}
	} else {
		err := m.checkPackage(ctx, groupPack)
		if err != nil {
			return nil, nil, nil, err
		}
	}

	checkGroupMarkMap := make(map[string]bool)
	for _, marketInfo := range groupPack.MarketList {
		// 校验是否存在重复的分组标记
		groupMark := fmt.Sprintf("%d_%+v", marketInfo.MarketId, marketInfo.IsIos)
		if _, ok := checkGroupMarkMap[groupMark]; ok {
			log.ErrorWithCtx(ctx, "AddGroupPackage duplicate market:%v", groupPack)
			return nil, nil, nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("分组%+v配置出错，同分组同应用-系统只能有1条配置", groupPack.GroupId))
		} else {
			checkGroupMarkMap[groupMark] = true
		}
		if marketInfo.IsIos {
			if len(marketInfo.ProductId) == 0 {
				log.ErrorWithCtx(ctx, "AddGroupPackage product_id is empty:%v", groupPack)
				return nil, nil, nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "APPSTORE产品ID为空")
			}
			if _, ok := appstoreProducts[marketInfo.ProductId]; ok {
				log.ErrorWithCtx(ctx, "AddGroupPackage duplicate product_id:%v", groupPack)
				return nil, nil, nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("%+v-%+v与已有套餐重复", marketInfo.ProductId, utils.GetMarketName(marketInfo.MarketId)))
			}
			appstoreProducts[marketInfo.ProductId] = struct{}{}
			if groupPack.SubType == pb.PackageSubType_PackageSubType_AutoRenew {
				packConf := m.GetPackageByProductID(marketInfo.GetProductId())
				if nil != packConf {
					log.ErrorWithCtx(ctx, "AddPackage 已经存在相同的produceID套餐 %v", marketInfo.ProductId)
					return nil, nil, nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("已经存在相同的produceID套餐 %v", marketInfo.GetProductId()))
				}

				packConf = m.GetSvipPackageByProductID(marketInfo.GetProductId())
				if nil != packConf {
					log.ErrorWithCtx(ctx, "AddPackage 已经存在相同的produceID套餐 %v", marketInfo.ProductId)
					return nil, nil, nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("已经存在相同的productID套餐 %v", marketInfo.GetProductId()))
				}
			}
		}
	}

	dbGroupPack := m.pbGroupPack2dbPack(ctx, groupPack)
	packs := make([]*Package, 0)
	upgradePacks := make([]*UpgradePackage, 0)
	now := time.Now()
	if isUpgrade {
		for _, marketInfo := range groupPack.MarketList {
			pkg := &UpgradePackage{
				ProductId:          marketInfo.GetProductId(),
				Name:               groupPack.GetName(),
				Desc:               groupPack.GetDesc(),
				OriginalPrice:      groupPack.GetOriginalPrice(),
				Price:              groupPack.GetPrice(),
				Value:              groupPack.Value,
				Days:               groupPack.GetDays(),
				PackageStatus:      groupPack.GetPackageStatus(),
				Operator:           groupPack.GetOperator(),
				UpdateTs:           uint64(now.Unix()),
				DiscountPrice:      0,
				MarketId:           marketInfo.MarketId,
				UpgradePackageType: uint32(pb.UpgradePackageType_ENUM_UPGRADE_PACKAGE_TYPE_NORMAL),
				UpgradeAllConfig:   "",
				IsShowCoupon:       groupPack.GetIsShowCoupon(),
			}
			if marketInfo.IsIos {
				pkg.PayChList = "APPSTORE"
			} else {
				pkg.PayChList = "ALIPAY,WECHAT"
			}
			upgradePacks = append(upgradePacks, pkg)
		}
	} else {
		isAuto := int8(0)
		if groupPack.SubType == pb.PackageSubType_PackageSubType_AutoRenew {
			isAuto = 1
		}
		for _, marketInfo := range groupPack.MarketList {
			pkg := &Package{
				ProductId:     marketInfo.GetProductId(),
				Name:          groupPack.GetName(),
				Desc:          groupPack.GetDesc(),
				OriginalPrice: groupPack.GetOriginalPrice(),
				Price:         groupPack.GetPrice(),
				Value:         groupPack.Value,
				Auto:          isAuto,
				Days:          groupPack.GetDays(),
				PackageStatus: groupPack.GetPackageStatus(),
				AutoValue:     groupPack.GetAutoValue(),
				Operator:      groupPack.GetOperator(),
				UpdateTs:      uint64(now.Unix()),
				DiscountPrice: 0,
				MarketId:      marketInfo.MarketId,
				PackageType:   uint32(groupPack.PackageType),
				IsShowCoupon:  groupPack.IsShowCoupon,
			}
			if marketInfo.IsIos {
				pkg.PayChList = "APPSTORE"
			} else {
				pkg.PayChList = "ALIPAY,WECHAT"
			}
			packs = append(packs, pkg)
		}
	}

	return dbGroupPack, packs, upgradePacks, nil
}

// AddGroupPackage 添加分组套餐
func (m *Manager) AddGroupPackage(ctx context.Context, groupPack *pb.GroupPackage) error {
	appstoreProducts := make(map[string]struct{})
	dbGroupPack, packs, upgradePacks, err := m.checkGroupPackage(ctx, groupPack, appstoreProducts)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddGroupPackage checkGroupPackage failed, err:%v", err)
		return err
	}

	err = m.Store.AddGroupPackage(ctx, dbGroupPack, packs, upgradePacks)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddGroupPackage failed, err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "添加分组套餐失败")
	}
	log.InfoWithCtx(ctx, "AddGroupPackage success, groupPack:%v", groupPack)
	return nil
}

func (m *Manager) BatchAddGroupPackage(ctx context.Context, groupList []*pb.GroupPackage) error {
	if len(groupList) == 0 {
		log.ErrorWithCtx(ctx, "BatchAddGroupPackage group list is empty")
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "分组套餐列表为空")
	}
	type DbGp struct {
		gp           *GroupPackage
		packs        []*Package
		upgradePacks []*UpgradePackage
	}
	appstoreProducts := make(map[string]struct{})
	dbGpList := make([]*DbGp, 0)
	for _, group := range groupList {
		dbGroupPack, packs, upgradePacks, err := m.checkGroupPackage(ctx, group, appstoreProducts)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchAddGroupPackage checkGroupPackage failed, err:%v", err)
			return err
		}
		dbGpList = append(dbGpList, &DbGp{
			gp:           dbGroupPack,
			packs:        packs,
			upgradePacks: upgradePacks,
		})
	}

	for _, dbGp := range dbGpList {
		err := m.Store.AddGroupPackage(ctx, dbGp.gp, dbGp.packs, dbGp.upgradePacks)
		if err != nil {
			log.ErrorWithCtx(ctx, "BatchAddGroupPackage failed, err:%v", err)
			return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "添加分组套餐失败")
		}
	}
	log.InfoWithCtx(ctx, "BatchAddGroupPackage success, groupList:%v", groupList)
	return nil
}

func (m *Manager) CheckBatchAddGroupPackage(ctx context.Context, groupList []*pb.GroupPackage) error {
	appstoreProducts := make(map[string]struct{})
	for _, group := range groupList {
		_, _, _, err := m.checkGroupPackage(ctx, group, appstoreProducts)
		if err != nil {
			var sErr protocol.ServerError
			if errors.As(err, &sErr) {
				err = protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, group.Name+" "+sErr.Message())
			}
			log.ErrorWithCtx(ctx, "CheckBatchAddGroupPackage checkGroupPackage failed, err:%v", err)
			return err
		}
	}
	return nil
}

func (m *Manager) GetGroupPackage(ctx context.Context, groupId uint32) (*pb.GroupPackage, error) {
	dbPack, err := m.Store.GetGroupPackage(ctx, groupId)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupPackage failed, groupId:%d, err:%v", groupId, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取分组套餐失败")
	}
	if dbPack == nil {
		log.ErrorWithCtx(ctx, "GetGroupPackage failed, groupId:%d, not found", groupId)
		return nil, nil
	}

	groupPack := m.dbGroupPkgToPbGroupPkg(dbPack)
	marketInfos, err := m.Store.GetPackageIdsByGroupId(ctx, dbPack.Id, dbPack.PackageType, dbPack.SubType)
	if err != nil {
		log.ErrorWithCtx(ctx, "GetGroupPackage GetPackageIdsByGroupId failed, groupId:%d, dbPack:%v err:%v", dbPack.Id, dbPack, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取分组套餐失败")
	}

	for _, info := range marketInfos {
		groupPack.MarketList = append(groupPack.MarketList, &pb.MarketInfo{
			MarketId:  info.MarketId,
			IsIos:     len(info.ProductId) > 0,
			ProductId: info.ProductId,
			PackageId: info.PackageId,
		})
	}
	log.DebugWithCtx(ctx, "GetGroupPackage success, groupPack:%v", groupPack)
	return groupPack, nil
}

// dbGroupPkgToPbGroupPkg 数据库分组套餐转pb
func (m *Manager) dbGroupPkgToPbGroupPkg(dbPack *GroupPackage) *pb.GroupPackage {
	return &pb.GroupPackage{
		GroupId:       dbPack.Id,
		Name:          dbPack.Name,
		Desc:          dbPack.Desc,
		PackageType:   pb.PackageType(dbPack.PackageType),
		SubType:       pb.PackageSubType(dbPack.SubType),
		PackageStatus: dbPack.PackageStatus,
		OriginalPrice: dbPack.OriginalPrice,
		Price:         dbPack.Price,
		Value:         dbPack.Value,
		Days:          dbPack.Days,
		AutoValue:     dbPack.AutoValue,
		Operator:      dbPack.Operator,
		IsShowCoupon:  dbPack.IsShowCoupon,
		MarketList:    make([]*pb.MarketInfo, 0),
	}
}

func (m *Manager) ModifyGroupPackage(ctx context.Context, groupPack *pb.GroupPackage) error {
	dbPack, err := m.Store.GetGroupPackage(ctx, groupPack.GroupId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage GetGroupPackage failed, groupId:%d, err:%v", groupPack.GroupId, err)
		return err
	}
	if dbPack == nil {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage GetGroupPackage failed, groupId:%d, not found", groupPack.GroupId)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "分组套餐不存在")
	}
	if len(groupPack.Name) == 0 {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage name is empty, pack:%v", groupPack)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "套餐名称不能为空")
	}

	// 优惠券校验
	if groupPack.GetOriginalPrice() == 0 && groupPack.GetIsShowCoupon() {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage original price unable be zero groupPack:%v", groupPack)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "勾选优惠券后，原价不能为0")
	}
	if groupPack.GetOriginalPrice() != 0 && groupPack.GetOriginalPrice() <= dbPack.Price {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage original price unable less than price groupPack:%v", groupPack)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "原价必须大于售价")
	}

	dbPack.Name = groupPack.Name
	dbPack.OriginalPrice = groupPack.OriginalPrice
	dbPack.IsShowCoupon = groupPack.IsShowCoupon
	dbPack.Desc = groupPack.Desc
	dbPack.Operator = groupPack.Operator
	err = m.Store.ModifyGroupPackage(ctx, dbPack)
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage failed, groupPack:%v, err:%v", err, groupPack)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "修改分组套餐失败")
	}
	log.InfoWithCtx(ctx, "ModifyGroupPackage success, groupPack:%v", groupPack)
	return nil
}

func (m *Manager) SetGroupPackageStatus(ctx context.Context, groupId, packageStatus uint32, operator string) error {
	dbPack, err := m.Store.GetGroupPackage(ctx, groupId)
	if err != nil {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage GetGroupPackage failed, groupId:%d, err:%v", groupId, err)
		return err
	}
	if dbPack == nil {
		log.ErrorWithCtx(ctx, "ModifyGroupPackage GetGroupPackage failed, groupId:%d, not found", groupId)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "分组套餐不存在")
	}
	if dbPack.PackageStatus == packageStatus {
		log.InfoWithCtx(ctx, "SetGroupPackageStatus status is same, groupId:%d, packageStatus:%d", groupId, packageStatus)
		return nil
	}
	// 检查套餐是否上架
	if packageStatus == uint32(pb.PackageStatus_ENUM_PACKAGE_STATUS_STOPING) {
		marketInfo, err := m.Store.GetPackageIdsByGroupId(ctx, dbPack.Id, dbPack.PackageType, dbPack.SubType)
		if err != nil {
			log.ErrorWithCtx(ctx, "SetGroupPackageStatus GetPackageIdsByGroupId failed, groupId:%d, err:%v", groupId, err)
			return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取分组套餐失败")
		}
		if len(marketInfo) > 0 {
			salesPackages, err := m.Store.GetInEffectSalePackageListAll(ctx, dbPack.PackageType)
			if err != nil {
				log.ErrorWithCtx(ctx, "SetGroupPackageStatus GetInEffectSalePackageListAll failed, packageType:%d, err:%v", dbPack.PackageType, err)
				return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取套餐列表失败")
			}
			allSalesMap := make(map[uint32]*SalePackage)
			for _, salePack := range salesPackages {
				allSalesMap[salePack.PackageId] = salePack
			}
			for _, info := range marketInfo {
				if _, ok := allSalesMap[info.PackageId]; ok {
					log.ErrorWithCtx(ctx, "SetGroupPackageStatus package already in sale, groupId:%v, packageId:%d", groupId, info.PackageId)
					return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("存在上架的套餐ID:%d，不能停用", info.PackageId))
				}
			}
		}
	}
	err = m.Store.SetGroupPackageStatus(ctx, groupId, dbPack.PackageType, dbPack.SubType, packageStatus, operator)
	if err != nil {
		log.ErrorWithCtx(ctx, "SetGroupPackageStatus failed, groupId:%d, packageStatus:%d, err:%v", groupId, packageStatus, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "修改分组套餐状态失败")
	}
	log.InfoWithCtx(ctx, "SetGroupPackageStatus success, groupId:%d, packageStatus:%d", groupId, packageStatus)
	return nil
}

// AddGroup 新增分组
func (m *Manager) AddGroup(ctx context.Context, packageType, subType uint32, marketInfos []*pb.MarketInfo, operator string) error {
	if len(marketInfos) < 2 {
		log.ErrorWithCtx(ctx, "AddGroup market list is empty, packageType:%v", packageType)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "应用信息至少选择两个")
	}
	packageIds := make([]uint32, 0)
	for _, marketInfo := range marketInfos {
		if marketInfo.PackageId == 0 {
			log.ErrorWithCtx(ctx, "AddGroup package_id is empty:%v", marketInfo)
			return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "套餐ID为空")
		}
		packageIds = append(packageIds, marketInfo.PackageId)
	}
	// 检查套餐关键字段是否一致
	groupPack, err := m.checkGroupAndPackagesSame(ctx, packageType, subType, nil, packageIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddGroup checkGroupAndPackagesSame failed, packageType:%v, subType:%v, marketInfo:%v, err:%v", packageType, subType, marketInfos, err)
		return err
	}
	groupPack.Operator = operator
	err = m.Store.AddGroup(ctx, groupPack, packageIds)
	if err != nil {
		log.ErrorWithCtx(ctx, "AddGroup failed, err:%v", err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "添加分组失败")
	}
	log.InfoWithCtx(ctx, "AddGroup success, groupPack:%v, packageIds:%v", groupPack, packageIds)
	return nil
}

// checkGroupAndPackagesSame 检查分组和套餐列表关键信息是否一致
func (m *Manager) checkGroupAndPackagesSame(ctx context.Context, packageType, subType uint32, groupPack *GroupPackage, packageIds []uint32) (*GroupPackage, error) {
	if subType == uint32(pb.PackageSubType_PackageSubType_UpgradeSVIP) {
		packs, err := m.Store.GetUpgradePackageListByPids(ctx, packageIds)
		if err != nil {
			log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame GetUpgradePackageListByPids failed, packageIds:%v, err:%v", packageIds, err)
			return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取升级套餐失败")
		}
		packsMap := make(map[uint32]*UpgradePackage)
		existPackageIds := make([]uint32, 0)
		for _, pack := range packs {
			if pack.GroupId > 0 {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package already in group, packageId:%d", pack.Id)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("套餐ID:%d已经在分组中", pack.Id))
			}
			if pack.PackageStatus != uint32(pb.PackageStatus_ENUM_PACKAGE_STATUS_USING) {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package status not using, pack:%v", pack)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("套餐ID:%d停用状态，不能加入分组", pack.Id))
			}
			existPackageIds = append(existPackageIds, pack.Id)
			packsMap[pack.Id] = pack
			if groupPack == nil { // 第一个套餐信息
				groupPack = &GroupPackage{
					Name:          pack.Name,
					Desc:          pack.Desc,
					PackageType:   packageType,
					SubType:       subType,
					PackageStatus: pack.PackageStatus,
					OriginalPrice: pack.OriginalPrice,
					Price:         pack.Price,
					Value:         pack.Value,
					Days:          pack.Days,
					IsShowCoupon:  pack.IsShowCoupon,
					Operator:      pack.Operator,
				}
				continue
			}
			if groupPack.Name != pack.Name || groupPack.Desc != pack.Desc || groupPack.OriginalPrice != pack.OriginalPrice || groupPack.Price != pack.Price ||
				groupPack.Value != pack.Value || groupPack.Days != pack.Days || groupPack.IsShowCoupon != pack.IsShowCoupon || uint32(pb.UpgradePackageType_ENUM_UPGRADE_PACKAGE_TYPE_NORMAL) != pack.UpgradePackageType || groupPack.PackageStatus != pack.PackageStatus {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package info not match, groupPack:%v, pack:%v", groupPack, pack)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "套餐信息不一致")
			}
		}

		if len(packs) != len(packageIds) {
			notExistPackIds := make([]string, 0)
			for _, pid := range packageIds {
				if _, ok := packsMap[pid]; !ok {
					notExistPackIds = append(notExistPackIds, fmt.Sprintf("%d", pid))
				}
			}
			log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package not found, packageIds:%v, notExistPackIds:%v", packageIds, notExistPackIds)
			return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("套餐ID(%s)不存在", strings.Join(notExistPackIds, ",")))
		}

	} else {
		packsMap := make(map[uint32]*Package)
		existPackageIds := make([]uint32, 0)
		var packs []*Package
		var err error
		if packageType == uint32(pb.PackageType_ENUM_PACKAGE_TYPE_SVIP) {
			packs, err = m.Store.GetSvipPackageListByPids(ctx, packageIds)
			if err != nil {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame GetSvipPackageListByPids failed, packageIds:%v, err:%v", packageIds, err)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取套餐失败")
			}
		} else {
			packs, err = m.Store.GetPackageListByPids(ctx, packageIds)
			if err != nil {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame GetPackageListByPids failed, packageIds:%v, err:%v", packageIds, err)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取套餐失败")
			}
		}
		for _, pack := range packs {
			if pack.GroupId > 0 {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package already in group, packageId:%d", pack.Id)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("套餐ID:%d已经在分组中", pack.Id))
			}
			existPackageIds = append(existPackageIds, pack.Id)
			packsMap[pack.Id] = pack
			if groupPack == nil {
				groupPack = &GroupPackage{
					Name:          pack.Name,
					Desc:          pack.Desc,
					PackageType:   packageType,
					SubType:       subType,
					PackageStatus: pack.PackageStatus,
					OriginalPrice: pack.OriginalPrice,
					Price:         pack.Price,
					Value:         pack.Value,
					AutoValue:     pack.AutoValue,
					Days:          pack.Days,
					IsShowCoupon:  pack.IsShowCoupon,
					Operator:      pack.Operator,
				}
				continue
			}
			if groupPack.Name != pack.Name || groupPack.Desc != pack.Desc || groupPack.OriginalPrice != pack.OriginalPrice || groupPack.Price != pack.Price || groupPack.Value != pack.Value || groupPack.Days != pack.Days ||
				groupPack.AutoValue != pack.AutoValue || groupPack.PackageStatus != pack.PackageStatus || groupPack.IsShowCoupon != pack.IsShowCoupon {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package info not match, firstInfo:%v, pack:%v", groupPack, pack)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "套餐信息不一致")
			}

			if pack.Auto == 1 && groupPack.SubType != uint32(pb.PackageSubType_PackageSubType_AutoRenew) {
				log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package info not match, firstInfo:%v, pack:%v", groupPack, pack)
				return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "套餐信息不一致")
			}
		}
		if len(packs) != len(packageIds) {
			notExistPackIds := make([]string, 0)
			for _, pid := range packageIds {
				if _, ok := packsMap[pid]; !ok {
					notExistPackIds = append(notExistPackIds, fmt.Sprintf("%d", pid))
				}
			}
			log.ErrorWithCtx(ctx, "checkGroupAndPackagesSame package not found, packageIds:%v, notExistPackIds:%v", packageIds, notExistPackIds)
			return groupPack, protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, fmt.Sprintf("套餐ID(%s)不存在", strings.Join(notExistPackIds, ",")))
		}
	}
	return groupPack, nil
}

// EditGroup 编辑分组
func (m *Manager) EditGroup(ctx context.Context, groupId uint32, marketInfos []*pb.MarketInfo, operator string) error {
	dbPack, err := m.Store.GetGroupPackage(ctx, groupId)
	if err != nil {
		log.ErrorWithCtx(ctx, "EditGroup GetGroupPackage failed, groupId:%d, err:%v", groupId, err)
		return err
	}
	if dbPack == nil {
		log.ErrorWithCtx(ctx, "EditGroup GetGroupPackage failed, groupId:%d, not found", groupId)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "分组不存在")
	}
	packageIds := make([]uint32, 0)
	packageMaps := make(map[uint32]*pb.MarketInfo)
	for _, marketInfo := range marketInfos {
		if marketInfo.PackageId == 0 {
			log.ErrorWithCtx(ctx, "EditGroup package_id is empty:%v", marketInfo)
			return protocol.NewExactServerError(nil, status.ErrSuperPlayerInvalidPara, "套餐ID为空")
		}
		packageIds = append(packageIds, marketInfo.PackageId)
		packageMaps[marketInfo.PackageId] = marketInfo
	}
	dbPack.Operator = operator
	if len(packageIds) <= 1 { //解散分组
		err = m.Store.DisbandGroup(ctx, dbPack)
		if err != nil {
			log.ErrorWithCtx(ctx, "EditGroup failed, groupId:%v err:%v", groupId, err)
			return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "解散分组失败")
		}
		log.InfoWithCtx(ctx, "EditGroup DisbandGroup success, groupId:%d", groupId)
		return nil
	}

	markInfos, err := m.Store.GetPackageIdsByGroupId(ctx, groupId, dbPack.PackageType, dbPack.SubType)
	if err != nil {
		log.ErrorWithCtx(ctx, "EditGroup GetPackageIdsByGroupId failed, groupId:%d, err:%v", groupId, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取分组套餐失败")
	}
	existMarketInfos := make(map[uint32]*MarketInfo)
	for _, info := range markInfos {
		existMarketInfos[info.PackageId] = info
	}

	needAddPkgId := make([]uint32, 0)
	needDelPkgId := make([]uint32, 0)
	for _, pid := range packageIds {
		if _, ok := existMarketInfos[pid]; ok {
			continue
		}
		needAddPkgId = append(needAddPkgId, pid)
	}
	for pid := range existMarketInfos {
		if _, ok := packageMaps[pid]; !ok {
			needDelPkgId = append(needDelPkgId, pid)
		}
	}
	if len(needAddPkgId) > 0 {
		// 检查分组和套餐关键字段是否一致
		_, err = m.checkGroupAndPackagesSame(ctx, dbPack.PackageType, dbPack.SubType, dbPack, needAddPkgId)
		if err != nil {
			log.ErrorWithCtx(ctx, "EditGroup checkGroupAndPackagesSame failed, dbPack:%v, marketInfo:%v, err:%v", dbPack, marketInfos, err)
			return err
		}
	}
	err = m.Store.EditGroup(ctx, dbPack, needAddPkgId, needDelPkgId)
	if err != nil {
		log.ErrorWithCtx(ctx, "EditGroup failed, groupId:%v err:%v", groupId, err)
		return protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "编辑分组失败")
	}
	log.InfoWithCtx(ctx, "EditGroup success, groupId:%d, addPkg:%v, delPkg:%v", groupId, needAddPkgId, needDelPkgId)
	return nil
}

// BatchGetGroupPackage 批量获取分组套餐
func (m *Manager) BatchGetGroupPackage(ctx context.Context, pkgType uint32, keyword string) ([]*pb.GroupPackage, error) {
	log.DebugWithCtx(ctx, "BatchGetGroup start, pkgType:%v, keyword:%v", pkgType, keyword)
	// 获取分组套餐信息
	groups, err := m.Store.BatchGetGroupByKeyword(ctx, pkgType, keyword, uint32(pb.PackageStatus_ENUM_PACKAGE_STATUS_USING))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGroupPackage BatchGetGroupByKeyword failed, err:%v", err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取分组套餐失败")
	}
	log.InfoWithCtx(ctx, "BatchGetGroupPackage success, groups:%v", groups)

	// 转化并统计结果
	groupIds := make([]uint32, 0)
	for _, group := range groups {
		groupIds = append(groupIds, group.Id)
	}

	if len(groupIds) == 0 {
		log.DebugWithCtx(ctx, "BatchGetGroupPackage success, pkgType:%v, keyword:%v, result length:0", pkgType, keyword)
		return []*pb.GroupPackage{}, nil
	}

	// 查询分组套餐关联的套餐信息
	pkgMarketInfoMap, err := m.Store.GetPackageIdsByGroupIds(ctx, groupIds, pkgType, uint32(pb.PackageSubType_PackageSubType_Normal))
	if err != nil {
		log.ErrorWithCtx(ctx, "BatchGetGroupPackage GetPackageIdsByGroupIds failed, groupIds:%v, err:%v", groupIds, err)
		return nil, protocol.NewExactServerError(nil, status.ErrSuperPlayerSysErr, "获取分组套餐关联套餐失败")
	}

	// 填充套餐信息
	result := make([]*pb.GroupPackage, 0)
	for _, group := range groups {
		newPbGroup := m.dbGroupPkgToPbGroupPkg(group)
		pkgMarketInfos, ok := pkgMarketInfoMap[newPbGroup.GroupId]
		if !ok {
			log.WarnWithCtx(ctx, "BatchGetGroupPackage pkgMarketInfos not found, groupId:%d", newPbGroup.GroupId)
			continue
		}
		if len(pkgMarketInfos) == 0 {
			log.WarnWithCtx(ctx, "BatchGetGroupPackage pkgMarketInfos is empty, groupId:%d", newPbGroup.GroupId)
			continue
		}
		for _, info := range pkgMarketInfos {
			newPbGroup.MarketList = append(newPbGroup.MarketList, &pb.MarketInfo{
				MarketId:  info.MarketId,
				IsIos:     len(info.ProductId) > 0,
				ProductId: info.ProductId,
				PackageId: info.PackageId,
			})
		}
		result = append(result, newPbGroup)
	}

	log.DebugWithCtx(ctx, "BatchGetGroupPackage success, pkgType:%v, keyword:%v, result length:%v", pkgType, keyword, len(result))
	return result, nil
}
