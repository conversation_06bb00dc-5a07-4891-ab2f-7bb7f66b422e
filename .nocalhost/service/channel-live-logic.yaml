- name: channel-live-logic # 服务deploy名称
  serviceType: deployment
  containers:
    - name: service
      dev:
        ${_INCLUDE_:- ../include/service_dev_config_base.yaml | nindent 8}
        command:
          run: # run参数
            - go run ./services/channel-live-logic/main.go
            - --config=/config/channel-live-logic.json
            - --listen=:80
            - --log_level=debug
            - --with-small-process=true
          debug:
            - dlv # debug参数
            - --headless
            - --log
            - --listen :9999
            - --api-version 2
            - --accept-multiclient
            - debug
            - ./services/channel-live-logic/main.go
            - -- --config=/config/channel-live-logic.json
            - --listen=:80
            - --log_level=debug
            - --with-small-process=true
        hotReload: false
        sync:
          mode: "pattern"
          type: "send"
          filePattern:
            - "clients"
            - "protocol"
            - "pkg"
            - "go.mod"
            - "go.sum"
            - "services/channel-live-logic" # 服务代码目录
            - "services/ugc/common/event"
            - "services/helper-from-cpp"
            - "services/notify"
            - "services/runtime/v2"
            - "services/logic-grpc-gateway/gwcontext"
            - "services/logic-grpc-gateway/logicproto"
          ignoreFilePattern: [ ]
        portForward: []