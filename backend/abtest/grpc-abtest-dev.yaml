apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-abtest-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.abtest.AbtestLogic/
    rewrite:
      uri: /logic.AbtestLogic/
    delegate:
       name: abtest-logic-delegator-80
       namespace: quicksilver


