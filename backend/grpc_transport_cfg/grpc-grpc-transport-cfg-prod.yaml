apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-grpc-transport-cfg-logic
spec:
  gateways:
  - quicksilver/tt-api-gateway
  - quicksilver/tt-api-gateway-internal
  hosts:
  - apiv2.52tt.com
  http:
  - match:
    - uri:
        prefix: /ga.api.grpc_transport_cfg.GrpcTransportCfgLogic/
    route:
    - destination:
        host: grpc-transport-cfg-logic.quicksilver.svc.cluster.local
        port:
          number: 80
