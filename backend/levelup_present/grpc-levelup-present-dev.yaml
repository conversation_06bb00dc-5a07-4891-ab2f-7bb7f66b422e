apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-levelup-present-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - dev-apiv2.ttyuyin.com
  http:
  - match:
    - uri:
        prefix: /ga.api.levelup_present.LevelUpPresentLogic/
    rewrite:
      uri: /logic.LevelUpPresentLogic/
    route:
    - destination:
        host: levelup-present-logic.quicksilver.svc.cluster.local
        port:
          number: 80
