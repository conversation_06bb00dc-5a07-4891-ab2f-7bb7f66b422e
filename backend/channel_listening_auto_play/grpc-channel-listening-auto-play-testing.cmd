# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

32098:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 32098 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningLikeSongWYY
50800:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50800 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ChannelPlayStatus
50801:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50801 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/SwitchChannelPlay
50802:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50802 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/CutAutoModeSong
50803:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50803 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ReportChannelAutoSongProgress
50804:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50804 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ChannelRcmdMusicMenu
50805:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50805 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/SetVolume
50806:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50806 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/SetChannelPlayMode
50807:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50807 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/SetChannelPlayStatus
50808:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50808 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/SelectAutoPlayPlayMode
50809:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50809 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningChangePlayer
50810:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50810 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningGetPlayerStatus
50811:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50811 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningSearchSongByKey
50812:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50812 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningGetSongListByType
50813:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50813 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningGetSongListList
50814:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50814 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningGetPlayerUserInfo
50815:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50815 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningOrderSong
50816:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50816 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningGetPlayList
50817:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50817 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningGetSongResource
50818:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50818 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningReportRecord
50819:
api-route-configurator --etcd-endpoints go-etcd-test-1.ttyuyin.com:2379 create --id 50819 --source api/channel_listening_auto_play/grpc_channel_listening_auto_play.proto --lang go --method /ga.api.channel_listening_auto_play.ChannelListeningAutoPlayLogic/ListeningLogout
