# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36671:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36671 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/GetVirtualAvatarEntry
36672:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36672 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarList
36673:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36673 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarInUse
36674:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36674 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarInUse
36675:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36675 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarUseScope
36676:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36676 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarUseScope
36677:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36677 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/GetUserCurrMicAvatarType
36678:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36678 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/SetUserCurrMicAvatarType
36679:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36679 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/SetUserVirtualAvatarUseScopeV2
36680:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36680 --source api/virtual_avatar/grpc_virtual_avatar_logic.proto --lang go --method /ga.api.virtual_avatar.VirtualAvatarLogic/GetUserVirtualAvatarUseScopeV2
