# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

31400:
api-route-configurator --etcd-endpoints *************:2379 create --id 31400 --source api/numeric/grpc_numeric.proto --lang go --method /ga.api.numeric.NumericLogic/GetUserRichSwitch
31401:
api-route-configurator --etcd-endpoints *************:2379 create --id 31401 --source api/numeric/grpc_numeric.proto --lang go --method /ga.api.numeric.NumericLogic/SetUserRichSwitch
31410:
api-route-configurator --etcd-endpoints *************:2379 create --id 31410 --source api/numeric/grpc_numeric.proto --lang go --method /ga.api.numeric.NumericLogic/GetUserGloryRank
31415:
api-route-configurator --etcd-endpoints *************:2379 create --id 31415 --source api/numeric/grpc_numeric.proto --lang go --method /ga.api.numeric.NumericLogic/GetUserVipGiftPackageInfo
