apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  namespace: quicksilver
  name: grpc-sakura-logic
spec:
  gateways:
  - istio-system/apiv2-gateway
  - istio-system/apiv2-gateway-internal
  hosts:
  - '*'
  http:
  - match:
    - uri:
        prefix: /ga.api.sakura.Sakura/
    rewrite:
      uri: /logic.bots.Sakura/
    delegate:
       name: sakura-bot-delegator-80
       namespace: quicksilver


