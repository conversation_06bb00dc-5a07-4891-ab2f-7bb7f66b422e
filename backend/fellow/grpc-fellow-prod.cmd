# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

30661:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30661 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowList
30662:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30662 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowCandidateList
30663:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30663 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowCandidateInfo
30664:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30664 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/SendFellowInvite
30665:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30665 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowInviteInfoById
30666:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30666 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowInviteList
30667:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30667 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/HandleFellowInvite
30668:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30668 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/CheckFellowInvite
30669:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30669 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/UnlockFellowPosition
30672:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30672 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/CancelFellowInvite
30673:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30673 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowPoint
30674:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30674 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowPresentDetail
30675:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30675 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/SendFellowPresent
30676:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30676 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowInfoByUid
30677:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30677 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/SendChannelFellowInvite
30678:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30678 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/HandleChannelFellowInvite
30679:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30679 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/ChannelSendFellowPresent
30680:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30680 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetRoomFellowList
30681:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30681 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetAllChannelFellowInvite
30682:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30682 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetChannelFellowCandidateInfo
30683:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30683 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetOnMicFellowList
30684:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30684 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetSendInviteList
30685:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30685 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetRareConfig
30686:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30686 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/SetBindRelation
30687:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30687 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetChannelRareConfig
30688:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30688 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetRareList
30689:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30689 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/DelRare
30690:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30690 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/ChangeFellowBindType
30691:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30691 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowHouseInuseList
30692:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30692 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowHouseEntryInfo
30693:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30693 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetFellowCardInfo
30694:
api-route-configurator --etcd-endpoints 10.217.198.88:2379 create --id 30694 --source api/fellow/grpc_fellow.proto --lang go --method /ga.api.fellow.FellowLogic/GetExpiringFellowHouseList
