# This is a file of api route command
# 1st cmdID 2nd command
# 云测试、云开发环境允许直接修改api路由，将命令行的create 换成force-update 即可
# 但是线上不允许通过force-update修改！

36611:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36611 --source api/glory_world/grpc_glory_world.proto --lang go --method /ga.api.glory_world.GloryWorldLogic/GetGloryEnterInfo
36612:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36612 --source api/glory_world/grpc_glory_world.proto --lang go --method /ga.api.glory_world.GloryWorldLogic/ReceiveReward
36613:
api-route-configurator --etcd-endpoints 10.112.205.39:2379 create --id 36613 --source api/glory_world/grpc_glory_world.proto --lang go --method /ga.api.glory_world.GloryWorldLogic/GetFloatingLayerInfo
