syntax="proto2";

import "common/tlvpickle/skbuiltintype.proto";

package redpacket;

// 红包活动事件
message RedPacketActivityBase {
    required uint32 activity_id = 1;   // 红包活动事件ID 为0表示需要创建一个红包活动事件
    required string activity_name = 2; // 红包活动名称

	required string activity_begin_time = 3;   // 开始时间 类似 '2016-01-20 15:28:00' 这样的表示方式
	required string activity_end_time = 4;     // 结束时间
}


// 红包活动事件的阶段
// 阶段类型
enum REDPACKET_STAGE_TYPE {
	REDPACKET_STAGE_DEFAULT = 1;          // 默认阶段 即什么也不会干的阶段 或者其他不连续的空余时间阶段
	REDPACKET_STAGE_LOTTERY = 2;          // 执行抽奖阶段 两次抽奖阶段的间隔必须在10分钟以上
    REDPACKET_STAGE_MASS_GUILDBUFF = 3;   // 攒公会BUFF阶段
	REDPACKET_STAGE_PREPARE = 4;          // 准备阶段(意思就是其他阶段 即不是抽奖也不是攒BUFF的 但是又有特殊提示显示的某个时间段的阶段)
}

message RedPacketActivityStage {
	required uint32 stage_id = 1;      // 阶段ID 为0表示需要创建一个阶段信息
    required uint32 activity_id = 2;   // 该阶段对应的红包活动事件ID
	required uint32 stage_type  = 3;   // 阶段类型 see REDPACKET_STAGE_TYPE

	optional string stage_name = 4;         // 阶段名称
    optional string stage_begin_time = 5;   // 阶段开始时间  类似 '2016-01-20 15:28:00' 这样的表示方式
	optional string stage_end_time = 6;     // 阶段结束时间
	repeated string stage_ad_text_list = 7; // 阶段内的提示广告语
}

// 礼物
// 礼物类型
enum REDPACKET_GIFT_TYPE {
	REDPACKET_GIFT_NOMAL = 1;          // 普通礼物
	REDPACKET_GIFT_DEBRIS = 2;         // 碎片型礼物
    REDPACKET_GIFT_VOUCHER = 3;        // 代金券
	REDPACKET_GIFT_REDDIAMOND = 4;     // 红钻
	REDPACKET_GIFT_GUILDSHORTID = 5;   // 公会短位ID使用权
	REDPACKET_GIFT_MEDAL = 6;          // 勋章
}
// 礼物支持的平台类型
enum REDPACKET_GIFT_PLATFORM_TYPE {
	REDPACKET_GIFT_PLATFORM_ALL = 1;         //全平台
    REDPACKET_GIFT_PLATFORM_ANDROID = 2;     //安卓
    REDPACKET_GIFT_PLATFORM_IOS     = 3;     //苹果
}

// 后台存储的礼物类型
message RedPacketGiftBase
{
	required uint32 gift_classify_id = 1;    // 礼物大类ID 为0表示需要创建一个礼物大类
	required string gift_classify_name = 2;  // 礼物大类名称 比如'10个Q币', 'TT公会4位短号', '传奇50元代金券', 'CF黄金M4枪'
	required uint32 gift_type = 3;           // 礼物大类类型 see REDPACKET_GIFT_TYPE
	required uint32 gift_platform = 4;       // 礼物大类支持的平台类型 see REDPACKET_GIFT_PLATFORM_TYPE
    required uint32 split_cnt_pergift = 5;   // 每个礼物需要拆分数目(最小为1 最大为10),非碎片型礼物必须为1 碎片型礼物必须大于1, 一旦配置超过1 那么就会出现ABCD....这样的礼物碎片 集齐碎片就可以拿到一份礼物
	optional bytes gift_external_info = 6;   // 礼物大类附加说明 根据不同礼物类型可能是不同的PB结构体 etc:RedPacketGiftExter_Medal RedPacketGiftExter_RedDiamond RedPacketGiftExter_Debris
}

// 礼物扩展信息
message RedPacketGiftExter_Medal
{
	required uint32 medal_id = 1;
}

message RedPacketGiftExter_RedDiamond
{
	required uint32 diamond_cnt = 1;
}

message RedPacketGiftExter_Debris
{
	required string debris_desc = 1; // 碎片描述文本 比如集齐ABCD四个才能兑换
}

//*****************************************************************
//
// 通用接口
//
//*****************************************************************

message GetGiftsByIDListReq {
    repeated uint32 gift_id_list = 1;
}

message GetGiftsByIDListResp {
    repeated RedPacketGiftBase gift_list = 1;
}

message GetActivityByIdReq {
    required uint32 activity_id = 1;
}

message GetActivityByIdResp {
    required RedPacketActivityBase activity = 1;
}

//*****************************************************************
//
// 给运营后台使用的接口
//
//*****************************************************************


// 礼物配置
message RedPacketGift_Stock_ConfInf{
	required RedPacketGiftBase gift_base = 1;
	required uint32 gift_cnt = 2;            // 礼物数目
}

// 阶段礼物配置
message RedPacketGift_Stage_ConfInf{
	required uint32 gift_id = 1;          // 礼物ID
	required uint32 draw_limit_size = 2;  // 本礼物最多能在本阶段抽中的数量(如果不想在本阶段被抽中完整的礼物 可以填0, 对于碎片型的礼物 填0的话用户可以抽中分离的碎片但是抽不中合体的结果 )
}

message RedPacketActivity_Stage_ConfInf{
	required RedPacketActivityStage stage_info = 1;
	repeated RedPacketGift_Stage_ConfInf gift_info = 2;
}

// 创建/更新礼品
message CreateOrUpdateGiftReq {
    required RedPacketGiftBase gift = 1;    // 创建时, gift.gift_classify_id填0
}

message CreateOrUpdateGiftResp {
    required uint32 gift_classify_id = 1;
}

// 创建活动
message CreateRedPacketActivityReq {

	required RedPacketActivityBase activity_info = 1;
}

message CreateRedPacketActivityResp {
	required uint32 activity_id = 1;
}

// 修改活动
message ModifyRedPacketActivityReq {

	required RedPacketActivityBase activity_info = 1;
	repeated RedPacketActivity_Stage_ConfInf stage_info_list = 2;
}

message ModifyRedPacketActivityResp {
	required uint32 activity_id = 1;
	repeated uint32 stage_id_list = 2;
}

// 删除活动
message DelRedPacketActivityReq {
	required uint32 activity_id = 1;
}

message DelRedPacketActivityResp {
	required uint32 activity_id = 1;
}

// 增加阶段
message CreateRedPacketStageReq {

	required uint32 activity_id = 1;
	required RedPacketActivityStage stage = 2;
}

message CreateRedPacketStageResp {
	required uint32 stage_id = 1;
}

// 修改阶段
message ModifyRedPacketStageReq
{
	required uint32 activity_id = 1;
	required RedPacketActivityStage stage = 2;
}

message ModifyRedPacketStageResp
{
}

message ModifyRedPacketStageGiftConfReq {
    required uint32 activity_id = 1;
    required uint32 stage_id = 2;
    repeated RedPacketGift_Stage_ConfInf gift_info_list = 3;
}

message ModifyRedPacketStageGiftConfResp {

}

// 删除阶段
message DelRedPacketStageReq
{
	required uint32 activity_id = 1;
	required uint32 stage_id = 2;
}

message DelRedPacketStageResp
{
}

message GetRedPacketStageDetailReq
{
    required uint32 activity_id = 1;
    required uint32 stage_id = 2;
}

message GetRedPacketStageDetailResp
{
    optional RedPacketActivity_Stage_ConfInf stage_detail = 1;
}

// 增加礼物库存
message AddConfigGiftStoreReq
{
	required RedPacketGift_Stock_ConfInf gift_info = 1; // 礼物ID为0表示新建一种礼物类型
}

message AddConfigGiftStoreResp
{
	required uint32 gift_id = 1;
}

// 修改礼物库存数目
message SetConfigGiftStoreSizeReq
{
	required uint32 gift_id = 1;
	required uint32 gift_store_size = 2;
}
message SetConfigGiftStoreSizeResp
{
	required uint32 gift_id = 1;
	required uint32 gift_store_size = 2;
}

// 获取当前全部库存礼物列表
message GetAllConfigStoreGiftListReq
{
}
message GetAllConfigStoreGiftListResp
{
	repeated RedPacketGift_Stock_ConfInf gift_list = 1;
}

// 将礼物分配给活动(已经分配给活动的礼物在活动结束之前不能回收)
message DistributGiftToActivityReq
{
	required uint32 activity_id = 1;
	repeated RedPacketGift_Stock_ConfInf gift_list = 2; // PS 如果活动内已经有相同礼物 那么这就是增加数量
}
message DistributGiftToActivityResp
{
}

// 获取已经分配给指定活动的礼物列表
message GetActivityDistributedGiftListReq
{
	required uint32 activity_id = 1;
}
message GetActivityDistributedGiftListResp
{
	required uint32 activity_id = 1;
	repeated RedPacketGift_Stock_ConfInf gift_list = 2;
}

// 获取全部配置的活动列表
message GetAllConfigRedPacketActivityListReq
{
}

message GetAllConfigRedPacketActivityListResp
{
	repeated RedPacketActivityBase activity_list = 1;
}

// 获取指定活动的全部配置的阶段列表
message GetRedPacketActivityAllConfigStageListReq {

	required uint32 activity_id = 1;
}

message GetRedPacketActivityAllConfigStageListResp {
	repeated RedPacketActivityStage stage_list = 1;
}

//*****************************************************************
//
// 给Logic的接口
//
//*****************************************************************

// 一个完整的活动结构体
message RedPacketActivityDetail {

	required RedPacketActivityBase activity_base = 1;
	repeated RedPacketActivityStage stage_list =2;
}

// 获取当前有效活动详细信息列表
message GetAllActivityDetailListReq
{
}

message GetAllActivityDetailListResp
{
	repeated RedPacketActivityDetail activity_detail_list = 1;
}

// 用户取到的存储的礼物类型 与 RedPacketGiftBase 的区别主要在于碎片型礼物需要在完整礼物后面加上X号碎片
message UserRedPacketGift
{
	required uint32 user_gift_id = 1;        // 用户礼物ID 与用户ID一起作为KEY （注意与礼物ID不是同一个意思）
	required string user_gift_name = 2;      // 礼物名称 比如'10个Q币', 'TT公会4位短号', 'CF黄金M4枪A号碎片'
	required uint32 gift_type = 3;           // 礼物类型 see REDPACKET_GIFT_TYPE
	required uint32 gift_platform = 4;       // 礼物支持的平台类型 see REDPACKET_GIFT_PLATFORM_TYPE
	optional bytes gift_external_info = 5;   // 礼物附加说明 根据不同礼物类型可能是不同的PB结构体 etc:RedPacketGiftExter_Medal RedPacketGiftExter_RedDiamond RedPacketGiftExter_Debris
	optional bool   voucher_is_cash = 6;     // 代金券是否已经兑换(不用于客户端显示 只用于后台记录)
	optional string voucher_cash_info = 7;   // 代金券兑换信息
    optional uint32 timestamp = 8;           // 中奖时间
}

// 抽红包
message DrawLotteryReq
{
	required uint32 uid = 1;
	required uint32 combo_size = 2;
	required uint32 guild_id = 3;
	required bool is_guild_owner = 4;   // 是否是会长
	required uint32 platform = 5;       // 礼物支持的平台类型 see REDPACKET_GIFT_PLATFORM_TYPE
}

message DrawLotteryResp
{
	optional UserRedPacketGift user_gift = 1; // 如果中奖才会有数据返回
}

// 攒BUFF
message MassGuildBuffReq
{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
}

message MassGuildBuffResp
{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
	required uint32 buff_value = 3;  // 公会BUFF value 最小为0 最大1000 用于百分比显示 精确到小数点后1位
}

// 获取公会抽奖属性(根据阶段如果是攒BUFF和抽奖阶段返回BUFF值 如果是其他阶段返回上一阶段的BUFF值和BUFF全网排名)
message GetGuildLotteryBuffReq
{
	required uint32 uid = 1;
	required uint32 guild_id = 2;
}

message GetGuildLotteryBuffResp
{
	required uint32 guild_id = 1;
	optional uint32 massbuff_member_size = 2; // 本阶段参与攒公会BUFF的公会成员人数(只在抽奖和攒BUFF阶段有值)
	optional uint32 buff_value = 3;           // 公会BUFF value 最小为0 最大1000 用于百分比显示 精确到小数点后1位
	optional uint32 buff_rank_value = 4;      // 公会BUFF排名(只在不是抽奖 以及 不是攒BUFF阶段有值)
	optional uint32 unity_value = 5;          // 活跃度 最小为0 最大1000 用于百分比显示 精确到小数点后1位(只在不是抽奖 以及 不是攒BUFF阶段可能有值)
	optional uint32 unity_rank_value = 6;     // 活跃度排名 (只在不是抽奖 以及 不是攒BUFF阶段可能有值)
	optional uint32 curr_activity_id = 7;     // 当前活动ID
	optional uint32 curr_stage_id    = 8;     // 当前阶段ID
	optional uint32 curr_stage_type  = 9;     // 当前阶段类型 see REDPACKET_STAGE_TYPE
}


// 获取我的奖品列表

message GetUserLotteryGiftListReq
{
	required uint32 uid = 1;
}

message GetUserLotteryGiftListResp
{
	required uint32 uid = 1;
	repeated UserRedPacketGift gift_list = 2; //
}

// 用户设置代金券兑换信息
message SetUserVoucherGiftInfoReq
{
	required uint32 uid = 1;
	required uint32 user_gift_id = 2;         // 用于唯一标识一个用户自己的礼物
	required string voucher_cash_info = 3;    // 代金券兑换信息 (游戏帐号 游戏区服 等信息)
}

message SetUserVoucherGiftInfoResp
{
	required uint32 uid = 1;
	optional UserRedPacketGift gift_info= 2; //
}

// 公会BUFF排行榜
message GuildRankStatInfo
{
	required uint32 guild_id = 1;
	optional uint32 unity_value = 2;  // 团结度最小为0 最大1000 表示千分之几 用于百分比显示 精确到小数点后1位
	optional uint32 buff_value = 3;   // 最大BUFF值 最小为0 最大1000 表示千分之几 用于百分比显示 精确到小数点后1位
	optional uint32 win_shortid_size = 4;   // 中了公会短位ID的数量
}

message GuildBuffTopN
{
	required string day_time = 1; // 日期 类似 '2016-01-20' 这样的表示方式
	repeated GuildRankStatInfo guildbuffstat_list = 2;
}

message BatchGetGuildBuffTopNReq
{
	required uint32 uid = 1;
	required string begin_day_time = 2; // 日期 类似 '2016-01-20' 这样的表示方式
	required string end_day_time = 3;   // 日期 类似 '2016-01-20' 这样的表示方式
	optional uint32 top_n_size = 4;     // 每个排行榜最多获取多个公会
}

message BatchGetGuildBuffTopNResp
{
	repeated GuildBuffTopN topn_list= 1;
}



//*****************************************************************
//
// 统计数据
//
//*****************************************************************

// 指定活动中已经抽中的礼物数

// 指定活动中在指定阶段已经被抽中的礼物数

// 指定活动中 各个阶段的参与用户数

// 指定活动中 公会在各个攒BUFF阶段的BUFF值和参与人数

//*****************************************************************
//
// 异步数据
//
//*****************************************************************

message AsyncUnityCalcNotify
{
    required uint32 activity_id = 1;
    optional uint32 single_stage_id = 2;
	optional uint32 type = 3; // 1=sigle_clac 算一个阶段的团结度 , 2=batch_clac 算一个阶段集合的最大团结度
	repeated uint32 batch_stage_id_list = 4;
	optional string day_time = 5; // 日期 类似 '2016-01-20' 这样的表示方式
}

service RedPacket {
    option( tlvpickle.Magic ) = 15280;

	// 创建活动（仅用于后台配置）
    rpc CreateRedPacketActivity( CreateRedPacketActivityReq ) returns( CreateRedPacketActivityResp ){
		option( tlvpickle.CmdID ) = 1;
        option( tlvpickle.OptString ) = "n:b:e:";
        option( tlvpickle.Usage ) = "-n <name> -b <begin time> -e <end time>";
	}

	// 修改活动（仅用于后台配置）
	rpc ModifyRedPacketActivity( ModifyRedPacketActivityReq ) returns( ModifyRedPacketActivityResp ){
		option( tlvpickle.CmdID ) = 2;
        option( tlvpickle.OptString ) = "n:d:b:e:";
        option( tlvpickle.Usage ) = "-n <id> -d <desc> -b <begin time> -e <end time>";
	}

	// 删除活动（仅用于后台配置）
	rpc DelRedPacketActivity( DelRedPacketActivityReq ) returns( DelRedPacketActivityResp ){
		option( tlvpickle.CmdID ) = 3;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <id> ";
	}

	// 创建阶段（仅用于后台配置）
    rpc CreateRedPacketStage( CreateRedPacketStageReq ) returns( CreateRedPacketStageResp ){
		option( tlvpickle.CmdID ) = 4;
        option( tlvpickle.OptString ) = "a:t:n:b:e:x:";
        option( tlvpickle.Usage ) = "-a <act_id> -t <type> -n <name> -b <begin time> -e <end time> -x <ad_list, split by |>";
	}

	// 修改阶段（仅用于后台配置）
	rpc ModifyRedPacketStage( ModifyRedPacketStageReq ) returns( ModifyRedPacketStageResp ){
		option( tlvpickle.CmdID ) = 5;
        option( tlvpickle.OptString ) = "a:s:t:n:b:e:x:";
        option( tlvpickle.Usage ) = "-a <act_id> -s <stage_id> -t <type> -n <name> -b <begin time> -e <end time> -x <ad_list, split by |>";
	}

	// 删除阶段（仅用于后台配置）
	rpc DelRedPacketStage( DelRedPacketStageReq ) returns( DelRedPacketStageResp ){
		option( tlvpickle.CmdID ) = 6;
        option( tlvpickle.OptString ) = "a:s:";
        option( tlvpickle.Usage ) = "-a <act_id> -s <stage_id>";
	}

    // 增加礼物库存（仅用于后台配置）
	rpc AddConfigGiftStore( AddConfigGiftStoreReq ) returns( AddConfigGiftStoreResp ){
		option( tlvpickle.CmdID ) = 7;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <id> ";
	}

	// 修改礼物库存数目（仅用于后台配置）
	rpc SetConfigGiftStoreSize( SetConfigGiftStoreSizeReq ) returns( SetConfigGiftStoreSizeResp ){
		option( tlvpickle.CmdID ) = 8;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <id> ";
	}

	// 获取当前全部库存礼物列表（仅用于后台配置）
	rpc GetAllConfigStoreGiftList( GetAllConfigStoreGiftListReq ) returns( GetAllConfigStoreGiftListResp ){
		option( tlvpickle.CmdID ) = 9;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <id> ";
	}

	// 将礼物分配给活动(已经分配给活动的礼物在活动结束之前不能回收)（仅用于后台配置）
	rpc DistributGiftToActivity( DistributGiftToActivityReq ) returns( DistributGiftToActivityResp ){
		option( tlvpickle.CmdID ) = 10;
        option( tlvpickle.OptString ) = "n:x:";
        option( tlvpickle.Usage ) = "-n <gift id> -x <activity id>";
	}

	// 获取已经分配给指定活动的礼物列表（仅用于后台配置）
	rpc GetActivityDistributedGiftList( GetActivityDistributedGiftListReq ) returns( GetActivityDistributedGiftListResp ){
		option( tlvpickle.CmdID ) = 11;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <activity id> ";
	}

	// 获取全部配置的活动列表（仅用于后台配置）
	rpc GetAllConfigRedPacketActivityList( GetAllConfigRedPacketActivityListReq ) returns( GetAllConfigRedPacketActivityListResp ){
		option( tlvpickle.CmdID ) = 12;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
	}

	// 获取指定活动的全部配置的阶段列表 （仅用于后台配置）
	rpc GetRedPacketActivityAllConfigStageList( GetRedPacketActivityAllConfigStageListReq ) returns( GetRedPacketActivityAllConfigStageListResp ){
		option( tlvpickle.CmdID ) = 13;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <act_id> ";
	}

	// Logic获取当前有效的全部活动列表
	rpc GetAllActivityDetailList( GetAllActivityDetailListReq ) returns( GetAllActivityDetailListResp ){
		option( tlvpickle.CmdID ) = 14;
        option( tlvpickle.OptString ) = "n:";
        option( tlvpickle.Usage ) = "-n <id> ";
	}

	// 抽红包
	rpc DrawLottery( DrawLotteryReq ) returns( DrawLotteryResp ){
		option( tlvpickle.CmdID ) = 15;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid> ";
	}

	// 攒BUFF
	rpc MassGuildBuff( MassGuildBuffReq ) returns( MassGuildBuffResp ){
		option( tlvpickle.CmdID ) = 16;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x <guild id>";
	}

	// 获取公会抽奖属性(根据阶段如果是攒BUFF和抽奖阶段返回BUFF值 如果是其他阶段返回上一阶段的BUFF值和BUFF全网排名)
	rpc GetGuildLotteryBuff( GetGuildLotteryBuffReq ) returns( GetGuildLotteryBuffResp ){
		option( tlvpickle.CmdID ) = 17;
        option( tlvpickle.OptString ) = "u:x:";
        option( tlvpickle.Usage ) = "-u <uid> -x <guild id>";
	}

	// 获取我的奖品列表
	rpc GetUserLotteryGiftList( GetUserLotteryGiftListReq ) returns( GetUserLotteryGiftListResp ){
		option( tlvpickle.CmdID ) = 18;
        option( tlvpickle.OptString ) = "u:";
        option( tlvpickle.Usage ) = "-u <uid>";
	}

	// 用户设置代金券兑换信息
	rpc SetUserVoucherGiftInfo( SetUserVoucherGiftInfoReq ) returns( SetUserVoucherGiftInfoResp ){
		option( tlvpickle.CmdID ) = 19;
        option( tlvpickle.OptString ) = "u:x:y:";
        option( tlvpickle.Usage ) = "-u <uid> -x <user_gift_id> -y <info>";
	}

    rpc CreateOrUpdateGift( CreateOrUpdateGiftReq ) returns( CreateOrUpdateGiftResp ) {
        option( tlvpickle.CmdID ) = 20;
        option( tlvpickle.OptString ) = "g:n:t:p:s:";
        option( tlvpickle.Usage ) = "-g <gift_id> -n <name> -t <type> -p <platform> -s <split>";
    }

    rpc GetGiftsByIDList( GetGiftsByIDListReq ) returns( GetGiftsByIDListResp ) {
        option( tlvpickle.CmdID ) = 21;
        option( tlvpickle.OptString ) = "g:";
        option( tlvpickle.Usage ) = "-g <gift_id_list_split_by_comma>";
    }

    rpc GetActivityById( GetActivityByIdReq ) returns( GetActivityByIdResp ) {
        option( tlvpickle.CmdID ) = 22;
        option( tlvpickle.OptString ) = "a:";
        option( tlvpickle.Usage ) = "-a <act_id>";
    }

    rpc GetStageDetail( GetRedPacketStageDetailReq ) returns( GetRedPacketStageDetailResp ) {
        option( tlvpickle.CmdID ) = 23;
        option( tlvpickle.OptString ) = "a:s:";
        option( tlvpickle.Usage ) = "-a <act_id> -s <stage_id>";
    }

    rpc ModifyRedPacketStageGiftConf( ModifyRedPacketStageGiftConfReq ) returns( ModifyRedPacketStageGiftConfResp ) {
        option( tlvpickle.CmdID ) = 24;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

	// 获取公会BUFF排行榜
	rpc BatchGetGuildBuffTopN( BatchGetGuildBuffTopNReq ) returns( BatchGetGuildBuffTopNResp ) {
        option( tlvpickle.CmdID ) = 25;
        option( tlvpickle.OptString ) = "";
        option( tlvpickle.Usage ) = "";
    }

}
