syntax="proto2";
package newyearbeat2019.async;

message UserAwardAsyncJobNotify
{
  required uint32 channel_id = 1;
  required uint32 game_id = 2;
  required uint32 award_uid = 3;
  required uint32 match_uid = 4;
  required uint32 award_type = 5;
  required string award_name = 6;
  required string award_url = 7;
  required bool award_is_limit =8;
  required uint32 game_time = 9;
}

message UserUnAwardAsyncJobNotify
{
  required uint32 channel_id = 1;
  required uint32 game_id = 2;
  required uint32 uid = 3;
}

message ActDateStatisAsyncJobNotify
{
  required uint32 channel_id = 1;
  required uint32 game_id = 2;
  required uint32 uid = 3;
  required uint32 beat_cnt = 4;
  required uint32 game_time = 5;
  required uint32 award_beat_num = 6;
}

message GameAllBroAsyncJobNotify
{
  required uint32 game_id = 1;
  required uint32 max_price_uid = 2;
  optional uint32 max_price_uid_room = 3;
}



message LotteryAwardAsync
{
  required uint32 uid = 1;
  required uint32 lottery_type = 2; // ELOTTERY_ITEM_TYPE
  required uint32 source_id = 3;
  required string source_key = 4;
  required uint32 effect_second = 5;
  
  optional uint32 lottery_item_pool_idx = 6;
  optional uint32 lottery_item_price = 7;
  optional uint32 lottery_item_real_price_sort = 8;

  optional uint32 lottery_game_period_id = 9;
  optional uint32 lottery_game_ts = 10;

  optional bool is_test_award =11;
  optional uint32 channel_id = 12;
  required uint32 game_time = 13;
}


message LotteryCntStat
{
	required string lottery_item_key = 1;
	required string lottery_item_name = 2;
	required uint32 lottery_item_cnt = 3;
	optional uint32 lottery_item_pool_idx = 4;
}

message LotteryStatNotifyAsync
{
	required uint32 game_period_id = 1;
	required bool is_test_evn = 2;
	repeated LotteryCntStat cnt_stat_list = 3;
	optional uint32 game_period_begints = 4;
	optional string game_name = 5;
}

message GameStatisDingNotifyAsync
{
  required uint32 game_id = 1;
  required uint32 game_time = 2;
  required bool is_test_evn = 3;
  optional string game_name = 4;
}

message GameAllBroPresentAsync
{
  required uint32 game_id =1;
  required uint32 uid = 2;
  required string prefix_msg = 3;
  required string content = 4;
}

