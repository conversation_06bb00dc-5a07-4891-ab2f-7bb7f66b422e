syntax="proto3";

import "bsoncxx/odm/descriptor.proto";

package ipc;

option (bsoncxx.odm.default_omit_empty) = false;

enum GenderPrefer {
    UNRESTRICTED = 0;               // 不限制
    ATLEAST_ONE_OPPOSITE_SEX = 1;   // 至少一名异性
}

message MatchOption {
    string game_name = 1;
    map<string, uint32> original_options = 2;
    uint32 zone = 3;                    // 服务器/大区
    uint32 tier = 4;                    // 段位
    uint32 mode = 5;                    // 模式（人数）
    uint32 gender_prefer = 6;           // 性别偏好
    uint32 game_id = 7;
    string match_message = 8;           // 匹配成功推送的消息
}

message SupplementInfo {
    uint32 channel_id = 1;  
    uint32 count = 2;
}

message QuickMatchInfo {
    string          session_id = 1 [ (bsoncxx.odm.field_name) = "_id" ]; 
    uint32          user_id = 2;    
    MatchOption     options = 3;
    uint64          starts_at = 4;
    uint64          expires_at = 5;
    uint32          gender = 6; //性别 0 女 1 男
    SupplementInfo  supplement_info = 7;
}

message CancelingSession {
    string          session_id = 1 [ (bsoncxx.odm.field_name) = "_id" ]; 
    uint32          user_id = 2;        
}