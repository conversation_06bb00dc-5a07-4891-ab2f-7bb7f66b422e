syntax = "proto3";

package ga.pia;

import "ga_base.proto";

option java_package = "com.yiyou.ga.model.proto";
option go_package = "golang.52tt.com/protocol/app/pia";


// 阶段类型
enum PiaPhaseType {
    PIA_PHASE_CLOSE = 0;              // 未开始/结束
    PIA_PHASE_ON_PLAY = 1;            // 开始
}



// 获取房间Pia戏开启状态
message GetChannelPiaStatusReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

message GetChannelPiaStatusResp {
    ga.BaseResp base_resp = 1;
    uint32 channel_id = 2;
    bool entry = 3;             // 是否有Pia入口
    bool is_open = 4;           // 房间当前是否开启Pia戏玩法
}

// 设置主持麦位
message SetCompereMicReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 mic = 3; // 麦位0-8
}

message SetCompereMicResp {
    ga.BaseResp base_resp = 1;
}

// 切换成Pia戏模式
message SetPiaSwitchReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    bool is_open = 3;           // 开启Pia戏玩法
}

message SetPiaSwitchResp {
    ga.BaseResp base_resp = 1;
}



// 获取剧本概要
message GetDramaReq {
    ga.BaseReq base_req = 1;
    repeated SearchOption search_option = 2;
    uint32 page_token = 3; // 分页游标 上一页的最后一个id, 第一页传0
    uint32 page_size = 4; // 单页条目数
}

// 剧本信息
message Drama {
    uint32 id = 1;                 //ID
    string title = 2;              //标题
    string cover_url = 3;          //封面url
    string author = 4;             //作者
    string desc = 5;               //简介描述
    uint32 male_cnt = 6;           //男性角色数量
    uint32 female_cnt = 7;         //女性角色数量
    uint32 word_cnt = 8;           //字数
    repeated string bgm_url = 9;   //背景音乐
    repeated string tags = 10;     //剧本标签(跟SearchOption是同一个东西)
    bool has_playing = 11;         // 是否有正在玩该剧本的房间
}

// 获取剧本概要
message GetDramaResp {
    ga.BaseResp base_resp = 1;
    repeated Drama drama_list = 2;  //剧本概要信息列表
}

// 获取筛选标签组
message GetSearchOptionGroupReq {
    ga.BaseReq base_req = 1;
}

// 筛选标签组
message SearchOptionGroup {
    string group_name = 1; // 组名
    repeated SubSearchOptionGroup sub_search_option_group = 2;
}

// 筛选标签子分组名
message SubSearchOptionGroup {
    uint32 search_type = 1; // 搜索项类型
    string group_name = 2; // 子组名
    repeated SearchOption search_option = 3;
    string display_format = 4; // 选择后显示的文案, 格式:{value}固定文本
    string display_separator = 5; // 分割符号, 多选时候使用
}

// 获取筛选标签组
message GetSearchOptionGroupResp {
    ga.BaseResp base_resp = 1;
    repeated SearchOptionGroup search_option_group = 2;
}

// 筛选项
message SearchOption {
    uint32 search_type = 1; // 搜索类型
    string label = 2; // 标签(search_type为0时为搜索框关键字)
}

// 获取当前房间Pia戏信息
message GetCurrentPiaInfoReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

// Pia戏信息, (广播类型见channel_.proto ChannelMsgType.PIA_INFO)
message PiaInfo {
    Drama drama = 1;           //剧本信息
    uint32 phase = 2;          //Pia戏阶段
    string progress = 3;       //剧本进度
    uint32 compere_mic = 4;    //主持麦位
    repeated string all_mic_progress = 5; //所有麦位的进度列表
}

// 获取当前房间Pia戏信息
message GetCurrentPiaInfoResp {
    ga.BaseResp base_resp = 1;
    PiaInfo pia_info = 2;
    string msg_for_screen = 3; // 公屏提示信息
}

// 设置Pia戏阶段
message SetPiaPhaseReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 drama_id = 3; // 剧本ID
    uint32 phase = 4; // pia戏阶段, 见PiaPhaseType
}

// 设置Pia戏阶段
message SetPiaPhaseResp {
    ga.BaseResp base_resp = 1;
}

// 设置Pia戏进度
message SetPiaProgressReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    string progress = 4; // pia戏进度
    uint32 op_mic = 5; // 操作麦位
}

// 设置Pia戏进度
message SetPiaProgressResp {
    ga.BaseResp base_resp = 1;
}

// 设置bgm进度
message SetBgmInfoReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    string bgm_info = 3;
}

// 设置bgm进度
message SetBgmInfoResp {
    ga.BaseResp base_resp = 1;
}

// 获取bgm进度
message GetBgmInfoReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
}

// 获取bgm进度
message GetBgmInfoResp {
    ga.BaseResp base_resp = 1;
    string bgm_info = 2;
}

// 获取正在玩的房间
message GetPlayingChannelReq {
    ga.BaseReq base_req = 1;
    uint32 drama_id = 2; // 剧本ID
}

// 获取正在玩的房间
message PlayingChannelInfo {
    uint32 channel_id = 1;              // 房间id
    string channel_name = 2;            // 房间名字
    uint32 channel_member_count = 3;    // 房间人数
    uint32 channel_display_id = 4;      // 房间的显示ID
    uint32 channel_type = 5;  // 房间类型
    string channel_icon = 6; // 房间图标的MD5
    string channel_desc = 7; // 房间话题的描述(标题)
    string channel_creator_account = 8; // 频道创建者account
    bool is_playing = 9; // 是否读本中
}

// 获取正在玩的房间
message GetPlayingChannelResp {
    ga.BaseResp base_resp = 1;
    repeated PlayingChannelInfo playing_channels = 2;
}

// Pia戏模式切换广播, 见channel_.proto ChannelMsgType.PIA_SWITCH
message PiaSwitch {
    bool is_open = 1; // 是否开启
    PiaInfo pia_info = 2; // 上一次的剧本信息, 开启时会返回
    string desc = 3;  // Pia戏切房提示
    string hint = 4; // pia戏提示
}

// 选本
message SelectDramaReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    uint32 drama_id = 3;
}

// 选本
message SelectDramaResp {
    ga.BaseResp base_resp = 1;
}

//========================聚合页======================


// 获取优质剧场列表-请求
message GetQualityDramaListReq {
    ga.BaseReq base_req = 1;
}
// 获取优质剧场列表-响应
message GetQualityDramaListResp {
    ga.BaseResp base_resp = 1;
    repeated QualityDrama list = 2;
    uint32 tab_id = 3; // tab id
}
// 获取排练列表-请求
message GetPracticeDramaListReq {
    ga.BaseReq base_req = 1;
    int32 page_size = 2; // 每页大小
    string page_token = 3;
    string channel_package_id = 4; // 渠道包id
    DramaRoomUserSex sex = 6;// 性别
}
// 获取排练列表-响应
message GetPracticeDramaListResp {
    ga.BaseResp base_resp = 1;
    repeated PracticeDrama list = 2;
    string next_page_token = 3; // 下一页token。如果为空，则表示没有下一页
    uint32 tab_id = 4; // tab id
}

// 剧本房间信息
message DramaRoom {
    uint32 id = 1;// 房间id
    string avatar = 2; // 房间头像
    string name = 3; // 房间名称
    string owner_avatar = 4; // 房主头像
    string owner_name = 5; // 房主名称
    int32 user_num = 6; // 房间人数
    int32 heat = 7;// 房间热度
    string tags = 8;// 房间标签
    int32 sex = 9; // 房主性别
    uint32 channel_bind_id = 10;
    uint32 channel_type = 11;
}
// 剧本信息
message DramaInfoForAggPage {
    uint32 id = 1;// 剧本id
    string name = 2;// 剧本名称
    string type = 3;// 剧本类型
    repeated string tags = 4;// 剧本标签
    string summary = 5;// 剧本简介
}
// 剧本房间的基础信息
message DramaRoomBaseInfo {
    enum PiaStage {
        PIA_STAGE_NONE = 0; // 未开启
        PIA_STAGE_SELECT_ROLE = 1; // 选角阶段
        PIA_STAGE_PLAYING = 2; // 演戏阶段
    }
    PiaPhaseType drama_phase = 1;// 剧本状态
    DramaRoom room_info = 2; // 房间信息
    DramaInfoForAggPage drama_info = 3;// 剧本信息
    uint32 tab_id = 4; // tab id
    PiaStage pia_stage = 5; // pia戏阶段
}
// 优质剧场房间信息
message QualityDrama {
    DramaRoomBaseInfo base_info = 1; // 基础信息
}
// 排练房间信息
message PracticeDrama {
    DramaRoomBaseInfo base_info = 1; // 基础信息
}

enum DramaRoomUserSex {
    unknown = 0;
    Sex_Male = 1;
    Sex_Female = 2;
}

//========================聚合页======================
//========================新pia戏玩法=================
//========================已点列表====================

// 已点列表，(广播类型见channel_.proto ChannelMsgType.PIA_DRAMA_ORDER_LIST)
message DramaOrderList {
    message UserOrderInfo {
        DramaSubInfo drama_sub_info = 1; // 剧本信息
        uint32 user_id = 2;// 用户id
        string user_name = 3;// 用户昵称
        string user_avatar = 4;// 用户头像
        uint32 user_sex = 5; //性别，0-女，1-男
        int64 index_id = 6; // 已点记录的id
        PiaDramaCreatorInfo creator_info = 7; // 创建者信息，可能是空
        bool is_copy_drama = 8; // 是否是副本
    }
    repeated UserOrderInfo list = 1;
    uint32 channel_id = 2;// 房间id
    int64 version = 3; // 版本号
}
// 点本请求
message OrderDramaReq {
    ga.BaseReq base_req = 1;
    uint32 drama_id = 2; // 剧本id
    uint32 channel_id = 3; // 房间id
}
// 点本响应
message OrderDramaResp {
    ga.BaseResp base_resp = 1;
    DramaOrderList order_list = 2; // 已点列表
}
// 获取已点列表请求
message GetOrderDramaListReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
}
// 获取已点列表响应
message GetOrderDramaListResp {
    ga.BaseResp base_resp = 1;
    DramaOrderList order_list = 2; // 已点列表
}
// 删除已点记录请求
message DeleteOrderDramaReq {
    ga.BaseReq base_req = 1;
    repeated int64 index_id_list = 2; // 剧本id
    uint32 channel_id = 3; // 房间id
}
// 删除已点记录响应
message DeleteOrderDramaResp {
    ga.BaseResp base_resp = 1;
    DramaOrderList order_list = 2; // 已点列表
}
//========================已点列表====================
//========================选角色======================

// 麦位绑定的角色列表，(广播类型见channel_.proto ChannelMsgType.PIA_MIC_ROLE_MAP)
message MicRoleMap {
    message RoleInfoList {
        repeated string id = 1; // 角色id
        int64 join_time = 2; // 加入时间戳
    }
    map<uint32, RoleInfoList> map = 1; // key:麦位id，value:角色列表
    int64 version = 2; // 版本号
    bool skip = 3; // 新版本是否跳过
}
// 选择角色请求
message PiaSelectRoleReq {
    ga.BaseReq base_req = 1;
    uint32 drama_id = 2; // 剧本id
    uint32 channel_id = 3; // 房间id，必传
    string role_id = 4; // 角色id，没有角色时可省略
    uint32 mic_number = 5; // 麦位号，必传
    int64 round_id = 6; // 场次id
}
// 选择角色响应
message PiaSelectRoleResp {
    ga.BaseResp base_resp = 1;
    MicRoleMap mic_role_map = 2; // 麦位对应的角色列表
}
// 角色取消选择请求
message PiaCancelSelectRoleReq {
    ga.BaseReq base_req = 1;
    uint32 drama_id = 2; // 剧本id
    uint32 channel_id = 3; // 房间id
    string role_id = 4; // 角色id，没有角色时可省略
    uint32 mic_number = 5; // 麦位号
    int64 round_id = 6; // 场次id
}
// 角色取消选择响应
message PiaCancelSelectRoleResp {
    ga.BaseResp base_resp = 1;
    MicRoleMap mic_role_map = 2; // 麦位对应的角色列表
}
//========================选角色======================
//========================走本操作====================

// 走本阶段
enum DramaPhase {
    DRAMA_PHASE_UNSPECIFIED = 0; // 未指定状态
    DRAMA_PHASE_SELECT_ROLE = 1; // 选角阶段
    DRAMA_PHASE_PLAY = 2; // 播放阶段
    DRAMA_PHASE_END = 3; // 结束阶段
    DRAMA_PHASE_PAUSE = 4; // 暂停阶段
}
// 操作类型
enum DramaOperationType {
    DRAMA_OPERATION_TYPE_UNSPECIFIED = 0; // 未指定类型
    DRAMA_OPERATION_TYPE_PLAY = 1; // 播放操作
    DRAMA_OPERATION_TYPE_PAUSE = 2; // 暂停操作
    DRAMA_OPERATION_TYPE_END = 3; // 结束操作
}
message ChannelDramaProgress {
    uint32 cur_index = 1;// 当前进度的段落下标
    int64 time_offset = 2; // 当前段落的时间偏移，单位秒，不一定可靠
}
// 房间剧本状态，(广播类型见channel_.proto ChannelMsgType.PIA_CHANNEL_DRAMA_STATUS)
message ChannelDramaStatus {
    DramaPhase drama_phase = 1;// 剧本状态
    uint32 channel_id = 2; // 房间id
    DramaV2 drama_info = 3; // 剧本信息
    int64 version = 4; // 版本号
    ChannelDramaProgress progress = 5; // 剧本进度
    int64 start_time = 6; // 剧本开始时间戳
    int64 round_id = 7;// 场次id
    bool skip = 8; // 是否跳过不处理当前的消息
    PiaChannelDramaPlayingType playing_type = 9; // 剧本的走本方式
    bool can_change_playing_type = 10; // 是否可以切换走本方式的开关
}
// 选本操作请求
message SelectDramaV2Req {
    ga.BaseReq base_req = 1;
    uint32 drama_id = 2; // 剧本id，必传
    uint32 channel_id = 3; // 房间id，必传
    int64 index_id = 4;// 已点记录id。如果是从已点列表选本的，则必传。
}
// 选本操作响应
message SelectDramaV2Resp {
    ga.BaseResp base_resp = 1;
    ChannelDramaStatus drama_status = 2; // 房间剧本状态
}
// 走本操作请求
message PiaOperateDramaReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    DramaOperationType operation_type = 3; // 操作类型
    uint32 drama_section_index = 4; // 剧本章节索引，播放操作必传
    uint32 delay_time = 5; // 延迟时间，告知服务端延后多少秒才开始走本
    int64 round_id = 6;// 场次id
}
// 走本操作响应
message PiaOperateDramaResp {
    ga.BaseResp base_resp = 1;
    ChannelDramaStatus drama_status = 2; // 房间剧本状态
}
// 当前房间走本详细信息
message ChannelDramaInfoDetail {
    uint32 channel_id = 1; // 房间id
    ChannelDramaStatus drama_status = 2; // 房间剧本状态
    DramaBgmStatus drama_bgm_status = 3; // 房间剧本bgm状态
    MicRoleMap mic_role_map = 4; // 麦位对应的角色列表
    DramaBgmVolStatus drama_bgm_vol_status = 5; // 房间剧本bgm音量状态
    DramaV2 origin_drama_info = 6; // 原始的剧本信息
}
// 获取当前房间走本详情请求
message PiaGetDramaStatusReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
}
// 获取当前房间走本详情响应
message PiaGetDramaStatusResp {
    ga.BaseResp base_resp = 1;
    ChannelDramaInfoDetail drama_info_detail = 2; // 房间走本详情
}

// 获取剧本副本ID请求
message PiaGetDramaCopyIdReq {
    ga.BaseReq base_req = 1;
    uint32 origin_drama_id = 2;  // 原本id
}
// 获取剧本副本ID响应
message PiaGetDramaCopyIdResp {
    ga.BaseResp base_resp = 1;
    uint32 copy_drama_id = 2;   // 副本id，如果不存在副本，则为0
}

// 生成剧本副本请求，如果剧本还未结束，则用当前时间结束剧本。
message PiaCreateDramaCopyReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 场次id
}
// 生成剧本副本响应
message PiaCreateDramaCopyResp {
    ga.BaseResp base_resp = 1;
    uint32 copy_drama_id = 2;   // 副本id
}

//========================走本操作====================
//========================BGM操作（没有时间轴的剧本）=====================

// BGM状态
enum DramaBGMPhase {
    DRAMA_BGM_PHASE_UNSPECIFIED = 0; // 未知状态
    DRAMA_BGM_PHASE_PLAY = 1; // 播放状态
    DRAMA_BGM_PHASE_PAUSE = 2; // 暂停状态
}
// BGM操作类型
enum DramaBGMOperationType {
    DRAMA_BGM_OPERATION_TYPE_UNSPECIFIED = 0; // 未知操作
    DRAMA_BGM_OPERATION_TYPE_PLAY = 1; // 播放操作
    DRAMA_BGM_OPERATION_TYPE_PAUSE = 2; // 暂停操作
}
// 剧本BGM状态，(广播类型见channel_.proto ChannelMsgType.PIA_CHANNEL_DRAMA_BGM_STATUS)
message DramaBgmStatus {
    string bgm_id = 1; // BGMid
    DramaBGMPhase bgm_phase = 2; // BGM状态
    int64 bgm_progress = 3;// BGM进度
    int64 version = 4; // 版本号
    int64 operation_time = 5;// 操作时间
}
// 剧本BGM音量状态，(广播类型见channel_.proto ChannelMsgType.PIA_CHANNEL_DRAMA_BGM_STATUS)
message DramaBgmVolStatus {
    int32 vol = 6; // 音量
}
// BGM操作请求
message PiaOperateBgmReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    DramaBGMOperationType operation_type = 3; // 操作类型
    string bgm_id = 4; // BGMid
    int64 cur_progress = 5; // 当前进度
    int64 next_progress = 6; // 下一阶段进度，播放操作必传
    int64 round_id = 7;// 场次id
}
// BGM操作响应
message PiaOperateBgmResp {
    ga.BaseResp base_resp = 1;
    DramaBgmStatus bgm_status = 2; // 剧本BGM状态
}
// BGM音量操作请求
message PiaOperateBgmVolReq {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    int32 vol = 3; // 音量
}
// BGM音量操作响应
message PiaOperateBgmVolResp {
    ga.BaseResp base_resp = 1;
    DramaBgmVolStatus bgm_vol_status = 2; // 剧本BGM音量状态
}
//========================BGM操作=====================

//======================新剧本========================

// pia戏角色
message PiaRole {
    string id = 1; // 唯一id
    string name = 2; // 角色名
    uint32 sex = 3; //性别，1-男，2-女
    string avatar = 4; // 链接
    string introduction = 5; // 简介
    string color = 6; // 角色颜色
    double dialogue_ratio = 7; // 对白占比
}

message PiaDuration {
    int64 begin_time = 1; // 开始时间（秒）
    int64 end_time = 2; // 结束时间（秒）
}

// 剧本内容（段落）
message PiaContent {
    string id = 1; // id
    string role_id = 2; // 角色id
    string dialogue = 3; // 对白
    PiaDuration duration = 4; // 时间轴
    string role_name = 5; // 角色名称
    string color = 6; // 颜色
    string style = 7; // 样式
    string dialogueV2 = 8; // 对白
}

message PiaPicture {
    string id = 1; // 唯一id
    string url = 2;  // 图片链接
    PiaDuration duration = 3;
}

message PiaBGM {
    string id = 1;
    string name = 2; // bgm名称
    string url = 3; // 音乐链接
    PiaDuration duration = 4;
    uint32 length = 5;// bgm长度
}


// 剧本子信息(概要信息)
message DramaSubInfo {
    uint32 id = 1;                 //ID
    string title = 2;              //标题
    string cover_url = 3;          //封面url
    string author = 4;             //作者
    string desc = 5;               //简介描述
    uint32 male_cnt = 6;           //男性角色数量
    uint32 female_cnt = 7;         //女性角色数量
    uint32 word_cnt = 8;           //字数
    repeated string tag_list = 9;     //剧本标签
    string type = 10;              // 剧本类型
    uint64 display_id = 11;         // 外显id
    uint32 author_id = 12;         // 作者id
    uint32 duration = 13;          // 剧本时长
    int64 create_time = 14;         // 创建时间
    uint32 related_drama_id = 15;  // 关联的原剧本id
    PiaPlayType play_type = 16;
}

// 带剧本状态的信息
message DramaInfoWithStatus {
    DramaSubInfo drama_sub_info = 1;
    bool has_play = 2; // 是否有正在玩该剧本的房间
    string stick_tag = 3; // 剧本置顶标签
}

// 播放类型
enum PiaPlayType {
    PLAY_TYPE_UNKNOWN = 0;
    PLAY_TYPE_AUTO = 1; // 时间轴播放
    PLAY_TYPE_MANUAL = 2; // 手动切
}

// 新剧本信息
message DramaV2 {
    DramaSubInfo drama_sub_info = 1; // 剧本子信息
    repeated PiaRole role_list = 2; // 角色列表
    repeated PiaContent content_list = 3; // 段落列表
    repeated PiaBGM bgm_url = 4;   //背景音乐列表
    repeated PiaPicture picture_list = 5; // 背景图片列表 保留字段
    PiaPlayType play_type = 6; // 播放类型
    PiaDramaCreatorInfo creator_info = 7; // 创建者信息，可能是空
    bool is_copy_drama = 8; // 是否是副本
}
//======================新剧本========================

//======================剧本库========================

// 获取筛选标签组 新版
message GetSearchOptionGroupV2Req {
    enum SearchOptionType {
        SEARCH_OPTION_TYPE_DRAMA = 0; // 剧本库
        SEARCH_OPTION_TYPE_COPY = 1; // 副本库
    }
    ga.BaseReq base_req = 1;
    SearchOptionType search_option_type = 2;
}
// 获取筛选标签组 新版
message GetSearchOptionGroupV2Resp {
    ga.BaseResp base_resp = 1;
    repeated SearchOptionGroup search_option_group = 2;
}

// 分页获取剧本库列表
message GetDramaListReq {
    ga.BaseReq base_req = 1;
    repeated SearchOption search_option = 2;
    string page_token = 3; // 分页token 第一页传空
    uint32 page_size = 4; // 单页条目数
    uint32 channel_id = 5; // 房间id
}

// 分页获取剧本库列表
message GetDramaListResp {
    ga.BaseResp base_resp = 1;
    repeated DramaInfoWithStatus drama_info_with_status_list = 2;  //剧本概要信息列表
    string page_token = 3; // 服务端返回的token,为空则无需后续请求
}

// 剧本详情
message GetDramaDetailByIdReq {
    ga.BaseReq base_req = 1;
    uint32 id = 2; // 剧本id
}

//用户uid、accout信息
message PiaUserInfo {
    uint32 uid = 1;
    string nickname = 2;
    string accout = 3; // 用户获取头像
    string username = 4;// 对应ID
    int32 sex = 5; // 性别，0-女，1-男
}

message GetDramaDetailByIdResp {
    ga.BaseResp base_resp = 1;
    DramaV2 drama = 2;
    uint32 collect_num = 3;     // 收藏量
    bool is_collected = 4;     // 是否收藏
    bool has_play = 5;  // 是否有房间在玩
    PiaUserInfo author_info = 6;      // 作者信息
}

// 在玩房间状态
enum PiaChannelStatusType {
    PIA_CHANNEL_STATUS_INVALID = 0; // 非法值
    PIA_CHANNEL_STATUS_PLAY = 1;  // 走本中
    PIA_CHANNEL_STATUS_WAIT = 2;  // 等你来pia
    PIA_CHANNEL_STATUS_HOT = 3;  // 热播中
}

// 获取正在玩的房间v2
message PlayingChannelInfoV2 {
    uint32 channel_id = 1;              // 频道id
    string channel_name = 2;            // 频道名字
    uint32 channel_member_count = 3;    // 频道人数(热度)
    uint32 channel_display_id = 4;      // 房间的显示ID
    uint32 channel_type = 5;  // 房间类型
    string channel_icon = 6; // 房间图标的MD5
    PiaChannelStatusType channel_status_type = 7;
    string channel_desc = 8; // 房间话题的描述(标题)
    string channel_creator_account = 9; // 频道创建者account
    uint32 channel_bind_id = 10; // 公会id
}

// 获取正在玩的房间v2
message GetPlayingChannelV2Req {
    ga.BaseReq base_req = 1;
    uint32 drama_id = 2;
    string page_token = 3; // 分页token 第一页传空
    uint32 page_size = 4; // 单页条目数
}

message GetPlayingChannelV2Resp {
    ga.BaseResp base_resp = 1;
    repeated PlayingChannelInfoV2 playing_channels = 2;
    string page_token = 3; // 请求下一页时带上,返回为空则分页请求结束
}


enum PiaRankingListType {
    PIA_RANKING_LIST_INVALID = 0;
    PIA_RANKING_LIST_COLLECT = 1;   // 收藏榜
    PIA_RANKING_LIST_RISE = 2;   // 上升榜
}

// 获取排行榜剧本列表Req
message PiaGetRankingListReq {
    ga.BaseReq base_req = 1;
    PiaRankingListType ranking_list_type = 2;
    string page_token = 3; // 分页token 第一页传空
    uint32 page_size = 4; // 单页条目数
}

// 获取排行榜剧本列表Resp
message PiaGetRankingListResp {
    ga.BaseResp base_resp = 1;
    repeated DramaInfoWithStatus drama_info_with_status_list = 2;  //剧本概要信息列表
    string page_token = 3; // 服务端返回的token,为空则无需后续请求
}

//======================剧本库========================


//======================收藏========================

enum CollectOpType {
    PIA_DRAMA_COLLECT_INVALID = 0;
    PIA_DRAMA_COLLECT_SELECT = 1;   // 收藏
    PIA_DRAMA_COLLECT_CANCEL = 2;   // 取消收藏
}

// 剧本收藏、取消收藏实现
message DoUserDramaCollectReq {
    ga.BaseReq base_req = 1;
    uint32 drama_id = 2;
    CollectOpType op = 3;  // 操作类型
}

message DoUserDramaCollectResp {
    ga.BaseResp base_resp = 1;
    uint32 collect_num = 2;     // 剧本收藏数量
    bool is_collected = 3;      // 收藏状态
}

// 查看用户的剧本收藏列表
message GetUserDramaCollectionReq {
    ga.BaseReq base_req = 1;
    repeated SearchOption search_option = 2;
    uint32 page_size = 3;   // 每页数量
    string page_token = 4;  // 服务端返回的page_token，第一页传默认零值
}

message GetUserDramaCollectionResp {
    ga.BaseResp base_resp = 1;
    repeated DramaInfoWithStatus drama_list = 2;
    string page_token = 3;  // 请求下一页时带上，返回空值时，没有下一页
}
//======================收藏========================


//======================切换玩法========================

message PiaChannelDramaPlayingType {
    enum PlayingTypeRole {
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_INVALID = 0;
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_NONE = 1; // 没角色
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_ROLE_HAS = 2; // 有角色
    }
    enum PlayingTypeTime {
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_INVALID = 0;
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_NONE = 1; // 没时间
        PIA_CHANNEL_DRAMA_PLAYING_TYPE_TIME_HAS = 2; // 有时间
    }
    repeated PlayingTypeRole support_playing_type_roles = 1; // 当前支持的角色类型
    repeated PlayingTypeTime support_playing_type_times = 2; // 当前支持的时间类型
    PlayingTypeRole current_playing_type_role = 3; // 当前玩法的角色类型
    PlayingTypeTime current_playing_type_time = 4; // 当前玩法的时间类型
}
// PiaChangePlayType 切换玩法的推送
message PiaChangePlayType {
    uint32 channel_id = 1; // 房间id
    int64 version = 2; // 版本号
    ChannelDramaProgress progress = 3; // 剧本进度
    int64 start_time = 4; // 剧本开始时间戳
    int64 round_id = 5;// 场次id
    PiaChannelDramaPlayingType playing_type = 6; // 剧本的走本方式
    MicRoleMap mic_role_map = 7; // 麦位对应的角色列表
    DramaBgmStatus bgm_status = 8; // 剧本的bgm状态
    DramaBgmVolStatus bgm_vol_status = 9; // 剧本的bgm音量状态
    DramaPhase drama_phase = 10;// 走本状态
}

// 剧本切换玩法请求
message PiaChangePlayTypeReq {
    ga.BaseReq base_req = 1;
    PiaChannelDramaPlayingType channel_drama_playing_type = 2; // 只要填写当前玩法即可，即current_xxxx
    uint32 channel_id = 3; // 房间id
    int64 round_id = 4; // 场次id
}
// 剧本切换玩法响应
message PiaChangePlayTypeResp {
    ga.BaseResp base_resp = 1;
    PiaChannelDramaPlayingType channel_drama_playing_type = 2; // 当前玩法的角色类型和时间类型
}

//======================切换玩法========================

//======================我的参演记录========================

// 获取我的参演记录列表请求
message GetMyDramaPlayingRecordReq {
    ga.BaseReq base_req = 1;
    uint32 page_size = 2; // 每页数量
    string page_token = 3; // 服务端返回的page_token，第一页传默认零值
    repeated SearchOption search_option = 4; // 搜索条件
}
// 获取我的参演记录列表响应
message GetMyDramaPlayingRecordResp {
    ga.BaseResp base_resp = 1;
    repeated PiaDramaPlayingRecord drama_playing_record_list = 2;
    string page_token = 3; // 请求下一页时带上，返回空值时，没有下一页
}
// 我的参演记录
message PiaDramaPlayingRecord {
    DramaSubInfo drama_sub_info = 1; // 剧本信息
    int64 playing_time = 2; // 参演时间
    bool is_pull_off = 3;// 是否被下架
    string id = 4;// 记录id
    bool is_copy = 5; // 是否是副本
}

// 批量删除我的参演记录请求
message PiaBatchDeleteMyPlayingRecordReq {
    ga.BaseReq base_req = 1;
    repeated string record_id_list = 2; // 记录id列表
}
// 批量删除我的参演记录响应
message PiaBatchDeleteMyPlayingRecordResp {
    ga.BaseResp base_resp = 1;
}

// 根据筛选条件获取所有相关参演记录id列表
message PiaGetMyPlayingRecordIdListReq {
    ga.BaseReq base_req = 1;
    repeated SearchOption search_option = 2;
}

// 根据筛选条件获取所有相关参演记录id列表响应
message PiaGetMyPlayingRecordIdListResp {
    ga.BaseResp base_resp = 1;
    repeated string record_id_list = 2; // 记录id列表
}


//======================我的参演记录========================

//======================作者个人作品页========================

message PiaAuthorGenInfoReq {
    ga.BaseReq base_req = 1;
    uint32 author_id = 2;// 作者id
}

message PiaAuthorGenInfoResp {
    ga.BaseResp base_resp = 1;
    uint32 author_id = 2; // 作者id
    string nickname = 3; // 昵称
    PiaUserInfo author_binding_info = 4; // 作者绑定的TT账号信息，
    uint32 drama_count = 5; // 作品数量
    uint32 collected_count = 6; // 收藏数量
}

enum PiaAuthorWorksListSortType {
    PIA_AUTHOR_WORKS_LIST_SORT_TYPE_INVALID = 0;
    PIA_AUTHOR_WORKS_LIST_SORT_TYPE_COLLECTED_COUNT = 1; // 按收藏数量排序
    PIA_AUTHOR_WORKS_LIST_SORT_TYPE_CREATE_TIME = 2; // 按创建时间排序，默认类型
}

message PiaAuthorWorksListReq {
    ga.BaseReq base_req = 1;
    string page_token = 2; // 分页token 第一页传空
    uint32 page_size = 3; // 单页条目数
    uint32 author_id = 4; // 作者id
    PiaAuthorWorksListSortType sort_type = 5; // 排序类型
}

message PiaAuthorWorksListResp {
    ga.BaseResp base_resp = 1;
    repeated DramaInfoWithStatus drama_list = 2;  //剧本概要信息列表
    string next_page_token = 3; // 服务端返回的token,为空则无需后续请求
}

//======================作者个人作品页========================

//======================副本库列表========================

message PiaDramaCreatorInfo {
    uint32 uid = 1; // 用户uid
    string nickname = 2; // 昵称
    string account = 3;// 用户名，用于头像
}

message PiaCopyDramaInfoWithStatus {
    DramaSubInfo drama_sub_info = 1; // 剧本信息
    PiaDramaCreatorInfo creator_info = 2; // 创建者信息
    bool is_public = 3; // 是否公开
    bool is_delete = 4; // 是否删除
    uint32 use_count = 5; // 使用次数
}

// 具体某个副本的副本库列表
message PiaCopyDramaListReq {
    ga.BaseReq base_req = 1;
    string page_token = 2; // 分页token 第一页传空
    uint32 page_size = 3; // 单页条目数
    uint32 drama_id = 4; // 剧本id
    uint32 uid = 5; // 用户uid
    bool is_public = 6; // 是否公开
    uint32 exclude_uid = 7; // 排除的用户uid
}

message PiaCopyDramaListResp {
    ga.BaseResp base_resp = 1;
    repeated PiaCopyDramaInfoWithStatus copy_drama_list = 2;  // 副本列表
    string next_page_token = 3; // 服务端返回的token,为空则无需后续请求
}

// 获取我的副本库列表，带搜索条件
message GetMyDramaCopyListReq {
    ga.BaseReq base_req = 1;
    repeated SearchOption search_option = 2;
    string page_token = 3; // 分页token 第一页传空
    uint32 page_size = 4; // 单页条目数
}

message PiaCopyDramaItem {
    DramaSubInfo drama_sub_info = 1; // 剧本信息
    bool is_public = 2; // 是否公开
    bool is_pull_off = 3; // 是否下架
}

// 获取我的副本库列表 响应
message GetMyDramaCopyListResp {
    ga.BaseResp base_resp = 1;
    repeated PiaCopyDramaItem copy_drama_item_list = 2;  // 我的副本列表
    string next_page_token = 3; // 服务端返回的token,为空则无需后续请求
}

// 副本状态管理
message SetDramaCopyStatusReq {
    enum DramaCopyStatus {
        DRAMA_COPY_STATUS_INVALID = 0;
        DRAMA_COPY_STATUS_PUBLIC = 1;
        DRAMA_COPY_STATUS_PRIVATE = 2;
    }
    ga.BaseReq base_req = 1;
    uint32 id = 2; // 副本id
    DramaCopyStatus drama_copy_status = 3;
}

// 副本状态管理resp
message SetDramaCopyStatusResp {
    ga.BaseResp base_resp = 1;
}

// 删除副本req
message DeleteDramaCopyReq {
    ga.BaseReq base_req = 1;
    repeated uint32 id_list = 2;
}

// 删除副本resp
message DeleteDramaCopyResp {
    ga.BaseResp base_resp = 1;
}
//======================副本库列表========================

//======================确认保存副本========================

message PiaConfirmCoverCopyDramaReq {
    ga.BaseReq base_req = 1;
    string confirm_copy_drama_id = 2; // 副本的确认id，出现超过单个剧本上限时，需要用户确认是否保存副本，然后再次请求确认接口时使用
    uint32 covered_copy_drama_id = 3; // 确认被覆盖的副本id
}

message PiaConfirmCoverCopyDramaResp {
    ga.BaseResp base_resp = 1;
}

//======================确认保存副本========================

//======================生成副本V2========================

// 生成剧本副本请求，如果剧本还未结束，则用当前时间结束剧本。
message PiaCreateDramaCopyV2Req {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 场次id
    bool is_public = 4; // 是否公开
}
// 生成剧本副本响应
message PiaCreateDramaCopyV2Resp {
    ga.BaseResp base_resp = 1;
    uint32 copy_drama_id = 2;   // 副本id
    bool is_over_single_limit = 3; // 是否超过单个剧本上限
    repeated PiaCopyDramaInfoWithStatus drama_list = 4; // 如果超过了单个剧本的上限，则增加副本列表
    string confirm_copy_drama_id = 5; // 副本的确认id，出现超过单个剧本上限时，需要用户确认是否保存副本，然后再次请求确认接口时使用
}

//======================生成副本V2========================

//======================选择新本演绎========================

message PiaPerformDramaRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    uint32 drama_id = 4; // 剧本id
    int64 index_id = 5;// 已点记录id。如果是从已点列表选本的，则必传，其他情况传0即可。
    bool is_copy_drama = 6; // 是否是副本，保留字段，暂时不用
    int64 round_id = 7; // 场次id，如果是已经开始走本了，中途换剧本，则必传
}

message PiaPerformDramaResponse {
    ga.BaseResp base_resp = 1;
    ChannelDramaStatus drama_status = 2; // 房间剧本状态
}

//======================选择新本演绎========================

//======================== 走本跟随/台词定位================

// 发送台词定位请求
message PiaSendDialogueIndexRequest {
    ga.BaseReq base_req = 1; 
    uint32 channel_id = 2;  // 当前房间id
    uint32 dialogue_index = 3; // 段落数
    repeated uint32 uid_list = 4; // 台词定位的接收用户列表
    int64 round_id = 5; // 场次id
}

// 发送台词定位请求
message PiaSendDialogueIndexResponse {
    ga.BaseResp base_resp = 1;
}

// 麦位枚举值
enum PiaMicType {
    PIA_MIC_TYPE_NIL = 0; // 麦下
    PIA_MIC_TYPE_VIRTUAL = 0x1; // 虚拟麦位，即auto模式
    PIA_MIC_TYPE_ZERO = 0x2;
    PIA_MIC_TYPE_ONE = 0x4;
    PIA_MIC_TYPE_TWO = 0x8;
    PIA_MIC_TYPE_THREE = 0x10;
    PIA_MIC_TYPE_FOUR = 0x20;
    PIA_MIC_TYPE_FIVE = 0x40;
    PIA_MIC_TYPE_SIX = 0x80;
    PIA_MIC_TYPE_SEVEN = 0x100;
    PIA_MIC_TYPE_EIGHT = 0x200;
}

// 发起跟随请求
message PiaFollowMicRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;  // 房间id
    int64 round_id = 3;  // 场次id
    PiaMicType target_mic = 4; // 跟随对象
    PiaMicType my_mic = 5; // 我的麦位， 麦下用户填MIC_TYPE_NIL
}

// 发起跟随响应
message PiaFollowMicResponse {
    ga.BaseResp base_resp = 1;
    uint32 dialogue_index = 2; // 返回当前跟随麦位段落数
}

// 取消跟随 用户发生滑动时调用（仅麦上用户调用）
message PiaUnFollowMicRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    int64 round_id = 3;
    PiaMicType my_mic = 4; // 我的麦位
}

message PiaUnFollowMicResponse {
    ga.BaseResp base_resp = 1;
}

// 上报段落请求
message PiaReportDialogueIndexRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 场次id
    uint32 dialogue_index = 4; // 段落数
    PiaMicType my_mic = 5; // 我的麦位 麦下用户不上报
}

// 上报段落请求响应
message PiaReportDialogueIndexResponse {
    ga.BaseResp base_resp = 1;
}

// 获取上个用户的段落记录请求
message PiaGetPreviousDialogueIndexRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2; // 房间id
    int64 round_id = 3; // 场次id
    PiaMicType my_mic = 4; // mic位号
}

// 获取上个用户的段落记录请求响应
message PiaGetPreviousDialogueIndexResponse {
    ga.BaseResp base_resp = 1;
    uint32 dialogue_index = 2; // 段落数
}

// 获取我的跟随状态请求
message PiaGetMyFollowInfoRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    int64 round_id = 3;
}

// 获取我的跟随状态响应
message PiaGetMyFollowInfoResponse {
    ga.BaseResp base_resp = 1;
    PiaMicType target_mic = 2;
}


// 获取pia戏房间各个麦位的跟随状态请求
message PiaGetFollowedStatusOfMicListRequest {
    ga.BaseReq base_req = 1;
    uint32 channel_id = 2;
    int64 round_id = 3;
}

// 获取当前房间各个麦位的跟随状态响应
message PiaGetFollowedStatusOfMicListResponse {
    message PiaMicStatus {
        uint32 mic_num = 1;
        bool can_be_followed  = 2; // 该麦位是否可以跟随
    }
    ga.BaseResp base_resp = 1;
    repeated PiaMicStatus mic_status_list = 2;
}

// 台词定位推送
message PiaDialogueIndexLocationMsg {
    uint32 dialogue_index = 1;
    uint32 channel_id = 2;
    int64 round_id = 3;
    int64 version = 4; // 推送版本号，越大越新
}

// 走本跟随推送
message PiaDialogueIndexFollowMsg {
    uint32 mic_mask = 1; // 有效麦位掩码 对照MicType
    uint32 dialogue_index = 2;
    uint32 channel_id = 3;
    int64 round_id = 4;
    int64 version = 5; // 推送版本号，越大越新
}

//======================== 走本跟随/台词定位================

//========================新pia戏玩法=================