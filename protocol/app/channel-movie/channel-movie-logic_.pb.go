// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-movie-logic_.proto

package channel_movie // import "golang.52tt.com/protocol/app/channel-movie"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetChannelMovieStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelMovieStatusReq) Reset()         { *m = GetChannelMovieStatusReq{} }
func (m *GetChannelMovieStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMovieStatusReq) ProtoMessage()    {}
func (*GetChannelMovieStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{0}
}
func (m *GetChannelMovieStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMovieStatusReq.Unmarshal(m, b)
}
func (m *GetChannelMovieStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMovieStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelMovieStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMovieStatusReq.Merge(dst, src)
}
func (m *GetChannelMovieStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelMovieStatusReq.Size(m)
}
func (m *GetChannelMovieStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMovieStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMovieStatusReq proto.InternalMessageInfo

func (m *GetChannelMovieStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelMovieStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMovieStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelMovieStatusResp) Reset()         { *m = GetChannelMovieStatusResp{} }
func (m *GetChannelMovieStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMovieStatusResp) ProtoMessage()    {}
func (*GetChannelMovieStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{1}
}
func (m *GetChannelMovieStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelMovieStatusResp.Unmarshal(m, b)
}
func (m *GetChannelMovieStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelMovieStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelMovieStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelMovieStatusResp.Merge(dst, src)
}
func (m *GetChannelMovieStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelMovieStatusResp.Size(m)
}
func (m *GetChannelMovieStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelMovieStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelMovieStatusResp proto.InternalMessageInfo

func (m *GetChannelMovieStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type PlayChannelMovieReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PlayChannelMovieReq) Reset()         { *m = PlayChannelMovieReq{} }
func (m *PlayChannelMovieReq) String() string { return proto.CompactTextString(m) }
func (*PlayChannelMovieReq) ProtoMessage()    {}
func (*PlayChannelMovieReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{2}
}
func (m *PlayChannelMovieReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayChannelMovieReq.Unmarshal(m, b)
}
func (m *PlayChannelMovieReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayChannelMovieReq.Marshal(b, m, deterministic)
}
func (dst *PlayChannelMovieReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayChannelMovieReq.Merge(dst, src)
}
func (m *PlayChannelMovieReq) XXX_Size() int {
	return xxx_messageInfo_PlayChannelMovieReq.Size(m)
}
func (m *PlayChannelMovieReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayChannelMovieReq.DiscardUnknown(m)
}

var xxx_messageInfo_PlayChannelMovieReq proto.InternalMessageInfo

func (m *PlayChannelMovieReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *PlayChannelMovieReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type PlayChannelMovieResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *PlayChannelMovieResp) Reset()         { *m = PlayChannelMovieResp{} }
func (m *PlayChannelMovieResp) String() string { return proto.CompactTextString(m) }
func (*PlayChannelMovieResp) ProtoMessage()    {}
func (*PlayChannelMovieResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{3}
}
func (m *PlayChannelMovieResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlayChannelMovieResp.Unmarshal(m, b)
}
func (m *PlayChannelMovieResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlayChannelMovieResp.Marshal(b, m, deterministic)
}
func (dst *PlayChannelMovieResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlayChannelMovieResp.Merge(dst, src)
}
func (m *PlayChannelMovieResp) XXX_Size() int {
	return xxx_messageInfo_PlayChannelMovieResp.Size(m)
}
func (m *PlayChannelMovieResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PlayChannelMovieResp.DiscardUnknown(m)
}

var xxx_messageInfo_PlayChannelMovieResp proto.InternalMessageInfo

func (m *PlayChannelMovieResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type ChangeChannelMovieStatusReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChangeChannelMovieStatusReq) Reset()         { *m = ChangeChannelMovieStatusReq{} }
func (m *ChangeChannelMovieStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelMovieStatusReq) ProtoMessage()    {}
func (*ChangeChannelMovieStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{4}
}
func (m *ChangeChannelMovieStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelMovieStatusReq.Unmarshal(m, b)
}
func (m *ChangeChannelMovieStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelMovieStatusReq.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelMovieStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelMovieStatusReq.Merge(dst, src)
}
func (m *ChangeChannelMovieStatusReq) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelMovieStatusReq.Size(m)
}
func (m *ChangeChannelMovieStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelMovieStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelMovieStatusReq proto.InternalMessageInfo

func (m *ChangeChannelMovieStatusReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChangeChannelMovieStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChangeChannelMovieStatusResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChangeChannelMovieStatusResp) Reset()         { *m = ChangeChannelMovieStatusResp{} }
func (m *ChangeChannelMovieStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeChannelMovieStatusResp) ProtoMessage()    {}
func (*ChangeChannelMovieStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{5}
}
func (m *ChangeChannelMovieStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeChannelMovieStatusResp.Unmarshal(m, b)
}
func (m *ChangeChannelMovieStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeChannelMovieStatusResp.Marshal(b, m, deterministic)
}
func (dst *ChangeChannelMovieStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeChannelMovieStatusResp.Merge(dst, src)
}
func (m *ChangeChannelMovieStatusResp) XXX_Size() int {
	return xxx_messageInfo_ChangeChannelMovieStatusResp.Size(m)
}
func (m *ChangeChannelMovieStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeChannelMovieStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeChannelMovieStatusResp proto.InternalMessageInfo

func (m *ChangeChannelMovieStatusResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type DragChannelMovieBarReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DragChannelMovieBarReq) Reset()         { *m = DragChannelMovieBarReq{} }
func (m *DragChannelMovieBarReq) String() string { return proto.CompactTextString(m) }
func (*DragChannelMovieBarReq) ProtoMessage()    {}
func (*DragChannelMovieBarReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{6}
}
func (m *DragChannelMovieBarReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DragChannelMovieBarReq.Unmarshal(m, b)
}
func (m *DragChannelMovieBarReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DragChannelMovieBarReq.Marshal(b, m, deterministic)
}
func (dst *DragChannelMovieBarReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DragChannelMovieBarReq.Merge(dst, src)
}
func (m *DragChannelMovieBarReq) XXX_Size() int {
	return xxx_messageInfo_DragChannelMovieBarReq.Size(m)
}
func (m *DragChannelMovieBarReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DragChannelMovieBarReq.DiscardUnknown(m)
}

var xxx_messageInfo_DragChannelMovieBarReq proto.InternalMessageInfo

func (m *DragChannelMovieBarReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *DragChannelMovieBarReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DragChannelMovieBarResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *DragChannelMovieBarResp) Reset()         { *m = DragChannelMovieBarResp{} }
func (m *DragChannelMovieBarResp) String() string { return proto.CompactTextString(m) }
func (*DragChannelMovieBarResp) ProtoMessage()    {}
func (*DragChannelMovieBarResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_movie_logic__67f4ce4d15863408, []int{7}
}
func (m *DragChannelMovieBarResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DragChannelMovieBarResp.Unmarshal(m, b)
}
func (m *DragChannelMovieBarResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DragChannelMovieBarResp.Marshal(b, m, deterministic)
}
func (dst *DragChannelMovieBarResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DragChannelMovieBarResp.Merge(dst, src)
}
func (m *DragChannelMovieBarResp) XXX_Size() int {
	return xxx_messageInfo_DragChannelMovieBarResp.Size(m)
}
func (m *DragChannelMovieBarResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DragChannelMovieBarResp.DiscardUnknown(m)
}

var xxx_messageInfo_DragChannelMovieBarResp proto.InternalMessageInfo

func (m *DragChannelMovieBarResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func init() {
	proto.RegisterType((*GetChannelMovieStatusReq)(nil), "ga.channel_movie.GetChannelMovieStatusReq")
	proto.RegisterType((*GetChannelMovieStatusResp)(nil), "ga.channel_movie.GetChannelMovieStatusResp")
	proto.RegisterType((*PlayChannelMovieReq)(nil), "ga.channel_movie.PlayChannelMovieReq")
	proto.RegisterType((*PlayChannelMovieResp)(nil), "ga.channel_movie.PlayChannelMovieResp")
	proto.RegisterType((*ChangeChannelMovieStatusReq)(nil), "ga.channel_movie.ChangeChannelMovieStatusReq")
	proto.RegisterType((*ChangeChannelMovieStatusResp)(nil), "ga.channel_movie.ChangeChannelMovieStatusResp")
	proto.RegisterType((*DragChannelMovieBarReq)(nil), "ga.channel_movie.DragChannelMovieBarReq")
	proto.RegisterType((*DragChannelMovieBarResp)(nil), "ga.channel_movie.DragChannelMovieBarResp")
}

func init() {
	proto.RegisterFile("channel-movie-logic_.proto", fileDescriptor_channel_movie_logic__67f4ce4d15863408)
}

var fileDescriptor_channel_movie_logic__67f4ce4d15863408 = []byte{
	// 292 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x93, 0x51, 0x4b, 0xc3, 0x30,
	0x14, 0x85, 0xa9, 0x0f, 0xba, 0xdd, 0x39, 0x90, 0x2a, 0x5a, 0xa7, 0xc2, 0xe8, 0x83, 0x4c, 0x61,
	0x29, 0x4c, 0xfc, 0x01, 0xd6, 0x31, 0xd9, 0x83, 0x20, 0xf5, 0x4d, 0x84, 0x72, 0xdb, 0x86, 0x58,
	0x48, 0x9b, 0xb4, 0xc9, 0x84, 0xfe, 0x7b, 0x69, 0x1b, 0x71, 0xa2, 0x7b, 0x28, 0x6c, 0x6f, 0x97,
	0x7b, 0x4e, 0xce, 0x77, 0x08, 0x09, 0x8c, 0xe2, 0x0f, 0xcc, 0x73, 0xca, 0xa7, 0x99, 0xf8, 0x4c,
	0xe9, 0x94, 0x0b, 0x96, 0xc6, 0x21, 0x91, 0xa5, 0xd0, 0xc2, 0x3e, 0x62, 0x48, 0x8c, 0x1c, 0x36,
	0xf2, 0x68, 0xc8, 0x30, 0x8c, 0x50, 0xd1, 0xd6, 0xe0, 0x22, 0x38, 0x4f, 0x54, 0x3f, 0xb6, 0x96,
	0xe7, 0xda, 0xf1, 0xaa, 0x51, 0xaf, 0x54, 0x40, 0x0b, 0xfb, 0x1a, 0x7a, 0xb5, 0x33, 0x2c, 0x69,
	0xe1, 0x58, 0x63, 0x6b, 0x32, 0x98, 0x0d, 0x08, 0x43, 0xe2, 0xa3, 0xa2, 0x01, 0x2d, 0x82, 0x83,
	0xa8, 0x1d, 0xec, 0x2b, 0x80, 0x6f, 0x46, 0x9a, 0x38, 0x7b, 0x63, 0x6b, 0x32, 0x0c, 0xfa, 0x66,
	0xb3, 0x4c, 0xdc, 0x05, 0x9c, 0x6f, 0x40, 0x28, 0x69, 0xdf, 0x40, 0xdf, 0x30, 0x94, 0x34, 0x90,
	0xc3, 0x1f, 0x88, 0x92, 0x41, 0x2f, 0x32, 0x93, 0xfb, 0x0e, 0xc7, 0x2f, 0x1c, 0xab, 0xf5, 0xa0,
	0x2d, 0xb6, 0x7c, 0x80, 0x93, 0xbf, 0xe9, 0xdd, 0x0a, 0x26, 0x70, 0x51, 0x1f, 0x67, 0x74, 0xa7,
	0xd7, 0xb9, 0x84, 0xcb, 0xcd, 0x94, 0x6e, 0x85, 0x43, 0x38, 0x9d, 0x97, 0xc8, 0xd6, 0x83, 0x7c,
	0x2c, 0xb7, 0xd8, 0x75, 0x0e, 0x67, 0xff, 0x02, 0x3a, 0xd5, 0xf4, 0x17, 0xe0, 0xc4, 0x22, 0x23,
	0x55, 0x5a, 0x89, 0x55, 0x6d, 0xc9, 0x44, 0x42, 0x79, 0xfb, 0x7e, 0xdf, 0x6e, 0x99, 0xe0, 0x98,
	0x33, 0x72, 0x3f, 0xd3, 0x9a, 0xc4, 0x22, 0xf3, 0x9a, 0x75, 0x2c, 0xb8, 0x87, 0x52, 0x7a, 0xbf,
	0x7e, 0x46, 0xb4, 0xdf, 0x68, 0x77, 0x5f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xec, 0x3d, 0x6a, 0x35,
	0x31, 0x03, 0x00, 0x00,
}
