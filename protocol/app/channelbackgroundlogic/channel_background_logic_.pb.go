// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_background_logic_.proto

package channelbackgroundlogic // import "golang.52tt.com/protocol/app/channelbackgroundlogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 背景类型
type BackgroundType int32

const (
	BackgroundType_Forever   BackgroundType = 0
	BackgroundType_TimeLimit BackgroundType = 1
	BackgroundType_Default   BackgroundType = 2
	BackgroundType_DIYLimit  BackgroundType = 4
)

var BackgroundType_name = map[int32]string{
	0: "Forever",
	1: "TimeLimit",
	2: "Default",
	4: "DIYLimit",
}
var BackgroundType_value = map[string]int32{
	"Forever":   0,
	"TimeLimit": 1,
	"Default":   2,
	"DIYLimit":  4,
}

func (x BackgroundType) String() string {
	return proto.EnumName(BackgroundType_name, int32(x))
}
func (BackgroundType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{0}
}

type BackgroundResourceType int32

const (
	BackgroundResourceType_UNKNOWN BackgroundResourceType = 0
	BackgroundResourceType_PHOTO   BackgroundResourceType = 1
	BackgroundResourceType_VIDEO   BackgroundResourceType = 2
)

var BackgroundResourceType_name = map[int32]string{
	0: "UNKNOWN",
	1: "PHOTO",
	2: "VIDEO",
}
var BackgroundResourceType_value = map[string]int32{
	"UNKNOWN": 0,
	"PHOTO":   1,
	"VIDEO":   2,
}

func (x BackgroundResourceType) String() string {
	return proto.EnumName(BackgroundResourceType_name, int32(x))
}
func (BackgroundResourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{1}
}

type SpecialChannelType int32

const (
	SpecialChannelType_Common              SpecialChannelType = 0
	SpecialChannelType_LiveChannel         SpecialChannelType = 1
	SpecialChannelType_OfficialLiveChannel SpecialChannelType = 2
	SpecialChannelType_CplSuperChannel     SpecialChannelType = 3
)

var SpecialChannelType_name = map[int32]string{
	0: "Common",
	1: "LiveChannel",
	2: "OfficialLiveChannel",
	3: "CplSuperChannel",
}
var SpecialChannelType_value = map[string]int32{
	"Common":              0,
	"LiveChannel":         1,
	"OfficialLiveChannel": 2,
	"CplSuperChannel":     3,
}

func (x SpecialChannelType) String() string {
	return proto.EnumName(SpecialChannelType_name, int32(x))
}
func (SpecialChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{2}
}

type LiveChannelBackgroundInfo_BackgroundType int32

const (
	LiveChannelBackgroundInfo_Default   LiveChannelBackgroundInfo_BackgroundType = 0
	LiveChannelBackgroundInfo_TimeLimit LiveChannelBackgroundInfo_BackgroundType = 1
	LiveChannelBackgroundInfo_Forever   LiveChannelBackgroundInfo_BackgroundType = 2
)

var LiveChannelBackgroundInfo_BackgroundType_name = map[int32]string{
	0: "Default",
	1: "TimeLimit",
	2: "Forever",
}
var LiveChannelBackgroundInfo_BackgroundType_value = map[string]int32{
	"Default":   0,
	"TimeLimit": 1,
	"Forever":   2,
}

func (x LiveChannelBackgroundInfo_BackgroundType) String() string {
	return proto.EnumName(LiveChannelBackgroundInfo_BackgroundType_name, int32(x))
}
func (LiveChannelBackgroundInfo_BackgroundType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{1, 0}
}

type ChannelBackgroundInfo struct {
	BackgroundId         uint64                 `protobuf:"varint,1,opt,name=background_id,json=backgroundId,proto3" json:"background_id,omitempty"`
	BackgroundName       string                 `protobuf:"bytes,2,opt,name=background_name,json=backgroundName,proto3" json:"background_name,omitempty"`
	BackgroundType       BackgroundType         `protobuf:"varint,3,opt,name=background_type,json=backgroundType,proto3,enum=ga.channelbackgroundlogic.BackgroundType" json:"background_type,omitempty"`
	IsInUsing            bool                   `protobuf:"varint,4,opt,name=is_in_using,json=isInUsing,proto3" json:"is_in_using,omitempty"`
	BackgroundUrl        string                 `protobuf:"bytes,5,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	Md5Sum               string                 `protobuf:"bytes,6,opt,name=md5_sum,json=md5Sum,proto3" json:"md5_sum,omitempty"`
	IsNew                bool                   `protobuf:"varint,7,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	StartTime            int64                  `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64                  `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ResourceType         BackgroundResourceType `protobuf:"varint,10,opt,name=resource_type,json=resourceType,proto3,enum=ga.channelbackgroundlogic.BackgroundResourceType" json:"resource_type,omitempty"`
	ResourceUrl          string                 `protobuf:"bytes,11,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ChannelBackgroundInfo) Reset()         { *m = ChannelBackgroundInfo{} }
func (m *ChannelBackgroundInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelBackgroundInfo) ProtoMessage()    {}
func (*ChannelBackgroundInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{0}
}
func (m *ChannelBackgroundInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelBackgroundInfo.Unmarshal(m, b)
}
func (m *ChannelBackgroundInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelBackgroundInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelBackgroundInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelBackgroundInfo.Merge(dst, src)
}
func (m *ChannelBackgroundInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelBackgroundInfo.Size(m)
}
func (m *ChannelBackgroundInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelBackgroundInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelBackgroundInfo proto.InternalMessageInfo

func (m *ChannelBackgroundInfo) GetBackgroundId() uint64 {
	if m != nil {
		return m.BackgroundId
	}
	return 0
}

func (m *ChannelBackgroundInfo) GetBackgroundName() string {
	if m != nil {
		return m.BackgroundName
	}
	return ""
}

func (m *ChannelBackgroundInfo) GetBackgroundType() BackgroundType {
	if m != nil {
		return m.BackgroundType
	}
	return BackgroundType_Forever
}

func (m *ChannelBackgroundInfo) GetIsInUsing() bool {
	if m != nil {
		return m.IsInUsing
	}
	return false
}

func (m *ChannelBackgroundInfo) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *ChannelBackgroundInfo) GetMd5Sum() string {
	if m != nil {
		return m.Md5Sum
	}
	return ""
}

func (m *ChannelBackgroundInfo) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

func (m *ChannelBackgroundInfo) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ChannelBackgroundInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ChannelBackgroundInfo) GetResourceType() BackgroundResourceType {
	if m != nil {
		return m.ResourceType
	}
	return BackgroundResourceType_UNKNOWN
}

func (m *ChannelBackgroundInfo) GetResourceUrl() string {
	if m != nil {
		return m.ResourceUrl
	}
	return ""
}

// 语音直播房房间背景
type LiveChannelBackgroundInfo struct {
	BackgroundId            uint64   `protobuf:"varint,1,opt,name=background_id,json=backgroundId,proto3" json:"background_id,omitempty"`
	BackgroundName          string   `protobuf:"bytes,2,opt,name=background_name,json=backgroundName,proto3" json:"background_name,omitempty"`
	BackgroundType          uint32   `protobuf:"varint,3,opt,name=background_type,json=backgroundType,proto3" json:"background_type,omitempty"`
	StaticPicture           string   `protobuf:"bytes,4,opt,name=static_picture,json=staticPicture,proto3" json:"static_picture,omitempty"`
	MicPictureUrl           string   `protobuf:"bytes,5,opt,name=mic_picture_url,json=micPictureUrl,proto3" json:"mic_picture_url,omitempty"`
	MicDynamicUrl           string   `protobuf:"bytes,6,opt,name=mic_dynamic_url,json=micDynamicUrl,proto3" json:"mic_dynamic_url,omitempty"`
	MicDynamicMd5Sum        string   `protobuf:"bytes,7,opt,name=mic_dynamic_md5sum,json=micDynamicMd5sum,proto3" json:"mic_dynamic_md5sum,omitempty"`
	BackgroundVideoUrl      string   `protobuf:"bytes,8,opt,name=background_video_url,json=backgroundVideoUrl,proto3" json:"background_video_url,omitempty"`
	BackgroundVideoMd5Sum   string   `protobuf:"bytes,9,opt,name=background_video_md5sum,json=backgroundVideoMd5sum,proto3" json:"background_video_md5sum,omitempty"`
	ObtainTime              int64    `protobuf:"varint,10,opt,name=obtain_time,json=obtainTime,proto3" json:"obtain_time,omitempty"`
	EndTime                 int64    `protobuf:"varint,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	IsInUsing               bool     `protobuf:"varint,12,opt,name=is_in_using,json=isInUsing,proto3" json:"is_in_using,omitempty"`
	BackgroundThumbnailUrl  string   `protobuf:"bytes,13,opt,name=background_thumbnail_url,json=backgroundThumbnailUrl,proto3" json:"background_thumbnail_url,omitempty"`
	VoiceMicDynamicUrl      string   `protobuf:"bytes,14,opt,name=voice_mic_dynamic_url,json=voiceMicDynamicUrl,proto3" json:"voice_mic_dynamic_url,omitempty"`
	VoiceMicDynamicMd5Sum   string   `protobuf:"bytes,15,opt,name=voice_mic_dynamic_md5sum,json=voiceMicDynamicMd5sum,proto3" json:"voice_mic_dynamic_md5sum,omitempty"`
	LowVersionBgVideoUrl    string   `protobuf:"bytes,16,opt,name=low_version_bg_video_url,json=lowVersionBgVideoUrl,proto3" json:"low_version_bg_video_url,omitempty"`
	LowVersionBgVideoMd5Sum string   `protobuf:"bytes,17,opt,name=low_version_bg_video_md5sum,json=lowVersionBgVideoMd5sum,proto3" json:"low_version_bg_video_md5sum,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *LiveChannelBackgroundInfo) Reset()         { *m = LiveChannelBackgroundInfo{} }
func (m *LiveChannelBackgroundInfo) String() string { return proto.CompactTextString(m) }
func (*LiveChannelBackgroundInfo) ProtoMessage()    {}
func (*LiveChannelBackgroundInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{1}
}
func (m *LiveChannelBackgroundInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveChannelBackgroundInfo.Unmarshal(m, b)
}
func (m *LiveChannelBackgroundInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveChannelBackgroundInfo.Marshal(b, m, deterministic)
}
func (dst *LiveChannelBackgroundInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveChannelBackgroundInfo.Merge(dst, src)
}
func (m *LiveChannelBackgroundInfo) XXX_Size() int {
	return xxx_messageInfo_LiveChannelBackgroundInfo.Size(m)
}
func (m *LiveChannelBackgroundInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveChannelBackgroundInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LiveChannelBackgroundInfo proto.InternalMessageInfo

func (m *LiveChannelBackgroundInfo) GetBackgroundId() uint64 {
	if m != nil {
		return m.BackgroundId
	}
	return 0
}

func (m *LiveChannelBackgroundInfo) GetBackgroundName() string {
	if m != nil {
		return m.BackgroundName
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetBackgroundType() uint32 {
	if m != nil {
		return m.BackgroundType
	}
	return 0
}

func (m *LiveChannelBackgroundInfo) GetStaticPicture() string {
	if m != nil {
		return m.StaticPicture
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetMicPictureUrl() string {
	if m != nil {
		return m.MicPictureUrl
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetMicDynamicUrl() string {
	if m != nil {
		return m.MicDynamicUrl
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetMicDynamicMd5Sum() string {
	if m != nil {
		return m.MicDynamicMd5Sum
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetBackgroundVideoUrl() string {
	if m != nil {
		return m.BackgroundVideoUrl
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetBackgroundVideoMd5Sum() string {
	if m != nil {
		return m.BackgroundVideoMd5Sum
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetObtainTime() int64 {
	if m != nil {
		return m.ObtainTime
	}
	return 0
}

func (m *LiveChannelBackgroundInfo) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *LiveChannelBackgroundInfo) GetIsInUsing() bool {
	if m != nil {
		return m.IsInUsing
	}
	return false
}

func (m *LiveChannelBackgroundInfo) GetBackgroundThumbnailUrl() string {
	if m != nil {
		return m.BackgroundThumbnailUrl
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetVoiceMicDynamicUrl() string {
	if m != nil {
		return m.VoiceMicDynamicUrl
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetVoiceMicDynamicMd5Sum() string {
	if m != nil {
		return m.VoiceMicDynamicMd5Sum
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetLowVersionBgVideoUrl() string {
	if m != nil {
		return m.LowVersionBgVideoUrl
	}
	return ""
}

func (m *LiveChannelBackgroundInfo) GetLowVersionBgVideoMd5Sum() string {
	if m != nil {
		return m.LowVersionBgVideoMd5Sum
	}
	return ""
}

type GetCurChannelBackgroundInfoReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	SpecialChannelType   uint32       `protobuf:"varint,4,opt,name=special_channel_type,json=specialChannelType,proto3" json:"special_channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCurChannelBackgroundInfoReq) Reset()         { *m = GetCurChannelBackgroundInfoReq{} }
func (m *GetCurChannelBackgroundInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetCurChannelBackgroundInfoReq) ProtoMessage()    {}
func (*GetCurChannelBackgroundInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{2}
}
func (m *GetCurChannelBackgroundInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurChannelBackgroundInfoReq.Unmarshal(m, b)
}
func (m *GetCurChannelBackgroundInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurChannelBackgroundInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetCurChannelBackgroundInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurChannelBackgroundInfoReq.Merge(dst, src)
}
func (m *GetCurChannelBackgroundInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetCurChannelBackgroundInfoReq.Size(m)
}
func (m *GetCurChannelBackgroundInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurChannelBackgroundInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurChannelBackgroundInfoReq proto.InternalMessageInfo

func (m *GetCurChannelBackgroundInfoReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetCurChannelBackgroundInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetCurChannelBackgroundInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCurChannelBackgroundInfoReq) GetSpecialChannelType() uint32 {
	if m != nil {
		return m.SpecialChannelType
	}
	return 0
}

type GetCurChannelBackgroundInfoResp struct {
	BaseResp                      *app.BaseResp              `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ForceUseChannelBackgroundInfo *ChannelBackgroundInfo     `protobuf:"bytes,2,opt,name=force_use_channel_background_info,json=forceUseChannelBackgroundInfo,proto3" json:"force_use_channel_background_info,omitempty"`
	UserUseChannelBackgroundInfo  *ChannelBackgroundInfo     `protobuf:"bytes,3,opt,name=user_use_channel_background_info,json=userUseChannelBackgroundInfo,proto3" json:"user_use_channel_background_info,omitempty"`
	LiveChannelBackgroundInfo     *LiveChannelBackgroundInfo `protobuf:"bytes,4,opt,name=live_channel_background_info,json=liveChannelBackgroundInfo,proto3" json:"live_channel_background_info,omitempty"`
	DefaultLiveBackgroundInfo     *LiveChannelBackgroundInfo `protobuf:"bytes,5,opt,name=default_live_background_info,json=defaultLiveBackgroundInfo,proto3" json:"default_live_background_info,omitempty"`
	XXX_NoUnkeyedLiteral          struct{}                   `json:"-"`
	XXX_unrecognized              []byte                     `json:"-"`
	XXX_sizecache                 int32                      `json:"-"`
}

func (m *GetCurChannelBackgroundInfoResp) Reset()         { *m = GetCurChannelBackgroundInfoResp{} }
func (m *GetCurChannelBackgroundInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetCurChannelBackgroundInfoResp) ProtoMessage()    {}
func (*GetCurChannelBackgroundInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{3}
}
func (m *GetCurChannelBackgroundInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurChannelBackgroundInfoResp.Unmarshal(m, b)
}
func (m *GetCurChannelBackgroundInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurChannelBackgroundInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetCurChannelBackgroundInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurChannelBackgroundInfoResp.Merge(dst, src)
}
func (m *GetCurChannelBackgroundInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetCurChannelBackgroundInfoResp.Size(m)
}
func (m *GetCurChannelBackgroundInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurChannelBackgroundInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurChannelBackgroundInfoResp proto.InternalMessageInfo

func (m *GetCurChannelBackgroundInfoResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCurChannelBackgroundInfoResp) GetForceUseChannelBackgroundInfo() *ChannelBackgroundInfo {
	if m != nil {
		return m.ForceUseChannelBackgroundInfo
	}
	return nil
}

func (m *GetCurChannelBackgroundInfoResp) GetUserUseChannelBackgroundInfo() *ChannelBackgroundInfo {
	if m != nil {
		return m.UserUseChannelBackgroundInfo
	}
	return nil
}

func (m *GetCurChannelBackgroundInfoResp) GetLiveChannelBackgroundInfo() *LiveChannelBackgroundInfo {
	if m != nil {
		return m.LiveChannelBackgroundInfo
	}
	return nil
}

func (m *GetCurChannelBackgroundInfoResp) GetDefaultLiveBackgroundInfo() *LiveChannelBackgroundInfo {
	if m != nil {
		return m.DefaultLiveBackgroundInfo
	}
	return nil
}

type ChangeCurChannelBackgroundReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BackgroundId         uint64       `protobuf:"varint,4,opt,name=background_id,json=backgroundId,proto3" json:"background_id,omitempty"`
	SpecialChannelType   uint32       `protobuf:"varint,5,opt,name=special_channel_type,json=specialChannelType,proto3" json:"special_channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChangeCurChannelBackgroundReq) Reset()         { *m = ChangeCurChannelBackgroundReq{} }
func (m *ChangeCurChannelBackgroundReq) String() string { return proto.CompactTextString(m) }
func (*ChangeCurChannelBackgroundReq) ProtoMessage()    {}
func (*ChangeCurChannelBackgroundReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{4}
}
func (m *ChangeCurChannelBackgroundReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeCurChannelBackgroundReq.Unmarshal(m, b)
}
func (m *ChangeCurChannelBackgroundReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeCurChannelBackgroundReq.Marshal(b, m, deterministic)
}
func (dst *ChangeCurChannelBackgroundReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeCurChannelBackgroundReq.Merge(dst, src)
}
func (m *ChangeCurChannelBackgroundReq) XXX_Size() int {
	return xxx_messageInfo_ChangeCurChannelBackgroundReq.Size(m)
}
func (m *ChangeCurChannelBackgroundReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeCurChannelBackgroundReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeCurChannelBackgroundReq proto.InternalMessageInfo

func (m *ChangeCurChannelBackgroundReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChangeCurChannelBackgroundReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeCurChannelBackgroundReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChangeCurChannelBackgroundReq) GetBackgroundId() uint64 {
	if m != nil {
		return m.BackgroundId
	}
	return 0
}

func (m *ChangeCurChannelBackgroundReq) GetSpecialChannelType() uint32 {
	if m != nil {
		return m.SpecialChannelType
	}
	return 0
}

type ChangeCurChannelBackgroundResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ChangeCurChannelBackgroundResp) Reset()         { *m = ChangeCurChannelBackgroundResp{} }
func (m *ChangeCurChannelBackgroundResp) String() string { return proto.CompactTextString(m) }
func (*ChangeCurChannelBackgroundResp) ProtoMessage()    {}
func (*ChangeCurChannelBackgroundResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{5}
}
func (m *ChangeCurChannelBackgroundResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeCurChannelBackgroundResp.Unmarshal(m, b)
}
func (m *ChangeCurChannelBackgroundResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeCurChannelBackgroundResp.Marshal(b, m, deterministic)
}
func (dst *ChangeCurChannelBackgroundResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeCurChannelBackgroundResp.Merge(dst, src)
}
func (m *ChangeCurChannelBackgroundResp) XXX_Size() int {
	return xxx_messageInfo_ChangeCurChannelBackgroundResp.Size(m)
}
func (m *ChangeCurChannelBackgroundResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeCurChannelBackgroundResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeCurChannelBackgroundResp proto.InternalMessageInfo

func (m *ChangeCurChannelBackgroundResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetChannelBackgroundInfoListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32       `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	SpecialChannelType   uint32       `protobuf:"varint,4,opt,name=special_channel_type,json=specialChannelType,proto3" json:"special_channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelBackgroundInfoListReq) Reset()         { *m = GetChannelBackgroundInfoListReq{} }
func (m *GetChannelBackgroundInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelBackgroundInfoListReq) ProtoMessage()    {}
func (*GetChannelBackgroundInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{6}
}
func (m *GetChannelBackgroundInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBackgroundInfoListReq.Unmarshal(m, b)
}
func (m *GetChannelBackgroundInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBackgroundInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelBackgroundInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBackgroundInfoListReq.Merge(dst, src)
}
func (m *GetChannelBackgroundInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelBackgroundInfoListReq.Size(m)
}
func (m *GetChannelBackgroundInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBackgroundInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBackgroundInfoListReq proto.InternalMessageInfo

func (m *GetChannelBackgroundInfoListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelBackgroundInfoListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelBackgroundInfoListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelBackgroundInfoListReq) GetSpecialChannelType() uint32 {
	if m != nil {
		return m.SpecialChannelType
	}
	return 0
}

type GetChannelBackgroundInfoListResp struct {
	BaseResp                       *app.BaseResp                `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ForeverChannelBackgroundList   []*ChannelBackgroundInfo     `protobuf:"bytes,2,rep,name=forever_channel_background_list,json=foreverChannelBackgroundList,proto3" json:"forever_channel_background_list,omitempty"`
	TimeLimitChannelBackgroundList []*ChannelBackgroundInfo     `protobuf:"bytes,3,rep,name=time_limit_channel_background_list,json=timeLimitChannelBackgroundList,proto3" json:"time_limit_channel_background_list,omitempty"`
	Version                        int64                        `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	LiveChannelBackgroundList      []*LiveChannelBackgroundInfo `protobuf:"bytes,5,rep,name=live_channel_background_list,json=liveChannelBackgroundList,proto3" json:"live_channel_background_list,omitempty"`
	DIYLimitChannelBackgroundList  []*ChannelBackgroundInfo     `protobuf:"bytes,6,rep,name=DIY_limit_channel_background_list,json=DIYLimitChannelBackgroundList,proto3" json:"DIY_limit_channel_background_list,omitempty"`
	XXX_NoUnkeyedLiteral           struct{}                     `json:"-"`
	XXX_unrecognized               []byte                       `json:"-"`
	XXX_sizecache                  int32                        `json:"-"`
}

func (m *GetChannelBackgroundInfoListResp) Reset()         { *m = GetChannelBackgroundInfoListResp{} }
func (m *GetChannelBackgroundInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelBackgroundInfoListResp) ProtoMessage()    {}
func (*GetChannelBackgroundInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{7}
}
func (m *GetChannelBackgroundInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelBackgroundInfoListResp.Unmarshal(m, b)
}
func (m *GetChannelBackgroundInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelBackgroundInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelBackgroundInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelBackgroundInfoListResp.Merge(dst, src)
}
func (m *GetChannelBackgroundInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelBackgroundInfoListResp.Size(m)
}
func (m *GetChannelBackgroundInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelBackgroundInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelBackgroundInfoListResp proto.InternalMessageInfo

func (m *GetChannelBackgroundInfoListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelBackgroundInfoListResp) GetForeverChannelBackgroundList() []*ChannelBackgroundInfo {
	if m != nil {
		return m.ForeverChannelBackgroundList
	}
	return nil
}

func (m *GetChannelBackgroundInfoListResp) GetTimeLimitChannelBackgroundList() []*ChannelBackgroundInfo {
	if m != nil {
		return m.TimeLimitChannelBackgroundList
	}
	return nil
}

func (m *GetChannelBackgroundInfoListResp) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *GetChannelBackgroundInfoListResp) GetLiveChannelBackgroundList() []*LiveChannelBackgroundInfo {
	if m != nil {
		return m.LiveChannelBackgroundList
	}
	return nil
}

func (m *GetChannelBackgroundInfoListResp) GetDIYLimitChannelBackgroundList() []*ChannelBackgroundInfo {
	if m != nil {
		return m.DIYLimitChannelBackgroundList
	}
	return nil
}

type CheckChannelBackgroundUpdateReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CheckChannelBackgroundUpdateReq) Reset()         { *m = CheckChannelBackgroundUpdateReq{} }
func (m *CheckChannelBackgroundUpdateReq) String() string { return proto.CompactTextString(m) }
func (*CheckChannelBackgroundUpdateReq) ProtoMessage()    {}
func (*CheckChannelBackgroundUpdateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{8}
}
func (m *CheckChannelBackgroundUpdateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelBackgroundUpdateReq.Unmarshal(m, b)
}
func (m *CheckChannelBackgroundUpdateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelBackgroundUpdateReq.Marshal(b, m, deterministic)
}
func (dst *CheckChannelBackgroundUpdateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelBackgroundUpdateReq.Merge(dst, src)
}
func (m *CheckChannelBackgroundUpdateReq) XXX_Size() int {
	return xxx_messageInfo_CheckChannelBackgroundUpdateReq.Size(m)
}
func (m *CheckChannelBackgroundUpdateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelBackgroundUpdateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelBackgroundUpdateReq proto.InternalMessageInfo

func (m *CheckChannelBackgroundUpdateReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckChannelBackgroundUpdateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckChannelBackgroundUpdateResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Version              int64         `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CheckChannelBackgroundUpdateResp) Reset()         { *m = CheckChannelBackgroundUpdateResp{} }
func (m *CheckChannelBackgroundUpdateResp) String() string { return proto.CompactTextString(m) }
func (*CheckChannelBackgroundUpdateResp) ProtoMessage()    {}
func (*CheckChannelBackgroundUpdateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_background_logic__12eb1569d9d33b96, []int{9}
}
func (m *CheckChannelBackgroundUpdateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckChannelBackgroundUpdateResp.Unmarshal(m, b)
}
func (m *CheckChannelBackgroundUpdateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckChannelBackgroundUpdateResp.Marshal(b, m, deterministic)
}
func (dst *CheckChannelBackgroundUpdateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckChannelBackgroundUpdateResp.Merge(dst, src)
}
func (m *CheckChannelBackgroundUpdateResp) XXX_Size() int {
	return xxx_messageInfo_CheckChannelBackgroundUpdateResp.Size(m)
}
func (m *CheckChannelBackgroundUpdateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckChannelBackgroundUpdateResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckChannelBackgroundUpdateResp proto.InternalMessageInfo

func (m *CheckChannelBackgroundUpdateResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckChannelBackgroundUpdateResp) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func init() {
	proto.RegisterType((*ChannelBackgroundInfo)(nil), "ga.channelbackgroundlogic.ChannelBackgroundInfo")
	proto.RegisterType((*LiveChannelBackgroundInfo)(nil), "ga.channelbackgroundlogic.LiveChannelBackgroundInfo")
	proto.RegisterType((*GetCurChannelBackgroundInfoReq)(nil), "ga.channelbackgroundlogic.GetCurChannelBackgroundInfoReq")
	proto.RegisterType((*GetCurChannelBackgroundInfoResp)(nil), "ga.channelbackgroundlogic.GetCurChannelBackgroundInfoResp")
	proto.RegisterType((*ChangeCurChannelBackgroundReq)(nil), "ga.channelbackgroundlogic.ChangeCurChannelBackgroundReq")
	proto.RegisterType((*ChangeCurChannelBackgroundResp)(nil), "ga.channelbackgroundlogic.ChangeCurChannelBackgroundResp")
	proto.RegisterType((*GetChannelBackgroundInfoListReq)(nil), "ga.channelbackgroundlogic.GetChannelBackgroundInfoListReq")
	proto.RegisterType((*GetChannelBackgroundInfoListResp)(nil), "ga.channelbackgroundlogic.GetChannelBackgroundInfoListResp")
	proto.RegisterType((*CheckChannelBackgroundUpdateReq)(nil), "ga.channelbackgroundlogic.CheckChannelBackgroundUpdateReq")
	proto.RegisterType((*CheckChannelBackgroundUpdateResp)(nil), "ga.channelbackgroundlogic.CheckChannelBackgroundUpdateResp")
	proto.RegisterEnum("ga.channelbackgroundlogic.BackgroundType", BackgroundType_name, BackgroundType_value)
	proto.RegisterEnum("ga.channelbackgroundlogic.BackgroundResourceType", BackgroundResourceType_name, BackgroundResourceType_value)
	proto.RegisterEnum("ga.channelbackgroundlogic.SpecialChannelType", SpecialChannelType_name, SpecialChannelType_value)
	proto.RegisterEnum("ga.channelbackgroundlogic.LiveChannelBackgroundInfo_BackgroundType", LiveChannelBackgroundInfo_BackgroundType_name, LiveChannelBackgroundInfo_BackgroundType_value)
}

func init() {
	proto.RegisterFile("channel_background_logic_.proto", fileDescriptor_channel_background_logic__12eb1569d9d33b96)
}

var fileDescriptor_channel_background_logic__12eb1569d9d33b96 = []byte{
	// 1169 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x57, 0x6d, 0x73, 0xdb, 0x44,
	0x10, 0xae, 0xfc, 0xee, 0x75, 0x9c, 0x88, 0x6b, 0xd3, 0x28, 0x90, 0x17, 0xc7, 0x4c, 0xdb, 0x34,
	0xc3, 0x38, 0x6d, 0x4a, 0x0a, 0x0c, 0x7c, 0x4a, 0x02, 0xc5, 0xd3, 0x34, 0xee, 0x28, 0x71, 0x98,
	0xc2, 0x07, 0x8d, 0x2c, 0x9d, 0x95, 0x9b, 0x4a, 0x3a, 0x45, 0x27, 0x39, 0xa4, 0xc3, 0x3f, 0x82,
	0x0f, 0xfc, 0x13, 0x86, 0x19, 0xfe, 0x02, 0xff, 0x83, 0xb9, 0x93, 0x6c, 0xcb, 0xb6, 0x64, 0x48,
	0xca, 0x30, 0x7c, 0xb2, 0xb4, 0xfb, 0xec, 0xed, 0xee, 0xb3, 0x2f, 0x27, 0xc3, 0xa6, 0x71, 0xa1,
	0xbb, 0x2e, 0xb6, 0xb5, 0x9e, 0x6e, 0xbc, 0xb5, 0x7c, 0x1a, 0xba, 0xa6, 0x66, 0x53, 0x8b, 0x18,
	0x5a, 0xcb, 0xf3, 0x69, 0x40, 0xd1, 0xaa, 0xa5, 0xb7, 0x62, 0xcc, 0x18, 0x22, 0x10, 0x1f, 0xd6,
	0x2d, 0x5d, 0xeb, 0xe9, 0x0c, 0x47, 0xc8, 0xe6, 0x9f, 0x79, 0x58, 0x3e, 0x8c, 0x90, 0x07, 0x23,
	0x64, 0xdb, 0xed, 0x53, 0xf4, 0x31, 0xd4, 0x13, 0xc7, 0x13, 0x53, 0x91, 0x1a, 0xd2, 0x76, 0x41,
	0x5d, 0x18, 0x0b, 0xdb, 0x26, 0x7a, 0x04, 0x4b, 0x09, 0x90, 0xab, 0x3b, 0x58, 0xc9, 0x35, 0xa4,
	0xed, 0xaa, 0xba, 0x38, 0x16, 0x9f, 0xe8, 0x0e, 0x46, 0xea, 0x04, 0x30, 0xb8, 0xf6, 0xb0, 0x92,
	0x6f, 0x48, 0xdb, 0x8b, 0x7b, 0x8f, 0x5b, 0x99, 0xb1, 0xb6, 0xc6, 0x11, 0x9d, 0x5d, 0x7b, 0x38,
	0x79, 0x26, 0x7f, 0x47, 0x1b, 0x50, 0x23, 0x4c, 0x23, 0xae, 0x16, 0x32, 0xe2, 0x5a, 0x4a, 0xa1,
	0x21, 0x6d, 0x57, 0xd4, 0x2a, 0x61, 0x6d, 0xb7, 0xcb, 0x05, 0xe8, 0x01, 0x24, 0x2c, 0xb4, 0xd0,
	0xb7, 0x95, 0xa2, 0x88, 0x2d, 0x91, 0x57, 0xd7, 0xb7, 0xd1, 0x0a, 0x94, 0x1d, 0x73, 0x5f, 0x63,
	0xa1, 0xa3, 0x94, 0x84, 0xbe, 0xe4, 0x98, 0xfb, 0xa7, 0xa1, 0x83, 0x96, 0xa1, 0x44, 0x98, 0xe6,
	0xe2, 0x2b, 0xa5, 0x2c, 0x8e, 0x2e, 0x12, 0x76, 0x82, 0xaf, 0xd0, 0x3a, 0x00, 0x0b, 0x74, 0x3f,
	0xd0, 0x02, 0xe2, 0x60, 0xa5, 0xd2, 0x90, 0xb6, 0xf3, 0x6a, 0x55, 0x48, 0xce, 0x88, 0x83, 0xd1,
	0x2a, 0x54, 0x30, 0x4f, 0x91, 0x2b, 0xab, 0x42, 0x59, 0xc6, 0xae, 0x29, 0x54, 0xe7, 0x50, 0xf7,
	0x31, 0xa3, 0xa1, 0x6f, 0xe0, 0x88, 0x02, 0x10, 0x14, 0x3c, 0xfd, 0x47, 0x14, 0xa8, 0xb1, 0xa5,
	0xa0, 0x62, 0xc1, 0x4f, 0xbc, 0xa1, 0x2d, 0x18, 0xbd, 0x8b, 0x34, 0x6b, 0x22, 0x8d, 0xda, 0x50,
	0xd6, 0xf5, 0xed, 0xe6, 0x1f, 0x25, 0x58, 0x3d, 0x26, 0x03, 0xfc, 0x5f, 0xd4, 0xfa, 0x51, 0x7a,
	0xad, 0xeb, 0x33, 0x05, 0x7c, 0x00, 0x8b, 0x2c, 0xd0, 0x03, 0x62, 0x68, 0x1e, 0x31, 0x82, 0xd0,
	0xc7, 0xa2, 0x86, 0x55, 0xb5, 0x1e, 0x49, 0x5f, 0x47, 0x42, 0xf4, 0x10, 0x96, 0x9c, 0x31, 0x26,
	0x59, 0x48, 0x67, 0x04, 0xe2, 0x85, 0x8c, 0x71, 0xe6, 0xb5, 0xab, 0xf3, 0x5f, 0x8e, 0x2b, 0x8d,
	0x70, 0x47, 0x91, 0x94, 0xe3, 0x3e, 0x01, 0x94, 0xc4, 0x39, 0xe6, 0x3e, 0xaf, 0x7d, 0x59, 0x40,
	0xe5, 0x31, 0xf4, 0x95, 0x90, 0xa3, 0x27, 0x70, 0x2f, 0x91, 0xcd, 0x80, 0x98, 0x98, 0x8a, 0xa3,
	0x2b, 0x02, 0x8f, 0xc6, 0xba, 0x73, 0xae, 0xe2, 0xe7, 0x3f, 0x87, 0x95, 0x19, 0x8b, 0xd8, 0x49,
	0x55, 0x18, 0x2d, 0x4f, 0x19, 0xc5, 0x9e, 0x36, 0xa1, 0x46, 0x7b, 0x81, 0x4e, 0xdc, 0xa8, 0x79,
	0x40, 0x34, 0x0f, 0x44, 0xa2, 0x99, 0xd6, 0xaa, 0x4d, 0xb6, 0xd6, 0xd4, 0x2c, 0x2c, 0x4c, 0xcf,
	0xc2, 0xe7, 0xa0, 0x24, 0x6b, 0x72, 0x11, 0x3a, 0x3d, 0x57, 0x27, 0xb6, 0xc8, 0xa4, 0x2e, 0x82,
	0xba, 0x9f, 0x28, 0xce, 0x50, 0xcd, 0xb3, 0x79, 0x0a, 0xcb, 0x03, 0x4a, 0x0c, 0xac, 0x4d, 0x73,
	0xbb, 0x18, 0x11, 0x20, 0x94, 0xaf, 0x26, 0x08, 0xfe, 0x0c, 0x94, 0x59, 0x93, 0x98, 0x81, 0xa5,
	0x88, 0x81, 0x29, 0xab, 0x98, 0x81, 0xe7, 0xa0, 0xd8, 0xf4, 0x4a, 0x1b, 0x60, 0x9f, 0x11, 0xea,
	0x6a, 0x3d, 0x2b, 0xc1, 0xb7, 0x2c, 0x0c, 0xef, 0xd9, 0xf4, 0xea, 0x3c, 0x52, 0x1f, 0x58, 0x23,
	0xc6, 0xbf, 0x82, 0x8f, 0x52, 0xed, 0x62, 0x9f, 0x1f, 0x08, 0xd3, 0x95, 0x19, 0xd3, 0xc8, 0x6b,
	0xf3, 0x0b, 0x58, 0x9c, 0xdc, 0x34, 0xa8, 0x06, 0xe5, 0x23, 0xdc, 0xd7, 0x43, 0x3b, 0x90, 0xef,
	0xa0, 0x3a, 0x54, 0x39, 0xc5, 0xc7, 0xc4, 0x21, 0x81, 0x2c, 0x71, 0xdd, 0x37, 0xd4, 0xc7, 0x03,
	0xec, 0xcb, 0xb9, 0xe6, 0xcf, 0x12, 0x6c, 0xbc, 0xc0, 0xc1, 0x61, 0xe8, 0xa7, 0x0e, 0x96, 0x8a,
	0x2f, 0xd1, 0x43, 0xa8, 0xf0, 0x7d, 0xab, 0xf9, 0xf8, 0x52, 0x8c, 0x55, 0x6d, 0xaf, 0xc6, 0xe7,
	0xfd, 0x40, 0x67, 0x58, 0xc5, 0x97, 0x6a, 0xb9, 0x17, 0x3d, 0xf0, 0xb5, 0x32, 0x5c, 0xeb, 0xc4,
	0x14, 0x93, 0x55, 0x57, 0xab, 0xb1, 0xa4, 0x6d, 0x22, 0x19, 0xf2, 0x21, 0x31, 0xe3, 0x41, 0xe2,
	0x8f, 0xbc, 0x31, 0x99, 0x87, 0x0d, 0xa2, 0xdb, 0xda, 0xd0, 0x50, 0xcc, 0x5a, 0x41, 0x40, 0x50,
	0xac, 0x8b, 0xe3, 0xe2, 0x69, 0x35, 0x7f, 0x2d, 0xc0, 0xe6, 0xdc, 0x68, 0x99, 0x87, 0x1e, 0x43,
	0x35, 0x0e, 0x97, 0x79, 0x71, 0xbc, 0x0b, 0xe3, 0x78, 0x99, 0xa7, 0x56, 0x7a, 0xf1, 0x13, 0x7a,
	0x07, 0x5b, 0x7d, 0x2a, 0x76, 0x0e, 0xc3, 0x5a, 0xca, 0x95, 0x44, 0xdc, 0x3e, 0x15, 0x89, 0xd4,
	0xf6, 0x9e, 0xcc, 0x59, 0x71, 0xe9, 0xb1, 0xac, 0x8b, 0xa3, 0xbb, 0x2c, 0x63, 0x63, 0xfd, 0x08,
	0x8d, 0x90, 0x61, 0x7f, 0xae, 0xeb, 0xfc, 0x2d, 0x5d, 0xaf, 0xf1, 0x93, 0x33, 0x3d, 0x87, 0xb0,
	0x66, 0x93, 0x41, 0xb6, 0xd7, 0x82, 0xf0, 0xfa, 0xe9, 0x1c, 0xaf, 0x99, 0x7b, 0x58, 0x5d, 0xb5,
	0x33, 0x57, 0x74, 0x08, 0x6b, 0x66, 0xd4, 0x92, 0x9a, 0x70, 0x3f, 0xed, 0xb6, 0xf8, 0x3e, 0x6e,
	0xe3, 0x93, 0x39, 0x62, 0x52, 0xd5, 0xfc, 0x4d, 0x82, 0x75, 0x6e, 0x64, 0xe1, 0xb4, 0xae, 0xb9,
	0x49, 0x7f, 0xc7, 0x0d, 0x9c, 0x1b, 0x37, 0xf0, 0x64, 0xc7, 0xe7, 0xa7, 0x3b, 0x7e, 0xe6, 0x52,
	0x2a, 0xa4, 0x5c, 0x4a, 0x59, 0x43, 0x50, 0xcc, 0x1c, 0x82, 0x97, 0xb0, 0x31, 0x2f, 0xa1, 0x1b,
	0x8d, 0x40, 0xf3, 0x17, 0x29, 0x9a, 0xa8, 0x34, 0x5a, 0x8f, 0x09, 0x0b, 0xfe, 0x67, 0x0b, 0xe0,
	0xf7, 0x02, 0x34, 0xe6, 0x87, 0x7b, 0xb3, 0x0d, 0x70, 0x05, 0x9b, 0xfd, 0x68, 0x17, 0xa6, 0x8d,
	0x83, 0x4d, 0x58, 0xa0, 0xe4, 0x1a, 0xf9, 0xdb, 0x0d, 0x61, 0x7c, 0xf0, 0x8c, 0x96, 0xc7, 0x89,
	0x7e, 0x82, 0x26, 0xbf, 0x05, 0x35, 0x9b, 0x2f, 0xe5, 0x4c, 0xdf, 0xf9, 0x5b, 0xfa, 0xde, 0x08,
	0x86, 0xfb, 0x3e, 0xdd, 0xbb, 0x02, 0xe5, 0xf8, 0xaa, 0x11, 0x5c, 0xe7, 0xd5, 0xe1, 0xeb, 0xbc,
	0xe5, 0x20, 0x22, 0x2a, 0x8a, 0x88, 0xfe, 0xcd, 0xe5, 0x20, 0x02, 0x7a, 0x07, 0x5b, 0x47, 0xed,
	0x37, 0x7f, 0xc3, 0x46, 0xe9, 0x96, 0x6c, 0xac, 0x1f, 0xb5, 0xdf, 0x64, 0x93, 0xd1, 0xfc, 0x01,
	0x36, 0x0f, 0x2f, 0xb0, 0xf1, 0x76, 0x46, 0xdb, 0xf5, 0x4c, 0x3d, 0xc0, 0xef, 0xb5, 0x22, 0x9a,
	0x16, 0x34, 0xe6, 0x1f, 0x7e, 0xb3, 0x7e, 0x4d, 0x14, 0x2e, 0x37, 0x51, 0xb8, 0x9d, 0x17, 0x69,
	0xdf, 0x00, 0xc3, 0x7b, 0x3e, 0xed, 0x1b, 0x60, 0xf8, 0x7d, 0x90, 0x43, 0x0b, 0x50, 0x19, 0x32,
	0x24, 0x17, 0x76, 0xbe, 0x84, 0xfb, 0xe9, 0xdf, 0xec, 0xdc, 0xa8, 0x7b, 0xf2, 0xf2, 0xa4, 0xf3,
	0xdd, 0x89, 0x7c, 0x07, 0x55, 0xa1, 0xf8, 0xfa, 0xdb, 0xce, 0x59, 0x47, 0x96, 0xf8, 0xe3, 0x79,
	0xfb, 0xe8, 0xeb, 0x8e, 0x9c, 0xdb, 0xd1, 0x00, 0x9d, 0xce, 0x4c, 0x2d, 0x02, 0x28, 0x1d, 0x52,
	0xc7, 0xa1, 0xae, 0x7c, 0x07, 0x2d, 0x41, 0x2d, 0xd1, 0x21, 0xb2, 0x84, 0x56, 0xe0, 0x6e, 0xa7,
	0xdf, 0x27, 0xdc, 0x26, 0xa9, 0xc8, 0xa1, 0xbb, 0xb0, 0x74, 0xe8, 0xd9, 0xa7, 0xa1, 0x37, 0x9a,
	0x21, 0x39, 0x7f, 0xd0, 0x01, 0xc5, 0xa0, 0x4e, 0xeb, 0x9a, 0x5c, 0xd3, 0x90, 0x73, 0xe4, 0x50,
	0x13, 0xdb, 0xd1, 0x5f, 0xc1, 0xef, 0x9f, 0x59, 0xd4, 0xd6, 0x5d, 0xab, 0xb5, 0xbf, 0x17, 0x04,
	0x2d, 0x83, 0x3a, 0xbb, 0x42, 0x6c, 0x50, 0x7b, 0x57, 0xf7, 0xbc, 0xdd, 0xf4, 0x96, 0xe9, 0x95,
	0x04, 0xe8, 0xd9, 0x5f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xc5, 0xb5, 0x94, 0x43, 0x93, 0x0e, 0x00,
	0x00,
}
