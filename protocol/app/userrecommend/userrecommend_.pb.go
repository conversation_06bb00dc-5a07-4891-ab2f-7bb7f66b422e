// Code generated by protoc-gen-gogo.
// source: userrecommend_.proto
// DO NOT EDIT!

/*
	Package userrecommend is a generated protocol buffer package.

	It is generated from these files:
		userrecommend_.proto

	It has these top-level messages:
		AddOrUpdateContactsReq
		AddOrUpdateContactsResp
		RejectRecommendReq
		RejectRecommendResp
		ChangeRecommendStatusReq
		ChangeRecommendStatusResp
		GetRecommendStatusReq
		GetRecommendStatusResp
		RecommendFromContacts
		RecommendFromAll
		GetRecommendFromContactsReq
		GetRecommendFromContactsResp
		GetUserRecommendReq
		GetUserRecommendResp
*/
package userrecommend

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import ga "golang.52tt.com/protocol/app"

import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto3 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ContactsOperType int32

const (
	ContactsOperType_CONTACTS_UNDEFINED ContactsOperType = 0
	ContactsOperType_CONTACTS_ADD       ContactsOperType = 1
	ContactsOperType_CONTACTS_DELETE    ContactsOperType = 2
)

var ContactsOperType_name = map[int32]string{
	0: "CONTACTS_UNDEFINED",
	1: "CONTACTS_ADD",
	2: "CONTACTS_DELETE",
}
var ContactsOperType_value = map[string]int32{
	"CONTACTS_UNDEFINED": 0,
	"CONTACTS_ADD":       1,
	"CONTACTS_DELETE":    2,
}

func (x ContactsOperType) Enum() *ContactsOperType {
	p := new(ContactsOperType)
	*p = x
	return p
}
func (x ContactsOperType) String() string {
	return proto.EnumName(ContactsOperType_name, int32(x))
}
func (x *ContactsOperType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ContactsOperType_value, data, "ContactsOperType")
	if err != nil {
		return err
	}
	*x = ContactsOperType(value)
	return nil
}
func (ContactsOperType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{0}
}

type RejectRecommendReq_RejectType int32

const (
	RejectRecommendReq_REJECT_FROM_CONTACT RejectRecommendReq_RejectType = 1
	RejectRecommendReq_REJECT_FROM_GAME    RejectRecommendReq_RejectType = 2
)

var RejectRecommendReq_RejectType_name = map[int32]string{
	1: "REJECT_FROM_CONTACT",
	2: "REJECT_FROM_GAME",
}
var RejectRecommendReq_RejectType_value = map[string]int32{
	"REJECT_FROM_CONTACT": 1,
	"REJECT_FROM_GAME":    2,
}

func (x RejectRecommendReq_RejectType) Enum() *RejectRecommendReq_RejectType {
	p := new(RejectRecommendReq_RejectType)
	*p = x
	return p
}
func (x RejectRecommendReq_RejectType) String() string {
	return proto.EnumName(RejectRecommendReq_RejectType_name, int32(x))
}
func (x *RejectRecommendReq_RejectType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RejectRecommendReq_RejectType_value, data, "RejectRecommendReq_RejectType")
	if err != nil {
		return err
	}
	*x = RejectRecommendReq_RejectType(value)
	return nil
}
func (RejectRecommendReq_RejectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{2, 0}
}

type RecommendFromAll_RecommendType int32

const (
	RecommendFromAll_RECOMMEND_FROM_CONTACTS RecommendFromAll_RecommendType = 1
	RecommendFromAll_RECOMMEND_FROM_GAME     RecommendFromAll_RecommendType = 2
)

var RecommendFromAll_RecommendType_name = map[int32]string{
	1: "RECOMMEND_FROM_CONTACTS",
	2: "RECOMMEND_FROM_GAME",
}
var RecommendFromAll_RecommendType_value = map[string]int32{
	"RECOMMEND_FROM_CONTACTS": 1,
	"RECOMMEND_FROM_GAME":     2,
}

func (x RecommendFromAll_RecommendType) Enum() *RecommendFromAll_RecommendType {
	p := new(RecommendFromAll_RecommendType)
	*p = x
	return p
}
func (x RecommendFromAll_RecommendType) String() string {
	return proto.EnumName(RecommendFromAll_RecommendType_name, int32(x))
}
func (x *RecommendFromAll_RecommendType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RecommendFromAll_RecommendType_value, data, "RecommendFromAll_RecommendType")
	if err != nil {
		return err
	}
	*x = RecommendFromAll_RecommendType(value)
	return nil
}
func (RecommendFromAll_RecommendType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{9, 0}
}

// 添加或更新
type AddOrUpdateContactsReq struct {
	BaseReq    *ga.BaseReq      `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	PhoneList  []string         `protobuf:"bytes,2,rep,name=phone_list,json=phoneList" json:"phone_list,omitempty"`
	IsFullData bool             `protobuf:"varint,3,req,name=is_full_data,json=isFullData" json:"is_full_data"`
	OperType   ContactsOperType `protobuf:"varint,4,req,name=oper_type,json=operType,enum=ga.ContactsOperType" json:"oper_type"`
}

func (m *AddOrUpdateContactsReq) Reset()         { *m = AddOrUpdateContactsReq{} }
func (m *AddOrUpdateContactsReq) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateContactsReq) ProtoMessage()    {}
func (*AddOrUpdateContactsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{0}
}

func (m *AddOrUpdateContactsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AddOrUpdateContactsReq) GetPhoneList() []string {
	if m != nil {
		return m.PhoneList
	}
	return nil
}

func (m *AddOrUpdateContactsReq) GetIsFullData() bool {
	if m != nil {
		return m.IsFullData
	}
	return false
}

func (m *AddOrUpdateContactsReq) GetOperType() ContactsOperType {
	if m != nil {
		return m.OperType
	}
	return ContactsOperType_CONTACTS_UNDEFINED
}

type AddOrUpdateContactsResp struct {
	BaseResp       *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	ContactVersion uint32       `protobuf:"varint,2,opt,name=contact_version,json=contactVersion" json:"contact_version"`
}

func (m *AddOrUpdateContactsResp) Reset()         { *m = AddOrUpdateContactsResp{} }
func (m *AddOrUpdateContactsResp) String() string { return proto.CompactTextString(m) }
func (*AddOrUpdateContactsResp) ProtoMessage()    {}
func (*AddOrUpdateContactsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{1}
}

func (m *AddOrUpdateContactsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *AddOrUpdateContactsResp) GetContactVersion() uint32 {
	if m != nil {
		return m.ContactVersion
	}
	return 0
}

// 不再推荐
type RejectRecommendReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Phone   string      `protobuf:"bytes,2,opt,name=phone" json:"phone"`
	Account string      `protobuf:"bytes,3,opt,name=account" json:"account"`
	Type    uint32      `protobuf:"varint,4,opt,name=type" json:"type"`
}

func (m *RejectRecommendReq) Reset()                    { *m = RejectRecommendReq{} }
func (m *RejectRecommendReq) String() string            { return proto.CompactTextString(m) }
func (*RejectRecommendReq) ProtoMessage()               {}
func (*RejectRecommendReq) Descriptor() ([]byte, []int) { return fileDescriptorUserrecommend_, []int{2} }

func (m *RejectRecommendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *RejectRecommendReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RejectRecommendReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RejectRecommendReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type RejectRecommendResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Phone    string       `protobuf:"bytes,2,opt,name=phone" json:"phone"`
	Account  string       `protobuf:"bytes,3,opt,name=account" json:"account"`
}

func (m *RejectRecommendResp) Reset()         { *m = RejectRecommendResp{} }
func (m *RejectRecommendResp) String() string { return proto.CompactTextString(m) }
func (*RejectRecommendResp) ProtoMessage()    {}
func (*RejectRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{3}
}

func (m *RejectRecommendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *RejectRecommendResp) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RejectRecommendResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

// 是否向其他用户推荐
type ChangeRecommendStatusReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
	Status  bool        `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *ChangeRecommendStatusReq) Reset()         { *m = ChangeRecommendStatusReq{} }
func (m *ChangeRecommendStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusReq) ProtoMessage()    {}
func (*ChangeRecommendStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{4}
}

func (m *ChangeRecommendStatusReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *ChangeRecommendStatusReq) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

type ChangeRecommendStatusResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Status   bool         `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *ChangeRecommendStatusResp) Reset()         { *m = ChangeRecommendStatusResp{} }
func (m *ChangeRecommendStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusResp) ProtoMessage()    {}
func (*ChangeRecommendStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{5}
}

func (m *ChangeRecommendStatusResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *ChangeRecommendStatusResp) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

// 获取自己推荐状态
type GetRecommendStatusReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetRecommendStatusReq) Reset()         { *m = GetRecommendStatusReq{} }
func (m *GetRecommendStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStatusReq) ProtoMessage()    {}
func (*GetRecommendStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{6}
}

func (m *GetRecommendStatusReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRecommendStatusResp struct {
	BaseResp *ga.BaseResp `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	Status   bool         `protobuf:"varint,2,req,name=status" json:"status"`
}

func (m *GetRecommendStatusResp) Reset()         { *m = GetRecommendStatusResp{} }
func (m *GetRecommendStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStatusResp) ProtoMessage()    {}
func (*GetRecommendStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{7}
}

func (m *GetRecommendStatusResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecommendStatusResp) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

// 推荐通讯录细节信息
type RecommendFromContacts struct {
	Phone    string `protobuf:"bytes,1,req,name=phone" json:"phone"`
	Account  string `protobuf:"bytes,2,opt,name=account" json:"account"`
	Nickname string `protobuf:"bytes,3,opt,name=nickname" json:"nickname"`
}

func (m *RecommendFromContacts) Reset()         { *m = RecommendFromContacts{} }
func (m *RecommendFromContacts) String() string { return proto.CompactTextString(m) }
func (*RecommendFromContacts) ProtoMessage()    {}
func (*RecommendFromContacts) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{8}
}

func (m *RecommendFromContacts) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RecommendFromContacts) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RecommendFromContacts) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// 通讯录和同玩游戏推荐
type RecommendFromAll struct {
	RecommendType uint32 `protobuf:"varint,1,req,name=recommend_type,json=recommendType" json:"recommend_type"`
	Account       string `protobuf:"bytes,2,req,name=account" json:"account"`
	Nickname      string `protobuf:"bytes,3,req,name=nickname" json:"nickname"`
	Phone         string `protobuf:"bytes,4,req,name=phone" json:"phone"`
	GameName      string `protobuf:"bytes,5,opt,name=game_name,json=gameName" json:"game_name"`
	City          string `protobuf:"bytes,6,opt,name=city" json:"city"`
}

func (m *RecommendFromAll) Reset()                    { *m = RecommendFromAll{} }
func (m *RecommendFromAll) String() string            { return proto.CompactTextString(m) }
func (*RecommendFromAll) ProtoMessage()               {}
func (*RecommendFromAll) Descriptor() ([]byte, []int) { return fileDescriptorUserrecommend_, []int{9} }

func (m *RecommendFromAll) GetRecommendType() uint32 {
	if m != nil {
		return m.RecommendType
	}
	return 0
}

func (m *RecommendFromAll) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *RecommendFromAll) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *RecommendFromAll) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *RecommendFromAll) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *RecommendFromAll) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

// 从通讯录推荐
type GetRecommendFromContactsReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetRecommendFromContactsReq) Reset()         { *m = GetRecommendFromContactsReq{} }
func (m *GetRecommendFromContactsReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendFromContactsReq) ProtoMessage()    {}
func (*GetRecommendFromContactsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{10}
}

func (m *GetRecommendFromContactsReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetRecommendFromContactsResp struct {
	BaseResp       *ga.BaseResp             `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RecommendList  []*RecommendFromContacts `protobuf:"bytes,2,rep,name=recommend_list,json=recommendList" json:"recommend_list,omitempty"`
	ContactVersion uint32                   `protobuf:"varint,3,opt,name=contact_version,json=contactVersion" json:"contact_version"`
}

func (m *GetRecommendFromContactsResp) Reset()         { *m = GetRecommendFromContactsResp{} }
func (m *GetRecommendFromContactsResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendFromContactsResp) ProtoMessage()    {}
func (*GetRecommendFromContactsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{11}
}

func (m *GetRecommendFromContactsResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetRecommendFromContactsResp) GetRecommendList() []*RecommendFromContacts {
	if m != nil {
		return m.RecommendList
	}
	return nil
}

func (m *GetRecommendFromContactsResp) GetContactVersion() uint32 {
	if m != nil {
		return m.ContactVersion
	}
	return 0
}

// 总的推荐，包括通讯录和游戏
type GetUserRecommendReq struct {
	BaseReq *ga.BaseReq `protobuf:"bytes,1,req,name=base_req,json=baseReq" json:"base_req,omitempty"`
}

func (m *GetUserRecommendReq) Reset()         { *m = GetUserRecommendReq{} }
func (m *GetUserRecommendReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRecommendReq) ProtoMessage()    {}
func (*GetUserRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{12}
}

func (m *GetUserRecommendReq) GetBaseReq() *ga.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetUserRecommendResp struct {
	BaseResp         *ga.BaseResp        `protobuf:"bytes,1,req,name=base_resp,json=baseResp" json:"base_resp,omitempty"`
	RecommendAllList []*RecommendFromAll `protobuf:"bytes,2,rep,name=recommend_all_list,json=recommendAllList" json:"recommend_all_list,omitempty"`
	ContactVersion   uint32              `protobuf:"varint,3,opt,name=contact_version,json=contactVersion" json:"contact_version"`
	BatchNum         uint32              `protobuf:"varint,4,opt,name=batch_num,json=batchNum" json:"batch_num"`
}

func (m *GetUserRecommendResp) Reset()         { *m = GetUserRecommendResp{} }
func (m *GetUserRecommendResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRecommendResp) ProtoMessage()    {}
func (*GetUserRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorUserrecommend_, []int{13}
}

func (m *GetUserRecommendResp) GetBaseResp() *ga.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserRecommendResp) GetRecommendAllList() []*RecommendFromAll {
	if m != nil {
		return m.RecommendAllList
	}
	return nil
}

func (m *GetUserRecommendResp) GetContactVersion() uint32 {
	if m != nil {
		return m.ContactVersion
	}
	return 0
}

func (m *GetUserRecommendResp) GetBatchNum() uint32 {
	if m != nil {
		return m.BatchNum
	}
	return 0
}

func init() {
	proto.RegisterType((*AddOrUpdateContactsReq)(nil), "ga.AddOrUpdateContactsReq")
	proto.RegisterType((*AddOrUpdateContactsResp)(nil), "ga.AddOrUpdateContactsResp")
	proto.RegisterType((*RejectRecommendReq)(nil), "ga.RejectRecommendReq")
	proto.RegisterType((*RejectRecommendResp)(nil), "ga.RejectRecommendResp")
	proto.RegisterType((*ChangeRecommendStatusReq)(nil), "ga.ChangeRecommendStatusReq")
	proto.RegisterType((*ChangeRecommendStatusResp)(nil), "ga.ChangeRecommendStatusResp")
	proto.RegisterType((*GetRecommendStatusReq)(nil), "ga.GetRecommendStatusReq")
	proto.RegisterType((*GetRecommendStatusResp)(nil), "ga.GetRecommendStatusResp")
	proto.RegisterType((*RecommendFromContacts)(nil), "ga.RecommendFromContacts")
	proto.RegisterType((*RecommendFromAll)(nil), "ga.RecommendFromAll")
	proto.RegisterType((*GetRecommendFromContactsReq)(nil), "ga.GetRecommendFromContactsReq")
	proto.RegisterType((*GetRecommendFromContactsResp)(nil), "ga.GetRecommendFromContactsResp")
	proto.RegisterType((*GetUserRecommendReq)(nil), "ga.GetUserRecommendReq")
	proto.RegisterType((*GetUserRecommendResp)(nil), "ga.GetUserRecommendResp")
	proto.RegisterEnum("ga.ContactsOperType", ContactsOperType_name, ContactsOperType_value)
	proto.RegisterEnum("ga.RejectRecommendReq_RejectType", RejectRecommendReq_RejectType_name, RejectRecommendReq_RejectType_value)
	proto.RegisterEnum("ga.RecommendFromAll_RecommendType", RecommendFromAll_RecommendType_name, RecommendFromAll_RecommendType_value)
}
func (m *AddOrUpdateContactsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateContactsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseReq.Size()))
		n1, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	if m.IsFullData {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(m.OperType))
	return i, nil
}

func (m *AddOrUpdateContactsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddOrUpdateContactsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseResp.Size()))
		n2, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(m.ContactVersion))
	return i, nil
}

func (m *RejectRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RejectRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseReq.Size()))
		n3, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *RejectRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RejectRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseResp.Size()))
		n4, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	return i, nil
}

func (m *ChangeRecommendStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeRecommendStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseReq.Size()))
		n5, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	if m.Status {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChangeRecommendStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChangeRecommendStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseResp.Size()))
		n6, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	if m.Status {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetRecommendStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseReq.Size()))
		n7, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetRecommendStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseResp.Size()))
		n8, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	dAtA[i] = 0x10
	i++
	if m.Status {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RecommendFromContacts) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendFromContacts) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	return i, nil
}

func (m *RecommendFromAll) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendFromAll) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(m.RecommendType))
	dAtA[i] = 0x12
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.GameName)))
	i += copy(dAtA[i:], m.GameName)
	dAtA[i] = 0x32
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(len(m.City)))
	i += copy(dAtA[i:], m.City)
	return i, nil
}

func (m *GetRecommendFromContactsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendFromContactsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseReq.Size()))
		n9, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *GetRecommendFromContactsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendFromContactsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseResp.Size()))
		n10, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if len(m.RecommendList) > 0 {
		for _, msg := range m.RecommendList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserrecommend_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(m.ContactVersion))
	return i, nil
}

func (m *GetUserRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseReq == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_req")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseReq.Size()))
		n11, err := m.BaseReq.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *GetUserRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResp == nil {
		return 0, github_com_gogo_protobuf_proto2.NewRequiredNotSetError("base_resp")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BaseResp.Size()))
		n12, err := m.BaseResp.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if len(m.RecommendAllList) > 0 {
		for _, msg := range m.RecommendAllList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintUserrecommend_(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(m.ContactVersion))
	dAtA[i] = 0x20
	i++
	i = encodeVarintUserrecommend_(dAtA, i, uint64(m.BatchNum))
	return i, nil
}

func encodeFixed64Userrecommend_(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Userrecommend_(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintUserrecommend_(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AddOrUpdateContactsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	if len(m.PhoneList) > 0 {
		for _, s := range m.PhoneList {
			l = len(s)
			n += 1 + l + sovUserrecommend_(uint64(l))
		}
	}
	n += 2
	n += 1 + sovUserrecommend_(uint64(m.OperType))
	return n
}

func (m *AddOrUpdateContactsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	n += 1 + sovUserrecommend_(uint64(m.ContactVersion))
	return n
}

func (m *RejectRecommendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	l = len(m.Phone)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovUserrecommend_(uint64(l))
	n += 1 + sovUserrecommend_(uint64(m.Type))
	return n
}

func (m *RejectRecommendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	l = len(m.Phone)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovUserrecommend_(uint64(l))
	return n
}

func (m *ChangeRecommendStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	n += 2
	return n
}

func (m *ChangeRecommendStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	n += 2
	return n
}

func (m *GetRecommendStatusReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	return n
}

func (m *GetRecommendStatusResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	n += 2
	return n
}

func (m *RecommendFromContacts) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.Account)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovUserrecommend_(uint64(l))
	return n
}

func (m *RecommendFromAll) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovUserrecommend_(uint64(m.RecommendType))
	l = len(m.Account)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.GameName)
	n += 1 + l + sovUserrecommend_(uint64(l))
	l = len(m.City)
	n += 1 + l + sovUserrecommend_(uint64(l))
	return n
}

func (m *GetRecommendFromContactsReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	return n
}

func (m *GetRecommendFromContactsResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	if len(m.RecommendList) > 0 {
		for _, e := range m.RecommendList {
			l = e.Size()
			n += 1 + l + sovUserrecommend_(uint64(l))
		}
	}
	n += 1 + sovUserrecommend_(uint64(m.ContactVersion))
	return n
}

func (m *GetUserRecommendReq) Size() (n int) {
	var l int
	_ = l
	if m.BaseReq != nil {
		l = m.BaseReq.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	return n
}

func (m *GetUserRecommendResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResp != nil {
		l = m.BaseResp.Size()
		n += 1 + l + sovUserrecommend_(uint64(l))
	}
	if len(m.RecommendAllList) > 0 {
		for _, e := range m.RecommendAllList {
			l = e.Size()
			n += 1 + l + sovUserrecommend_(uint64(l))
		}
	}
	n += 1 + sovUserrecommend_(uint64(m.ContactVersion))
	n += 1 + sovUserrecommend_(uint64(m.BatchNum))
	return n
}

func sovUserrecommend_(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozUserrecommend_(x uint64) (n int) {
	return sovUserrecommend_(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AddOrUpdateContactsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateContactsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateContactsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PhoneList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PhoneList = append(m.PhoneList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFullData", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFullData = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (ContactsOperType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("is_full_data")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("oper_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddOrUpdateContactsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddOrUpdateContactsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddOrUpdateContactsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContactVersion", wireType)
			}
			m.ContactVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContactVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RejectRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RejectRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RejectRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RejectRecommendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RejectRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RejectRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeRecommendStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeRecommendStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeRecommendStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChangeRecommendStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChangeRecommendStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChangeRecommendStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecommendFromContacts) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecommendFromContacts: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecommendFromContacts: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecommendFromAll) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecommendFromAll: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecommendFromAll: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendType", wireType)
			}
			m.RecommendType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RecommendType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field City", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.City = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("recommend_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("nickname")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendFromContactsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendFromContactsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendFromContactsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendFromContactsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendFromContactsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendFromContactsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendList = append(m.RecommendList, &RecommendFromContacts{})
			if err := m.RecommendList[len(m.RecommendList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContactVersion", wireType)
			}
			m.ContactVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContactVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseReq", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseReq == nil {
				m.BaseReq = &ga.BaseReq{}
			}
			if err := m.BaseReq.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_req")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRecommendResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResp == nil {
				m.BaseResp = &ga.BaseResp{}
			}
			if err := m.BaseResp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendAllList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendAllList = append(m.RecommendAllList, &RecommendFromAll{})
			if err := m.RecommendAllList[len(m.RecommendAllList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContactVersion", wireType)
			}
			m.ContactVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContactVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BatchNum", wireType)
			}
			m.BatchNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipUserrecommend_(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthUserrecommend_
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto3.NewRequiredNotSetError("base_resp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipUserrecommend_(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowUserrecommend_
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowUserrecommend_
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthUserrecommend_
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowUserrecommend_
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipUserrecommend_(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthUserrecommend_ = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowUserrecommend_   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("userrecommend_.proto", fileDescriptorUserrecommend_) }

var fileDescriptorUserrecommend_ = []byte{
	// 786 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0xcd, 0x6e, 0xf3, 0x44,
	0x14, 0xad, 0x9d, 0xb4, 0x4d, 0x6e, 0x9b, 0xd6, 0x9a, 0xa4, 0xad, 0xfb, 0x43, 0x08, 0x5e, 0x54,
	0xa1, 0x88, 0x54, 0x8a, 0x84, 0x58, 0x20, 0x04, 0xf9, 0x71, 0x2a, 0x50, 0x93, 0x08, 0x37, 0xed,
	0x82, 0x8d, 0x99, 0x38, 0x43, 0x6a, 0x18, 0x7b, 0x5c, 0x7b, 0x8c, 0x14, 0x89, 0x87, 0xe0, 0x15,
	0x78, 0x0d, 0x56, 0x2c, 0xbb, 0x83, 0x1d, 0x3b, 0x84, 0xca, 0x8b, 0x20, 0xff, 0xc4, 0xb1, 0x49,
	0xda, 0x4f, 0xe9, 0xf7, 0xed, 0x92, 0x73, 0x7d, 0xe7, 0xdc, 0x73, 0xef, 0x99, 0x6b, 0x43, 0xc5,
	0xf7, 0x88, 0xeb, 0x12, 0x83, 0x59, 0x16, 0xb1, 0x27, 0x7a, 0xc3, 0x71, 0x19, 0x67, 0x48, 0x9c,
	0xe2, 0x93, 0xd2, 0x14, 0xeb, 0x63, 0xec, 0x91, 0x08, 0x52, 0x7e, 0x17, 0xe0, 0xb0, 0x35, 0x99,
	0x0c, 0xdd, 0x5b, 0x67, 0x82, 0x39, 0xe9, 0x30, 0x9b, 0x63, 0x83, 0x7b, 0x1a, 0x79, 0x40, 0xe7,
	0x50, 0x08, 0x1e, 0xd4, 0x5d, 0xf2, 0x20, 0x0b, 0x35, 0xb1, 0xbe, 0xd3, 0xdc, 0x69, 0x4c, 0x71,
	0xa3, 0x8d, 0x3d, 0xa2, 0x91, 0x07, 0x6d, 0x7b, 0x1c, 0xfd, 0x40, 0xef, 0x01, 0x38, 0xf7, 0xcc,
	0x26, 0x3a, 0x35, 0x3d, 0x2e, 0x8b, 0xb5, 0x5c, 0xbd, 0xa8, 0x15, 0x43, 0xe4, 0xda, 0xf4, 0x38,
	0x3a, 0x87, 0x5d, 0xd3, 0xd3, 0xbf, 0xf7, 0x29, 0xd5, 0x27, 0x98, 0x63, 0x39, 0x57, 0x13, 0xeb,
	0x85, 0x76, 0xfe, 0xf1, 0xef, 0xf7, 0x37, 0x34, 0x30, 0xbd, 0x9e, 0x4f, 0x69, 0x17, 0x73, 0x8c,
	0x3e, 0x85, 0x22, 0x73, 0x88, 0xab, 0xf3, 0x99, 0x43, 0xe4, 0x7c, 0x4d, 0xac, 0xef, 0x35, 0x2b,
	0x01, 0xdf, 0xbc, 0xa4, 0xa1, 0x43, 0xdc, 0xd1, 0xcc, 0x21, 0x71, 0x6a, 0x81, 0xc5, 0xff, 0x15,
	0x0f, 0x8e, 0x56, 0x2a, 0xf0, 0x1c, 0xf4, 0x21, 0x14, 0x63, 0x09, 0x9e, 0x13, 0x6b, 0xd8, 0x5d,
	0x68, 0xf0, 0x1c, 0xad, 0x30, 0x8e, 0x7f, 0xa1, 0x8f, 0x61, 0xdf, 0x88, 0x52, 0xf5, 0x9f, 0x88,
	0xeb, 0x99, 0xcc, 0x96, 0xc5, 0x9a, 0x50, 0x2f, 0xc5, 0x74, 0x7b, 0x71, 0xf0, 0x2e, 0x8a, 0x29,
	0x7f, 0x08, 0x80, 0x34, 0xf2, 0x03, 0x31, 0xb8, 0x36, 0xef, 0xf2, 0x3a, 0x3d, 0x3b, 0x81, 0xcd,
	0xb0, 0x43, 0x21, 0x47, 0x31, 0xe6, 0x88, 0x20, 0x54, 0x85, 0x6d, 0x6c, 0x18, 0xcc, 0xb7, 0xb9,
	0x9c, 0x4b, 0x45, 0xe7, 0x20, 0x92, 0x21, 0x1f, 0xf7, 0x68, 0x51, 0x5e, 0x88, 0x28, 0x9f, 0x01,
	0x44, 0x35, 0x05, 0x7d, 0x41, 0x47, 0x50, 0xd6, 0xd4, 0xaf, 0xd5, 0xce, 0x48, 0xef, 0x69, 0xc3,
	0xbe, 0xde, 0x19, 0x0e, 0x46, 0xad, 0xce, 0x48, 0x12, 0x50, 0x05, 0xa4, 0x74, 0xe0, 0xaa, 0xd5,
	0x57, 0x25, 0x51, 0xf9, 0x19, 0xca, 0x4b, 0x82, 0xd6, 0x6b, 0xe1, 0x5b, 0x88, 0x52, 0xbe, 0x03,
	0xb9, 0x73, 0x8f, 0xed, 0x29, 0x49, 0xd8, 0x6f, 0x38, 0xe6, 0xfe, 0x5a, 0x46, 0x3c, 0x83, 0x2d,
	0x2f, 0x4c, 0x92, 0xc5, 0x94, 0xc7, 0x62, 0x4c, 0x99, 0xc0, 0xf1, 0x33, 0x0c, 0xeb, 0xa9, 0x7c,
	0x99, 0xe5, 0x0b, 0x38, 0xb8, 0x22, 0xfc, 0xf5, 0x22, 0x14, 0x0c, 0x87, 0xab, 0x0e, 0x78, 0x97,
	0x35, 0xfa, 0x70, 0x90, 0x9c, 0xdf, 0x73, 0x99, 0x35, 0xbf, 0x32, 0x8b, 0x01, 0x06, 0xa7, 0x3f,
	0x3f, 0x40, 0x71, 0x95, 0x2b, 0x6b, 0x50, 0xb0, 0x4d, 0xe3, 0x47, 0x1b, 0x5b, 0x24, 0x33, 0xe1,
	0x04, 0x55, 0x7e, 0x15, 0x41, 0xca, 0xf0, 0xb6, 0x28, 0x45, 0x1f, 0xc1, 0xde, 0x62, 0x4d, 0x85,
	0xb6, 0x0e, 0xb8, 0xe7, 0xb6, 0x2e, 0x25, 0xb1, 0xd0, 0xd1, 0x99, 0x1a, 0xc4, 0x37, 0xd5, 0x20,
	0x2e, 0xd7, 0xb0, 0x50, 0x98, 0x5f, 0x56, 0xf8, 0x01, 0x14, 0xa7, 0xd8, 0x22, 0x7a, 0x98, 0xbe,
	0x99, 0x96, 0x10, 0xc0, 0x83, 0x20, 0x5d, 0x86, 0xbc, 0x61, 0xf2, 0x99, 0xbc, 0x95, 0x8a, 0x86,
	0x88, 0xa2, 0x42, 0x49, 0xcb, 0xd4, 0x7a, 0x0a, 0x47, 0x9a, 0xda, 0x19, 0xf6, 0xfb, 0xea, 0xa0,
	0x9b, 0xb9, 0x80, 0x37, 0x92, 0x10, 0x5d, 0xcd, 0x4c, 0x30, 0xbe, 0x84, 0x2a, 0x9c, 0xa6, 0xa7,
	0x9f, 0x9e, 0xce, 0x3a, 0x26, 0xfa, 0x4d, 0x80, 0xb3, 0xe7, 0xcf, 0x59, 0xcf, 0x4b, 0x5f, 0xa6,
	0x27, 0x94, 0xac, 0xf8, 0x9d, 0xe6, 0x71, 0xf0, 0xfc, 0x6a, 0x86, 0xc5, 0xd8, 0xc2, 0x37, 0xc0,
	0x8a, 0xd5, 0x9a, 0x7b, 0x61, 0xb5, 0x7e, 0x0e, 0xe5, 0x2b, 0xc2, 0x6f, 0x3d, 0xe2, 0xbe, 0x66,
	0xb5, 0x2a, 0x7f, 0x09, 0x50, 0x59, 0xce, 0x5f, 0x4f, 0x73, 0x1b, 0xd0, 0x42, 0x33, 0xa6, 0x34,
	0xad, 0xbb, 0xb2, 0xa4, 0xbb, 0x45, 0xa9, 0x26, 0x25, 0xcf, 0xb7, 0x28, 0x7d, 0x85, 0xea, 0xc0,
	0x7d, 0x63, 0xcc, 0x8d, 0x7b, 0xdd, 0xf6, 0xad, 0xcc, 0x6a, 0x2f, 0x84, 0xf0, 0xc0, 0xb7, 0x2e,
	0xbe, 0x01, 0xe9, 0xff, 0x2f, 0x43, 0x74, 0x08, 0x68, 0xee, 0x2b, 0xfd, 0x76, 0xd0, 0x55, 0x7b,
	0x5f, 0x0d, 0xd4, 0xae, 0xb4, 0x81, 0x24, 0xd8, 0x4d, 0xf0, 0x56, 0xb7, 0x2b, 0x09, 0xa8, 0x0c,
	0xfb, 0x09, 0xd2, 0x55, 0xaf, 0xd5, 0x91, 0x2a, 0x89, 0xed, 0xbb, 0xc7, 0xa7, 0xaa, 0xf0, 0xe7,
	0x53, 0x55, 0xf8, 0xe7, 0xa9, 0x2a, 0xfc, 0xf2, 0x6f, 0x75, 0x03, 0x64, 0x83, 0x59, 0x8d, 0x99,
	0x39, 0x63, 0x7e, 0xa0, 0xd3, 0x62, 0x13, 0x42, 0xa3, 0x4f, 0x85, 0x6f, 0x2f, 0xa6, 0x8c, 0x62,
	0x7b, 0xda, 0xf8, 0xa4, 0xc9, 0x79, 0xc3, 0x60, 0xd6, 0x65, 0x08, 0x1b, 0x8c, 0x5e, 0x62, 0xc7,
	0xb9, 0xcc, 0x7c, 0x70, 0xfc, 0x17, 0x00, 0x00, 0xff, 0xff, 0x1a, 0x36, 0xfb, 0xed, 0x80, 0x08,
	0x00, 0x00,
}
