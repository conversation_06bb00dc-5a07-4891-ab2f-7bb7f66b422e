// Code generated by protoc-gen-go. DO NOT EDIT.
// source: oauth2-logic_.proto

package oauth2logic // import "golang.52tt.com/protocol/app/oauth2logic"

import (
	fmt "fmt"

	proto "github.com/golang/protobuf/proto"

	math "math"

	app "golang.52tt.com/protocol/app"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetScopeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AppId                string       `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetScopeReq) Reset()         { *m = GetScopeReq{} }
func (m *GetScopeReq) String() string { return proto.CompactTextString(m) }
func (*GetScopeReq) ProtoMessage()    {}
func (*GetScopeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_oauth2_logic__98bdc56a974952c0, []int{0}
}
func (m *GetScopeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScopeReq.Unmarshal(m, b)
}
func (m *GetScopeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScopeReq.Marshal(b, m, deterministic)
}
func (dst *GetScopeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScopeReq.Merge(dst, src)
}
func (m *GetScopeReq) XXX_Size() int {
	return xxx_messageInfo_GetScopeReq.Size(m)
}
func (m *GetScopeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScopeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetScopeReq proto.InternalMessageInfo

func (m *GetScopeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetScopeReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

type GetScopeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Scope                string        `protobuf:"bytes,2,opt,name=scope,proto3" json:"scope,omitempty"`
	ScopeDes             string        `protobuf:"bytes,3,opt,name=scope_des,json=scopeDes,proto3" json:"scope_des,omitempty"`
	AppIcon              string        `protobuf:"bytes,4,opt,name=app_icon,json=appIcon,proto3" json:"app_icon,omitempty"`
	AppName              string        `protobuf:"bytes,5,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetScopeResp) Reset()         { *m = GetScopeResp{} }
func (m *GetScopeResp) String() string { return proto.CompactTextString(m) }
func (*GetScopeResp) ProtoMessage()    {}
func (*GetScopeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_oauth2_logic__98bdc56a974952c0, []int{1}
}
func (m *GetScopeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScopeResp.Unmarshal(m, b)
}
func (m *GetScopeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScopeResp.Marshal(b, m, deterministic)
}
func (dst *GetScopeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScopeResp.Merge(dst, src)
}
func (m *GetScopeResp) XXX_Size() int {
	return xxx_messageInfo_GetScopeResp.Size(m)
}
func (m *GetScopeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScopeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScopeResp proto.InternalMessageInfo

func (m *GetScopeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetScopeResp) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *GetScopeResp) GetScopeDes() string {
	if m != nil {
		return m.ScopeDes
	}
	return ""
}

func (m *GetScopeResp) GetAppIcon() string {
	if m != nil {
		return m.AppIcon
	}
	return ""
}

func (m *GetScopeResp) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

type GetAuthorizeCodeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	AppId                string       `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	State                string       `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Scope                string       `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`
	ResponseType         string       `protobuf:"bytes,5,opt,name=response_type,json=responseType,proto3" json:"response_type,omitempty"`
	IsNewUser            bool         `protobuf:"varint,6,opt,name=is_new_user,json=isNewUser,proto3" json:"is_new_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAuthorizeCodeReq) Reset()         { *m = GetAuthorizeCodeReq{} }
func (m *GetAuthorizeCodeReq) String() string { return proto.CompactTextString(m) }
func (*GetAuthorizeCodeReq) ProtoMessage()    {}
func (*GetAuthorizeCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_oauth2_logic__98bdc56a974952c0, []int{2}
}
func (m *GetAuthorizeCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthorizeCodeReq.Unmarshal(m, b)
}
func (m *GetAuthorizeCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthorizeCodeReq.Marshal(b, m, deterministic)
}
func (dst *GetAuthorizeCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthorizeCodeReq.Merge(dst, src)
}
func (m *GetAuthorizeCodeReq) XXX_Size() int {
	return xxx_messageInfo_GetAuthorizeCodeReq.Size(m)
}
func (m *GetAuthorizeCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthorizeCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthorizeCodeReq proto.InternalMessageInfo

func (m *GetAuthorizeCodeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetAuthorizeCodeReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *GetAuthorizeCodeReq) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *GetAuthorizeCodeReq) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

func (m *GetAuthorizeCodeReq) GetResponseType() string {
	if m != nil {
		return m.ResponseType
	}
	return ""
}

func (m *GetAuthorizeCodeReq) GetIsNewUser() bool {
	if m != nil {
		return m.IsNewUser
	}
	return false
}

type GetAuthorizeCodeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Code                 string        `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	State                string        `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetAuthorizeCodeResp) Reset()         { *m = GetAuthorizeCodeResp{} }
func (m *GetAuthorizeCodeResp) String() string { return proto.CompactTextString(m) }
func (*GetAuthorizeCodeResp) ProtoMessage()    {}
func (*GetAuthorizeCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_oauth2_logic__98bdc56a974952c0, []int{3}
}
func (m *GetAuthorizeCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAuthorizeCodeResp.Unmarshal(m, b)
}
func (m *GetAuthorizeCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAuthorizeCodeResp.Marshal(b, m, deterministic)
}
func (dst *GetAuthorizeCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAuthorizeCodeResp.Merge(dst, src)
}
func (m *GetAuthorizeCodeResp) XXX_Size() int {
	return xxx_messageInfo_GetAuthorizeCodeResp.Size(m)
}
func (m *GetAuthorizeCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAuthorizeCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAuthorizeCodeResp proto.InternalMessageInfo

func (m *GetAuthorizeCodeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetAuthorizeCodeResp) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *GetAuthorizeCodeResp) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func init() {
	proto.RegisterType((*GetScopeReq)(nil), "ga.oauth2logic.GetScopeReq")
	proto.RegisterType((*GetScopeResp)(nil), "ga.oauth2logic.GetScopeResp")
	proto.RegisterType((*GetAuthorizeCodeReq)(nil), "ga.oauth2logic.GetAuthorizeCodeReq")
	proto.RegisterType((*GetAuthorizeCodeResp)(nil), "ga.oauth2logic.GetAuthorizeCodeResp")
}

func init() { proto.RegisterFile("oauth2-logic_.proto", fileDescriptor_oauth2_logic__98bdc56a974952c0) }

var fileDescriptor_oauth2_logic__98bdc56a974952c0 = []byte{
	// 376 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x92, 0xdf, 0x8a, 0xd3, 0x40,
	0x14, 0xc6, 0x89, 0xf6, 0x4f, 0x32, 0x69, 0xbd, 0x98, 0x56, 0x88, 0x0a, 0x52, 0x22, 0x48, 0xbc,
	0x30, 0x85, 0x8a, 0x0f, 0x60, 0x2d, 0x14, 0x41, 0x7a, 0x11, 0xf5, 0xc6, 0x9b, 0x61, 0x32, 0x39,
	0xa4, 0xc1, 0x24, 0x67, 0x9a, 0x99, 0x50, 0xe2, 0xe3, 0xec, 0xbb, 0xec, 0x7b, 0x2d, 0x99, 0x24,
	0xdb, 0xc2, 0xee, 0xcd, 0xc2, 0xde, 0x9d, 0xf9, 0x7e, 0xcc, 0xc7, 0xf7, 0x1d, 0x0e, 0x59, 0x20,
	0xaf, 0xf5, 0x71, 0xf3, 0x39, 0xc7, 0x34, 0x13, 0x2c, 0x94, 0x15, 0x6a, 0xa4, 0xaf, 0x52, 0x1e,
	0x76, 0xba, 0x91, 0xdf, 0xce, 0x53, 0xce, 0x62, 0xae, 0xa0, 0xc3, 0xfe, 0x4f, 0xe2, 0xee, 0x41,
	0xff, 0x12, 0x28, 0x21, 0x82, 0x13, 0xfd, 0x48, 0xec, 0x16, 0xb2, 0x0a, 0x4e, 0x9e, 0xb5, 0xb2,
	0x02, 0x77, 0xe3, 0x86, 0x29, 0x0f, 0xb7, 0x5c, 0xb5, 0x38, 0x9a, 0xc6, 0xdd, 0x40, 0x5f, 0x93,
	0x09, 0x97, 0x92, 0x65, 0x89, 0xf7, 0x62, 0x65, 0x05, 0x4e, 0x34, 0xe6, 0x52, 0xfe, 0x48, 0xfc,
	0x1b, 0x8b, 0xcc, 0x2e, 0x76, 0x4a, 0xd2, 0x4f, 0xc4, 0xe9, 0xfd, 0x94, 0xec, 0x0d, 0x67, 0x17,
	0x43, 0x25, 0x23, 0x3b, 0xee, 0x27, 0xba, 0x24, 0x63, 0xd5, 0xfe, 0x1b, 0x1c, 0xcd, 0x83, 0xbe,
	0x23, 0x8e, 0x19, 0x58, 0x02, 0xca, 0x7b, 0x69, 0x88, 0x6d, 0x84, 0x1d, 0x28, 0xfa, 0x86, 0xd8,
	0x26, 0x85, 0xc0, 0xd2, 0x1b, 0x19, 0x36, 0x6d, 0x73, 0x08, 0x2c, 0x07, 0x54, 0xf2, 0x02, 0xbc,
	0xf1, 0x3d, 0x3a, 0xf0, 0x02, 0xfc, 0x5b, 0x8b, 0x2c, 0xf6, 0xa0, 0xbf, 0xd5, 0xfa, 0x88, 0x55,
	0xf6, 0x1f, 0xbe, 0x63, 0xf2, 0x0c, 0xdd, 0x4d, 0x7e, 0xcd, 0x35, 0xf4, 0x29, 0xbb, 0xc7, 0xa5,
	0xd5, 0xe8, 0xba, 0xd5, 0x07, 0x32, 0x6f, 0x37, 0x82, 0xa5, 0x02, 0xa6, 0x1b, 0x39, 0x44, 0x9c,
	0x0d, 0xe2, 0xef, 0x46, 0x02, 0x7d, 0x4f, 0xdc, 0x4c, 0xb1, 0x12, 0xce, 0xac, 0x56, 0x50, 0x79,
	0x93, 0x95, 0x15, 0xd8, 0x91, 0x93, 0xa9, 0x03, 0x9c, 0xff, 0x28, 0xa8, 0xfc, 0x7f, 0x64, 0xf9,
	0xb0, 0xc6, 0xd3, 0x76, 0x4e, 0xc9, 0x48, 0x60, 0x32, 0xac, 0xdc, 0xcc, 0x8f, 0xf7, 0xd8, 0xee,
	0x88, 0x27, 0xb0, 0x08, 0x9b, 0xac, 0xc1, 0xba, 0x35, 0x2b, 0x30, 0x81, 0xbc, 0xbb, 0xa1, 0xbf,
	0x41, 0x8a, 0x39, 0x2f, 0xd3, 0xf0, 0xeb, 0x46, 0xeb, 0x50, 0x60, 0xb1, 0x36, 0xb2, 0xc0, 0x7c,
	0xcd, 0xa5, 0x5c, 0x5f, 0x1d, 0x5f, 0x3c, 0x31, 0xe4, 0xcb, 0x5d, 0x00, 0x00, 0x00, 0xff, 0xff,
	0x4e, 0xd3, 0x43, 0x8a, 0xaa, 0x02, 0x00, 0x00,
}
