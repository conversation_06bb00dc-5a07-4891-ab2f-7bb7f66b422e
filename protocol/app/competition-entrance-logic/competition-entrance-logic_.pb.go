// Code generated by protoc-gen-go. DO NOT EDIT.
// source: competition-entrance-logic_.proto

package competition_entrance_logic // import "golang.52tt.com/protocol/app/competition-entrance-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"
import channel "golang.52tt.com/protocol/app/channel"
import game_tmp_channel "golang.52tt.com/protocol/app/game_tmp_channel"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type CheckShowFloatingComponentReq_Source int32

const (
	CheckShowFloatingComponentReq_SourceNone      CheckShowFloatingComponentReq_Source = 0
	CheckShowFloatingComponentReq_SourceEnterRoom CheckShowFloatingComponentReq_Source = 1
	CheckShowFloatingComponentReq_SourceExitRoom  CheckShowFloatingComponentReq_Source = 2
)

var CheckShowFloatingComponentReq_Source_name = map[int32]string{
	0: "SourceNone",
	1: "SourceEnterRoom",
	2: "SourceExitRoom",
}
var CheckShowFloatingComponentReq_Source_value = map[string]int32{
	"SourceNone":      0,
	"SourceEnterRoom": 1,
	"SourceExitRoom":  2,
}

func (x CheckShowFloatingComponentReq_Source) String() string {
	return proto.EnumName(CheckShowFloatingComponentReq_Source_name, int32(x))
}
func (CheckShowFloatingComponentReq_Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{9, 0}
}

// 赛事
type Competition struct {
	// 赛事主标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 赛事副标题
	Subtitle string `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// 赛事背景图
	Background string `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	// 跳转链接
	Link                 string   `protobuf:"bytes,4,opt,name=link,proto3" json:"link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Competition) Reset()         { *m = Competition{} }
func (m *Competition) String() string { return proto.CompactTextString(m) }
func (*Competition) ProtoMessage()    {}
func (*Competition) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{0}
}
func (m *Competition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Competition.Unmarshal(m, b)
}
func (m *Competition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Competition.Marshal(b, m, deterministic)
}
func (dst *Competition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Competition.Merge(dst, src)
}
func (m *Competition) XXX_Size() int {
	return xxx_messageInfo_Competition.Size(m)
}
func (m *Competition) XXX_DiscardUnknown() {
	xxx_messageInfo_Competition.DiscardUnknown(m)
}

var xxx_messageInfo_Competition proto.InternalMessageInfo

func (m *Competition) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Competition) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *Competition) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *Competition) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

// 赛事入口
type Entrance struct {
	// 入口主标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 入口副标题
	Subtitle string `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// 入口背景图
	Background string `protobuf:"bytes,3,opt,name=background,proto3" json:"background,omitempty"`
	// 入口副标题1
	Subtitle1 string `protobuf:"bytes,5,opt,name=subtitle1,proto3" json:"subtitle1,omitempty"`
	// 入口背景图1
	Background1 string `protobuf:"bytes,6,opt,name=background1,proto3" json:"background1,omitempty"`
	// 入口副标题2
	Subtitle2 string `protobuf:"bytes,7,opt,name=subtitle2,proto3" json:"subtitle2,omitempty"`
	// 入口背景图2
	Background2 string `protobuf:"bytes,8,opt,name=background2,proto3" json:"background2,omitempty"`
	// 入口副标题3(欢游专用)
	Subtitle3 string `protobuf:"bytes,9,opt,name=subtitle3,proto3" json:"subtitle3,omitempty"`
	// 入口背景图3(欢游专用)
	Background3 string `protobuf:"bytes,10,opt,name=background3,proto3" json:"background3,omitempty"`
	// 赛事列表
	CompetitionList      []*Competition `protobuf:"bytes,4,rep,name=competition_list,json=competitionList,proto3" json:"competition_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *Entrance) Reset()         { *m = Entrance{} }
func (m *Entrance) String() string { return proto.CompactTextString(m) }
func (*Entrance) ProtoMessage()    {}
func (*Entrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{1}
}
func (m *Entrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Entrance.Unmarshal(m, b)
}
func (m *Entrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Entrance.Marshal(b, m, deterministic)
}
func (dst *Entrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entrance.Merge(dst, src)
}
func (m *Entrance) XXX_Size() int {
	return xxx_messageInfo_Entrance.Size(m)
}
func (m *Entrance) XXX_DiscardUnknown() {
	xxx_messageInfo_Entrance.DiscardUnknown(m)
}

var xxx_messageInfo_Entrance proto.InternalMessageInfo

func (m *Entrance) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Entrance) GetSubtitle() string {
	if m != nil {
		return m.Subtitle
	}
	return ""
}

func (m *Entrance) GetBackground() string {
	if m != nil {
		return m.Background
	}
	return ""
}

func (m *Entrance) GetSubtitle1() string {
	if m != nil {
		return m.Subtitle1
	}
	return ""
}

func (m *Entrance) GetBackground1() string {
	if m != nil {
		return m.Background1
	}
	return ""
}

func (m *Entrance) GetSubtitle2() string {
	if m != nil {
		return m.Subtitle2
	}
	return ""
}

func (m *Entrance) GetBackground2() string {
	if m != nil {
		return m.Background2
	}
	return ""
}

func (m *Entrance) GetSubtitle3() string {
	if m != nil {
		return m.Subtitle3
	}
	return ""
}

func (m *Entrance) GetBackground3() string {
	if m != nil {
		return m.Background3
	}
	return ""
}

func (m *Entrance) GetCompetitionList() []*Competition {
	if m != nil {
		return m.CompetitionList
	}
	return nil
}

type GetCompetitionEntranceReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetCompetitionEntranceReq) Reset()         { *m = GetCompetitionEntranceReq{} }
func (m *GetCompetitionEntranceReq) String() string { return proto.CompactTextString(m) }
func (*GetCompetitionEntranceReq) ProtoMessage()    {}
func (*GetCompetitionEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{2}
}
func (m *GetCompetitionEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCompetitionEntranceReq.Unmarshal(m, b)
}
func (m *GetCompetitionEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCompetitionEntranceReq.Marshal(b, m, deterministic)
}
func (dst *GetCompetitionEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCompetitionEntranceReq.Merge(dst, src)
}
func (m *GetCompetitionEntranceReq) XXX_Size() int {
	return xxx_messageInfo_GetCompetitionEntranceReq.Size(m)
}
func (m *GetCompetitionEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCompetitionEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCompetitionEntranceReq proto.InternalMessageInfo

func (m *GetCompetitionEntranceReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

type GetCompetitionEntranceResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Entrance             *Entrance     `protobuf:"bytes,2,opt,name=entrance,proto3" json:"entrance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetCompetitionEntranceResp) Reset()         { *m = GetCompetitionEntranceResp{} }
func (m *GetCompetitionEntranceResp) String() string { return proto.CompactTextString(m) }
func (*GetCompetitionEntranceResp) ProtoMessage()    {}
func (*GetCompetitionEntranceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{3}
}
func (m *GetCompetitionEntranceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCompetitionEntranceResp.Unmarshal(m, b)
}
func (m *GetCompetitionEntranceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCompetitionEntranceResp.Marshal(b, m, deterministic)
}
func (dst *GetCompetitionEntranceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCompetitionEntranceResp.Merge(dst, src)
}
func (m *GetCompetitionEntranceResp) XXX_Size() int {
	return xxx_messageInfo_GetCompetitionEntranceResp.Size(m)
}
func (m *GetCompetitionEntranceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCompetitionEntranceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCompetitionEntranceResp proto.InternalMessageInfo

func (m *GetCompetitionEntranceResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetCompetitionEntranceResp) GetEntrance() *Entrance {
	if m != nil {
		return m.Entrance
	}
	return nil
}

type GetGameTmpChannelCfgReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetGameTmpChannelCfgReq) Reset()         { *m = GetGameTmpChannelCfgReq{} }
func (m *GetGameTmpChannelCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTmpChannelCfgReq) ProtoMessage()    {}
func (*GetGameTmpChannelCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{4}
}
func (m *GetGameTmpChannelCfgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTmpChannelCfgReq.Unmarshal(m, b)
}
func (m *GetGameTmpChannelCfgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTmpChannelCfgReq.Marshal(b, m, deterministic)
}
func (dst *GetGameTmpChannelCfgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTmpChannelCfgReq.Merge(dst, src)
}
func (m *GetGameTmpChannelCfgReq) XXX_Size() int {
	return xxx_messageInfo_GetGameTmpChannelCfgReq.Size(m)
}
func (m *GetGameTmpChannelCfgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTmpChannelCfgReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTmpChannelCfgReq proto.InternalMessageInfo

func (m *GetGameTmpChannelCfgReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetGameTmpChannelCfgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGameTmpChannelCfgResp struct {
	BaseResp             *app.BaseResp                   `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ActivityId           string                          `protobuf:"bytes,2,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	ContestInfo          *game_tmp_channel.ContestInfo   `protobuf:"bytes,3,opt,name=contest_info,json=contestInfo,proto3" json:"contest_info,omitempty"`
	Capacity             uint32                          `protobuf:"varint,4,opt,name=capacity,proto3" json:"capacity,omitempty"`
	Players              []*game_tmp_channel.PlayerLabel `protobuf:"bytes,5,rep,name=players,proto3" json:"players,omitempty"`
	ButtonInfo           *game_tmp_channel.ButtonInfo    `protobuf:"bytes,6,opt,name=button_info,json=buttonInfo,proto3" json:"button_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetGameTmpChannelCfgResp) Reset()         { *m = GetGameTmpChannelCfgResp{} }
func (m *GetGameTmpChannelCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetGameTmpChannelCfgResp) ProtoMessage()    {}
func (*GetGameTmpChannelCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{5}
}
func (m *GetGameTmpChannelCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTmpChannelCfgResp.Unmarshal(m, b)
}
func (m *GetGameTmpChannelCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTmpChannelCfgResp.Marshal(b, m, deterministic)
}
func (dst *GetGameTmpChannelCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTmpChannelCfgResp.Merge(dst, src)
}
func (m *GetGameTmpChannelCfgResp) XXX_Size() int {
	return xxx_messageInfo_GetGameTmpChannelCfgResp.Size(m)
}
func (m *GetGameTmpChannelCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTmpChannelCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTmpChannelCfgResp proto.InternalMessageInfo

func (m *GetGameTmpChannelCfgResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetGameTmpChannelCfgResp) GetActivityId() string {
	if m != nil {
		return m.ActivityId
	}
	return ""
}

func (m *GetGameTmpChannelCfgResp) GetContestInfo() *game_tmp_channel.ContestInfo {
	if m != nil {
		return m.ContestInfo
	}
	return nil
}

func (m *GetGameTmpChannelCfgResp) GetCapacity() uint32 {
	if m != nil {
		return m.Capacity
	}
	return 0
}

func (m *GetGameTmpChannelCfgResp) GetPlayers() []*game_tmp_channel.PlayerLabel {
	if m != nil {
		return m.Players
	}
	return nil
}

func (m *GetGameTmpChannelCfgResp) GetButtonInfo() *game_tmp_channel.ButtonInfo {
	if m != nil {
		return m.ButtonInfo
	}
	return nil
}

type ChannelCompetitionEntrance struct {
	CompetitionId        uint32            `protobuf:"varint,1,opt,name=competition_id,json=competitionId,proto3" json:"competition_id,omitempty"`
	EntranceIcon         string            `protobuf:"bytes,2,opt,name=entrance_icon,json=entranceIcon,proto3" json:"entrance_icon,omitempty"`
	ShowName             string            `protobuf:"bytes,3,opt,name=show_name,json=showName,proto3" json:"show_name,omitempty"`
	BizId                string            `protobuf:"bytes,4,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	Links                map[uint32]string `protobuf:"bytes,5,rep,name=links,proto3" json:"links,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ChannelCompetitionEntrance) Reset()         { *m = ChannelCompetitionEntrance{} }
func (m *ChannelCompetitionEntrance) String() string { return proto.CompactTextString(m) }
func (*ChannelCompetitionEntrance) ProtoMessage()    {}
func (*ChannelCompetitionEntrance) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{6}
}
func (m *ChannelCompetitionEntrance) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCompetitionEntrance.Unmarshal(m, b)
}
func (m *ChannelCompetitionEntrance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCompetitionEntrance.Marshal(b, m, deterministic)
}
func (dst *ChannelCompetitionEntrance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCompetitionEntrance.Merge(dst, src)
}
func (m *ChannelCompetitionEntrance) XXX_Size() int {
	return xxx_messageInfo_ChannelCompetitionEntrance.Size(m)
}
func (m *ChannelCompetitionEntrance) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCompetitionEntrance.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCompetitionEntrance proto.InternalMessageInfo

func (m *ChannelCompetitionEntrance) GetCompetitionId() uint32 {
	if m != nil {
		return m.CompetitionId
	}
	return 0
}

func (m *ChannelCompetitionEntrance) GetEntranceIcon() string {
	if m != nil {
		return m.EntranceIcon
	}
	return ""
}

func (m *ChannelCompetitionEntrance) GetShowName() string {
	if m != nil {
		return m.ShowName
	}
	return ""
}

func (m *ChannelCompetitionEntrance) GetBizId() string {
	if m != nil {
		return m.BizId
	}
	return ""
}

func (m *ChannelCompetitionEntrance) GetLinks() map[uint32]string {
	if m != nil {
		return m.Links
	}
	return nil
}

type GetChannelCompetitionEntranceListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetChannelCompetitionEntranceListReq) Reset()         { *m = GetChannelCompetitionEntranceListReq{} }
func (m *GetChannelCompetitionEntranceListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCompetitionEntranceListReq) ProtoMessage()    {}
func (*GetChannelCompetitionEntranceListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{7}
}
func (m *GetChannelCompetitionEntranceListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCompetitionEntranceListReq.Unmarshal(m, b)
}
func (m *GetChannelCompetitionEntranceListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCompetitionEntranceListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelCompetitionEntranceListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCompetitionEntranceListReq.Merge(dst, src)
}
func (m *GetChannelCompetitionEntranceListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelCompetitionEntranceListReq.Size(m)
}
func (m *GetChannelCompetitionEntranceListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCompetitionEntranceListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCompetitionEntranceListReq proto.InternalMessageInfo

func (m *GetChannelCompetitionEntranceListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetChannelCompetitionEntranceListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetChannelCompetitionEntranceListResp struct {
	BaseResp             *app.BaseResp                 `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	List                 []*ChannelCompetitionEntrance `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetChannelCompetitionEntranceListResp) Reset()         { *m = GetChannelCompetitionEntranceListResp{} }
func (m *GetChannelCompetitionEntranceListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCompetitionEntranceListResp) ProtoMessage()    {}
func (*GetChannelCompetitionEntranceListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{8}
}
func (m *GetChannelCompetitionEntranceListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelCompetitionEntranceListResp.Unmarshal(m, b)
}
func (m *GetChannelCompetitionEntranceListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelCompetitionEntranceListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelCompetitionEntranceListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelCompetitionEntranceListResp.Merge(dst, src)
}
func (m *GetChannelCompetitionEntranceListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelCompetitionEntranceListResp.Size(m)
}
func (m *GetChannelCompetitionEntranceListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelCompetitionEntranceListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelCompetitionEntranceListResp proto.InternalMessageInfo

func (m *GetChannelCompetitionEntranceListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetChannelCompetitionEntranceListResp) GetList() []*ChannelCompetitionEntrance {
	if m != nil {
		return m.List
	}
	return nil
}

type CheckShowFloatingComponentReq struct {
	BaseReq              *app.BaseReq                         `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	Uid                  uint32                               `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	RoomerUid            uint32                               `protobuf:"varint,3,opt,name=roomer_uid,json=roomerUid,proto3" json:"roomer_uid,omitempty"`
	ChannelId            uint32                               `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32                               `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	BizId                string                               `protobuf:"bytes,6,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	Source               CheckShowFloatingComponentReq_Source `protobuf:"varint,7,opt,name=source,proto3,enum=ga.competition_entrance_logic.CheckShowFloatingComponentReq_Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *CheckShowFloatingComponentReq) Reset()         { *m = CheckShowFloatingComponentReq{} }
func (m *CheckShowFloatingComponentReq) String() string { return proto.CompactTextString(m) }
func (*CheckShowFloatingComponentReq) ProtoMessage()    {}
func (*CheckShowFloatingComponentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{9}
}
func (m *CheckShowFloatingComponentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckShowFloatingComponentReq.Unmarshal(m, b)
}
func (m *CheckShowFloatingComponentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckShowFloatingComponentReq.Marshal(b, m, deterministic)
}
func (dst *CheckShowFloatingComponentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckShowFloatingComponentReq.Merge(dst, src)
}
func (m *CheckShowFloatingComponentReq) XXX_Size() int {
	return xxx_messageInfo_CheckShowFloatingComponentReq.Size(m)
}
func (m *CheckShowFloatingComponentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckShowFloatingComponentReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckShowFloatingComponentReq proto.InternalMessageInfo

func (m *CheckShowFloatingComponentReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *CheckShowFloatingComponentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckShowFloatingComponentReq) GetRoomerUid() uint32 {
	if m != nil {
		return m.RoomerUid
	}
	return 0
}

func (m *CheckShowFloatingComponentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckShowFloatingComponentReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *CheckShowFloatingComponentReq) GetBizId() string {
	if m != nil {
		return m.BizId
	}
	return ""
}

func (m *CheckShowFloatingComponentReq) GetSource() CheckShowFloatingComponentReq_Source {
	if m != nil {
		return m.Source
	}
	return CheckShowFloatingComponentReq_SourceNone
}

type CheckShowFloatingComponentResp struct {
	BaseResp             *app.BaseResp                        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Links                map[uint32]*channel.CompetitionLinks `protobuf:"bytes,2,rep,name=links,proto3" json:"links,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	BizId                string                               `protobuf:"bytes,3,opt,name=biz_id,json=bizId,proto3" json:"biz_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *CheckShowFloatingComponentResp) Reset()         { *m = CheckShowFloatingComponentResp{} }
func (m *CheckShowFloatingComponentResp) String() string { return proto.CompactTextString(m) }
func (*CheckShowFloatingComponentResp) ProtoMessage()    {}
func (*CheckShowFloatingComponentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a, []int{10}
}
func (m *CheckShowFloatingComponentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckShowFloatingComponentResp.Unmarshal(m, b)
}
func (m *CheckShowFloatingComponentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckShowFloatingComponentResp.Marshal(b, m, deterministic)
}
func (dst *CheckShowFloatingComponentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckShowFloatingComponentResp.Merge(dst, src)
}
func (m *CheckShowFloatingComponentResp) XXX_Size() int {
	return xxx_messageInfo_CheckShowFloatingComponentResp.Size(m)
}
func (m *CheckShowFloatingComponentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckShowFloatingComponentResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckShowFloatingComponentResp proto.InternalMessageInfo

func (m *CheckShowFloatingComponentResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *CheckShowFloatingComponentResp) GetLinks() map[uint32]*channel.CompetitionLinks {
	if m != nil {
		return m.Links
	}
	return nil
}

func (m *CheckShowFloatingComponentResp) GetBizId() string {
	if m != nil {
		return m.BizId
	}
	return ""
}

func init() {
	proto.RegisterType((*Competition)(nil), "ga.competition_entrance_logic.Competition")
	proto.RegisterType((*Entrance)(nil), "ga.competition_entrance_logic.Entrance")
	proto.RegisterType((*GetCompetitionEntranceReq)(nil), "ga.competition_entrance_logic.GetCompetitionEntranceReq")
	proto.RegisterType((*GetCompetitionEntranceResp)(nil), "ga.competition_entrance_logic.GetCompetitionEntranceResp")
	proto.RegisterType((*GetGameTmpChannelCfgReq)(nil), "ga.competition_entrance_logic.GetGameTmpChannelCfgReq")
	proto.RegisterType((*GetGameTmpChannelCfgResp)(nil), "ga.competition_entrance_logic.GetGameTmpChannelCfgResp")
	proto.RegisterType((*ChannelCompetitionEntrance)(nil), "ga.competition_entrance_logic.ChannelCompetitionEntrance")
	proto.RegisterMapType((map[uint32]string)(nil), "ga.competition_entrance_logic.ChannelCompetitionEntrance.LinksEntry")
	proto.RegisterType((*GetChannelCompetitionEntranceListReq)(nil), "ga.competition_entrance_logic.GetChannelCompetitionEntranceListReq")
	proto.RegisterType((*GetChannelCompetitionEntranceListResp)(nil), "ga.competition_entrance_logic.GetChannelCompetitionEntranceListResp")
	proto.RegisterType((*CheckShowFloatingComponentReq)(nil), "ga.competition_entrance_logic.CheckShowFloatingComponentReq")
	proto.RegisterType((*CheckShowFloatingComponentResp)(nil), "ga.competition_entrance_logic.CheckShowFloatingComponentResp")
	proto.RegisterMapType((map[uint32]*channel.CompetitionLinks)(nil), "ga.competition_entrance_logic.CheckShowFloatingComponentResp.LinksEntry")
	proto.RegisterEnum("ga.competition_entrance_logic.CheckShowFloatingComponentReq_Source", CheckShowFloatingComponentReq_Source_name, CheckShowFloatingComponentReq_Source_value)
}

func init() {
	proto.RegisterFile("competition-entrance-logic_.proto", fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a)
}

var fileDescriptor_competition_entrance_logic__1ed1b2d2589f692a = []byte{
	// 933 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0xdd, 0x72, 0xdb, 0x44,
	0x14, 0xc6, 0x76, 0xec, 0xda, 0x47, 0x71, 0xea, 0x59, 0xda, 0xa9, 0x30, 0xa4, 0x0d, 0x82, 0x42,
	0xe9, 0x4c, 0x94, 0xa9, 0x3c, 0x0c, 0xa5, 0x57, 0x10, 0x51, 0x82, 0x67, 0x42, 0x86, 0x51, 0xe8,
	0x4d, 0x99, 0x41, 0xac, 0xa4, 0x8d, 0xb2, 0x63, 0x69, 0x57, 0x95, 0xd6, 0x2d, 0xee, 0x33, 0x70,
	0xc5, 0x1b, 0xf0, 0x0a, 0xbc, 0x03, 0x8f, 0xc2, 0x13, 0xf0, 0x02, 0xcc, 0xee, 0x4a, 0xf6, 0x26,
	0xcd, 0x0f, 0x86, 0xe1, 0x6e, 0xcf, 0x77, 0xfe, 0xf6, 0x9c, 0xfd, 0xce, 0x91, 0xe0, 0xfd, 0x98,
	0xe7, 0x05, 0x11, 0x54, 0x50, 0xce, 0x76, 0x09, 0x13, 0x25, 0x66, 0x31, 0xd9, 0xcd, 0x78, 0x4a,
	0xe3, 0xd0, 0x2d, 0x4a, 0x2e, 0x38, 0xda, 0x4e, 0xb1, 0x6b, 0x58, 0x85, 0x8d, 0x55, 0xa8, 0xac,
	0xc6, 0xc3, 0x14, 0x87, 0x11, 0xae, 0x88, 0xb6, 0x1e, 0xdf, 0x49, 0x71, 0x4e, 0x76, 0x45, 0x5e,
	0xec, 0xc6, 0xa7, 0x98, 0x31, 0x92, 0xd5, 0x61, 0xc6, 0xa8, 0x91, 0x79, 0x21, 0x6a, 0xcc, 0xa9,
	0xc0, 0xf2, 0x57, 0x91, 0xd1, 0x2d, 0xe8, 0x0a, 0x2a, 0x32, 0x62, 0xb7, 0x76, 0x5a, 0x0f, 0x06,
	0x81, 0x16, 0xd0, 0x18, 0xfa, 0xd5, 0x3c, 0xd2, 0x8a, 0xb6, 0x52, 0x2c, 0x65, 0x74, 0x17, 0x20,
	0xc2, 0xf1, 0x2c, 0x2d, 0xf9, 0x9c, 0x25, 0x76, 0x47, 0x69, 0x0d, 0x04, 0x21, 0xd8, 0xc8, 0x28,
	0x9b, 0xd9, 0x1b, 0x4a, 0xa3, 0xce, 0xce, 0x5f, 0x6d, 0xe8, 0x3f, 0xad, 0x6b, 0xf8, 0x1f, 0x52,
	0xbe, 0x07, 0x83, 0xc6, 0xf6, 0x91, 0xdd, 0x55, 0xea, 0x15, 0x80, 0x76, 0xc0, 0x5a, 0xd9, 0x3e,
	0xb2, 0x7b, 0x4a, 0x6f, 0x42, 0xa6, 0xbf, 0x67, 0xdf, 0x38, 0xeb, 0xef, 0x9d, 0xf5, 0xf7, 0xec,
	0xfe, 0x79, 0x7f, 0xcf, 0xf4, 0x9f, 0xd8, 0x83, 0xb3, 0xfe, 0x93, 0xb3, 0xfe, 0x13, 0x1b, 0xce,
	0xfb, 0x4f, 0xd0, 0x33, 0x18, 0x99, 0xaf, 0x9d, 0xd1, 0x4a, 0xd8, 0x1b, 0x3b, 0x9d, 0x07, 0x96,
	0xf7, 0xd0, 0xbd, 0x92, 0x09, 0xae, 0xf1, 0x94, 0xc1, 0x4d, 0xc3, 0xee, 0x90, 0x56, 0xc2, 0xf1,
	0xe1, 0x9d, 0x03, 0x22, 0x0c, 0x93, 0xe6, 0x09, 0x02, 0xf2, 0x02, 0x7d, 0x04, 0x7d, 0x49, 0xa1,
	0xb0, 0x24, 0x2f, 0xd4, 0x43, 0x58, 0x9e, 0x25, 0x73, 0xed, 0xe3, 0x4a, 0xaa, 0x83, 0x1b, 0x91,
	0x3e, 0x38, 0xbf, 0xb4, 0x60, 0x7c, 0x59, 0x94, 0xaa, 0x40, 0x9f, 0xc0, 0xa0, 0x0e, 0x53, 0x15,
	0x75, 0x9c, 0xcd, 0x55, 0x9c, 0xaa, 0x08, 0xfa, 0x51, 0x7d, 0x42, 0x3e, 0xf4, 0x9b, 0xdb, 0xab,
	0x17, 0xb6, 0xbc, 0x8f, 0xaf, 0xa9, 0x6e, 0x99, 0x69, 0xe9, 0xe8, 0xfc, 0x04, 0x77, 0x0e, 0x88,
	0x38, 0xc0, 0x39, 0xf9, 0x3e, 0x2f, 0x7c, 0x4d, 0x6f, 0xff, 0x24, 0x5d, 0xa3, 0x22, 0xb4, 0x0d,
	0xd0, 0xcc, 0x05, 0x4d, 0xd4, 0x4d, 0x86, 0xc1, 0xa0, 0x46, 0xa6, 0x89, 0xf3, 0x47, 0x1b, 0xec,
	0x8b, 0x53, 0xac, 0x57, 0xee, 0x3d, 0xb0, 0x70, 0x2c, 0xe8, 0x4b, 0x2a, 0x16, 0x4d, 0x9e, 0x41,
	0x00, 0x0d, 0x34, 0x4d, 0x90, 0x0f, 0x9b, 0x31, 0x67, 0x82, 0x54, 0x22, 0xa4, 0xec, 0x84, 0x2b,
	0x5e, 0x5b, 0xde, 0x8e, 0x0c, 0x27, 0x07, 0x3a, 0x14, 0x79, 0x11, 0xd6, 0xd7, 0x72, 0x7d, 0x6d,
	0x38, 0x65, 0x27, 0x3c, 0xb0, 0xe2, 0x95, 0x20, 0xc7, 0x26, 0xc6, 0x05, 0x8e, 0xa9, 0x58, 0xa8,
	0x89, 0x1b, 0x06, 0x4b, 0x19, 0x3d, 0x81, 0x1b, 0x45, 0x86, 0x17, 0xa4, 0xac, 0xec, 0xae, 0x62,
	0xd3, 0xc5, 0xb1, 0xbf, 0x53, 0x36, 0x87, 0x38, 0x22, 0x59, 0xd0, 0x38, 0xa0, 0x2f, 0xc0, 0x8a,
	0xe6, 0x42, 0x70, 0xa6, 0xef, 0xd6, 0x53, 0x77, 0xbb, 0x77, 0xa1, 0xff, 0xbe, 0xb2, 0x53, 0x57,
	0x83, 0x68, 0x79, 0x76, 0x7e, 0x6f, 0xc3, 0xb8, 0xe9, 0xde, 0x9b, 0xe4, 0x41, 0xf7, 0x61, 0xcb,
	0x7c, 0x79, 0x9a, 0xa8, 0x76, 0x0e, 0x83, 0xa1, 0x81, 0x4e, 0x13, 0xf4, 0x01, 0x0c, 0x97, 0xa4,
	0xa0, 0x31, 0x67, 0x75, 0x1f, 0x37, 0x1b, 0x70, 0x1a, 0x73, 0x86, 0xde, 0x85, 0x41, 0x75, 0xca,
	0x5f, 0x85, 0x0c, 0xe7, 0xa4, 0x5e, 0x0f, 0x7d, 0x09, 0x1c, 0xe1, 0x9c, 0xa0, 0xdb, 0xd0, 0x8b,
	0xe8, 0x6b, 0x99, 0x40, 0x6f, 0xa4, 0x6e, 0x44, 0x5f, 0x4f, 0x13, 0xf4, 0x1c, 0xba, 0x72, 0x35,
	0x35, 0xad, 0xf9, 0xea, 0xba, 0x41, 0xbb, 0xb4, 0x12, 0xf7, 0x50, 0x86, 0x91, 0xd2, 0x22, 0xd0,
	0x21, 0xc7, 0x8f, 0x01, 0x56, 0x20, 0x1a, 0x41, 0x67, 0x46, 0x16, 0x75, 0x79, 0xf2, 0x28, 0x37,
	0xe0, 0x4b, 0x9c, 0xcd, 0x9b, 0x45, 0xa7, 0x85, 0x27, 0xed, 0xc7, 0x2d, 0x87, 0xc0, 0x87, 0x72,
	0xd8, 0x2e, 0x4d, 0x26, 0xe7, 0x7a, 0x1d, 0xae, 0xdf, 0x86, 0x9e, 0xc0, 0xd1, 0x8a, 0xe7, 0x5d,
	0x81, 0xa3, 0x69, 0xe2, 0xfc, 0xd6, 0x82, 0xfb, 0xff, 0x20, 0xcf, 0x7a, 0x84, 0xff, 0x56, 0x2e,
	0xfe, 0x4a, 0xd8, 0x6d, 0xd5, 0xd0, 0xcf, 0xff, 0x75, 0x43, 0x03, 0x15, 0xc6, 0xf9, 0xb3, 0x0d,
	0xdb, 0xfe, 0x29, 0x89, 0x67, 0xc7, 0xa7, 0xfc, 0xd5, 0xd7, 0x19, 0xc7, 0x82, 0xb2, 0x54, 0x9a,
	0x73, 0x46, 0xd8, 0x5a, 0x4d, 0x18, 0x41, 0x67, 0xbe, 0xec, 0x80, 0x3c, 0xca, 0x15, 0x50, 0x72,
	0x9e, 0x93, 0x32, 0x94, 0x8a, 0x8e, 0x5e, 0x01, 0x1a, 0x79, 0xa6, 0xd5, 0xc6, 0x86, 0xd8, 0x38,
	0xb7, 0x21, 0x8c, 0xa6, 0x76, 0x8d, 0xa6, 0x1a, 0x44, 0xeb, 0x99, 0x44, 0xfb, 0x01, 0x7a, 0x15,
	0x9f, 0x97, 0x31, 0x51, 0x5f, 0x96, 0x2d, 0xcf, 0xbf, 0xb6, 0x31, 0x57, 0xd4, 0xec, 0x1e, 0xab,
	0x50, 0x41, 0x1d, 0xd2, 0xf9, 0x12, 0x7a, 0x1a, 0x41, 0x5b, 0x00, 0xfa, 0x74, 0xc4, 0x19, 0x19,
	0xbd, 0x85, 0xde, 0x86, 0x9b, 0x5a, 0x7e, 0xca, 0x04, 0x29, 0x03, 0xce, 0xf3, 0x51, 0x0b, 0x21,
	0xd8, 0xaa, 0xc1, 0x9f, 0xa9, 0x50, 0x58, 0xdb, 0xf9, 0xb5, 0x0d, 0x77, 0xaf, 0xca, 0xb9, 0x1e,
	0x09, 0x7e, 0x6c, 0xc6, 0x4a, 0xb3, 0xe0, 0x9b, 0xff, 0x50, 0x6c, 0x55, 0xbc, 0x39, 0x5a, 0x46,
	0x93, 0x3b, 0x46, 0x93, 0xc7, 0x47, 0xd7, 0x4c, 0xdc, 0x43, 0x73, 0xe2, 0x2c, 0xef, 0x96, 0xbc,
	0x96, 0x6f, 0x7e, 0x2e, 0xd9, 0xac, 0x32, 0xe6, 0x70, 0xff, 0x18, 0xec, 0x98, 0xe7, 0xee, 0x82,
	0x2e, 0xf8, 0x5c, 0xda, 0xe6, 0x3c, 0x21, 0x99, 0xfe, 0x83, 0x7a, 0xfe, 0x59, 0xca, 0x33, 0xcc,
	0x52, 0xf7, 0x53, 0x4f, 0x08, 0x59, 0xdb, 0x9e, 0x82, 0x63, 0x9e, 0xed, 0xe1, 0xa2, 0xd8, 0xbb,
	0xfc, 0xe7, 0x2e, 0xea, 0x29, 0xc3, 0xc9, 0xdf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xe9, 0x67, 0x7e,
	0x88, 0x01, 0x0a, 0x00, 0x00,
}
