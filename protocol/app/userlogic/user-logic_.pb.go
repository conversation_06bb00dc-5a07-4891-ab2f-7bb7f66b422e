// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-logic_.proto

package userlogic // import "golang.52tt.com/protocol/app/userlogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import app "golang.52tt.com/protocol/app"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 降噪模式
type DenoiseMode int32

const (
	DenoiseMode_DenoiseModeDefault DenoiseMode = 0
	DenoiseMode_DenoiseModeNormal  DenoiseMode = 1
	DenoiseMode_DenoiseModeAI      DenoiseMode = 2
)

var DenoiseMode_name = map[int32]string{
	0: "DenoiseModeDefault",
	1: "DenoiseModeNormal",
	2: "DenoiseModeAI",
}
var DenoiseMode_value = map[string]int32{
	"DenoiseModeDefault": 0,
	"DenoiseModeNormal":  1,
	"DenoiseModeAI":      2,
}

func (x DenoiseMode) String() string {
	return proto.EnumName(DenoiseMode_name, int32(x))
}
func (DenoiseMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{0}
}

type UserContractInfoNew_UserContractType int32

const (
	UserContractInfoNew_INVALID_CONTRACT_TYPE               UserContractInfoNew_UserContractType = 0
	UserContractInfoNew_TT_SERVICE_CONTRACT                 UserContractInfoNew_UserContractType = 1
	UserContractInfoNew_HUANYOU_ANDROID_SERVICE_CONTRACT    UserContractInfoNew_UserContractType = 2
	UserContractInfoNew_HUANYOU_IOS_SERVICE_CONTRACT        UserContractInfoNew_UserContractType = 3
	UserContractInfoNew_TDIMENSION_ANDROID_SERVICE_CONTRACT UserContractInfoNew_UserContractType = 4
	UserContractInfoNew_TDIMENSION_IOS_SERVICE_CONTRACT     UserContractInfoNew_UserContractType = 5
	UserContractInfoNew_MAIKE_ANDROID_SERVICE_CONTRACT      UserContractInfoNew_UserContractType = 6
	UserContractInfoNew_MAIKE_IOS_SERVICE_CONTRACT          UserContractInfoNew_UserContractType = 7
)

var UserContractInfoNew_UserContractType_name = map[int32]string{
	0: "INVALID_CONTRACT_TYPE",
	1: "TT_SERVICE_CONTRACT",
	2: "HUANYOU_ANDROID_SERVICE_CONTRACT",
	3: "HUANYOU_IOS_SERVICE_CONTRACT",
	4: "TDIMENSION_ANDROID_SERVICE_CONTRACT",
	5: "TDIMENSION_IOS_SERVICE_CONTRACT",
	6: "MAIKE_ANDROID_SERVICE_CONTRACT",
	7: "MAIKE_IOS_SERVICE_CONTRACT",
}
var UserContractInfoNew_UserContractType_value = map[string]int32{
	"INVALID_CONTRACT_TYPE":               0,
	"TT_SERVICE_CONTRACT":                 1,
	"HUANYOU_ANDROID_SERVICE_CONTRACT":    2,
	"HUANYOU_IOS_SERVICE_CONTRACT":        3,
	"TDIMENSION_ANDROID_SERVICE_CONTRACT": 4,
	"TDIMENSION_IOS_SERVICE_CONTRACT":     5,
	"MAIKE_ANDROID_SERVICE_CONTRACT":      6,
	"MAIKE_IOS_SERVICE_CONTRACT":          7,
}

func (x UserContractInfoNew_UserContractType) String() string {
	return proto.EnumName(UserContractInfoNew_UserContractType_name, int32(x))
}
func (UserContractInfoNew_UserContractType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{0, 0}
}

// 用户服务协议
type UserContractInfoNew struct {
	ContractType         uint32   `protobuf:"varint,1,opt,name=contract_type,json=contractType,proto3" json:"contract_type,omitempty"`
	ContractUrl          string   `protobuf:"bytes,2,opt,name=contract_url,json=contractUrl,proto3" json:"contract_url,omitempty"`
	ContractVersion      string   `protobuf:"bytes,3,opt,name=contract_version,json=contractVersion,proto3" json:"contract_version,omitempty"`
	IsAgree              bool     `protobuf:"varint,4,opt,name=is_agree,json=isAgree,proto3" json:"is_agree,omitempty"`
	ConcealUrl           string   `protobuf:"bytes,5,opt,name=conceal_url,json=concealUrl,proto3" json:"conceal_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserContractInfoNew) Reset()         { *m = UserContractInfoNew{} }
func (m *UserContractInfoNew) String() string { return proto.CompactTextString(m) }
func (*UserContractInfoNew) ProtoMessage()    {}
func (*UserContractInfoNew) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{0}
}
func (m *UserContractInfoNew) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserContractInfoNew.Unmarshal(m, b)
}
func (m *UserContractInfoNew) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserContractInfoNew.Marshal(b, m, deterministic)
}
func (dst *UserContractInfoNew) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserContractInfoNew.Merge(dst, src)
}
func (m *UserContractInfoNew) XXX_Size() int {
	return xxx_messageInfo_UserContractInfoNew.Size(m)
}
func (m *UserContractInfoNew) XXX_DiscardUnknown() {
	xxx_messageInfo_UserContractInfoNew.DiscardUnknown(m)
}

var xxx_messageInfo_UserContractInfoNew proto.InternalMessageInfo

func (m *UserContractInfoNew) GetContractType() uint32 {
	if m != nil {
		return m.ContractType
	}
	return 0
}

func (m *UserContractInfoNew) GetContractUrl() string {
	if m != nil {
		return m.ContractUrl
	}
	return ""
}

func (m *UserContractInfoNew) GetContractVersion() string {
	if m != nil {
		return m.ContractVersion
	}
	return ""
}

func (m *UserContractInfoNew) GetIsAgree() bool {
	if m != nil {
		return m.IsAgree
	}
	return false
}

func (m *UserContractInfoNew) GetConcealUrl() string {
	if m != nil {
		return m.ConcealUrl
	}
	return ""
}

type GetUserContractInfoNewReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ContractType         uint32       `protobuf:"varint,2,opt,name=contract_type,json=contractType,proto3" json:"contract_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserContractInfoNewReq) Reset()         { *m = GetUserContractInfoNewReq{} }
func (m *GetUserContractInfoNewReq) String() string { return proto.CompactTextString(m) }
func (*GetUserContractInfoNewReq) ProtoMessage()    {}
func (*GetUserContractInfoNewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{1}
}
func (m *GetUserContractInfoNewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserContractInfoNewReq.Unmarshal(m, b)
}
func (m *GetUserContractInfoNewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserContractInfoNewReq.Marshal(b, m, deterministic)
}
func (dst *GetUserContractInfoNewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserContractInfoNewReq.Merge(dst, src)
}
func (m *GetUserContractInfoNewReq) XXX_Size() int {
	return xxx_messageInfo_GetUserContractInfoNewReq.Size(m)
}
func (m *GetUserContractInfoNewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserContractInfoNewReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserContractInfoNewReq proto.InternalMessageInfo

func (m *GetUserContractInfoNewReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUserContractInfoNewReq) GetContractType() uint32 {
	if m != nil {
		return m.ContractType
	}
	return 0
}

type GetUserContractInfoNewResp struct {
	BaseResp             *app.BaseResp        `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	ContractInfo         *UserContractInfoNew `protobuf:"bytes,2,opt,name=contract_info,json=contractInfo,proto3" json:"contract_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetUserContractInfoNewResp) Reset()         { *m = GetUserContractInfoNewResp{} }
func (m *GetUserContractInfoNewResp) String() string { return proto.CompactTextString(m) }
func (*GetUserContractInfoNewResp) ProtoMessage()    {}
func (*GetUserContractInfoNewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{2}
}
func (m *GetUserContractInfoNewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserContractInfoNewResp.Unmarshal(m, b)
}
func (m *GetUserContractInfoNewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserContractInfoNewResp.Marshal(b, m, deterministic)
}
func (dst *GetUserContractInfoNewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserContractInfoNewResp.Merge(dst, src)
}
func (m *GetUserContractInfoNewResp) XXX_Size() int {
	return xxx_messageInfo_GetUserContractInfoNewResp.Size(m)
}
func (m *GetUserContractInfoNewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserContractInfoNewResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserContractInfoNewResp proto.InternalMessageInfo

func (m *GetUserContractInfoNewResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUserContractInfoNewResp) GetContractInfo() *UserContractInfoNew {
	if m != nil {
		return m.ContractInfo
	}
	return nil
}

type AgreeUserContractNewReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ContractType         uint32       `protobuf:"varint,2,opt,name=contract_type,json=contractType,proto3" json:"contract_type,omitempty"`
	ContractVersion      string       `protobuf:"bytes,3,opt,name=contract_version,json=contractVersion,proto3" json:"contract_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AgreeUserContractNewReq) Reset()         { *m = AgreeUserContractNewReq{} }
func (m *AgreeUserContractNewReq) String() string { return proto.CompactTextString(m) }
func (*AgreeUserContractNewReq) ProtoMessage()    {}
func (*AgreeUserContractNewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{3}
}
func (m *AgreeUserContractNewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AgreeUserContractNewReq.Unmarshal(m, b)
}
func (m *AgreeUserContractNewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AgreeUserContractNewReq.Marshal(b, m, deterministic)
}
func (dst *AgreeUserContractNewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AgreeUserContractNewReq.Merge(dst, src)
}
func (m *AgreeUserContractNewReq) XXX_Size() int {
	return xxx_messageInfo_AgreeUserContractNewReq.Size(m)
}
func (m *AgreeUserContractNewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AgreeUserContractNewReq.DiscardUnknown(m)
}

var xxx_messageInfo_AgreeUserContractNewReq proto.InternalMessageInfo

func (m *AgreeUserContractNewReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *AgreeUserContractNewReq) GetContractType() uint32 {
	if m != nil {
		return m.ContractType
	}
	return 0
}

func (m *AgreeUserContractNewReq) GetContractVersion() string {
	if m != nil {
		return m.ContractVersion
	}
	return ""
}

type AgreeUserContractNewResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AgreeUserContractNewResp) Reset()         { *m = AgreeUserContractNewResp{} }
func (m *AgreeUserContractNewResp) String() string { return proto.CompactTextString(m) }
func (*AgreeUserContractNewResp) ProtoMessage()    {}
func (*AgreeUserContractNewResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{4}
}
func (m *AgreeUserContractNewResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AgreeUserContractNewResp.Unmarshal(m, b)
}
func (m *AgreeUserContractNewResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AgreeUserContractNewResp.Marshal(b, m, deterministic)
}
func (dst *AgreeUserContractNewResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AgreeUserContractNewResp.Merge(dst, src)
}
func (m *AgreeUserContractNewResp) XXX_Size() int {
	return xxx_messageInfo_AgreeUserContractNewResp.Size(m)
}
func (m *AgreeUserContractNewResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AgreeUserContractNewResp.DiscardUnknown(m)
}

var xxx_messageInfo_AgreeUserContractNewResp proto.InternalMessageInfo

func (m *AgreeUserContractNewResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type SetUgcChannelDenoiseModeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Mode                 DenoiseMode  `protobuf:"varint,3,opt,name=mode,proto3,enum=ga.userlogic.DenoiseMode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *SetUgcChannelDenoiseModeReq) Reset()         { *m = SetUgcChannelDenoiseModeReq{} }
func (m *SetUgcChannelDenoiseModeReq) String() string { return proto.CompactTextString(m) }
func (*SetUgcChannelDenoiseModeReq) ProtoMessage()    {}
func (*SetUgcChannelDenoiseModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{5}
}
func (m *SetUgcChannelDenoiseModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUgcChannelDenoiseModeReq.Unmarshal(m, b)
}
func (m *SetUgcChannelDenoiseModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUgcChannelDenoiseModeReq.Marshal(b, m, deterministic)
}
func (dst *SetUgcChannelDenoiseModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUgcChannelDenoiseModeReq.Merge(dst, src)
}
func (m *SetUgcChannelDenoiseModeReq) XXX_Size() int {
	return xxx_messageInfo_SetUgcChannelDenoiseModeReq.Size(m)
}
func (m *SetUgcChannelDenoiseModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUgcChannelDenoiseModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUgcChannelDenoiseModeReq proto.InternalMessageInfo

func (m *SetUgcChannelDenoiseModeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *SetUgcChannelDenoiseModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetUgcChannelDenoiseModeReq) GetMode() DenoiseMode {
	if m != nil {
		return m.Mode
	}
	return DenoiseMode_DenoiseModeDefault
}

type SetUgcChannelDenoiseModeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SetUgcChannelDenoiseModeResp) Reset()         { *m = SetUgcChannelDenoiseModeResp{} }
func (m *SetUgcChannelDenoiseModeResp) String() string { return proto.CompactTextString(m) }
func (*SetUgcChannelDenoiseModeResp) ProtoMessage()    {}
func (*SetUgcChannelDenoiseModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{6}
}
func (m *SetUgcChannelDenoiseModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUgcChannelDenoiseModeResp.Unmarshal(m, b)
}
func (m *SetUgcChannelDenoiseModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUgcChannelDenoiseModeResp.Marshal(b, m, deterministic)
}
func (dst *SetUgcChannelDenoiseModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUgcChannelDenoiseModeResp.Merge(dst, src)
}
func (m *SetUgcChannelDenoiseModeResp) XXX_Size() int {
	return xxx_messageInfo_SetUgcChannelDenoiseModeResp.Size(m)
}
func (m *SetUgcChannelDenoiseModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUgcChannelDenoiseModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUgcChannelDenoiseModeResp proto.InternalMessageInfo

func (m *SetUgcChannelDenoiseModeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

type GetUgcChannelDenoiseModeReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	ChannelId            uint32       `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUgcChannelDenoiseModeReq) Reset()         { *m = GetUgcChannelDenoiseModeReq{} }
func (m *GetUgcChannelDenoiseModeReq) String() string { return proto.CompactTextString(m) }
func (*GetUgcChannelDenoiseModeReq) ProtoMessage()    {}
func (*GetUgcChannelDenoiseModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{7}
}
func (m *GetUgcChannelDenoiseModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUgcChannelDenoiseModeReq.Unmarshal(m, b)
}
func (m *GetUgcChannelDenoiseModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUgcChannelDenoiseModeReq.Marshal(b, m, deterministic)
}
func (dst *GetUgcChannelDenoiseModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUgcChannelDenoiseModeReq.Merge(dst, src)
}
func (m *GetUgcChannelDenoiseModeReq) XXX_Size() int {
	return xxx_messageInfo_GetUgcChannelDenoiseModeReq.Size(m)
}
func (m *GetUgcChannelDenoiseModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUgcChannelDenoiseModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUgcChannelDenoiseModeReq proto.InternalMessageInfo

func (m *GetUgcChannelDenoiseModeReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUgcChannelDenoiseModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUgcChannelDenoiseModeResp struct {
	BaseResp             *app.BaseResp `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Mode                 DenoiseMode   `protobuf:"varint,2,opt,name=mode,proto3,enum=ga.userlogic.DenoiseMode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUgcChannelDenoiseModeResp) Reset()         { *m = GetUgcChannelDenoiseModeResp{} }
func (m *GetUgcChannelDenoiseModeResp) String() string { return proto.CompactTextString(m) }
func (*GetUgcChannelDenoiseModeResp) ProtoMessage()    {}
func (*GetUgcChannelDenoiseModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{8}
}
func (m *GetUgcChannelDenoiseModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUgcChannelDenoiseModeResp.Unmarshal(m, b)
}
func (m *GetUgcChannelDenoiseModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUgcChannelDenoiseModeResp.Marshal(b, m, deterministic)
}
func (dst *GetUgcChannelDenoiseModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUgcChannelDenoiseModeResp.Merge(dst, src)
}
func (m *GetUgcChannelDenoiseModeResp) XXX_Size() int {
	return xxx_messageInfo_GetUgcChannelDenoiseModeResp.Size(m)
}
func (m *GetUgcChannelDenoiseModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUgcChannelDenoiseModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUgcChannelDenoiseModeResp proto.InternalMessageInfo

func (m *GetUgcChannelDenoiseModeResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUgcChannelDenoiseModeResp) GetMode() DenoiseMode {
	if m != nil {
		return m.Mode
	}
	return DenoiseMode_DenoiseModeDefault
}

// UGC房间降噪模式变更推送
type UgcChannelDenoiseModeChangeNotify struct {
	// 房间id
	ChannelId uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 变更时间(毫秒)
	ChangedAt uint64 `protobuf:"varint,2,opt,name=changed_at,json=changedAt,proto3" json:"changed_at,omitempty"`
	// 变更后的降噪模式
	Mode                 DenoiseMode `protobuf:"varint,3,opt,name=mode,proto3,enum=ga.userlogic.DenoiseMode" json:"mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UgcChannelDenoiseModeChangeNotify) Reset()         { *m = UgcChannelDenoiseModeChangeNotify{} }
func (m *UgcChannelDenoiseModeChangeNotify) String() string { return proto.CompactTextString(m) }
func (*UgcChannelDenoiseModeChangeNotify) ProtoMessage()    {}
func (*UgcChannelDenoiseModeChangeNotify) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{9}
}
func (m *UgcChannelDenoiseModeChangeNotify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UgcChannelDenoiseModeChangeNotify.Unmarshal(m, b)
}
func (m *UgcChannelDenoiseModeChangeNotify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UgcChannelDenoiseModeChangeNotify.Marshal(b, m, deterministic)
}
func (dst *UgcChannelDenoiseModeChangeNotify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UgcChannelDenoiseModeChangeNotify.Merge(dst, src)
}
func (m *UgcChannelDenoiseModeChangeNotify) XXX_Size() int {
	return xxx_messageInfo_UgcChannelDenoiseModeChangeNotify.Size(m)
}
func (m *UgcChannelDenoiseModeChangeNotify) XXX_DiscardUnknown() {
	xxx_messageInfo_UgcChannelDenoiseModeChangeNotify.DiscardUnknown(m)
}

var xxx_messageInfo_UgcChannelDenoiseModeChangeNotify proto.InternalMessageInfo

func (m *UgcChannelDenoiseModeChangeNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UgcChannelDenoiseModeChangeNotify) GetChangedAt() uint64 {
	if m != nil {
		return m.ChangedAt
	}
	return 0
}

func (m *UgcChannelDenoiseModeChangeNotify) GetMode() DenoiseMode {
	if m != nil {
		return m.Mode
	}
	return DenoiseMode_DenoiseModeDefault
}

type UgcMoreConfig struct {
	IconImg              string   `protobuf:"bytes,1,opt,name=icon_img,json=iconImg,proto3" json:"icon_img,omitempty"`
	IconName             string   `protobuf:"bytes,2,opt,name=icon_name,json=iconName,proto3" json:"icon_name,omitempty"`
	Link                 string   `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UgcMoreConfig) Reset()         { *m = UgcMoreConfig{} }
func (m *UgcMoreConfig) String() string { return proto.CompactTextString(m) }
func (*UgcMoreConfig) ProtoMessage()    {}
func (*UgcMoreConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{10}
}
func (m *UgcMoreConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UgcMoreConfig.Unmarshal(m, b)
}
func (m *UgcMoreConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UgcMoreConfig.Marshal(b, m, deterministic)
}
func (dst *UgcMoreConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UgcMoreConfig.Merge(dst, src)
}
func (m *UgcMoreConfig) XXX_Size() int {
	return xxx_messageInfo_UgcMoreConfig.Size(m)
}
func (m *UgcMoreConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_UgcMoreConfig.DiscardUnknown(m)
}

var xxx_messageInfo_UgcMoreConfig proto.InternalMessageInfo

func (m *UgcMoreConfig) GetIconImg() string {
	if m != nil {
		return m.IconImg
	}
	return ""
}

func (m *UgcMoreConfig) GetIconName() string {
	if m != nil {
		return m.IconName
	}
	return ""
}

func (m *UgcMoreConfig) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

type GetUgcMoreConfigListReq struct {
	BaseReq              *app.BaseReq `protobuf:"bytes,1,opt,name=base_req,json=baseReq,proto3" json:"base_req,omitempty"`
	TabId                uint32       `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUgcMoreConfigListReq) Reset()         { *m = GetUgcMoreConfigListReq{} }
func (m *GetUgcMoreConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetUgcMoreConfigListReq) ProtoMessage()    {}
func (*GetUgcMoreConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{11}
}
func (m *GetUgcMoreConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUgcMoreConfigListReq.Unmarshal(m, b)
}
func (m *GetUgcMoreConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUgcMoreConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetUgcMoreConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUgcMoreConfigListReq.Merge(dst, src)
}
func (m *GetUgcMoreConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetUgcMoreConfigListReq.Size(m)
}
func (m *GetUgcMoreConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUgcMoreConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUgcMoreConfigListReq proto.InternalMessageInfo

func (m *GetUgcMoreConfigListReq) GetBaseReq() *app.BaseReq {
	if m != nil {
		return m.BaseReq
	}
	return nil
}

func (m *GetUgcMoreConfigListReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetUgcMoreConfigListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUgcMoreConfigListResp struct {
	BaseResp             *app.BaseResp    `protobuf:"bytes,1,opt,name=base_resp,json=baseResp,proto3" json:"base_resp,omitempty"`
	Configs              []*UgcMoreConfig `protobuf:"bytes,2,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUgcMoreConfigListResp) Reset()         { *m = GetUgcMoreConfigListResp{} }
func (m *GetUgcMoreConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetUgcMoreConfigListResp) ProtoMessage()    {}
func (*GetUgcMoreConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_logic__ded604d2ff32f8ac, []int{12}
}
func (m *GetUgcMoreConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUgcMoreConfigListResp.Unmarshal(m, b)
}
func (m *GetUgcMoreConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUgcMoreConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetUgcMoreConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUgcMoreConfigListResp.Merge(dst, src)
}
func (m *GetUgcMoreConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetUgcMoreConfigListResp.Size(m)
}
func (m *GetUgcMoreConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUgcMoreConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUgcMoreConfigListResp proto.InternalMessageInfo

func (m *GetUgcMoreConfigListResp) GetBaseResp() *app.BaseResp {
	if m != nil {
		return m.BaseResp
	}
	return nil
}

func (m *GetUgcMoreConfigListResp) GetConfigs() []*UgcMoreConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

func init() {
	proto.RegisterType((*UserContractInfoNew)(nil), "ga.userlogic.UserContractInfoNew")
	proto.RegisterType((*GetUserContractInfoNewReq)(nil), "ga.userlogic.GetUserContractInfoNewReq")
	proto.RegisterType((*GetUserContractInfoNewResp)(nil), "ga.userlogic.GetUserContractInfoNewResp")
	proto.RegisterType((*AgreeUserContractNewReq)(nil), "ga.userlogic.AgreeUserContractNewReq")
	proto.RegisterType((*AgreeUserContractNewResp)(nil), "ga.userlogic.AgreeUserContractNewResp")
	proto.RegisterType((*SetUgcChannelDenoiseModeReq)(nil), "ga.userlogic.SetUgcChannelDenoiseModeReq")
	proto.RegisterType((*SetUgcChannelDenoiseModeResp)(nil), "ga.userlogic.SetUgcChannelDenoiseModeResp")
	proto.RegisterType((*GetUgcChannelDenoiseModeReq)(nil), "ga.userlogic.GetUgcChannelDenoiseModeReq")
	proto.RegisterType((*GetUgcChannelDenoiseModeResp)(nil), "ga.userlogic.GetUgcChannelDenoiseModeResp")
	proto.RegisterType((*UgcChannelDenoiseModeChangeNotify)(nil), "ga.userlogic.UgcChannelDenoiseModeChangeNotify")
	proto.RegisterType((*UgcMoreConfig)(nil), "ga.userlogic.UgcMoreConfig")
	proto.RegisterType((*GetUgcMoreConfigListReq)(nil), "ga.userlogic.GetUgcMoreConfigListReq")
	proto.RegisterType((*GetUgcMoreConfigListResp)(nil), "ga.userlogic.GetUgcMoreConfigListResp")
	proto.RegisterEnum("ga.userlogic.DenoiseMode", DenoiseMode_name, DenoiseMode_value)
	proto.RegisterEnum("ga.userlogic.UserContractInfoNew_UserContractType", UserContractInfoNew_UserContractType_name, UserContractInfoNew_UserContractType_value)
}

func init() { proto.RegisterFile("user-logic_.proto", fileDescriptor_user_logic__ded604d2ff32f8ac) }

var fileDescriptor_user_logic__ded604d2ff32f8ac = []byte{
	// 772 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x55, 0xff, 0x6e, 0xe2, 0x46,
	0x10, 0x8e, 0x81, 0x04, 0x18, 0x42, 0x4b, 0x36, 0x4a, 0x63, 0x42, 0x9a, 0x10, 0xa7, 0x4a, 0x49,
	0xa5, 0x38, 0x12, 0x55, 0x1e, 0xc0, 0x01, 0x9a, 0x5a, 0x0d, 0xa6, 0x32, 0x10, 0x29, 0xed, 0x1f,
	0xd6, 0x62, 0x16, 0xc7, 0xaa, 0xed, 0x75, 0x6c, 0xd3, 0x1c, 0xd2, 0xbd, 0xc3, 0xdd, 0xe9, 0x5e,
	0xe0, 0xde, 0xee, 0x5e, 0xe3, 0xe4, 0xb5, 0x01, 0x1f, 0x3f, 0xa4, 0x43, 0xba, 0xfc, 0xb7, 0xfb,
	0xcd, 0xc7, 0x7c, 0x33, 0xf3, 0x0d, 0x5e, 0xd8, 0x1b, 0xfb, 0xc4, 0xbb, 0xb2, 0xa8, 0x61, 0xea,
	0x9a, 0xe8, 0x7a, 0x34, 0xa0, 0x68, 0xd7, 0xc0, 0x62, 0x88, 0x32, 0xf0, 0xa8, 0x68, 0x60, 0x6d,
	0x80, 0x7d, 0x12, 0x05, 0x85, 0xcf, 0x69, 0xd8, 0xef, 0xfb, 0xc4, 0x6b, 0x50, 0x27, 0xf0, 0xb0,
	0x1e, 0xc8, 0xce, 0x88, 0x2a, 0xe4, 0x05, 0x9d, 0x43, 0x51, 0x8f, 0x21, 0x2d, 0x98, 0xb8, 0x84,
	0xe7, 0xaa, 0x5c, 0xad, 0xa8, 0xee, 0x4e, 0xc1, 0xde, 0xc4, 0x25, 0xe8, 0x0c, 0x66, 0x77, 0x6d,
	0xec, 0x59, 0x7c, 0xaa, 0xca, 0xd5, 0xf2, 0x6a, 0x61, 0x8a, 0xf5, 0x3d, 0x0b, 0x5d, 0x42, 0x69,
	0x46, 0xf9, 0x9f, 0x78, 0xbe, 0x49, 0x1d, 0x3e, 0xcd, 0x68, 0x3f, 0x4e, 0xf1, 0x87, 0x08, 0x46,
	0x65, 0xc8, 0x99, 0xbe, 0x86, 0x0d, 0x8f, 0x10, 0x3e, 0x53, 0xe5, 0x6a, 0x39, 0x35, 0x6b, 0xfa,
	0x52, 0x78, 0x45, 0xa7, 0x10, 0x26, 0xd5, 0x09, 0xb6, 0x98, 0xce, 0x36, 0x4b, 0x00, 0x31, 0xd4,
	0xf7, 0x2c, 0xe1, 0x53, 0x0a, 0x4a, 0xc9, 0x36, 0x58, 0x79, 0x65, 0x38, 0x90, 0x95, 0x07, 0xe9,
	0x5e, 0x6e, 0x6a, 0x8d, 0x8e, 0xd2, 0x53, 0xa5, 0x46, 0x4f, 0xeb, 0x3d, 0xfe, 0xdd, 0x2a, 0x6d,
	0xa1, 0x43, 0xd8, 0xef, 0xf5, 0xb4, 0x6e, 0x4b, 0x7d, 0x90, 0x1b, 0xad, 0x59, 0xb4, 0xc4, 0xa1,
	0x5f, 0xa0, 0xfa, 0x67, 0x5f, 0x52, 0x1e, 0x3b, 0x7d, 0x4d, 0x52, 0x9a, 0x6a, 0x47, 0x6e, 0x2e,
	0xb3, 0x52, 0xa8, 0x0a, 0xc7, 0x53, 0x96, 0xdc, 0xe9, 0x2e, 0x33, 0xd2, 0xe8, 0x57, 0x38, 0xef,
	0x35, 0xe5, 0x76, 0x4b, 0xe9, 0xca, 0x1d, 0x65, 0x7d, 0xaa, 0x0c, 0x3a, 0x87, 0xd3, 0x04, 0x71,
	0x65, 0xb6, 0x6d, 0x24, 0xc0, 0x49, 0x5b, 0x92, 0xff, 0x6a, 0xad, 0x4f, 0xb4, 0x83, 0x4e, 0xe0,
	0x28, 0xe2, 0xac, 0xcc, 0x91, 0x15, 0x9e, 0xa0, 0x7c, 0x47, 0x82, 0x15, 0x5e, 0xab, 0xe4, 0x19,
	0x5d, 0x40, 0x2e, 0x5c, 0x0a, 0xcd, 0x23, 0xcf, 0xcc, 0xe9, 0x42, 0xbd, 0x20, 0x1a, 0x58, 0xbc,
	0xc5, 0x3e, 0x51, 0xc9, 0xb3, 0x9a, 0x1d, 0x44, 0x87, 0xe5, 0xb5, 0x48, 0x2d, 0xaf, 0x85, 0xf0,
	0x8e, 0x83, 0xa3, 0x75, 0x52, 0xbe, 0x8b, 0x2e, 0x21, 0x1f, 0x6b, 0xf9, 0x6e, 0x2c, 0xb6, 0x3b,
	0x17, 0xf3, 0x5d, 0x35, 0x37, 0x88, 0x4f, 0xe8, 0x8f, 0x84, 0x9c, 0xe9, 0x8c, 0x28, 0x93, 0x2b,
	0xd4, 0xcf, 0xc4, 0xe4, 0x4a, 0x8b, 0xab, 0x84, 0x66, 0x15, 0x85, 0x80, 0xf0, 0x81, 0x83, 0x43,
	0xb6, 0x49, 0x49, 0xea, 0x2b, 0xb4, 0xbe, 0xc1, 0xba, 0x0b, 0x2d, 0xe0, 0x57, 0x97, 0xb4, 0xd1,
	0x88, 0x84, 0x8f, 0x1c, 0x54, 0xba, 0x24, 0xe8, 0x1b, 0x7a, 0xe3, 0x09, 0x3b, 0x0e, 0xb1, 0x9a,
	0xc4, 0xa1, 0xa6, 0x4f, 0xda, 0x74, 0x48, 0x36, 0x69, 0xef, 0x67, 0x00, 0x3d, 0x4a, 0xa0, 0x99,
	0xc3, 0xb8, 0xb7, 0x7c, 0x8c, 0xc8, 0x43, 0x74, 0x05, 0x19, 0x9b, 0x0e, 0x09, 0x6b, 0xe6, 0x87,
	0x7a, 0xf9, 0x6b, 0x03, 0x92, 0x92, 0x8c, 0x26, 0xc8, 0x70, 0xbc, 0xbe, 0xa8, 0xcd, 0x1a, 0x1c,
	0x42, 0xe5, 0xee, 0xd5, 0xfb, 0x13, 0xde, 0xc0, 0xf1, 0xdd, 0xf7, 0x29, 0x78, 0x36, 0xaa, 0xd4,
	0xb7, 0x8d, 0xea, 0x3d, 0x07, 0x67, 0x2b, 0x75, 0x43, 0xc4, 0x20, 0x0a, 0x0d, 0xcc, 0xd1, 0x64,
	0xa1, 0x7c, 0x6e, 0xd1, 0x9e, 0x38, 0x6c, 0x90, 0xa1, 0x86, 0x03, 0xa6, 0x9c, 0x89, 0xc2, 0x06,
	0x19, 0x4a, 0xc1, 0xa6, 0xee, 0xfd, 0x0b, 0xc5, 0xbe, 0xa1, 0xb7, 0xa9, 0x47, 0x1a, 0xd4, 0x19,
	0x99, 0x06, 0xfb, 0x34, 0xeb, 0xd4, 0xd1, 0x4c, 0xdb, 0x60, 0xda, 0x79, 0x35, 0x1b, 0xde, 0x65,
	0xdb, 0x40, 0x15, 0xc8, 0xb3, 0x90, 0x83, 0x6d, 0x12, 0x3f, 0x00, 0x8c, 0xab, 0x60, 0x9b, 0x20,
	0x04, 0x19, 0xcb, 0x74, 0xfe, 0x8b, 0xff, 0x02, 0xec, 0x2c, 0xbc, 0xc0, 0x61, 0x34, 0xe9, 0x79,
	0xfe, 0x7b, 0xd3, 0x0f, 0x36, 0xf1, 0xf2, 0x00, 0x76, 0x02, 0x3c, 0x98, 0xfb, 0xb8, 0x1d, 0xe0,
	0xc1, 0x7c, 0x08, 0xf1, 0x8c, 0xd2, 0x8b, 0x16, 0xbf, 0x05, 0x7e, 0xb5, 0xf0, 0x66, 0xf6, 0xde,
	0x40, 0x56, 0x67, 0x3f, 0xf6, 0xf9, 0x54, 0x35, 0x5d, 0x2b, 0xd4, 0x2b, 0x0b, 0x5f, 0xa3, 0xa4,
	0x80, 0x3a, 0xe5, 0xfe, 0xd6, 0x81, 0x42, 0x62, 0xd0, 0xe8, 0x27, 0x40, 0x89, 0x6b, 0x93, 0x8c,
	0xf0, 0xd8, 0x0a, 0x4a, 0x5b, 0xe8, 0x00, 0xf6, 0x12, 0xb8, 0x42, 0x3d, 0x1b, 0x5b, 0x25, 0x0e,
	0xed, 0x41, 0x31, 0x01, 0x4b, 0x72, 0x29, 0x75, 0x7b, 0x0b, 0xbc, 0x4e, 0x6d, 0x71, 0x62, 0x4e,
	0xe8, 0x38, 0xac, 0x20, 0x74, 0xce, 0x8a, 0x5e, 0xf5, 0x7f, 0x2e, 0x0c, 0x6a, 0x61, 0xc7, 0x10,
	0x6f, 0xea, 0x41, 0x20, 0xea, 0xd4, 0xbe, 0x66, 0xb0, 0x4e, 0xad, 0x6b, 0xec, 0xba, 0xd7, 0xb3,
	0x5a, 0x07, 0x3b, 0x0c, 0xff, 0xfd, 0x4b, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa2, 0xf5, 0x4e, 0x46,
	0x36, 0x08, 0x00, 0x00,
}
