// Code generated by protoc-gen-go-grpc-mock. DO NOT EDIT.
// source: tt/quicksilver/channel-live-mgr/channel-live-mgr.proto

package channellivemgr

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	apicentergo "golang.52tt.com/protocol/services/apicentergo"
	reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"
	grpc "google.golang.org/grpc"
)

// MockChannelLiveMgrClient is a mock of ChannelLiveMgrClient interface.
type MockChannelLiveMgrClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelLiveMgrClientMockRecorder
}

// MockChannelLiveMgrClientMockRecorder is the mock recorder for MockChannelLiveMgrClient.
type MockChannelLiveMgrClientMockRecorder struct {
	mock *MockChannelLiveMgrClient
}

// NewMockChannelLiveMgrClient creates a new mock instance.
func NewMockChannelLiveMgrClient(ctrl *gomock.Controller) *MockChannelLiveMgrClient {
	mock := &MockChannelLiveMgrClient{ctrl: ctrl}
	mock.recorder = &MockChannelLiveMgrClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelLiveMgrClient) EXPECT() *MockChannelLiveMgrClientMockRecorder {
	return m.recorder
}

// AcceptAppointPk mocks base method.
func (m *MockChannelLiveMgrClient) AcceptAppointPk(ctx context.Context, in *AcceptAppointPkReq, opts ...grpc.CallOption) (*AcceptAppointPkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AcceptAppointPk", varargs...)
	ret0, _ := ret[0].(*AcceptAppointPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptAppointPk indicates an expected call of AcceptAppointPk.
func (mr *MockChannelLiveMgrClientMockRecorder) AcceptAppointPk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptAppointPk", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).AcceptAppointPk), varargs...)
}

// AddAnchorInBackList mocks base method.
func (m *MockChannelLiveMgrClient) AddAnchorInBackList(ctx context.Context, in *AddAnchorInBackListReq, opts ...grpc.CallOption) (*AddAnchorInBackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAnchorInBackList", varargs...)
	ret0, _ := ret[0].(*AddAnchorInBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAnchorInBackList indicates an expected call of AddAnchorInBackList.
func (mr *MockChannelLiveMgrClientMockRecorder) AddAnchorInBackList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAnchorInBackList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).AddAnchorInBackList), varargs...)
}

// AddAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrClient) AddAppointPkInfo(ctx context.Context, in *AddAppointPkInfoReq, opts ...grpc.CallOption) (*AddAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddAppointPkInfo", varargs...)
	ret0, _ := ret[0].(*AddAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAppointPkInfo indicates an expected call of AddAppointPkInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) AddAppointPkInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).AddAppointPkInfo), varargs...)
}

// AddChannelLiveAnchorScore mocks base method.
func (m *MockChannelLiveMgrClient) AddChannelLiveAnchorScore(ctx context.Context, in *AddChannelLiveAnchorScoreReq, opts ...grpc.CallOption) (*AddChannelLiveAnchorScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddChannelLiveAnchorScore", varargs...)
	ret0, _ := ret[0].(*AddChannelLiveAnchorScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelLiveAnchorScore indicates an expected call of AddChannelLiveAnchorScore.
func (mr *MockChannelLiveMgrClientMockRecorder) AddChannelLiveAnchorScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelLiveAnchorScore", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).AddChannelLiveAnchorScore), varargs...)
}

// AddVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrClient) AddVirtualAnchorPer(ctx context.Context, in *AddVirtualAnchorPerReq, opts ...grpc.CallOption) (*AddVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddVirtualAnchorPer", varargs...)
	ret0, _ := ret[0].(*AddVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddVirtualAnchorPer indicates an expected call of AddVirtualAnchorPer.
func (mr *MockChannelLiveMgrClientMockRecorder) AddVirtualAnchorPer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).AddVirtualAnchorPer), varargs...)
}

// ApplyPk mocks base method.
func (m *MockChannelLiveMgrClient) ApplyPk(ctx context.Context, in *ApplyPkReq, opts ...grpc.CallOption) (*ApplyPkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ApplyPk", varargs...)
	ret0, _ := ret[0].(*ApplyPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyPk indicates an expected call of ApplyPk.
func (mr *MockChannelLiveMgrClientMockRecorder) ApplyPk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyPk", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).ApplyPk), varargs...)
}

// BatDelChannelLiveInfoMgr mocks base method.
func (m *MockChannelLiveMgrClient) BatDelChannelLiveInfoMgr(ctx context.Context, in *apicentergo.BatDelChannelLiveInfoReq, opts ...grpc.CallOption) (*apicentergo.BatDelChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatDelChannelLiveInfoMgr", varargs...)
	ret0, _ := ret[0].(*apicentergo.BatDelChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelChannelLiveInfoMgr indicates an expected call of BatDelChannelLiveInfoMgr.
func (mr *MockChannelLiveMgrClientMockRecorder) BatDelChannelLiveInfoMgr(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelChannelLiveInfoMgr", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatDelChannelLiveInfoMgr), varargs...)
}

// BatGetChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrClient) BatGetChannelLiveInfo(ctx context.Context, in *BatGetChannelLiveInfoReq, opts ...grpc.CallOption) (*BatGetChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetChannelLiveInfo", varargs...)
	ret0, _ := ret[0].(*BatGetChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetChannelLiveInfo indicates an expected call of BatGetChannelLiveInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) BatGetChannelLiveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatGetChannelLiveInfo), varargs...)
}

// BatchAddAnchorMgr mocks base method.
func (m *MockChannelLiveMgrClient) BatchAddAnchorMgr(ctx context.Context, in *apicentergo.BatchAddAnchorReq, opts ...grpc.CallOption) (*apicentergo.BatchAddAnchorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchAddAnchorMgr", varargs...)
	ret0, _ := ret[0].(*apicentergo.BatchAddAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddAnchorMgr indicates an expected call of BatchAddAnchorMgr.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchAddAnchorMgr(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddAnchorMgr", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchAddAnchorMgr), varargs...)
}

// BatchGetAllChannelLive mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetAllChannelLive(ctx context.Context, in *BatchGetAllChannelLiveReq, opts ...grpc.CallOption) (*BatchGetAllChannelLiveResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAllChannelLive", varargs...)
	ret0, _ := ret[0].(*BatchGetAllChannelLiveResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAllChannelLive indicates an expected call of BatchGetAllChannelLive.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetAllChannelLive(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAllChannelLive", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetAllChannelLive), varargs...)
}

// BatchGetAnchorTotalData mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetAnchorTotalData(ctx context.Context, in *BatchGetAnchorTotalDataReq, opts ...grpc.CallOption) (*BatchGetAnchorTotalDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetAnchorTotalData", varargs...)
	ret0, _ := ret[0].(*BatchGetAnchorTotalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorTotalData indicates an expected call of BatchGetAnchorTotalData.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetAnchorTotalData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorTotalData", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetAnchorTotalData), varargs...)
}

// BatchGetChannelLiveRecord mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetChannelLiveRecord(ctx context.Context, in *BatchGetChannelLiveRecordReq, opts ...grpc.CallOption) (*BatchGetChannelLiveRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelLiveRecord", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelLiveRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveRecord indicates an expected call of BatchGetChannelLiveRecord.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetChannelLiveRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveRecord", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetChannelLiveRecord), varargs...)
}

// BatchGetChannelLiveStatus mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetChannelLiveStatus(ctx context.Context, in *BatchGetChannelLiveStatusReq, opts ...grpc.CallOption) (*BatchGetChannelLiveStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelLiveStatus", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelLiveStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveStatus indicates an expected call of BatchGetChannelLiveStatus.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetChannelLiveStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveStatus", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetChannelLiveStatus), varargs...)
}

// BatchGetChannelLiveStatusSimple mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetChannelLiveStatusSimple(ctx context.Context, in *BatchGetChannelLiveStatusSimpleReq, opts ...grpc.CallOption) (*BatchGetChannelLiveStatusSimpleResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelLiveStatusSimple", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelLiveStatusSimpleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveStatusSimple indicates an expected call of BatchGetChannelLiveStatusSimple.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetChannelLiveStatusSimple(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveStatusSimple", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetChannelLiveStatusSimple), varargs...)
}

// BatchGetChannelLiveTotalData mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetChannelLiveTotalData(ctx context.Context, in *BatchGetChannelLiveTotalDataReq, opts ...grpc.CallOption) (*BatchGetChannelLiveTotalDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetChannelLiveTotalData", varargs...)
	ret0, _ := ret[0].(*BatchGetChannelLiveTotalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveTotalData indicates an expected call of BatchGetChannelLiveTotalData.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetChannelLiveTotalData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveTotalData", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetChannelLiveTotalData), varargs...)
}

// BatchGetContestPkConfig mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetContestPkConfig(ctx context.Context, in *BatchGetContestPkConfigReq, opts ...grpc.CallOption) (*BatchGetContestPkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetContestPkConfig", varargs...)
	ret0, _ := ret[0].(*BatchGetContestPkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetContestPkConfig indicates an expected call of BatchGetContestPkConfig.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetContestPkConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContestPkConfig", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetContestPkConfig), varargs...)
}

// BatchGetContestPkInfo mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetContestPkInfo(ctx context.Context, in *BatchGetContestPkInfoReq, opts ...grpc.CallOption) (*BatchGetContestPkInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetContestPkInfo", varargs...)
	ret0, _ := ret[0].(*BatchGetContestPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetContestPkInfo indicates an expected call of BatchGetContestPkInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetContestPkInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContestPkInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetContestPkInfo), varargs...)
}

// BatchGetGroupFansGiftValue mocks base method.
func (m *MockChannelLiveMgrClient) BatchGetGroupFansGiftValue(ctx context.Context, in *BatchGetGroupFansGiftValueReq, opts ...grpc.CallOption) (*BatchGetGroupFansGiftValueResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetGroupFansGiftValue", varargs...)
	ret0, _ := ret[0].(*BatchGetGroupFansGiftValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupFansGiftValue indicates an expected call of BatchGetGroupFansGiftValue.
func (mr *MockChannelLiveMgrClientMockRecorder) BatchGetGroupFansGiftValue(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupFansGiftValue", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).BatchGetGroupFansGiftValue), varargs...)
}

// CancelContestPk mocks base method.
func (m *MockChannelLiveMgrClient) CancelContestPk(ctx context.Context, in *CancelContestPkReq, opts ...grpc.CallOption) (*CancelContestPkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelContestPk", varargs...)
	ret0, _ := ret[0].(*CancelContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelContestPk indicates an expected call of CancelContestPk.
func (mr *MockChannelLiveMgrClientMockRecorder) CancelContestPk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelContestPk", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).CancelContestPk), varargs...)
}

// CancelPKApply mocks base method.
func (m *MockChannelLiveMgrClient) CancelPKApply(ctx context.Context, in *CancelPKApplyReq, opts ...grpc.CallOption) (*CancelPKApplyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelPKApply", varargs...)
	ret0, _ := ret[0].(*CancelPKApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelPKApply indicates an expected call of CancelPKApply.
func (mr *MockChannelLiveMgrClientMockRecorder) CancelPKApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPKApply", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).CancelPKApply), varargs...)
}

// CancelPkMatch mocks base method.
func (m *MockChannelLiveMgrClient) CancelPkMatch(ctx context.Context, in *CancelPkMatchReq, opts ...grpc.CallOption) (*CancelPkMatchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelPkMatch", varargs...)
	ret0, _ := ret[0].(*CancelPkMatchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelPkMatch indicates an expected call of CancelPkMatch.
func (mr *MockChannelLiveMgrClientMockRecorder) CancelPkMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPkMatch", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).CancelPkMatch), varargs...)
}

// ChannelLiveHeartbeat mocks base method.
func (m *MockChannelLiveMgrClient) ChannelLiveHeartbeat(ctx context.Context, in *ChannelLiveHeartbeatReq, opts ...grpc.CallOption) (*ChannelLiveHeartbeatResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ChannelLiveHeartbeat", varargs...)
	ret0, _ := ret[0].(*ChannelLiveHeartbeatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChannelLiveHeartbeat indicates an expected call of ChannelLiveHeartbeat.
func (mr *MockChannelLiveMgrClientMockRecorder) ChannelLiveHeartbeat(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelLiveHeartbeat", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).ChannelLiveHeartbeat), varargs...)
}

// CheckAnchorPkAvailable mocks base method.
func (m *MockChannelLiveMgrClient) CheckAnchorPkAvailable(ctx context.Context, in *CheckAnchorPkAvailableReq, opts ...grpc.CallOption) (*CheckAnchorPkAvailableResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckAnchorPkAvailable", varargs...)
	ret0, _ := ret[0].(*CheckAnchorPkAvailableResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAnchorPkAvailable indicates an expected call of CheckAnchorPkAvailable.
func (mr *MockChannelLiveMgrClientMockRecorder) CheckAnchorPkAvailable(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAnchorPkAvailable", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).CheckAnchorPkAvailable), varargs...)
}

// CheckHasVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrClient) CheckHasVirtualAnchorPer(ctx context.Context, in *CheckHasVirtualAnchorPerReq, opts ...grpc.CallOption) (*CheckHasVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckHasVirtualAnchorPer", varargs...)
	ret0, _ := ret[0].(*CheckHasVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckHasVirtualAnchorPer indicates an expected call of CheckHasVirtualAnchorPer.
func (mr *MockChannelLiveMgrClientMockRecorder) CheckHasVirtualAnchorPer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHasVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).CheckHasVirtualAnchorPer), varargs...)
}

// CheckIsAnchorInBackList mocks base method.
func (m *MockChannelLiveMgrClient) CheckIsAnchorInBackList(ctx context.Context, in *CheckIsAnchorInBackListReq, opts ...grpc.CallOption) (*CheckIsAnchorInBackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIsAnchorInBackList", varargs...)
	ret0, _ := ret[0].(*CheckIsAnchorInBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsAnchorInBackList indicates an expected call of CheckIsAnchorInBackList.
func (mr *MockChannelLiveMgrClientMockRecorder) CheckIsAnchorInBackList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsAnchorInBackList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).CheckIsAnchorInBackList), varargs...)
}

// ClearVirtualLiveSecretRefreshLimit mocks base method.
func (m *MockChannelLiveMgrClient) ClearVirtualLiveSecretRefreshLimit(ctx context.Context, in *ClearVirtualLiveSecretRefreshLimitReq, opts ...grpc.CallOption) (*ClearVirtualLiveSecretRefreshLimitResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearVirtualLiveSecretRefreshLimit", varargs...)
	ret0, _ := ret[0].(*ClearVirtualLiveSecretRefreshLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearVirtualLiveSecretRefreshLimit indicates an expected call of ClearVirtualLiveSecretRefreshLimit.
func (mr *MockChannelLiveMgrClientMockRecorder) ClearVirtualLiveSecretRefreshLimit(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearVirtualLiveSecretRefreshLimit", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).ClearVirtualLiveSecretRefreshLimit), varargs...)
}

// ConfirmAppointPkPush mocks base method.
func (m *MockChannelLiveMgrClient) ConfirmAppointPkPush(ctx context.Context, in *ConfirmAppointPkPushReq, opts ...grpc.CallOption) (*ConfirmAppointPkPushResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ConfirmAppointPkPush", varargs...)
	ret0, _ := ret[0].(*ConfirmAppointPkPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmAppointPkPush indicates an expected call of ConfirmAppointPkPush.
func (mr *MockChannelLiveMgrClientMockRecorder) ConfirmAppointPkPush(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmAppointPkPush", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).ConfirmAppointPkPush), varargs...)
}

// CreateContestPk mocks base method.
func (m *MockChannelLiveMgrClient) CreateContestPk(ctx context.Context, in *CreateContestPkReq, opts ...grpc.CallOption) (*CreateContestPkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateContestPk", varargs...)
	ret0, _ := ret[0].(*CreateContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContestPk indicates an expected call of CreateContestPk.
func (mr *MockChannelLiveMgrClientMockRecorder) CreateContestPk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContestPk", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).CreateContestPk), varargs...)
}

// DelAnchorBackList mocks base method.
func (m *MockChannelLiveMgrClient) DelAnchorBackList(ctx context.Context, in *DelAnchorBackListReq, opts ...grpc.CallOption) (*DelAnchorBackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelAnchorBackList", varargs...)
	ret0, _ := ret[0].(*DelAnchorBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelAnchorBackList indicates an expected call of DelAnchorBackList.
func (mr *MockChannelLiveMgrClientMockRecorder) DelAnchorBackList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorBackList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).DelAnchorBackList), varargs...)
}

// DelAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrClient) DelAppointPkInfo(ctx context.Context, in *DelAppointPkInfoReq, opts ...grpc.CallOption) (*DelAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelAppointPkInfo", varargs...)
	ret0, _ := ret[0].(*DelAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelAppointPkInfo indicates an expected call of DelAppointPkInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) DelAppointPkInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).DelAppointPkInfo), varargs...)
}

// DelChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrClient) DelChannelLiveInfo(ctx context.Context, in *DelChannelLiveInfoReq, opts ...grpc.CallOption) (*DelChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelChannelLiveInfo", varargs...)
	ret0, _ := ret[0].(*DelChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChannelLiveInfo indicates an expected call of DelChannelLiveInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) DelChannelLiveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).DelChannelLiveInfo), varargs...)
}

// DelVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrClient) DelVirtualAnchorPer(ctx context.Context, in *DelVirtualAnchorPerReq, opts ...grpc.CallOption) (*DelVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelVirtualAnchorPer", varargs...)
	ret0, _ := ret[0].(*DelVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelVirtualAnchorPer indicates an expected call of DelVirtualAnchorPer.
func (mr *MockChannelLiveMgrClientMockRecorder) DelVirtualAnchorPer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).DelVirtualAnchorPer), varargs...)
}

// FinishContestPk mocks base method.
func (m *MockChannelLiveMgrClient) FinishContestPk(ctx context.Context, in *FinishContestPkReq, opts ...grpc.CallOption) (*FinishContestPkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FinishContestPk", varargs...)
	ret0, _ := ret[0].(*FinishContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishContestPk indicates an expected call of FinishContestPk.
func (mr *MockChannelLiveMgrClientMockRecorder) FinishContestPk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishContestPk", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).FinishContestPk), varargs...)
}

// GetAllAnchor mocks base method.
func (m *MockChannelLiveMgrClient) GetAllAnchor(ctx context.Context, in *GetAllAnchorReq, opts ...grpc.CallOption) (*GetAllAnchorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllAnchor", varargs...)
	ret0, _ := ret[0].(*GetAllAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAnchor indicates an expected call of GetAllAnchor.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAllAnchor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAnchor", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAllAnchor), varargs...)
}

// GetAnchorBackList mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorBackList(ctx context.Context, in *GetAnchorBackListReq, opts ...grpc.CallOption) (*GetAnchorBackListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorBackList", varargs...)
	ret0, _ := ret[0].(*GetAnchorBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorBackList indicates an expected call of GetAnchorBackList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorBackList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorBackList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorBackList), varargs...)
}

// GetAnchorByUidList mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorByUidList(ctx context.Context, in *GetAnchorByUidListReq, opts ...grpc.CallOption) (*GetAnchorByUidListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorByUidList", varargs...)
	ret0, _ := ret[0].(*GetAnchorByUidListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorByUidList indicates an expected call of GetAnchorByUidList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorByUidList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorByUidList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorByUidList), varargs...)
}

// GetAnchorList mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorList(ctx context.Context, in *GetAnchorListReq, opts ...grpc.CallOption) (*GetAnchorListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorList", varargs...)
	ret0, _ := ret[0].(*GetAnchorListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorList indicates an expected call of GetAnchorList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorList), varargs...)
}

// GetAnchorListMgr mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorListMgr(ctx context.Context, in *apicentergo.GetAnchorListReq, opts ...grpc.CallOption) (*apicentergo.GetAnchorListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorListMgr", varargs...)
	ret0, _ := ret[0].(*apicentergo.GetAnchorListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorListMgr indicates an expected call of GetAnchorListMgr.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorListMgr(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorListMgr", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorListMgr), varargs...)
}

// GetAnchorMonthScoreList mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorMonthScoreList(ctx context.Context, in *GetAnchorMonthScoreListReq, opts ...grpc.CallOption) (*GetAnchorMonthScoreListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorMonthScoreList", varargs...)
	ret0, _ := ret[0].(*GetAnchorMonthScoreListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorMonthScoreList indicates an expected call of GetAnchorMonthScoreList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorMonthScoreList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMonthScoreList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorMonthScoreList), varargs...)
}

// GetAnchorOperRecord mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorOperRecord(ctx context.Context, in *GetAnchorOperRecordReq, opts ...grpc.CallOption) (*GetAnchorOperRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorOperRecord", varargs...)
	ret0, _ := ret[0].(*GetAnchorOperRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorOperRecord indicates an expected call of GetAnchorOperRecord.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorOperRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorOperRecord", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorOperRecord), varargs...)
}

// GetAnchorOperRecordMgr mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorOperRecordMgr(ctx context.Context, in *apicentergo.GetAnchorOperRecordReq, opts ...grpc.CallOption) (*apicentergo.GetAnchorOperRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorOperRecordMgr", varargs...)
	ret0, _ := ret[0].(*apicentergo.GetAnchorOperRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorOperRecordMgr indicates an expected call of GetAnchorOperRecordMgr.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorOperRecordMgr(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorOperRecordMgr", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorOperRecordMgr), varargs...)
}

// GetAnchorScoreList mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorScoreList(ctx context.Context, in *GetAnchorScoreListReq, opts ...grpc.CallOption) (*GetAnchorScoreListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorScoreList", varargs...)
	ret0, _ := ret[0].(*GetAnchorScoreListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreList indicates an expected call of GetAnchorScoreList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorScoreList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorScoreList), varargs...)
}

// GetAnchorScoreOrderCount mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorScoreOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorScoreOrderCount", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreOrderCount indicates an expected call of GetAnchorScoreOrderCount.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorScoreOrderCount(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreOrderCount", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorScoreOrderCount), varargs...)
}

// GetAnchorScoreOrderIds mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorScoreOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorScoreOrderIds", varargs...)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreOrderIds indicates an expected call of GetAnchorScoreOrderIds.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorScoreOrderIds(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreOrderIds", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorScoreOrderIds), varargs...)
}

// GetAnchorScoreOrderList mocks base method.
func (m *MockChannelLiveMgrClient) GetAnchorScoreOrderList(ctx context.Context, in *GetAnchorScoreOrderListReq, opts ...grpc.CallOption) (*GetAnchorScoreOrderListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAnchorScoreOrderList", varargs...)
	ret0, _ := ret[0].(*GetAnchorScoreOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreOrderList indicates an expected call of GetAnchorScoreOrderList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAnchorScoreOrderList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreOrderList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAnchorScoreOrderList), varargs...)
}

// GetApplyList mocks base method.
func (m *MockChannelLiveMgrClient) GetApplyList(ctx context.Context, in *GetApplyListReq, opts ...grpc.CallOption) (*GetApplyListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetApplyList", varargs...)
	ret0, _ := ret[0].(*GetApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplyList indicates an expected call of GetApplyList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetApplyList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetApplyList), varargs...)
}

// GetAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrClient) GetAppointPkInfo(ctx context.Context, in *GetAppointPkInfoReq, opts ...grpc.CallOption) (*GetAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppointPkInfo", varargs...)
	ret0, _ := ret[0].(*GetAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppointPkInfo indicates an expected call of GetAppointPkInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAppointPkInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAppointPkInfo), varargs...)
}

// GetAppointPkInfoList mocks base method.
func (m *MockChannelLiveMgrClient) GetAppointPkInfoList(ctx context.Context, in *GetAppointPkInfoListReq, opts ...grpc.CallOption) (*GetAppointPkInfoListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAppointPkInfoList", varargs...)
	ret0, _ := ret[0].(*GetAppointPkInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppointPkInfoList indicates an expected call of GetAppointPkInfoList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetAppointPkInfoList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppointPkInfoList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetAppointPkInfoList), varargs...)
}

// GetChanneLivePkRankUser mocks base method.
func (m *MockChannelLiveMgrClient) GetChanneLivePkRankUser(ctx context.Context, in *GetChanneLivePkRankUserReq, opts ...grpc.CallOption) (*GetChanneLivePkRankUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChanneLivePkRankUser", varargs...)
	ret0, _ := ret[0].(*GetChanneLivePkRankUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanneLivePkRankUser indicates an expected call of GetChanneLivePkRankUser.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChanneLivePkRankUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanneLivePkRankUser", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChanneLivePkRankUser), varargs...)
}

// GetChannelLiveAnchorScore mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveAnchorScore(ctx context.Context, in *GetChannelLiveAnchorScoreReq, opts ...grpc.CallOption) (*GetChannelLiveAnchorScoreResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveAnchorScore", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveAnchorScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveAnchorScore indicates an expected call of GetChannelLiveAnchorScore.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveAnchorScore(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAnchorScore", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveAnchorScore), varargs...)
}

// GetChannelLiveAnchorScoreLog mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveAnchorScoreLog(ctx context.Context, in *GetChannelLiveAnchorScoreLogReq, opts ...grpc.CallOption) (*GetChannelLiveAnchorScoreLogResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveAnchorScoreLog", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveAnchorScoreLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveAnchorScoreLog indicates an expected call of GetChannelLiveAnchorScoreLog.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveAnchorScoreLog(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAnchorScoreLog", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveAnchorScoreLog), varargs...)
}

// GetChannelLiveAvg mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveAvg(ctx context.Context, in *GetChannelLiveAvgReq, opts ...grpc.CallOption) (*GetChannelLiveAvgResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveAvg", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveAvgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveAvg indicates an expected call of GetChannelLiveAvg.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveAvg(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAvg", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveAvg), varargs...)
}

// GetChannelLiveData mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveData(ctx context.Context, in *GetChannelLiveDataReq, opts ...grpc.CallOption) (*GetChannelLiveDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveData", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveData indicates an expected call of GetChannelLiveData.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveData", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveData), varargs...)
}

// GetChannelLiveHistoryRecord mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveHistoryRecord(ctx context.Context, in *GetChannelLiveHistoryRecordReq, opts ...grpc.CallOption) (*GetChannelLiveHistoryRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveHistoryRecord", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveHistoryRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveHistoryRecord indicates an expected call of GetChannelLiveHistoryRecord.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveHistoryRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveHistoryRecord", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveHistoryRecord), varargs...)
}

// GetChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveInfo(ctx context.Context, in *GetChannelLiveInfoReq, opts ...grpc.CallOption) (*GetChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveInfo", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveInfo indicates an expected call of GetChannelLiveInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveInfo), varargs...)
}

// GetChannelLivePKRecord mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLivePKRecord(ctx context.Context, in *GetChannelLivePKRecordReq, opts ...grpc.CallOption) (*GetChannelLivePKRecordResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLivePKRecord", varargs...)
	ret0, _ := ret[0].(*GetChannelLivePKRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLivePKRecord indicates an expected call of GetChannelLivePKRecord.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLivePKRecord(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLivePKRecord", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLivePKRecord), varargs...)
}

// GetChannelLiveRankUser mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveRankUser(ctx context.Context, in *GetChannelLiveRankUserReq, opts ...grpc.CallOption) (*GetChannelLiveRankUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveRankUser", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveRankUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveRankUser indicates an expected call of GetChannelLiveRankUser.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveRankUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveRankUser", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveRankUser), varargs...)
}

// GetChannelLiveStatus mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveStatus(ctx context.Context, in *GetChannelLiveStatusReq, opts ...grpc.CallOption) (*GetChannelLiveStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveStatus", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveStatus indicates an expected call of GetChannelLiveStatus.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveStatus", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveStatus), varargs...)
}

// GetChannelLiveTOPN mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveTOPN(ctx context.Context, in *GetChannelLiveTOPNReq, opts ...grpc.CallOption) (*GetChannelLiveTOPNResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveTOPN", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveTOPNResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveTOPN indicates an expected call of GetChannelLiveTOPN.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveTOPN(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveTOPN", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveTOPN), varargs...)
}

// GetChannelLiveTotalData mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveTotalData(ctx context.Context, in *GetChannelLiveTotalDataReq, opts ...grpc.CallOption) (*GetChannelLiveTotalDataResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveTotalData", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveTotalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveTotalData indicates an expected call of GetChannelLiveTotalData.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveTotalData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveTotalData", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveTotalData), varargs...)
}

// GetChannelLiveWatchTimeRankUser mocks base method.
func (m *MockChannelLiveMgrClient) GetChannelLiveWatchTimeRankUser(ctx context.Context, in *GetChannelLiveWatchTimeRankUserReq, opts ...grpc.CallOption) (*GetChannelLiveWatchTimeRankUserResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelLiveWatchTimeRankUser", varargs...)
	ret0, _ := ret[0].(*GetChannelLiveWatchTimeRankUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveWatchTimeRankUser indicates an expected call of GetChannelLiveWatchTimeRankUser.
func (mr *MockChannelLiveMgrClientMockRecorder) GetChannelLiveWatchTimeRankUser(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveWatchTimeRankUser", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetChannelLiveWatchTimeRankUser), varargs...)
}

// GetContestPkConfig mocks base method.
func (m *MockChannelLiveMgrClient) GetContestPkConfig(ctx context.Context, in *GetContestPkConfigReq, opts ...grpc.CallOption) (*GetContestPkConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContestPkConfig", varargs...)
	ret0, _ := ret[0].(*GetContestPkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContestPkConfig indicates an expected call of GetContestPkConfig.
func (mr *MockChannelLiveMgrClientMockRecorder) GetContestPkConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContestPkConfig", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetContestPkConfig), varargs...)
}

// GetContestPkInfo mocks base method.
func (m *MockChannelLiveMgrClient) GetContestPkInfo(ctx context.Context, in *GetContestPkInfoReq, opts ...grpc.CallOption) (*GetContestPkInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContestPkInfo", varargs...)
	ret0, _ := ret[0].(*GetContestPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContestPkInfo indicates an expected call of GetContestPkInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) GetContestPkInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContestPkInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetContestPkInfo), varargs...)
}

// GetContestPkResult mocks base method.
func (m *MockChannelLiveMgrClient) GetContestPkResult(ctx context.Context, in *GetContestPkResultReq, opts ...grpc.CallOption) (*GetContestPkResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetContestPkResult", varargs...)
	ret0, _ := ret[0].(*GetContestPkResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContestPkResult indicates an expected call of GetContestPkResult.
func (mr *MockChannelLiveMgrClientMockRecorder) GetContestPkResult(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContestPkResult", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetContestPkResult), varargs...)
}

// GetHeartBeatTimeOut mocks base method.
func (m *MockChannelLiveMgrClient) GetHeartBeatTimeOut(ctx context.Context, in *GetHeartBeatTimeOutReq, opts ...grpc.CallOption) (*GetHeartBeatTimeOutResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHeartBeatTimeOut", varargs...)
	ret0, _ := ret[0].(*GetHeartBeatTimeOutResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeartBeatTimeOut indicates an expected call of GetHeartBeatTimeOut.
func (mr *MockChannelLiveMgrClientMockRecorder) GetHeartBeatTimeOut(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeartBeatTimeOut", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetHeartBeatTimeOut), varargs...)
}

// GetItemConfig mocks base method.
func (m *MockChannelLiveMgrClient) GetItemConfig(ctx context.Context, in *GetItemConfigReq, opts ...grpc.CallOption) (*GetItemConfigResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetItemConfig", varargs...)
	ret0, _ := ret[0].(*GetItemConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemConfig indicates an expected call of GetItemConfig.
func (mr *MockChannelLiveMgrClientMockRecorder) GetItemConfig(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemConfig", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetItemConfig), varargs...)
}

// GetMyToolList mocks base method.
func (m *MockChannelLiveMgrClient) GetMyToolList(ctx context.Context, in *GetMyToolListReq, opts ...grpc.CallOption) (*GetMyToolListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMyToolList", varargs...)
	ret0, _ := ret[0].(*GetMyToolListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyToolList indicates an expected call of GetMyToolList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetMyToolList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyToolList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetMyToolList), varargs...)
}

// GetPKMatchInfo mocks base method.
func (m *MockChannelLiveMgrClient) GetPKMatchInfo(ctx context.Context, in *GetPKMatchInfoReq, opts ...grpc.CallOption) (*GetPKMatchInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPKMatchInfo", varargs...)
	ret0, _ := ret[0].(*GetPKMatchInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPKMatchInfo indicates an expected call of GetPKMatchInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) GetPKMatchInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPKMatchInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetPKMatchInfo), varargs...)
}

// GetPkInfo mocks base method.
func (m *MockChannelLiveMgrClient) GetPkInfo(ctx context.Context, in *GetPkInfoReq, opts ...grpc.CallOption) (*GetPkInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPkInfo", varargs...)
	ret0, _ := ret[0].(*GetPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPkInfo indicates an expected call of GetPkInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) GetPkInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPkInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetPkInfo), varargs...)
}

// GetUserPushCnt mocks base method.
func (m *MockChannelLiveMgrClient) GetUserPushCnt(ctx context.Context, in *GetUserPushCntReq, opts ...grpc.CallOption) (*GetUserPushCntResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserPushCnt", varargs...)
	ret0, _ := ret[0].(*GetUserPushCntResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPushCnt indicates an expected call of GetUserPushCnt.
func (mr *MockChannelLiveMgrClientMockRecorder) GetUserPushCnt(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPushCnt", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetUserPushCnt), varargs...)
}

// GetVirtualAnchorPerList mocks base method.
func (m *MockChannelLiveMgrClient) GetVirtualAnchorPerList(ctx context.Context, in *GetVirtualAnchorPerListReq, opts ...grpc.CallOption) (*GetVirtualAnchorPerListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualAnchorPerList", varargs...)
	ret0, _ := ret[0].(*GetVirtualAnchorPerListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualAnchorPerList indicates an expected call of GetVirtualAnchorPerList.
func (mr *MockChannelLiveMgrClientMockRecorder) GetVirtualAnchorPerList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualAnchorPerList", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetVirtualAnchorPerList), varargs...)
}

// GetVirtualLiveChannelSecret mocks base method.
func (m *MockChannelLiveMgrClient) GetVirtualLiveChannelSecret(ctx context.Context, in *GetVirtualLiveChannelSecretReq, opts ...grpc.CallOption) (*GetVirtualLiveChannelSecretResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualLiveChannelSecret", varargs...)
	ret0, _ := ret[0].(*GetVirtualLiveChannelSecretResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualLiveChannelSecret indicates an expected call of GetVirtualLiveChannelSecret.
func (mr *MockChannelLiveMgrClientMockRecorder) GetVirtualLiveChannelSecret(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualLiveChannelSecret", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetVirtualLiveChannelSecret), varargs...)
}

// GetVirtualLiveInfoBySecret mocks base method.
func (m *MockChannelLiveMgrClient) GetVirtualLiveInfoBySecret(ctx context.Context, in *GetVirtualLiveInfoBySecretReq, opts ...grpc.CallOption) (*GetVirtualLiveInfoBySecretResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVirtualLiveInfoBySecret", varargs...)
	ret0, _ := ret[0].(*GetVirtualLiveInfoBySecretResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualLiveInfoBySecret indicates an expected call of GetVirtualLiveInfoBySecret.
func (mr *MockChannelLiveMgrClientMockRecorder) GetVirtualLiveInfoBySecret(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualLiveInfoBySecret", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).GetVirtualLiveInfoBySecret), varargs...)
}

// HandlerApply mocks base method.
func (m *MockChannelLiveMgrClient) HandlerApply(ctx context.Context, in *HandlerApplyReq, opts ...grpc.CallOption) (*HandlerApplyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandlerApply", varargs...)
	ret0, _ := ret[0].(*HandlerApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlerApply indicates an expected call of HandlerApply.
func (mr *MockChannelLiveMgrClientMockRecorder) HandlerApply(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerApply", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).HandlerApply), varargs...)
}

// PushTest mocks base method.
func (m *MockChannelLiveMgrClient) PushTest(ctx context.Context, in *PushTestReq, opts ...grpc.CallOption) (*PushTestResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PushTest", varargs...)
	ret0, _ := ret[0].(*PushTestResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushTest indicates an expected call of PushTest.
func (mr *MockChannelLiveMgrClientMockRecorder) PushTest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushTest", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).PushTest), varargs...)
}

// ReportClientIDChange mocks base method.
func (m *MockChannelLiveMgrClient) ReportClientIDChange(ctx context.Context, in *ReportClientIDChangeReq, opts ...grpc.CallOption) (*ReportClientIDChangeResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportClientIDChange", varargs...)
	ret0, _ := ret[0].(*ReportClientIDChangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportClientIDChange indicates an expected call of ReportClientIDChange.
func (mr *MockChannelLiveMgrClientMockRecorder) ReportClientIDChange(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportClientIDChange", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).ReportClientIDChange), varargs...)
}

// SearchAnchor mocks base method.
func (m *MockChannelLiveMgrClient) SearchAnchor(ctx context.Context, in *SearchAnchorReq, opts ...grpc.CallOption) (*SearchAnchorResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchAnchor", varargs...)
	ret0, _ := ret[0].(*SearchAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAnchor indicates an expected call of SearchAnchor.
func (mr *MockChannelLiveMgrClientMockRecorder) SearchAnchor(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAnchor", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SearchAnchor), varargs...)
}

// SetAuthFlag mocks base method.
func (m *MockChannelLiveMgrClient) SetAuthFlag(ctx context.Context, in *SetAuthFlagReq, opts ...grpc.CallOption) (*SetAuthFlagResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetAuthFlag", varargs...)
	ret0, _ := ret[0].(*SetAuthFlagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAuthFlag indicates an expected call of SetAuthFlag.
func (mr *MockChannelLiveMgrClientMockRecorder) SetAuthFlag(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAuthFlag", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetAuthFlag), varargs...)
}

// SetChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrClient) SetChannelLiveInfo(ctx context.Context, in *SetChannelLiveInfoReq, opts ...grpc.CallOption) (*SetChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelLiveInfo", varargs...)
	ret0, _ := ret[0].(*SetChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveInfo indicates an expected call of SetChannelLiveInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) SetChannelLiveInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetChannelLiveInfo), varargs...)
}

// SetChannelLiveInfoForTest mocks base method.
func (m *MockChannelLiveMgrClient) SetChannelLiveInfoForTest(ctx context.Context, in *SetChannelLiveInfoForTestReq, opts ...grpc.CallOption) (*SetChannelLiveInfoForTestResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelLiveInfoForTest", varargs...)
	ret0, _ := ret[0].(*SetChannelLiveInfoForTestResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveInfoForTest indicates an expected call of SetChannelLiveInfoForTest.
func (mr *MockChannelLiveMgrClientMockRecorder) SetChannelLiveInfoForTest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveInfoForTest", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetChannelLiveInfoForTest), varargs...)
}

// SetChannelLiveOpponentMicFlag mocks base method.
func (m *MockChannelLiveMgrClient) SetChannelLiveOpponentMicFlag(ctx context.Context, in *SetChannelLiveOpponentMicFlagReq, opts ...grpc.CallOption) (*SetChannelLiveOpponentMicFlagResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelLiveOpponentMicFlag", varargs...)
	ret0, _ := ret[0].(*SetChannelLiveOpponentMicFlagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveOpponentMicFlag indicates an expected call of SetChannelLiveOpponentMicFlag.
func (mr *MockChannelLiveMgrClientMockRecorder) SetChannelLiveOpponentMicFlag(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveOpponentMicFlag", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetChannelLiveOpponentMicFlag), varargs...)
}

// SetChannelLiveStatus mocks base method.
func (m *MockChannelLiveMgrClient) SetChannelLiveStatus(ctx context.Context, in *SetChannelLiveStatusReq, opts ...grpc.CallOption) (*SetChannelLiveStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelLiveStatus", varargs...)
	ret0, _ := ret[0].(*SetChannelLiveStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveStatus indicates an expected call of SetChannelLiveStatus.
func (mr *MockChannelLiveMgrClientMockRecorder) SetChannelLiveStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveStatus", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetChannelLiveStatus), varargs...)
}

// SetChannelLiveTag mocks base method.
func (m *MockChannelLiveMgrClient) SetChannelLiveTag(ctx context.Context, in *SetChannelLiveTagReq, opts ...grpc.CallOption) (*SetChannelLiveTagResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelLiveTag", varargs...)
	ret0, _ := ret[0].(*SetChannelLiveTagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveTag indicates an expected call of SetChannelLiveTag.
func (mr *MockChannelLiveMgrClientMockRecorder) SetChannelLiveTag(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveTag", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetChannelLiveTag), varargs...)
}

// SetChannelLiveTagMgr mocks base method.
func (m *MockChannelLiveMgrClient) SetChannelLiveTagMgr(ctx context.Context, in *apicentergo.SetChannelLiveTagReq, opts ...grpc.CallOption) (*apicentergo.SetChannelLiveTagResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetChannelLiveTagMgr", varargs...)
	ret0, _ := ret[0].(*apicentergo.SetChannelLiveTagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveTagMgr indicates an expected call of SetChannelLiveTagMgr.
func (mr *MockChannelLiveMgrClientMockRecorder) SetChannelLiveTagMgr(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveTagMgr", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetChannelLiveTagMgr), varargs...)
}

// SetPkStatus mocks base method.
func (m *MockChannelLiveMgrClient) SetPkStatus(ctx context.Context, in *SetPkStatusReq, opts ...grpc.CallOption) (*SetPkStatusResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPkStatus", varargs...)
	ret0, _ := ret[0].(*SetPkStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPkStatus indicates an expected call of SetPkStatus.
func (mr *MockChannelLiveMgrClientMockRecorder) SetPkStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPkStatus", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).SetPkStatus), varargs...)
}

// StartContestPk mocks base method.
func (m *MockChannelLiveMgrClient) StartContestPk(ctx context.Context, in *StartContestPkReq, opts ...grpc.CallOption) (*StartContestPkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartContestPk", varargs...)
	ret0, _ := ret[0].(*StartContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartContestPk indicates an expected call of StartContestPk.
func (mr *MockChannelLiveMgrClientMockRecorder) StartContestPk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartContestPk", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).StartContestPk), varargs...)
}

// StartPkMatch mocks base method.
func (m *MockChannelLiveMgrClient) StartPkMatch(ctx context.Context, in *StartPkMatchReq, opts ...grpc.CallOption) (*StartPkMatchResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartPkMatch", varargs...)
	ret0, _ := ret[0].(*StartPkMatchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartPkMatch indicates an expected call of StartPkMatch.
func (mr *MockChannelLiveMgrClientMockRecorder) StartPkMatch(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartPkMatch", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).StartPkMatch), varargs...)
}

// UpdateAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrClient) UpdateAppointPkInfo(ctx context.Context, in *UpdateAppointPkInfoReq, opts ...grpc.CallOption) (*UpdateAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAppointPkInfo", varargs...)
	ret0, _ := ret[0].(*UpdateAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAppointPkInfo indicates an expected call of UpdateAppointPkInfo.
func (mr *MockChannelLiveMgrClientMockRecorder) UpdateAppointPkInfo(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).UpdateAppointPkInfo), varargs...)
}

// UpdateContestPk mocks base method.
func (m *MockChannelLiveMgrClient) UpdateContestPk(ctx context.Context, in *UpdateContestPkReq, opts ...grpc.CallOption) (*UpdateContestPkResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateContestPk", varargs...)
	ret0, _ := ret[0].(*UpdateContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateContestPk indicates an expected call of UpdateContestPk.
func (mr *MockChannelLiveMgrClientMockRecorder) UpdateContestPk(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateContestPk", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).UpdateContestPk), varargs...)
}

// UpdateContestPkResult mocks base method.
func (m *MockChannelLiveMgrClient) UpdateContestPkResult(ctx context.Context, in *UpdateContestPkResultReq, opts ...grpc.CallOption) (*UpdateContestPkResultResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateContestPkResult", varargs...)
	ret0, _ := ret[0].(*UpdateContestPkResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateContestPkResult indicates an expected call of UpdateContestPkResult.
func (mr *MockChannelLiveMgrClientMockRecorder) UpdateContestPkResult(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateContestPkResult", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).UpdateContestPkResult), varargs...)
}

// UpdateVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrClient) UpdateVirtualAnchorPer(ctx context.Context, in *UpdateVirtualAnchorPerReq, opts ...grpc.CallOption) (*UpdateVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateVirtualAnchorPer", varargs...)
	ret0, _ := ret[0].(*UpdateVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateVirtualAnchorPer indicates an expected call of UpdateVirtualAnchorPer.
func (mr *MockChannelLiveMgrClientMockRecorder) UpdateVirtualAnchorPer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrClient)(nil).UpdateVirtualAnchorPer), varargs...)
}

// MockChannelLiveMgrServer is a mock of ChannelLiveMgrServer interface.
type MockChannelLiveMgrServer struct {
	ctrl     *gomock.Controller
	recorder *MockChannelLiveMgrServerMockRecorder
}

// MockChannelLiveMgrServerMockRecorder is the mock recorder for MockChannelLiveMgrServer.
type MockChannelLiveMgrServerMockRecorder struct {
	mock *MockChannelLiveMgrServer
}

// NewMockChannelLiveMgrServer creates a new mock instance.
func NewMockChannelLiveMgrServer(ctrl *gomock.Controller) *MockChannelLiveMgrServer {
	mock := &MockChannelLiveMgrServer{ctrl: ctrl}
	mock.recorder = &MockChannelLiveMgrServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelLiveMgrServer) EXPECT() *MockChannelLiveMgrServerMockRecorder {
	return m.recorder
}

// AcceptAppointPk mocks base method.
func (m *MockChannelLiveMgrServer) AcceptAppointPk(ctx context.Context, in *AcceptAppointPkReq) (*AcceptAppointPkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptAppointPk", ctx, in)
	ret0, _ := ret[0].(*AcceptAppointPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcceptAppointPk indicates an expected call of AcceptAppointPk.
func (mr *MockChannelLiveMgrServerMockRecorder) AcceptAppointPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptAppointPk", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).AcceptAppointPk), ctx, in)
}

// AddAnchorInBackList mocks base method.
func (m *MockChannelLiveMgrServer) AddAnchorInBackList(ctx context.Context, in *AddAnchorInBackListReq) (*AddAnchorInBackListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAnchorInBackList", ctx, in)
	ret0, _ := ret[0].(*AddAnchorInBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAnchorInBackList indicates an expected call of AddAnchorInBackList.
func (mr *MockChannelLiveMgrServerMockRecorder) AddAnchorInBackList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAnchorInBackList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).AddAnchorInBackList), ctx, in)
}

// AddAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrServer) AddAppointPkInfo(ctx context.Context, in *AddAppointPkInfoReq) (*AddAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*AddAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddAppointPkInfo indicates an expected call of AddAppointPkInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) AddAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).AddAppointPkInfo), ctx, in)
}

// AddChannelLiveAnchorScore mocks base method.
func (m *MockChannelLiveMgrServer) AddChannelLiveAnchorScore(ctx context.Context, in *AddChannelLiveAnchorScoreReq) (*AddChannelLiveAnchorScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddChannelLiveAnchorScore", ctx, in)
	ret0, _ := ret[0].(*AddChannelLiveAnchorScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddChannelLiveAnchorScore indicates an expected call of AddChannelLiveAnchorScore.
func (mr *MockChannelLiveMgrServerMockRecorder) AddChannelLiveAnchorScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddChannelLiveAnchorScore", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).AddChannelLiveAnchorScore), ctx, in)
}

// AddVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrServer) AddVirtualAnchorPer(ctx context.Context, in *AddVirtualAnchorPerReq) (*AddVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddVirtualAnchorPer", ctx, in)
	ret0, _ := ret[0].(*AddVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddVirtualAnchorPer indicates an expected call of AddVirtualAnchorPer.
func (mr *MockChannelLiveMgrServerMockRecorder) AddVirtualAnchorPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).AddVirtualAnchorPer), ctx, in)
}

// ApplyPk mocks base method.
func (m *MockChannelLiveMgrServer) ApplyPk(ctx context.Context, in *ApplyPkReq) (*ApplyPkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyPk", ctx, in)
	ret0, _ := ret[0].(*ApplyPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyPk indicates an expected call of ApplyPk.
func (mr *MockChannelLiveMgrServerMockRecorder) ApplyPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyPk", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).ApplyPk), ctx, in)
}

// BatDelChannelLiveInfoMgr mocks base method.
func (m *MockChannelLiveMgrServer) BatDelChannelLiveInfoMgr(ctx context.Context, in *apicentergo.BatDelChannelLiveInfoReq) (*apicentergo.BatDelChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatDelChannelLiveInfoMgr", ctx, in)
	ret0, _ := ret[0].(*apicentergo.BatDelChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatDelChannelLiveInfoMgr indicates an expected call of BatDelChannelLiveInfoMgr.
func (mr *MockChannelLiveMgrServerMockRecorder) BatDelChannelLiveInfoMgr(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatDelChannelLiveInfoMgr", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatDelChannelLiveInfoMgr), ctx, in)
}

// BatGetChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrServer) BatGetChannelLiveInfo(ctx context.Context, in *BatGetChannelLiveInfoReq) (*BatGetChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatGetChannelLiveInfo", ctx, in)
	ret0, _ := ret[0].(*BatGetChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetChannelLiveInfo indicates an expected call of BatGetChannelLiveInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) BatGetChannelLiveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatGetChannelLiveInfo), ctx, in)
}

// BatchAddAnchorMgr mocks base method.
func (m *MockChannelLiveMgrServer) BatchAddAnchorMgr(ctx context.Context, in *apicentergo.BatchAddAnchorReq) (*apicentergo.BatchAddAnchorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAddAnchorMgr", ctx, in)
	ret0, _ := ret[0].(*apicentergo.BatchAddAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAddAnchorMgr indicates an expected call of BatchAddAnchorMgr.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchAddAnchorMgr(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAddAnchorMgr", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchAddAnchorMgr), ctx, in)
}

// BatchGetAllChannelLive mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetAllChannelLive(ctx context.Context, in *BatchGetAllChannelLiveReq) (*BatchGetAllChannelLiveResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAllChannelLive", ctx, in)
	ret0, _ := ret[0].(*BatchGetAllChannelLiveResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAllChannelLive indicates an expected call of BatchGetAllChannelLive.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetAllChannelLive(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAllChannelLive", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetAllChannelLive), ctx, in)
}

// BatchGetAnchorTotalData mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetAnchorTotalData(ctx context.Context, in *BatchGetAnchorTotalDataReq) (*BatchGetAnchorTotalDataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAnchorTotalData", ctx, in)
	ret0, _ := ret[0].(*BatchGetAnchorTotalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAnchorTotalData indicates an expected call of BatchGetAnchorTotalData.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetAnchorTotalData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAnchorTotalData", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetAnchorTotalData), ctx, in)
}

// BatchGetChannelLiveRecord mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetChannelLiveRecord(ctx context.Context, in *BatchGetChannelLiveRecordReq) (*BatchGetChannelLiveRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveRecord", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelLiveRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveRecord indicates an expected call of BatchGetChannelLiveRecord.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetChannelLiveRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveRecord", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetChannelLiveRecord), ctx, in)
}

// BatchGetChannelLiveStatus mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetChannelLiveStatus(ctx context.Context, in *BatchGetChannelLiveStatusReq) (*BatchGetChannelLiveStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveStatus", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelLiveStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveStatus indicates an expected call of BatchGetChannelLiveStatus.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetChannelLiveStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveStatus", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetChannelLiveStatus), ctx, in)
}

// BatchGetChannelLiveStatusSimple mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetChannelLiveStatusSimple(ctx context.Context, in *BatchGetChannelLiveStatusSimpleReq) (*BatchGetChannelLiveStatusSimpleResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveStatusSimple", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelLiveStatusSimpleResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveStatusSimple indicates an expected call of BatchGetChannelLiveStatusSimple.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetChannelLiveStatusSimple(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveStatusSimple", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetChannelLiveStatusSimple), ctx, in)
}

// BatchGetChannelLiveTotalData mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetChannelLiveTotalData(ctx context.Context, in *BatchGetChannelLiveTotalDataReq) (*BatchGetChannelLiveTotalDataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetChannelLiveTotalData", ctx, in)
	ret0, _ := ret[0].(*BatchGetChannelLiveTotalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetChannelLiveTotalData indicates an expected call of BatchGetChannelLiveTotalData.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetChannelLiveTotalData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetChannelLiveTotalData", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetChannelLiveTotalData), ctx, in)
}

// BatchGetContestPkConfig mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetContestPkConfig(ctx context.Context, in *BatchGetContestPkConfigReq) (*BatchGetContestPkConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetContestPkConfig", ctx, in)
	ret0, _ := ret[0].(*BatchGetContestPkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetContestPkConfig indicates an expected call of BatchGetContestPkConfig.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetContestPkConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContestPkConfig", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetContestPkConfig), ctx, in)
}

// BatchGetContestPkInfo mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetContestPkInfo(ctx context.Context, in *BatchGetContestPkInfoReq) (*BatchGetContestPkInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetContestPkInfo", ctx, in)
	ret0, _ := ret[0].(*BatchGetContestPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetContestPkInfo indicates an expected call of BatchGetContestPkInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetContestPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetContestPkInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetContestPkInfo), ctx, in)
}

// BatchGetGroupFansGiftValue mocks base method.
func (m *MockChannelLiveMgrServer) BatchGetGroupFansGiftValue(ctx context.Context, in *BatchGetGroupFansGiftValueReq) (*BatchGetGroupFansGiftValueResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetGroupFansGiftValue", ctx, in)
	ret0, _ := ret[0].(*BatchGetGroupFansGiftValueResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetGroupFansGiftValue indicates an expected call of BatchGetGroupFansGiftValue.
func (mr *MockChannelLiveMgrServerMockRecorder) BatchGetGroupFansGiftValue(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetGroupFansGiftValue", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).BatchGetGroupFansGiftValue), ctx, in)
}

// CancelContestPk mocks base method.
func (m *MockChannelLiveMgrServer) CancelContestPk(ctx context.Context, in *CancelContestPkReq) (*CancelContestPkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelContestPk", ctx, in)
	ret0, _ := ret[0].(*CancelContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelContestPk indicates an expected call of CancelContestPk.
func (mr *MockChannelLiveMgrServerMockRecorder) CancelContestPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelContestPk", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).CancelContestPk), ctx, in)
}

// CancelPKApply mocks base method.
func (m *MockChannelLiveMgrServer) CancelPKApply(ctx context.Context, in *CancelPKApplyReq) (*CancelPKApplyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelPKApply", ctx, in)
	ret0, _ := ret[0].(*CancelPKApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelPKApply indicates an expected call of CancelPKApply.
func (mr *MockChannelLiveMgrServerMockRecorder) CancelPKApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPKApply", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).CancelPKApply), ctx, in)
}

// CancelPkMatch mocks base method.
func (m *MockChannelLiveMgrServer) CancelPkMatch(ctx context.Context, in *CancelPkMatchReq) (*CancelPkMatchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelPkMatch", ctx, in)
	ret0, _ := ret[0].(*CancelPkMatchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelPkMatch indicates an expected call of CancelPkMatch.
func (mr *MockChannelLiveMgrServerMockRecorder) CancelPkMatch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelPkMatch", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).CancelPkMatch), ctx, in)
}

// ChannelLiveHeartbeat mocks base method.
func (m *MockChannelLiveMgrServer) ChannelLiveHeartbeat(ctx context.Context, in *ChannelLiveHeartbeatReq) (*ChannelLiveHeartbeatResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ChannelLiveHeartbeat", ctx, in)
	ret0, _ := ret[0].(*ChannelLiveHeartbeatResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ChannelLiveHeartbeat indicates an expected call of ChannelLiveHeartbeat.
func (mr *MockChannelLiveMgrServerMockRecorder) ChannelLiveHeartbeat(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ChannelLiveHeartbeat", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).ChannelLiveHeartbeat), ctx, in)
}

// CheckAnchorPkAvailable mocks base method.
func (m *MockChannelLiveMgrServer) CheckAnchorPkAvailable(ctx context.Context, in *CheckAnchorPkAvailableReq) (*CheckAnchorPkAvailableResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAnchorPkAvailable", ctx, in)
	ret0, _ := ret[0].(*CheckAnchorPkAvailableResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAnchorPkAvailable indicates an expected call of CheckAnchorPkAvailable.
func (mr *MockChannelLiveMgrServerMockRecorder) CheckAnchorPkAvailable(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAnchorPkAvailable", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).CheckAnchorPkAvailable), ctx, in)
}

// CheckHasVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrServer) CheckHasVirtualAnchorPer(ctx context.Context, in *CheckHasVirtualAnchorPerReq) (*CheckHasVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHasVirtualAnchorPer", ctx, in)
	ret0, _ := ret[0].(*CheckHasVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckHasVirtualAnchorPer indicates an expected call of CheckHasVirtualAnchorPer.
func (mr *MockChannelLiveMgrServerMockRecorder) CheckHasVirtualAnchorPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHasVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).CheckHasVirtualAnchorPer), ctx, in)
}

// CheckIsAnchorInBackList mocks base method.
func (m *MockChannelLiveMgrServer) CheckIsAnchorInBackList(ctx context.Context, in *CheckIsAnchorInBackListReq) (*CheckIsAnchorInBackListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckIsAnchorInBackList", ctx, in)
	ret0, _ := ret[0].(*CheckIsAnchorInBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIsAnchorInBackList indicates an expected call of CheckIsAnchorInBackList.
func (mr *MockChannelLiveMgrServerMockRecorder) CheckIsAnchorInBackList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIsAnchorInBackList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).CheckIsAnchorInBackList), ctx, in)
}

// ClearVirtualLiveSecretRefreshLimit mocks base method.
func (m *MockChannelLiveMgrServer) ClearVirtualLiveSecretRefreshLimit(ctx context.Context, in *ClearVirtualLiveSecretRefreshLimitReq) (*ClearVirtualLiveSecretRefreshLimitResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearVirtualLiveSecretRefreshLimit", ctx, in)
	ret0, _ := ret[0].(*ClearVirtualLiveSecretRefreshLimitResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearVirtualLiveSecretRefreshLimit indicates an expected call of ClearVirtualLiveSecretRefreshLimit.
func (mr *MockChannelLiveMgrServerMockRecorder) ClearVirtualLiveSecretRefreshLimit(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearVirtualLiveSecretRefreshLimit", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).ClearVirtualLiveSecretRefreshLimit), ctx, in)
}

// ConfirmAppointPkPush mocks base method.
func (m *MockChannelLiveMgrServer) ConfirmAppointPkPush(ctx context.Context, in *ConfirmAppointPkPushReq) (*ConfirmAppointPkPushResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfirmAppointPkPush", ctx, in)
	ret0, _ := ret[0].(*ConfirmAppointPkPushResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConfirmAppointPkPush indicates an expected call of ConfirmAppointPkPush.
func (mr *MockChannelLiveMgrServerMockRecorder) ConfirmAppointPkPush(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfirmAppointPkPush", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).ConfirmAppointPkPush), ctx, in)
}

// CreateContestPk mocks base method.
func (m *MockChannelLiveMgrServer) CreateContestPk(ctx context.Context, in *CreateContestPkReq) (*CreateContestPkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateContestPk", ctx, in)
	ret0, _ := ret[0].(*CreateContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateContestPk indicates an expected call of CreateContestPk.
func (mr *MockChannelLiveMgrServerMockRecorder) CreateContestPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateContestPk", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).CreateContestPk), ctx, in)
}

// DelAnchorBackList mocks base method.
func (m *MockChannelLiveMgrServer) DelAnchorBackList(ctx context.Context, in *DelAnchorBackListReq) (*DelAnchorBackListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAnchorBackList", ctx, in)
	ret0, _ := ret[0].(*DelAnchorBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelAnchorBackList indicates an expected call of DelAnchorBackList.
func (mr *MockChannelLiveMgrServerMockRecorder) DelAnchorBackList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAnchorBackList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).DelAnchorBackList), ctx, in)
}

// DelAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrServer) DelAppointPkInfo(ctx context.Context, in *DelAppointPkInfoReq) (*DelAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*DelAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelAppointPkInfo indicates an expected call of DelAppointPkInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) DelAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).DelAppointPkInfo), ctx, in)
}

// DelChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrServer) DelChannelLiveInfo(ctx context.Context, in *DelChannelLiveInfoReq) (*DelChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelChannelLiveInfo", ctx, in)
	ret0, _ := ret[0].(*DelChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelChannelLiveInfo indicates an expected call of DelChannelLiveInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) DelChannelLiveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).DelChannelLiveInfo), ctx, in)
}

// DelVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrServer) DelVirtualAnchorPer(ctx context.Context, in *DelVirtualAnchorPerReq) (*DelVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelVirtualAnchorPer", ctx, in)
	ret0, _ := ret[0].(*DelVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelVirtualAnchorPer indicates an expected call of DelVirtualAnchorPer.
func (mr *MockChannelLiveMgrServerMockRecorder) DelVirtualAnchorPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).DelVirtualAnchorPer), ctx, in)
}

// FinishContestPk mocks base method.
func (m *MockChannelLiveMgrServer) FinishContestPk(ctx context.Context, in *FinishContestPkReq) (*FinishContestPkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FinishContestPk", ctx, in)
	ret0, _ := ret[0].(*FinishContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishContestPk indicates an expected call of FinishContestPk.
func (mr *MockChannelLiveMgrServerMockRecorder) FinishContestPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishContestPk", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).FinishContestPk), ctx, in)
}

// GetAllAnchor mocks base method.
func (m *MockChannelLiveMgrServer) GetAllAnchor(ctx context.Context, in *GetAllAnchorReq) (*GetAllAnchorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllAnchor", ctx, in)
	ret0, _ := ret[0].(*GetAllAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllAnchor indicates an expected call of GetAllAnchor.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAllAnchor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllAnchor", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAllAnchor), ctx, in)
}

// GetAnchorBackList mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorBackList(ctx context.Context, in *GetAnchorBackListReq) (*GetAnchorBackListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorBackList", ctx, in)
	ret0, _ := ret[0].(*GetAnchorBackListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorBackList indicates an expected call of GetAnchorBackList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorBackList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorBackList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorBackList), ctx, in)
}

// GetAnchorByUidList mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorByUidList(ctx context.Context, in *GetAnchorByUidListReq) (*GetAnchorByUidListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorByUidList", ctx, in)
	ret0, _ := ret[0].(*GetAnchorByUidListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorByUidList indicates an expected call of GetAnchorByUidList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorByUidList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorByUidList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorByUidList), ctx, in)
}

// GetAnchorList mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorList(ctx context.Context, in *GetAnchorListReq) (*GetAnchorListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorList", ctx, in)
	ret0, _ := ret[0].(*GetAnchorListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorList indicates an expected call of GetAnchorList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorList), ctx, in)
}

// GetAnchorListMgr mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorListMgr(ctx context.Context, in *apicentergo.GetAnchorListReq) (*apicentergo.GetAnchorListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorListMgr", ctx, in)
	ret0, _ := ret[0].(*apicentergo.GetAnchorListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorListMgr indicates an expected call of GetAnchorListMgr.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorListMgr(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorListMgr", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorListMgr), ctx, in)
}

// GetAnchorMonthScoreList mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorMonthScoreList(ctx context.Context, in *GetAnchorMonthScoreListReq) (*GetAnchorMonthScoreListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorMonthScoreList", ctx, in)
	ret0, _ := ret[0].(*GetAnchorMonthScoreListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorMonthScoreList indicates an expected call of GetAnchorMonthScoreList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorMonthScoreList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorMonthScoreList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorMonthScoreList), ctx, in)
}

// GetAnchorOperRecord mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorOperRecord(ctx context.Context, in *GetAnchorOperRecordReq) (*GetAnchorOperRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorOperRecord", ctx, in)
	ret0, _ := ret[0].(*GetAnchorOperRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorOperRecord indicates an expected call of GetAnchorOperRecord.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorOperRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorOperRecord", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorOperRecord), ctx, in)
}

// GetAnchorOperRecordMgr mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorOperRecordMgr(ctx context.Context, in *apicentergo.GetAnchorOperRecordReq) (*apicentergo.GetAnchorOperRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorOperRecordMgr", ctx, in)
	ret0, _ := ret[0].(*apicentergo.GetAnchorOperRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorOperRecordMgr indicates an expected call of GetAnchorOperRecordMgr.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorOperRecordMgr(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorOperRecordMgr", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorOperRecordMgr), ctx, in)
}

// GetAnchorScoreList mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorScoreList(ctx context.Context, in *GetAnchorScoreListReq) (*GetAnchorScoreListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreList", ctx, in)
	ret0, _ := ret[0].(*GetAnchorScoreListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreList indicates an expected call of GetAnchorScoreList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorScoreList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorScoreList), ctx, in)
}

// GetAnchorScoreOrderCount mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorScoreOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreOrderCount", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.CountResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreOrderCount indicates an expected call of GetAnchorScoreOrderCount.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorScoreOrderCount(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreOrderCount", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorScoreOrderCount), ctx, in)
}

// GetAnchorScoreOrderIds mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorScoreOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreOrderIds", ctx, in)
	ret0, _ := ret[0].(*reconcile_v2.OrderIdsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreOrderIds indicates an expected call of GetAnchorScoreOrderIds.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorScoreOrderIds(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreOrderIds", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorScoreOrderIds), ctx, in)
}

// GetAnchorScoreOrderList mocks base method.
func (m *MockChannelLiveMgrServer) GetAnchorScoreOrderList(ctx context.Context, in *GetAnchorScoreOrderListReq) (*GetAnchorScoreOrderListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnchorScoreOrderList", ctx, in)
	ret0, _ := ret[0].(*GetAnchorScoreOrderListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnchorScoreOrderList indicates an expected call of GetAnchorScoreOrderList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAnchorScoreOrderList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnchorScoreOrderList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAnchorScoreOrderList), ctx, in)
}

// GetApplyList mocks base method.
func (m *MockChannelLiveMgrServer) GetApplyList(ctx context.Context, in *GetApplyListReq) (*GetApplyListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplyList", ctx, in)
	ret0, _ := ret[0].(*GetApplyListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplyList indicates an expected call of GetApplyList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetApplyList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplyList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetApplyList), ctx, in)
}

// GetAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrServer) GetAppointPkInfo(ctx context.Context, in *GetAppointPkInfoReq) (*GetAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*GetAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppointPkInfo indicates an expected call of GetAppointPkInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAppointPkInfo), ctx, in)
}

// GetAppointPkInfoList mocks base method.
func (m *MockChannelLiveMgrServer) GetAppointPkInfoList(ctx context.Context, in *GetAppointPkInfoListReq) (*GetAppointPkInfoListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppointPkInfoList", ctx, in)
	ret0, _ := ret[0].(*GetAppointPkInfoListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppointPkInfoList indicates an expected call of GetAppointPkInfoList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetAppointPkInfoList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppointPkInfoList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetAppointPkInfoList), ctx, in)
}

// GetChanneLivePkRankUser mocks base method.
func (m *MockChannelLiveMgrServer) GetChanneLivePkRankUser(ctx context.Context, in *GetChanneLivePkRankUserReq) (*GetChanneLivePkRankUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChanneLivePkRankUser", ctx, in)
	ret0, _ := ret[0].(*GetChanneLivePkRankUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChanneLivePkRankUser indicates an expected call of GetChanneLivePkRankUser.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChanneLivePkRankUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChanneLivePkRankUser", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChanneLivePkRankUser), ctx, in)
}

// GetChannelLiveAnchorScore mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveAnchorScore(ctx context.Context, in *GetChannelLiveAnchorScoreReq) (*GetChannelLiveAnchorScoreResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveAnchorScore", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveAnchorScoreResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveAnchorScore indicates an expected call of GetChannelLiveAnchorScore.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveAnchorScore(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAnchorScore", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveAnchorScore), ctx, in)
}

// GetChannelLiveAnchorScoreLog mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveAnchorScoreLog(ctx context.Context, in *GetChannelLiveAnchorScoreLogReq) (*GetChannelLiveAnchorScoreLogResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveAnchorScoreLog", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveAnchorScoreLogResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveAnchorScoreLog indicates an expected call of GetChannelLiveAnchorScoreLog.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveAnchorScoreLog(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAnchorScoreLog", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveAnchorScoreLog), ctx, in)
}

// GetChannelLiveAvg mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveAvg(ctx context.Context, in *GetChannelLiveAvgReq) (*GetChannelLiveAvgResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveAvg", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveAvgResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveAvg indicates an expected call of GetChannelLiveAvg.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveAvg(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveAvg", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveAvg), ctx, in)
}

// GetChannelLiveData mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveData(ctx context.Context, in *GetChannelLiveDataReq) (*GetChannelLiveDataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveData", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveData indicates an expected call of GetChannelLiveData.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveData", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveData), ctx, in)
}

// GetChannelLiveHistoryRecord mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveHistoryRecord(ctx context.Context, in *GetChannelLiveHistoryRecordReq) (*GetChannelLiveHistoryRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveHistoryRecord", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveHistoryRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveHistoryRecord indicates an expected call of GetChannelLiveHistoryRecord.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveHistoryRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveHistoryRecord", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveHistoryRecord), ctx, in)
}

// GetChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveInfo(ctx context.Context, in *GetChannelLiveInfoReq) (*GetChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveInfo", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveInfo indicates an expected call of GetChannelLiveInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveInfo), ctx, in)
}

// GetChannelLivePKRecord mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLivePKRecord(ctx context.Context, in *GetChannelLivePKRecordReq) (*GetChannelLivePKRecordResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLivePKRecord", ctx, in)
	ret0, _ := ret[0].(*GetChannelLivePKRecordResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLivePKRecord indicates an expected call of GetChannelLivePKRecord.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLivePKRecord(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLivePKRecord", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLivePKRecord), ctx, in)
}

// GetChannelLiveRankUser mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveRankUser(ctx context.Context, in *GetChannelLiveRankUserReq) (*GetChannelLiveRankUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveRankUser", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveRankUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveRankUser indicates an expected call of GetChannelLiveRankUser.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveRankUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveRankUser", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveRankUser), ctx, in)
}

// GetChannelLiveStatus mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveStatus(ctx context.Context, in *GetChannelLiveStatusReq) (*GetChannelLiveStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveStatus", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveStatus indicates an expected call of GetChannelLiveStatus.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveStatus", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveStatus), ctx, in)
}

// GetChannelLiveTOPN mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveTOPN(ctx context.Context, in *GetChannelLiveTOPNReq) (*GetChannelLiveTOPNResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveTOPN", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveTOPNResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveTOPN indicates an expected call of GetChannelLiveTOPN.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveTOPN(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveTOPN", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveTOPN), ctx, in)
}

// GetChannelLiveTotalData mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveTotalData(ctx context.Context, in *GetChannelLiveTotalDataReq) (*GetChannelLiveTotalDataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveTotalData", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveTotalDataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveTotalData indicates an expected call of GetChannelLiveTotalData.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveTotalData(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveTotalData", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveTotalData), ctx, in)
}

// GetChannelLiveWatchTimeRankUser mocks base method.
func (m *MockChannelLiveMgrServer) GetChannelLiveWatchTimeRankUser(ctx context.Context, in *GetChannelLiveWatchTimeRankUserReq) (*GetChannelLiveWatchTimeRankUserResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChannelLiveWatchTimeRankUser", ctx, in)
	ret0, _ := ret[0].(*GetChannelLiveWatchTimeRankUserResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelLiveWatchTimeRankUser indicates an expected call of GetChannelLiveWatchTimeRankUser.
func (mr *MockChannelLiveMgrServerMockRecorder) GetChannelLiveWatchTimeRankUser(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelLiveWatchTimeRankUser", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetChannelLiveWatchTimeRankUser), ctx, in)
}

// GetContestPkConfig mocks base method.
func (m *MockChannelLiveMgrServer) GetContestPkConfig(ctx context.Context, in *GetContestPkConfigReq) (*GetContestPkConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContestPkConfig", ctx, in)
	ret0, _ := ret[0].(*GetContestPkConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContestPkConfig indicates an expected call of GetContestPkConfig.
func (mr *MockChannelLiveMgrServerMockRecorder) GetContestPkConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContestPkConfig", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetContestPkConfig), ctx, in)
}

// GetContestPkInfo mocks base method.
func (m *MockChannelLiveMgrServer) GetContestPkInfo(ctx context.Context, in *GetContestPkInfoReq) (*GetContestPkInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContestPkInfo", ctx, in)
	ret0, _ := ret[0].(*GetContestPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContestPkInfo indicates an expected call of GetContestPkInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) GetContestPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContestPkInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetContestPkInfo), ctx, in)
}

// GetContestPkResult mocks base method.
func (m *MockChannelLiveMgrServer) GetContestPkResult(ctx context.Context, in *GetContestPkResultReq) (*GetContestPkResultResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetContestPkResult", ctx, in)
	ret0, _ := ret[0].(*GetContestPkResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetContestPkResult indicates an expected call of GetContestPkResult.
func (mr *MockChannelLiveMgrServerMockRecorder) GetContestPkResult(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetContestPkResult", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetContestPkResult), ctx, in)
}

// GetHeartBeatTimeOut mocks base method.
func (m *MockChannelLiveMgrServer) GetHeartBeatTimeOut(ctx context.Context, in *GetHeartBeatTimeOutReq) (*GetHeartBeatTimeOutResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeartBeatTimeOut", ctx, in)
	ret0, _ := ret[0].(*GetHeartBeatTimeOutResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeartBeatTimeOut indicates an expected call of GetHeartBeatTimeOut.
func (mr *MockChannelLiveMgrServerMockRecorder) GetHeartBeatTimeOut(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeartBeatTimeOut", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetHeartBeatTimeOut), ctx, in)
}

// GetItemConfig mocks base method.
func (m *MockChannelLiveMgrServer) GetItemConfig(ctx context.Context, in *GetItemConfigReq) (*GetItemConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItemConfig", ctx, in)
	ret0, _ := ret[0].(*GetItemConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemConfig indicates an expected call of GetItemConfig.
func (mr *MockChannelLiveMgrServerMockRecorder) GetItemConfig(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemConfig", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetItemConfig), ctx, in)
}

// GetMyToolList mocks base method.
func (m *MockChannelLiveMgrServer) GetMyToolList(ctx context.Context, in *GetMyToolListReq) (*GetMyToolListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMyToolList", ctx, in)
	ret0, _ := ret[0].(*GetMyToolListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMyToolList indicates an expected call of GetMyToolList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetMyToolList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMyToolList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetMyToolList), ctx, in)
}

// GetPKMatchInfo mocks base method.
func (m *MockChannelLiveMgrServer) GetPKMatchInfo(ctx context.Context, in *GetPKMatchInfoReq) (*GetPKMatchInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPKMatchInfo", ctx, in)
	ret0, _ := ret[0].(*GetPKMatchInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPKMatchInfo indicates an expected call of GetPKMatchInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) GetPKMatchInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPKMatchInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetPKMatchInfo), ctx, in)
}

// GetPkInfo mocks base method.
func (m *MockChannelLiveMgrServer) GetPkInfo(ctx context.Context, in *GetPkInfoReq) (*GetPkInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPkInfo", ctx, in)
	ret0, _ := ret[0].(*GetPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPkInfo indicates an expected call of GetPkInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) GetPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPkInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetPkInfo), ctx, in)
}

// GetUserPushCnt mocks base method.
func (m *MockChannelLiveMgrServer) GetUserPushCnt(ctx context.Context, in *GetUserPushCntReq) (*GetUserPushCntResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPushCnt", ctx, in)
	ret0, _ := ret[0].(*GetUserPushCntResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPushCnt indicates an expected call of GetUserPushCnt.
func (mr *MockChannelLiveMgrServerMockRecorder) GetUserPushCnt(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPushCnt", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetUserPushCnt), ctx, in)
}

// GetVirtualAnchorPerList mocks base method.
func (m *MockChannelLiveMgrServer) GetVirtualAnchorPerList(ctx context.Context, in *GetVirtualAnchorPerListReq) (*GetVirtualAnchorPerListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualAnchorPerList", ctx, in)
	ret0, _ := ret[0].(*GetVirtualAnchorPerListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualAnchorPerList indicates an expected call of GetVirtualAnchorPerList.
func (mr *MockChannelLiveMgrServerMockRecorder) GetVirtualAnchorPerList(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualAnchorPerList", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetVirtualAnchorPerList), ctx, in)
}

// GetVirtualLiveChannelSecret mocks base method.
func (m *MockChannelLiveMgrServer) GetVirtualLiveChannelSecret(ctx context.Context, in *GetVirtualLiveChannelSecretReq) (*GetVirtualLiveChannelSecretResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualLiveChannelSecret", ctx, in)
	ret0, _ := ret[0].(*GetVirtualLiveChannelSecretResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualLiveChannelSecret indicates an expected call of GetVirtualLiveChannelSecret.
func (mr *MockChannelLiveMgrServerMockRecorder) GetVirtualLiveChannelSecret(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualLiveChannelSecret", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetVirtualLiveChannelSecret), ctx, in)
}

// GetVirtualLiveInfoBySecret mocks base method.
func (m *MockChannelLiveMgrServer) GetVirtualLiveInfoBySecret(ctx context.Context, in *GetVirtualLiveInfoBySecretReq) (*GetVirtualLiveInfoBySecretResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetVirtualLiveInfoBySecret", ctx, in)
	ret0, _ := ret[0].(*GetVirtualLiveInfoBySecretResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVirtualLiveInfoBySecret indicates an expected call of GetVirtualLiveInfoBySecret.
func (mr *MockChannelLiveMgrServerMockRecorder) GetVirtualLiveInfoBySecret(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVirtualLiveInfoBySecret", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).GetVirtualLiveInfoBySecret), ctx, in)
}

// HandlerApply mocks base method.
func (m *MockChannelLiveMgrServer) HandlerApply(ctx context.Context, in *HandlerApplyReq) (*HandlerApplyResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandlerApply", ctx, in)
	ret0, _ := ret[0].(*HandlerApplyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandlerApply indicates an expected call of HandlerApply.
func (mr *MockChannelLiveMgrServerMockRecorder) HandlerApply(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandlerApply", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).HandlerApply), ctx, in)
}

// PushTest mocks base method.
func (m *MockChannelLiveMgrServer) PushTest(ctx context.Context, in *PushTestReq) (*PushTestResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushTest", ctx, in)
	ret0, _ := ret[0].(*PushTestResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PushTest indicates an expected call of PushTest.
func (mr *MockChannelLiveMgrServerMockRecorder) PushTest(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushTest", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).PushTest), ctx, in)
}

// ReportClientIDChange mocks base method.
func (m *MockChannelLiveMgrServer) ReportClientIDChange(ctx context.Context, in *ReportClientIDChangeReq) (*ReportClientIDChangeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportClientIDChange", ctx, in)
	ret0, _ := ret[0].(*ReportClientIDChangeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportClientIDChange indicates an expected call of ReportClientIDChange.
func (mr *MockChannelLiveMgrServerMockRecorder) ReportClientIDChange(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportClientIDChange", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).ReportClientIDChange), ctx, in)
}

// SearchAnchor mocks base method.
func (m *MockChannelLiveMgrServer) SearchAnchor(ctx context.Context, in *SearchAnchorReq) (*SearchAnchorResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAnchor", ctx, in)
	ret0, _ := ret[0].(*SearchAnchorResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAnchor indicates an expected call of SearchAnchor.
func (mr *MockChannelLiveMgrServerMockRecorder) SearchAnchor(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAnchor", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SearchAnchor), ctx, in)
}

// SetAuthFlag mocks base method.
func (m *MockChannelLiveMgrServer) SetAuthFlag(ctx context.Context, in *SetAuthFlagReq) (*SetAuthFlagResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAuthFlag", ctx, in)
	ret0, _ := ret[0].(*SetAuthFlagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetAuthFlag indicates an expected call of SetAuthFlag.
func (mr *MockChannelLiveMgrServerMockRecorder) SetAuthFlag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAuthFlag", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetAuthFlag), ctx, in)
}

// SetChannelLiveInfo mocks base method.
func (m *MockChannelLiveMgrServer) SetChannelLiveInfo(ctx context.Context, in *SetChannelLiveInfoReq) (*SetChannelLiveInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveInfo", ctx, in)
	ret0, _ := ret[0].(*SetChannelLiveInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveInfo indicates an expected call of SetChannelLiveInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) SetChannelLiveInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetChannelLiveInfo), ctx, in)
}

// SetChannelLiveInfoForTest mocks base method.
func (m *MockChannelLiveMgrServer) SetChannelLiveInfoForTest(ctx context.Context, in *SetChannelLiveInfoForTestReq) (*SetChannelLiveInfoForTestResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveInfoForTest", ctx, in)
	ret0, _ := ret[0].(*SetChannelLiveInfoForTestResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveInfoForTest indicates an expected call of SetChannelLiveInfoForTest.
func (mr *MockChannelLiveMgrServerMockRecorder) SetChannelLiveInfoForTest(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveInfoForTest", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetChannelLiveInfoForTest), ctx, in)
}

// SetChannelLiveOpponentMicFlag mocks base method.
func (m *MockChannelLiveMgrServer) SetChannelLiveOpponentMicFlag(ctx context.Context, in *SetChannelLiveOpponentMicFlagReq) (*SetChannelLiveOpponentMicFlagResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveOpponentMicFlag", ctx, in)
	ret0, _ := ret[0].(*SetChannelLiveOpponentMicFlagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveOpponentMicFlag indicates an expected call of SetChannelLiveOpponentMicFlag.
func (mr *MockChannelLiveMgrServerMockRecorder) SetChannelLiveOpponentMicFlag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveOpponentMicFlag", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetChannelLiveOpponentMicFlag), ctx, in)
}

// SetChannelLiveStatus mocks base method.
func (m *MockChannelLiveMgrServer) SetChannelLiveStatus(ctx context.Context, in *SetChannelLiveStatusReq) (*SetChannelLiveStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveStatus", ctx, in)
	ret0, _ := ret[0].(*SetChannelLiveStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveStatus indicates an expected call of SetChannelLiveStatus.
func (mr *MockChannelLiveMgrServerMockRecorder) SetChannelLiveStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveStatus", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetChannelLiveStatus), ctx, in)
}

// SetChannelLiveTag mocks base method.
func (m *MockChannelLiveMgrServer) SetChannelLiveTag(ctx context.Context, in *SetChannelLiveTagReq) (*SetChannelLiveTagResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveTag", ctx, in)
	ret0, _ := ret[0].(*SetChannelLiveTagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveTag indicates an expected call of SetChannelLiveTag.
func (mr *MockChannelLiveMgrServerMockRecorder) SetChannelLiveTag(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveTag", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetChannelLiveTag), ctx, in)
}

// SetChannelLiveTagMgr mocks base method.
func (m *MockChannelLiveMgrServer) SetChannelLiveTagMgr(ctx context.Context, in *apicentergo.SetChannelLiveTagReq) (*apicentergo.SetChannelLiveTagResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChannelLiveTagMgr", ctx, in)
	ret0, _ := ret[0].(*apicentergo.SetChannelLiveTagResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetChannelLiveTagMgr indicates an expected call of SetChannelLiveTagMgr.
func (mr *MockChannelLiveMgrServerMockRecorder) SetChannelLiveTagMgr(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChannelLiveTagMgr", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetChannelLiveTagMgr), ctx, in)
}

// SetPkStatus mocks base method.
func (m *MockChannelLiveMgrServer) SetPkStatus(ctx context.Context, in *SetPkStatusReq) (*SetPkStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPkStatus", ctx, in)
	ret0, _ := ret[0].(*SetPkStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetPkStatus indicates an expected call of SetPkStatus.
func (mr *MockChannelLiveMgrServerMockRecorder) SetPkStatus(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPkStatus", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).SetPkStatus), ctx, in)
}

// StartContestPk mocks base method.
func (m *MockChannelLiveMgrServer) StartContestPk(ctx context.Context, in *StartContestPkReq) (*StartContestPkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartContestPk", ctx, in)
	ret0, _ := ret[0].(*StartContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartContestPk indicates an expected call of StartContestPk.
func (mr *MockChannelLiveMgrServerMockRecorder) StartContestPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartContestPk", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).StartContestPk), ctx, in)
}

// StartPkMatch mocks base method.
func (m *MockChannelLiveMgrServer) StartPkMatch(ctx context.Context, in *StartPkMatchReq) (*StartPkMatchResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartPkMatch", ctx, in)
	ret0, _ := ret[0].(*StartPkMatchResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartPkMatch indicates an expected call of StartPkMatch.
func (mr *MockChannelLiveMgrServerMockRecorder) StartPkMatch(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartPkMatch", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).StartPkMatch), ctx, in)
}

// UpdateAppointPkInfo mocks base method.
func (m *MockChannelLiveMgrServer) UpdateAppointPkInfo(ctx context.Context, in *UpdateAppointPkInfoReq) (*UpdateAppointPkInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAppointPkInfo", ctx, in)
	ret0, _ := ret[0].(*UpdateAppointPkInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateAppointPkInfo indicates an expected call of UpdateAppointPkInfo.
func (mr *MockChannelLiveMgrServerMockRecorder) UpdateAppointPkInfo(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAppointPkInfo", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).UpdateAppointPkInfo), ctx, in)
}

// UpdateContestPk mocks base method.
func (m *MockChannelLiveMgrServer) UpdateContestPk(ctx context.Context, in *UpdateContestPkReq) (*UpdateContestPkResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateContestPk", ctx, in)
	ret0, _ := ret[0].(*UpdateContestPkResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateContestPk indicates an expected call of UpdateContestPk.
func (mr *MockChannelLiveMgrServerMockRecorder) UpdateContestPk(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateContestPk", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).UpdateContestPk), ctx, in)
}

// UpdateContestPkResult mocks base method.
func (m *MockChannelLiveMgrServer) UpdateContestPkResult(ctx context.Context, in *UpdateContestPkResultReq) (*UpdateContestPkResultResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateContestPkResult", ctx, in)
	ret0, _ := ret[0].(*UpdateContestPkResultResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateContestPkResult indicates an expected call of UpdateContestPkResult.
func (mr *MockChannelLiveMgrServerMockRecorder) UpdateContestPkResult(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateContestPkResult", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).UpdateContestPkResult), ctx, in)
}

// UpdateVirtualAnchorPer mocks base method.
func (m *MockChannelLiveMgrServer) UpdateVirtualAnchorPer(ctx context.Context, in *UpdateVirtualAnchorPerReq) (*UpdateVirtualAnchorPerResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVirtualAnchorPer", ctx, in)
	ret0, _ := ret[0].(*UpdateVirtualAnchorPerResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateVirtualAnchorPer indicates an expected call of UpdateVirtualAnchorPer.
func (mr *MockChannelLiveMgrServerMockRecorder) UpdateVirtualAnchorPer(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVirtualAnchorPer", reflect.TypeOf((*MockChannelLiveMgrServer)(nil).UpdateVirtualAnchorPer), ctx, in)
}
