// Code generated by protoc-gen-go. DO NOT EDIT.
// source: examine-helloworld/helloworld.proto

package examine_helloworld // import "golang.52tt.com/protocol/services/examine-helloworld"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type HelloReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HelloReq) Reset()         { *m = HelloReq{} }
func (m *HelloReq) String() string { return proto.CompactTextString(m) }
func (*HelloReq) ProtoMessage()    {}
func (*HelloReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_helloworld_c2a06d3959120ff5, []int{0}
}
func (m *HelloReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HelloReq.Unmarshal(m, b)
}
func (m *HelloReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HelloReq.Marshal(b, m, deterministic)
}
func (dst *HelloReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HelloReq.Merge(dst, src)
}
func (m *HelloReq) XXX_Size() int {
	return xxx_messageInfo_HelloReq.Size(m)
}
func (m *HelloReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HelloReq.DiscardUnknown(m)
}

var xxx_messageInfo_HelloReq proto.InternalMessageInfo

func (m *HelloReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type HelloResp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HelloResp) Reset()         { *m = HelloResp{} }
func (m *HelloResp) String() string { return proto.CompactTextString(m) }
func (*HelloResp) ProtoMessage()    {}
func (*HelloResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_helloworld_c2a06d3959120ff5, []int{1}
}
func (m *HelloResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HelloResp.Unmarshal(m, b)
}
func (m *HelloResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HelloResp.Marshal(b, m, deterministic)
}
func (dst *HelloResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HelloResp.Merge(dst, src)
}
func (m *HelloResp) XXX_Size() int {
	return xxx_messageInfo_HelloResp.Size(m)
}
func (m *HelloResp) XXX_DiscardUnknown() {
	xxx_messageInfo_HelloResp.DiscardUnknown(m)
}

var xxx_messageInfo_HelloResp proto.InternalMessageInfo

func (m *HelloResp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func init() {
	proto.RegisterType((*HelloReq)(nil), "hellworld.v1.HelloReq")
	proto.RegisterType((*HelloResp)(nil), "hellworld.v1.HelloResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GreeterClient is the client API for Greeter service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GreeterClient interface {
	SayT2840(ctx context.Context, in *HelloReq, opts ...grpc.CallOption) (*HelloResp, error)
}

type greeterClient struct {
	cc *grpc.ClientConn
}

func NewGreeterClient(cc *grpc.ClientConn) GreeterClient {
	return &greeterClient{cc}
}

func (c *greeterClient) SayT2840(ctx context.Context, in *HelloReq, opts ...grpc.CallOption) (*HelloResp, error) {
	out := new(HelloResp)
	err := c.cc.Invoke(ctx, "/hellworld.v1.Greeter/SayT2840", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GreeterServer is the server API for Greeter service.
type GreeterServer interface {
	SayT2840(context.Context, *HelloReq) (*HelloResp, error)
}

func RegisterGreeterServer(s *grpc.Server, srv GreeterServer) {
	s.RegisterService(&_Greeter_serviceDesc, srv)
}

func _Greeter_SayT2840_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HelloReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreeterServer).SayT2840(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/hellworld.v1.Greeter/SayT2840",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreeterServer).SayT2840(ctx, req.(*HelloReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Greeter_serviceDesc = grpc.ServiceDesc{
	ServiceName: "hellworld.v1.Greeter",
	HandlerType: (*GreeterServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SayT2840",
			Handler:    _Greeter_SayT2840_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "examine-helloworld/helloworld.proto",
}

func init() {
	proto.RegisterFile("examine-helloworld/helloworld.proto", fileDescriptor_helloworld_c2a06d3959120ff5)
}

var fileDescriptor_helloworld_c2a06d3959120ff5 = []byte{
	// 186 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x52, 0x4e, 0xad, 0x48, 0xcc,
	0xcd, 0xcc, 0x4b, 0xd5, 0xcd, 0x48, 0xcd, 0xc9, 0xc9, 0x2f, 0xcf, 0x2f, 0xca, 0x49, 0xd1, 0x47,
	0x30, 0xf5, 0x0a, 0x8a, 0xf2, 0x4b, 0xf2, 0x85, 0x78, 0x40, 0x22, 0x10, 0x81, 0x32, 0x43, 0x25,
	0x39, 0x2e, 0x0e, 0x0f, 0x90, 0x8a, 0xa0, 0xd4, 0x42, 0x21, 0x21, 0x2e, 0x96, 0xbc, 0xc4, 0xdc,
	0x54, 0x09, 0x46, 0x05, 0x46, 0x0d, 0xce, 0x20, 0x30, 0x5b, 0x49, 0x96, 0x8b, 0x13, 0x2a, 0x5f,
	0x5c, 0x20, 0x24, 0xc0, 0xc5, 0x9c, 0x5b, 0x9c, 0x0e, 0x95, 0x07, 0x31, 0x8d, 0xdc, 0xb8, 0xd8,
	0xdd, 0x8b, 0x52, 0x53, 0x4b, 0x52, 0x8b, 0x84, 0xac, 0xb9, 0x38, 0x82, 0x13, 0x2b, 0x43, 0x8c,
	0x2c, 0x4c, 0x0c, 0x84, 0xc4, 0xf4, 0x90, 0x2d, 0xd1, 0x83, 0xd9, 0x20, 0x25, 0x8e, 0x55, 0xbc,
	0xb8, 0xc0, 0xc9, 0x2c, 0xca, 0x24, 0x3d, 0x3f, 0x27, 0x31, 0x2f, 0x5d, 0xcf, 0xd4, 0xa8, 0xa4,
	0x44, 0x2f, 0x39, 0x3f, 0x57, 0x1f, 0xec, 0xda, 0xe4, 0xfc, 0x1c, 0xfd, 0xe2, 0xd4, 0xa2, 0xb2,
	0xcc, 0xe4, 0xd4, 0x62, 0x7d, 0x4c, 0xdf, 0x25, 0xb1, 0x81, 0x55, 0x19, 0x03, 0x02, 0x00, 0x00,
	0xff, 0xff, 0x2f, 0x40, 0xcf, 0xbd, 0xfa, 0x00, 0x00, 0x00,
}
