// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/slip-note-logic/slip-note-logic.proto

package slipnotelogic // import "golang.52tt.com/protocol/services/logicsvr-go/slipnotelogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import slipnotelogic "golang.52tt.com/protocol/app/slipnotelogic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SlipNoteLogicClient is the client API for SlipNoteLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SlipNoteLogicClient interface {
	GetSlipNoteConfig(ctx context.Context, in *slipnotelogic.GetSlipNoteConfigReq, opts ...grpc.CallOption) (*slipnotelogic.GetSlipNoteConfigResp, error)
	PublishSlipNote(ctx context.Context, in *slipnotelogic.PublishSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.PublishSlipNoteResp, error)
	CloseSlipNote(ctx context.Context, in *slipnotelogic.CloseSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.CloseSlipNoteResp, error)
	GetSlipNoteStatus(ctx context.Context, in *slipnotelogic.GetSlipNoteStatusReq, opts ...grpc.CallOption) (*slipnotelogic.GetSlipNoteStatusResp, error)
	PickSlipNote(ctx context.Context, in *slipnotelogic.PickSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.PickSlipNoteResp, error)
	SetSlipNote(ctx context.Context, in *slipnotelogic.SetSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.SetSlipNoteResp, error)
	FetchSlipNote(ctx context.Context, in *slipnotelogic.FetchSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.FetchSlipNoteResp, error)
	CommentToSlipNote(ctx context.Context, in *slipnotelogic.CommentToSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.CommentToSlipNoteResp, error)
	PullSlipNoteCommentList(ctx context.Context, in *slipnotelogic.PullSlipNoteCommentListReq, opts ...grpc.CallOption) (*slipnotelogic.PullSlipNoteCommentListResp, error)
	ReportSlipNoteStatus(ctx context.Context, in *slipnotelogic.ReportSlipNoteStatusReq, opts ...grpc.CallOption) (*slipnotelogic.ReportSlipNoteStatusResp, error)
	GetAccountsForSlipNote(ctx context.Context, in *slipnotelogic.GetAccountsForSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.GetAccountsForSlipNoteResp, error)
}

type slipNoteLogicClient struct {
	cc *grpc.ClientConn
}

func NewSlipNoteLogicClient(cc *grpc.ClientConn) SlipNoteLogicClient {
	return &slipNoteLogicClient{cc}
}

func (c *slipNoteLogicClient) GetSlipNoteConfig(ctx context.Context, in *slipnotelogic.GetSlipNoteConfigReq, opts ...grpc.CallOption) (*slipnotelogic.GetSlipNoteConfigResp, error) {
	out := new(slipnotelogic.GetSlipNoteConfigResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/GetSlipNoteConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) PublishSlipNote(ctx context.Context, in *slipnotelogic.PublishSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.PublishSlipNoteResp, error) {
	out := new(slipnotelogic.PublishSlipNoteResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/PublishSlipNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) CloseSlipNote(ctx context.Context, in *slipnotelogic.CloseSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.CloseSlipNoteResp, error) {
	out := new(slipnotelogic.CloseSlipNoteResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/CloseSlipNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) GetSlipNoteStatus(ctx context.Context, in *slipnotelogic.GetSlipNoteStatusReq, opts ...grpc.CallOption) (*slipnotelogic.GetSlipNoteStatusResp, error) {
	out := new(slipnotelogic.GetSlipNoteStatusResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/GetSlipNoteStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) PickSlipNote(ctx context.Context, in *slipnotelogic.PickSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.PickSlipNoteResp, error) {
	out := new(slipnotelogic.PickSlipNoteResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/PickSlipNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) SetSlipNote(ctx context.Context, in *slipnotelogic.SetSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.SetSlipNoteResp, error) {
	out := new(slipnotelogic.SetSlipNoteResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/SetSlipNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) FetchSlipNote(ctx context.Context, in *slipnotelogic.FetchSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.FetchSlipNoteResp, error) {
	out := new(slipnotelogic.FetchSlipNoteResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/FetchSlipNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) CommentToSlipNote(ctx context.Context, in *slipnotelogic.CommentToSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.CommentToSlipNoteResp, error) {
	out := new(slipnotelogic.CommentToSlipNoteResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/CommentToSlipNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) PullSlipNoteCommentList(ctx context.Context, in *slipnotelogic.PullSlipNoteCommentListReq, opts ...grpc.CallOption) (*slipnotelogic.PullSlipNoteCommentListResp, error) {
	out := new(slipnotelogic.PullSlipNoteCommentListResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/PullSlipNoteCommentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) ReportSlipNoteStatus(ctx context.Context, in *slipnotelogic.ReportSlipNoteStatusReq, opts ...grpc.CallOption) (*slipnotelogic.ReportSlipNoteStatusResp, error) {
	out := new(slipnotelogic.ReportSlipNoteStatusResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/ReportSlipNoteStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *slipNoteLogicClient) GetAccountsForSlipNote(ctx context.Context, in *slipnotelogic.GetAccountsForSlipNoteReq, opts ...grpc.CallOption) (*slipnotelogic.GetAccountsForSlipNoteResp, error) {
	out := new(slipnotelogic.GetAccountsForSlipNoteResp)
	err := c.cc.Invoke(ctx, "/logic.SlipNoteLogic/GetAccountsForSlipNote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SlipNoteLogicServer is the server API for SlipNoteLogic service.
type SlipNoteLogicServer interface {
	GetSlipNoteConfig(context.Context, *slipnotelogic.GetSlipNoteConfigReq) (*slipnotelogic.GetSlipNoteConfigResp, error)
	PublishSlipNote(context.Context, *slipnotelogic.PublishSlipNoteReq) (*slipnotelogic.PublishSlipNoteResp, error)
	CloseSlipNote(context.Context, *slipnotelogic.CloseSlipNoteReq) (*slipnotelogic.CloseSlipNoteResp, error)
	GetSlipNoteStatus(context.Context, *slipnotelogic.GetSlipNoteStatusReq) (*slipnotelogic.GetSlipNoteStatusResp, error)
	PickSlipNote(context.Context, *slipnotelogic.PickSlipNoteReq) (*slipnotelogic.PickSlipNoteResp, error)
	SetSlipNote(context.Context, *slipnotelogic.SetSlipNoteReq) (*slipnotelogic.SetSlipNoteResp, error)
	FetchSlipNote(context.Context, *slipnotelogic.FetchSlipNoteReq) (*slipnotelogic.FetchSlipNoteResp, error)
	CommentToSlipNote(context.Context, *slipnotelogic.CommentToSlipNoteReq) (*slipnotelogic.CommentToSlipNoteResp, error)
	PullSlipNoteCommentList(context.Context, *slipnotelogic.PullSlipNoteCommentListReq) (*slipnotelogic.PullSlipNoteCommentListResp, error)
	ReportSlipNoteStatus(context.Context, *slipnotelogic.ReportSlipNoteStatusReq) (*slipnotelogic.ReportSlipNoteStatusResp, error)
	GetAccountsForSlipNote(context.Context, *slipnotelogic.GetAccountsForSlipNoteReq) (*slipnotelogic.GetAccountsForSlipNoteResp, error)
}

func RegisterSlipNoteLogicServer(s *grpc.Server, srv SlipNoteLogicServer) {
	s.RegisterService(&_SlipNoteLogic_serviceDesc, srv)
}

func _SlipNoteLogic_GetSlipNoteConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.GetSlipNoteConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).GetSlipNoteConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/GetSlipNoteConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).GetSlipNoteConfig(ctx, req.(*slipnotelogic.GetSlipNoteConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_PublishSlipNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.PublishSlipNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).PublishSlipNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/PublishSlipNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).PublishSlipNote(ctx, req.(*slipnotelogic.PublishSlipNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_CloseSlipNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.CloseSlipNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).CloseSlipNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/CloseSlipNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).CloseSlipNote(ctx, req.(*slipnotelogic.CloseSlipNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_GetSlipNoteStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.GetSlipNoteStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).GetSlipNoteStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/GetSlipNoteStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).GetSlipNoteStatus(ctx, req.(*slipnotelogic.GetSlipNoteStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_PickSlipNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.PickSlipNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).PickSlipNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/PickSlipNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).PickSlipNote(ctx, req.(*slipnotelogic.PickSlipNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_SetSlipNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.SetSlipNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).SetSlipNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/SetSlipNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).SetSlipNote(ctx, req.(*slipnotelogic.SetSlipNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_FetchSlipNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.FetchSlipNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).FetchSlipNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/FetchSlipNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).FetchSlipNote(ctx, req.(*slipnotelogic.FetchSlipNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_CommentToSlipNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.CommentToSlipNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).CommentToSlipNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/CommentToSlipNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).CommentToSlipNote(ctx, req.(*slipnotelogic.CommentToSlipNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_PullSlipNoteCommentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.PullSlipNoteCommentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).PullSlipNoteCommentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/PullSlipNoteCommentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).PullSlipNoteCommentList(ctx, req.(*slipnotelogic.PullSlipNoteCommentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_ReportSlipNoteStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.ReportSlipNoteStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).ReportSlipNoteStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/ReportSlipNoteStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).ReportSlipNoteStatus(ctx, req.(*slipnotelogic.ReportSlipNoteStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SlipNoteLogic_GetAccountsForSlipNote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(slipnotelogic.GetAccountsForSlipNoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SlipNoteLogicServer).GetAccountsForSlipNote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.SlipNoteLogic/GetAccountsForSlipNote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SlipNoteLogicServer).GetAccountsForSlipNote(ctx, req.(*slipnotelogic.GetAccountsForSlipNoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SlipNoteLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.SlipNoteLogic",
	HandlerType: (*SlipNoteLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSlipNoteConfig",
			Handler:    _SlipNoteLogic_GetSlipNoteConfig_Handler,
		},
		{
			MethodName: "PublishSlipNote",
			Handler:    _SlipNoteLogic_PublishSlipNote_Handler,
		},
		{
			MethodName: "CloseSlipNote",
			Handler:    _SlipNoteLogic_CloseSlipNote_Handler,
		},
		{
			MethodName: "GetSlipNoteStatus",
			Handler:    _SlipNoteLogic_GetSlipNoteStatus_Handler,
		},
		{
			MethodName: "PickSlipNote",
			Handler:    _SlipNoteLogic_PickSlipNote_Handler,
		},
		{
			MethodName: "SetSlipNote",
			Handler:    _SlipNoteLogic_SetSlipNote_Handler,
		},
		{
			MethodName: "FetchSlipNote",
			Handler:    _SlipNoteLogic_FetchSlipNote_Handler,
		},
		{
			MethodName: "CommentToSlipNote",
			Handler:    _SlipNoteLogic_CommentToSlipNote_Handler,
		},
		{
			MethodName: "PullSlipNoteCommentList",
			Handler:    _SlipNoteLogic_PullSlipNoteCommentList_Handler,
		},
		{
			MethodName: "ReportSlipNoteStatus",
			Handler:    _SlipNoteLogic_ReportSlipNoteStatus_Handler,
		},
		{
			MethodName: "GetAccountsForSlipNote",
			Handler:    _SlipNoteLogic_GetAccountsForSlipNote_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/slip-note-logic/slip-note-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/slip-note-logic/slip-note-logic.proto", fileDescriptor_slip_note_logic_1314bf28a1aaa605)
}

var fileDescriptor_slip_note_logic_1314bf28a1aaa605 = []byte{
	// 476 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0x4d, 0x6b, 0x14, 0x31,
	0x18, 0xc7, 0x09, 0xa8, 0xd8, 0x68, 0x29, 0x0d, 0x52, 0x61, 0x4e, 0x76, 0x7d, 0xa3, 0xda, 0x9d,
	0xc1, 0x8a, 0x27, 0xf1, 0xa0, 0x0b, 0xed, 0xa5, 0xc8, 0xd2, 0xf5, 0x24, 0x88, 0x4e, 0x43, 0x9c,
	0x06, 0xb3, 0xf3, 0xc4, 0x49, 0xb6, 0xea, 0x45, 0xf0, 0x63, 0xf5, 0x53, 0xf4, 0x9b, 0xe4, 0x20,
	0xf8, 0x56, 0x5f, 0xc9, 0x0c, 0x33, 0xd9, 0x66, 0xb2, 0xce, 0xf4, 0xb4, 0x4c, 0xf2, 0xcb, 0xff,
	0x97, 0x7d, 0x9e, 0x87, 0xe0, 0x7b, 0x02, 0x32, 0x4e, 0xd5, 0x61, 0x31, 0xcc, 0x20, 0x51, 0x82,
	0xcb, 0x61, 0x0e, 0x9a, 0x0d, 0xcb, 0x55, 0xff, 0x3b, 0x96, 0x05, 0x68, 0x20, 0xe7, 0xcb, 0x8f,
	0x68, 0x63, 0xfe, 0x64, 0x96, 0x6a, 0xf6, 0x2e, 0xfd, 0x90, 0x80, 0xd4, 0x1c, 0x72, 0x55, 0xff,
	0x56, 0x27, 0xa2, 0x28, 0x95, 0xd2, 0x0f, 0x7b, 0x59, 0xed, 0x6d, 0x99, 0x25, 0xbc, 0x3c, 0x11,
	0x5c, 0x3e, 0x05, 0xcd, 0x76, 0xed, 0x06, 0x01, 0xbc, 0xba, 0xc3, 0x74, 0xbd, 0x36, 0x82, 0xfc,
	0x35, 0xcf, 0xc8, 0xad, 0x38, 0x4b, 0x63, 0x1b, 0x61, 0x13, 0xaa, 0xdb, 0xb4, 0xa0, 0x3d, 0xf6,
	0x36, 0xba, 0xdd, 0x8b, 0x53, 0x72, 0xb0, 0xf4, 0xf9, 0xf8, 0x28, 0x3e, 0x77, 0xf1, 0x8b, 0x41,
	0x84, 0xe3, 0x95, 0xf1, 0x6c, 0x5f, 0x70, 0x75, 0x50, 0x73, 0xe4, 0x46, 0x3b, 0xc6, 0x43, 0xac,
	0xec, 0x66, 0x0f, 0xca, 0xa9, 0xbe, 0x1a, 0x44, 0x28, 0x5e, 0x1e, 0x09, 0x50, 0xac, 0x11, 0x0d,
	0xda, 0x11, 0xa7, 0x00, 0xab, 0xb9, 0xde, 0xc9, 0x38, 0xc9, 0x37, 0x83, 0xbc, 0x02, 0x4e, 0x74,
	0xaa, 0x67, 0xaa, 0xa3, 0x80, 0x15, 0xd4, 0x5d, 0xc0, 0x9a, 0x73, 0xc2, 0xef, 0x06, 0x91, 0x57,
	0xf8, 0xf2, 0x98, 0xd3, 0x37, 0xcd, 0x9f, 0x5a, 0x0f, 0xd4, 0x65, 0x6e, 0xdf, 0x6a, 0x06, 0x5d,
	0x88, 0x33, 0xfc, 0x30, 0x88, 0xbc, 0xc0, 0x97, 0x26, 0xee, 0x16, 0xe4, 0x5a, 0xfb, 0xf4, 0xdc,
	0xb6, 0xcd, 0x5f, 0xef, 0x20, 0x5c, 0xfc, 0x49, 0xd5, 0x96, 0x6d, 0xa6, 0xe9, 0xc1, 0xff, 0xda,
	0x72, 0x0a, 0x58, 0xd0, 0x16, 0x8f, 0x71, 0x92, 0x9f, 0x55, 0x5b, 0x46, 0x30, 0x9d, 0xb2, 0x5c,
	0x3f, 0x83, 0x46, 0x14, 0x68, 0x4b, 0x0b, 0x5a, 0xd0, 0x96, 0x00, 0xe7, 0x84, 0xbf, 0x0c, 0x22,
	0x9f, 0x10, 0xbe, 0x3a, 0x9e, 0x09, 0xe1, 0xa6, 0xbf, 0x3c, 0xb0, 0xcb, 0x95, 0x26, 0x9b, 0xa1,
	0xd1, 0x0d, 0xa2, 0xd6, 0x3e, 0x3c, 0x03, 0xed, 0xee, 0xf0, 0xdb, 0x20, 0xf2, 0x1e, 0x5f, 0xd9,
	0x63, 0x12, 0x0a, 0x7f, 0x1c, 0x37, 0xda, 0x89, 0x21, 0xce, 0xca, 0xef, 0xf4, 0x45, 0x9d, 0xf9,
	0x8f, 0x41, 0xe4, 0x23, 0x5e, 0xdb, 0x61, 0xfa, 0x31, 0xa5, 0x30, 0xcb, 0xb5, 0xda, 0x86, 0xa2,
	0xa9, 0xf9, 0xdd, 0xe0, 0x88, 0x07, 0x48, 0x6b, 0xdf, 0xec, 0x0f, 0x3b, 0xff, 0x5f, 0x83, 0xa2,
	0xb5, 0x93, 0xe3, 0xa3, 0x78, 0x15, 0xaf, 0x78, 0xef, 0xde, 0x93, 0x47, 0xcf, 0x1f, 0x66, 0x20,
	0xd2, 0x3c, 0x8b, 0x1f, 0x6c, 0x69, 0x1d, 0x53, 0x98, 0x26, 0xe5, 0x4b, 0x48, 0x41, 0x24, 0x8a,
	0x15, 0x87, 0x9c, 0x32, 0x95, 0xf8, 0xaf, 0x72, 0x23, 0xdf, 0xbf, 0x50, 0xc2, 0xf7, 0xff, 0x05,
	0x00, 0x00, 0xff, 0xff, 0x17, 0x58, 0xd9, 0xee, 0xb9, 0x05, 0x00, 0x00,
}
