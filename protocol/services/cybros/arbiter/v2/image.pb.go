// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/cybros/arbiter/v2/image.proto

package v2 // import "golang.52tt.com/protocol/services/cybros/arbiter/v2"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ImageData struct {
	Metadata *Metadata `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Types that are valid to be assigned to ImageData:
	//	*ImageData_Url
	//	*ImageData_Content
	ImageData            isImageData_ImageData `protobuf_oneof:"image_data"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ImageData) Reset()         { *m = ImageData{} }
func (m *ImageData) String() string { return proto.CompactTextString(m) }
func (*ImageData) ProtoMessage()    {}
func (*ImageData) Descriptor() ([]byte, []int) {
	return fileDescriptor_image_04c3f85576d47d97, []int{0}
}
func (m *ImageData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImageData.Unmarshal(m, b)
}
func (m *ImageData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImageData.Marshal(b, m, deterministic)
}
func (dst *ImageData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImageData.Merge(dst, src)
}
func (m *ImageData) XXX_Size() int {
	return xxx_messageInfo_ImageData.Size(m)
}
func (m *ImageData) XXX_DiscardUnknown() {
	xxx_messageInfo_ImageData.DiscardUnknown(m)
}

var xxx_messageInfo_ImageData proto.InternalMessageInfo

func (m *ImageData) GetMetadata() *Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type isImageData_ImageData interface {
	isImageData_ImageData()
}

type ImageData_Url struct {
	Url string `protobuf:"bytes,3,opt,name=url,proto3,oneof"`
}

type ImageData_Content struct {
	Content []byte `protobuf:"bytes,4,opt,name=content,proto3,oneof"`
}

func (*ImageData_Url) isImageData_ImageData() {}

func (*ImageData_Content) isImageData_ImageData() {}

func (m *ImageData) GetImageData() isImageData_ImageData {
	if m != nil {
		return m.ImageData
	}
	return nil
}

func (m *ImageData) GetUrl() string {
	if x, ok := m.GetImageData().(*ImageData_Url); ok {
		return x.Url
	}
	return ""
}

func (m *ImageData) GetContent() []byte {
	if x, ok := m.GetImageData().(*ImageData_Content); ok {
		return x.Content
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ImageData) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ImageData_OneofMarshaler, _ImageData_OneofUnmarshaler, _ImageData_OneofSizer, []interface{}{
		(*ImageData_Url)(nil),
		(*ImageData_Content)(nil),
	}
}

func _ImageData_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ImageData)
	// image_data
	switch x := m.ImageData.(type) {
	case *ImageData_Url:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.Url)
	case *ImageData_Content:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		b.EncodeRawBytes(x.Content)
	case nil:
	default:
		return fmt.Errorf("ImageData.ImageData has unexpected type %T", x)
	}
	return nil
}

func _ImageData_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ImageData)
	switch tag {
	case 3: // image_data.url
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.ImageData = &ImageData_Url{x}
		return true, err
	case 4: // image_data.content
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeRawBytes(true)
		m.ImageData = &ImageData_Content{x}
		return true, err
	default:
		return false, nil
	}
}

func _ImageData_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ImageData)
	// image_data
	switch x := m.ImageData.(type) {
	case *ImageData_Url:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.Url)))
		n += len(x.Url)
	case *ImageData_Content:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.Content)))
		n += len(x.Content)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type ScanImageReq struct {
	Context    *TaskContext `protobuf:"bytes,1,opt,name=context,proto3" json:"context,omitempty"`
	ImageDatas []*ImageData `protobuf:"bytes,2,rep,name=image_datas,json=imageDatas,proto3" json:"image_datas,omitempty"`
	Callback   *Callback    `protobuf:"bytes,3,opt,name=callback,proto3" json:"callback,omitempty"`
	TextData   *TextData    `protobuf:"bytes,4,opt,name=text_data,json=textData,proto3" json:"text_data,omitempty"`
	// 私聊图片接收者Id
	ReceiveTokenId string `protobuf:"bytes,5,opt,name=receive_token_id,json=receiveTokenId,proto3" json:"receive_token_id,omitempty"`
	// 多张图片聚合为一个工单送审
	Aggregation          bool     `protobuf:"varint,6,opt,name=aggregation,proto3" json:"aggregation,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanImageReq) Reset()         { *m = ScanImageReq{} }
func (m *ScanImageReq) String() string { return proto.CompactTextString(m) }
func (*ScanImageReq) ProtoMessage()    {}
func (*ScanImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_image_04c3f85576d47d97, []int{1}
}
func (m *ScanImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanImageReq.Unmarshal(m, b)
}
func (m *ScanImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanImageReq.Marshal(b, m, deterministic)
}
func (dst *ScanImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanImageReq.Merge(dst, src)
}
func (m *ScanImageReq) XXX_Size() int {
	return xxx_messageInfo_ScanImageReq.Size(m)
}
func (m *ScanImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_ScanImageReq proto.InternalMessageInfo

func (m *ScanImageReq) GetContext() *TaskContext {
	if m != nil {
		return m.Context
	}
	return nil
}

func (m *ScanImageReq) GetImageDatas() []*ImageData {
	if m != nil {
		return m.ImageDatas
	}
	return nil
}

func (m *ScanImageReq) GetCallback() *Callback {
	if m != nil {
		return m.Callback
	}
	return nil
}

func (m *ScanImageReq) GetTextData() *TextData {
	if m != nil {
		return m.TextData
	}
	return nil
}

func (m *ScanImageReq) GetReceiveTokenId() string {
	if m != nil {
		return m.ReceiveTokenId
	}
	return ""
}

func (m *ScanImageReq) GetAggregation() bool {
	if m != nil {
		return m.Aggregation
	}
	return false
}

type ScanImageResp struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ScanImageResp) Reset()         { *m = ScanImageResp{} }
func (m *ScanImageResp) String() string { return proto.CompactTextString(m) }
func (*ScanImageResp) ProtoMessage()    {}
func (*ScanImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_image_04c3f85576d47d97, []int{2}
}
func (m *ScanImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ScanImageResp.Unmarshal(m, b)
}
func (m *ScanImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ScanImageResp.Marshal(b, m, deterministic)
}
func (dst *ScanImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ScanImageResp.Merge(dst, src)
}
func (m *ScanImageResp) XXX_Size() int {
	return xxx_messageInfo_ScanImageResp.Size(m)
}
func (m *ScanImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ScanImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_ScanImageResp proto.InternalMessageInfo

func (m *ScanImageResp) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type ImageResultDetail struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImageResultDetail) Reset()         { *m = ImageResultDetail{} }
func (m *ImageResultDetail) String() string { return proto.CompactTextString(m) }
func (*ImageResultDetail) ProtoMessage()    {}
func (*ImageResultDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_image_04c3f85576d47d97, []int{3}
}
func (m *ImageResultDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImageResultDetail.Unmarshal(m, b)
}
func (m *ImageResultDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImageResultDetail.Marshal(b, m, deterministic)
}
func (dst *ImageResultDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImageResultDetail.Merge(dst, src)
}
func (m *ImageResultDetail) XXX_Size() int {
	return xxx_messageInfo_ImageResultDetail.Size(m)
}
func (m *ImageResultDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ImageResultDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ImageResultDetail proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ImageData)(nil), "cybros.arbiter.v2.ImageData")
	proto.RegisterType((*ScanImageReq)(nil), "cybros.arbiter.v2.ScanImageReq")
	proto.RegisterType((*ScanImageResp)(nil), "cybros.arbiter.v2.ScanImageResp")
	proto.RegisterType((*ImageResultDetail)(nil), "cybros.arbiter.v2.ImageResultDetail")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ImageClient is the client API for Image service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ImageClient interface {
	ScanImage(ctx context.Context, in *ScanImageReq, opts ...grpc.CallOption) (*ScanImageResp, error)
	AsyncScanImage(ctx context.Context, in *ScanImageReq, opts ...grpc.CallOption) (*ScanImageResp, error)
}

type imageClient struct {
	cc *grpc.ClientConn
}

func NewImageClient(cc *grpc.ClientConn) ImageClient {
	return &imageClient{cc}
}

func (c *imageClient) ScanImage(ctx context.Context, in *ScanImageReq, opts ...grpc.CallOption) (*ScanImageResp, error) {
	out := new(ScanImageResp)
	err := c.cc.Invoke(ctx, "/cybros.arbiter.v2.Image/ScanImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *imageClient) AsyncScanImage(ctx context.Context, in *ScanImageReq, opts ...grpc.CallOption) (*ScanImageResp, error) {
	out := new(ScanImageResp)
	err := c.cc.Invoke(ctx, "/cybros.arbiter.v2.Image/AsyncScanImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ImageServer is the server API for Image service.
type ImageServer interface {
	ScanImage(context.Context, *ScanImageReq) (*ScanImageResp, error)
	AsyncScanImage(context.Context, *ScanImageReq) (*ScanImageResp, error)
}

func RegisterImageServer(s *grpc.Server, srv ImageServer) {
	s.RegisterService(&_Image_serviceDesc, srv)
}

func _Image_ScanImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageServer).ScanImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cybros.arbiter.v2.Image/ScanImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageServer).ScanImage(ctx, req.(*ScanImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Image_AsyncScanImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ImageServer).AsyncScanImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cybros.arbiter.v2.Image/AsyncScanImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ImageServer).AsyncScanImage(ctx, req.(*ScanImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Image_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cybros.arbiter.v2.Image",
	HandlerType: (*ImageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ScanImage",
			Handler:    _Image_ScanImage_Handler,
		},
		{
			MethodName: "AsyncScanImage",
			Handler:    _Image_AsyncScanImage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/cybros/arbiter/v2/image.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/cybros/arbiter/v2/image.proto", fileDescriptor_image_04c3f85576d47d97)
}

var fileDescriptor_image_04c3f85576d47d97 = []byte{
	// 453 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x92, 0xb1, 0x6e, 0xdb, 0x30,
	0x10, 0x86, 0xa3, 0xb8, 0x71, 0xec, 0xb3, 0x1b, 0x34, 0xec, 0x50, 0xc1, 0x2d, 0x5a, 0xc1, 0x93,
	0x80, 0xb6, 0x12, 0xa0, 0x20, 0x68, 0x96, 0x0e, 0x4d, 0x32, 0xc4, 0x43, 0x3b, 0x30, 0x9e, 0xba,
	0x18, 0x34, 0x4d, 0x08, 0x84, 0x68, 0xd1, 0x21, 0xcf, 0x82, 0xb3, 0xf6, 0x79, 0xfa, 0x1c, 0x7d,
	0xae, 0x82, 0x94, 0xec, 0x1a, 0x88, 0x50, 0x2f, 0xd9, 0xc4, 0xe3, 0xf7, 0xff, 0x77, 0xf7, 0x8b,
	0xf0, 0x09, 0x31, 0x7d, 0x58, 0x4b, 0x5e, 0x58, 0xa9, 0x2a, 0x61, 0x52, 0xfe, 0x38, 0x37, 0xda,
	0xa6, 0xcc, 0xcc, 0x25, 0x0a, 0x93, 0x56, 0x59, 0x2a, 0x97, 0x2c, 0x17, 0xc9, 0xca, 0x68, 0xd4,
	0xe4, 0xbc, 0xbe, 0x4e, 0x9a, 0xeb, 0xa4, 0xca, 0x46, 0x9f, 0x0f, 0x1a, 0xe4, 0x4a, 0xcf, 0x99,
	0xaa, 0x1d, 0x46, 0xe9, 0x41, 0x9c, 0x33, 0xa5, 0xe6, 0x8c, 0x17, 0x8d, 0xe0, 0xe3, 0x41, 0x01,
	0x8a, 0x0d, 0xd6, 0xf0, 0xf8, 0x57, 0x00, 0xfd, 0x89, 0x9b, 0xf7, 0x96, 0x21, 0x23, 0x5f, 0xa0,
	0xb7, 0x14, 0xc8, 0x16, 0x0c, 0x59, 0x18, 0x44, 0x41, 0x3c, 0xc8, 0xde, 0x26, 0x4f, 0x16, 0x48,
	0xbe, 0x37, 0x08, 0xdd, 0xc1, 0x84, 0x40, 0x67, 0x6d, 0x54, 0xd8, 0x89, 0x82, 0xb8, 0x7f, 0x77,
	0x44, 0xdd, 0x81, 0x8c, 0xe0, 0x94, 0xeb, 0x12, 0x45, 0x89, 0xe1, 0x8b, 0x28, 0x88, 0x87, 0x77,
	0x47, 0x74, 0x5b, 0xb8, 0x1e, 0x02, 0xf8, 0x94, 0x66, 0x4e, 0x3d, 0xfe, 0x73, 0x0c, 0xc3, 0x7b,
	0xce, 0x4a, 0x3f, 0x08, 0x15, 0x0f, 0xe4, 0xaa, 0x91, 0x6e, 0xb0, 0x19, 0xe3, 0x7d, 0xcb, 0x18,
	0x53, 0x66, 0x8b, 0x9b, 0x9a, 0xa2, 0x5b, 0x9c, 0x7c, 0x85, 0xc1, 0x3f, 0x63, 0x1b, 0x1e, 0x47,
	0x9d, 0x78, 0x90, 0xbd, 0x6b, 0x51, 0xef, 0x96, 0xa6, 0xf5, 0x24, 0xee, 0xd3, 0xba, 0x00, 0xb6,
	0x69, 0xfa, 0x65, 0xda, 0x03, 0xb8, 0x69, 0x10, 0xba, 0x83, 0xc9, 0x15, 0xf4, 0x5d, 0x7f, 0xdf,
	0xd6, 0xaf, 0xdb, 0xae, 0x9c, 0x8a, 0x0d, 0xfa, 0xa6, 0x3d, 0x6c, 0xbe, 0x48, 0x0c, 0xaf, 0x8c,
	0xe0, 0x42, 0x56, 0x62, 0x86, 0xba, 0x10, 0xe5, 0x4c, 0x2e, 0xc2, 0x13, 0x97, 0x23, 0x3d, 0x6b,
	0xea, 0x53, 0x57, 0x9e, 0x2c, 0x48, 0x04, 0x03, 0x96, 0xe7, 0x46, 0xe4, 0x0c, 0xa5, 0x2e, 0xc3,
	0x6e, 0x14, 0xc4, 0x3d, 0xba, 0x5f, 0x1a, 0xc7, 0xf0, 0x72, 0x2f, 0x47, 0xbb, 0x22, 0x6f, 0xe0,
	0x14, 0x99, 0x2d, 0x9c, 0x67, 0xe0, 0x3d, 0xbb, 0xee, 0x38, 0x59, 0x8c, 0x5f, 0xc3, 0xf9, 0x96,
	0x5a, 0x2b, 0xbc, 0x15, 0xc8, 0xa4, 0xca, 0x7e, 0x07, 0x70, 0xe2, 0xab, 0xe4, 0x07, 0xf4, 0x77,
	0x46, 0xe4, 0x43, 0xcb, 0x22, 0xfb, 0xbf, 0x6b, 0x14, 0xfd, 0x1f, 0xb0, 0x2b, 0x72, 0x0f, 0x67,
	0xdf, 0xec, 0x63, 0xc9, 0x9f, 0xd3, 0xf4, 0xfa, 0xf2, 0xe7, 0x45, 0xae, 0x15, 0x2b, 0xf3, 0xe4,
	0x32, 0x43, 0x4c, 0xb8, 0x5e, 0xa6, 0xfe, 0x51, 0x73, 0xad, 0x52, 0x2b, 0x4c, 0x25, 0xb9, 0xb0,
	0x4f, 0x5f, 0xff, 0xbc, 0xeb, 0xa1, 0x8b, 0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xc5, 0x1e, 0x44,
	0x17, 0xc9, 0x03, 0x00, 0x00,
}
