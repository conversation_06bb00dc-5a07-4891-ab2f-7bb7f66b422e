// Code generated by protoc-gen-gogo.
// source: src/minToolkit/kafka/pb/kfk_tt_auth_ev.proto
// DO NOT EDIT!

/*
	Package tt_auth_ev is a generated protocol buffer package.

	It is generated from these files:
		src/minToolkit/kafka/pb/kfk_tt_auth_ev.proto

	It has these top-level messages:
		TTAuthEvent
*/
package tt_auth_ev

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"

import io "io"
import fmt1 "fmt"
import github_com_gogo_protobuf_proto "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ETT_AUTH_EVENT_TYPE int32

const (
	ETT_AUTH_EVENT_TYPE_ENUM_TTAUTH_REG           ETT_AUTH_EVENT_TYPE = 1
	ETT_AUTH_EVENT_TYPE_ENUM_TTAUTH_LOGIN         ETT_AUTH_EVENT_TYPE = 2
	ETT_AUTH_EVENT_TYPE_ENUM_TTAUTH_SDKGAME_LOGIN ETT_AUTH_EVENT_TYPE = 3
)

var ETT_AUTH_EVENT_TYPE_name = map[int32]string{
	1: "ENUM_TTAUTH_REG",
	2: "ENUM_TTAUTH_LOGIN",
	3: "ENUM_TTAUTH_SDKGAME_LOGIN",
}
var ETT_AUTH_EVENT_TYPE_value = map[string]int32{
	"ENUM_TTAUTH_REG":           1,
	"ENUM_TTAUTH_LOGIN":         2,
	"ENUM_TTAUTH_SDKGAME_LOGIN": 3,
}

func (x ETT_AUTH_EVENT_TYPE) Enum() *ETT_AUTH_EVENT_TYPE {
	p := new(ETT_AUTH_EVENT_TYPE)
	*p = x
	return p
}
func (x ETT_AUTH_EVENT_TYPE) String() string {
	return proto.EnumName(ETT_AUTH_EVENT_TYPE_name, int32(x))
}
func (x *ETT_AUTH_EVENT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ETT_AUTH_EVENT_TYPE_value, data, "ETT_AUTH_EVENT_TYPE")
	if err != nil {
		return err
	}
	*x = ETT_AUTH_EVENT_TYPE(value)
	return nil
}
func (ETT_AUTH_EVENT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorKfkTtAuthEv, []int{0}
}

type TTAuthEvent struct {
	EventType         uint32 `protobuf:"varint,1,req,name=event_type,json=eventType" json:"event_type"`
	Uid               uint32 `protobuf:"varint,2,opt,name=uid" json:"uid"`
	Phone             string `protobuf:"bytes,3,opt,name=phone" json:"phone"`
	Imei              string `protobuf:"bytes,4,opt,name=imei" json:"imei"`
	Idfa              string `protobuf:"bytes,5,opt,name=idfa" json:"idfa"`
	ClientType        uint32 `protobuf:"varint,6,opt,name=client_type,json=clientType" json:"client_type"`
	DeviceIdHex       string `protobuf:"bytes,7,opt,name=device_id_hex,json=deviceIdHex" json:"device_id_hex"`
	Ip                string `protobuf:"bytes,8,opt,name=ip" json:"ip"`
	Ts                uint32 `protobuf:"varint,9,opt,name=ts" json:"ts"`
	IsAnti            bool   `protobuf:"varint,10,opt,name=is_anti,json=isAnti" json:"is_anti"`
	AppId             uint32 `protobuf:"varint,11,opt,name=app_id,json=appId" json:"app_id"`
	MarketId          uint32 `protobuf:"varint,12,opt,name=market_id,json=marketId" json:"market_id"`
	CmdId             uint32 `protobuf:"varint,13,opt,name=cmd_id,json=cmdId" json:"cmd_id"`
	ClientVersion     uint32 `protobuf:"varint,14,opt,name=client_version,json=clientVersion" json:"client_version"`
	IsAutoLogin       bool   `protobuf:"varint,15,opt,name=is_auto_login,json=isAutoLogin" json:"is_auto_login"`
	PkgChannel        string `protobuf:"bytes,16,opt,name=pkg_channel,json=pkgChannel" json:"pkg_channel"`
	SmDeviceId        string `protobuf:"bytes,17,opt,name=sm_device_id,json=smDeviceId" json:"sm_device_id"`
	DeviceModel       string `protobuf:"bytes,18,opt,name=device_model,json=deviceModel" json:"device_model"`
	SdkGameId         int64  `protobuf:"varint,19,opt,name=sdk_game_id,json=sdkGameId" json:"sdk_game_id"`
	LastLoginTs       uint32 `protobuf:"varint,20,opt,name=last_login_ts,json=lastLoginTs" json:"last_login_ts"`
	DeviceInfo        string `protobuf:"bytes,21,opt,name=device_info,json=deviceInfo" json:"device_info"`
	Oaid              string `protobuf:"bytes,22,opt,name=oaid" json:"oaid"`
	AndroidId         string `protobuf:"bytes,23,opt,name=android_id,json=androidId" json:"android_id"`
	ThirdPartyType    uint32 `protobuf:"varint,24,opt,name=third_party_type,json=thirdPartyType" json:"third_party_type"`
	ThirdPartyOpenId  string `protobuf:"bytes,25,opt,name=third_party_open_id,json=thirdPartyOpenId" json:"third_party_open_id"`
	ThirdPartyUnionId string `protobuf:"bytes,26,opt,name=third_party_union_id,json=thirdPartyUnionId" json:"third_party_union_id"`
	Source            uint32 `protobuf:"varint,27,opt,name=source" json:"source"`
	ThirdPartyAppId   string `protobuf:"bytes,28,opt,name=third_party_app_id,json=thirdPartyAppId" json:"third_party_app_id"`
	TerminalType      uint32 `protobuf:"varint,29,opt,name=terminal_type,json=terminalType" json:"terminal_type"`
	OsVer             string `protobuf:"bytes,30,opt,name=os_ver,json=osVer" json:"os_ver"`
	OsType            string `protobuf:"bytes,31,opt,name=os_type,json=osType" json:"os_type"`
	ScreenWidth       uint32 `protobuf:"varint,32,opt,name=screen_width,json=screenWidth" json:"screen_width"`
	ScreenHeight      uint32 `protobuf:"varint,33,opt,name=screen_height,json=screenHeight" json:"screen_height"`
}

func (m *TTAuthEvent) Reset()                    { *m = TTAuthEvent{} }
func (m *TTAuthEvent) String() string            { return proto.CompactTextString(m) }
func (*TTAuthEvent) ProtoMessage()               {}
func (*TTAuthEvent) Descriptor() ([]byte, []int) { return fileDescriptorKfkTtAuthEv, []int{0} }

func (m *TTAuthEvent) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

func (m *TTAuthEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TTAuthEvent) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *TTAuthEvent) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *TTAuthEvent) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *TTAuthEvent) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *TTAuthEvent) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

func (m *TTAuthEvent) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *TTAuthEvent) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *TTAuthEvent) GetIsAnti() bool {
	if m != nil {
		return m.IsAnti
	}
	return false
}

func (m *TTAuthEvent) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *TTAuthEvent) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *TTAuthEvent) GetCmdId() uint32 {
	if m != nil {
		return m.CmdId
	}
	return 0
}

func (m *TTAuthEvent) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *TTAuthEvent) GetIsAutoLogin() bool {
	if m != nil {
		return m.IsAutoLogin
	}
	return false
}

func (m *TTAuthEvent) GetPkgChannel() string {
	if m != nil {
		return m.PkgChannel
	}
	return ""
}

func (m *TTAuthEvent) GetSmDeviceId() string {
	if m != nil {
		return m.SmDeviceId
	}
	return ""
}

func (m *TTAuthEvent) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *TTAuthEvent) GetSdkGameId() int64 {
	if m != nil {
		return m.SdkGameId
	}
	return 0
}

func (m *TTAuthEvent) GetLastLoginTs() uint32 {
	if m != nil {
		return m.LastLoginTs
	}
	return 0
}

func (m *TTAuthEvent) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *TTAuthEvent) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *TTAuthEvent) GetAndroidId() string {
	if m != nil {
		return m.AndroidId
	}
	return ""
}

func (m *TTAuthEvent) GetThirdPartyType() uint32 {
	if m != nil {
		return m.ThirdPartyType
	}
	return 0
}

func (m *TTAuthEvent) GetThirdPartyOpenId() string {
	if m != nil {
		return m.ThirdPartyOpenId
	}
	return ""
}

func (m *TTAuthEvent) GetThirdPartyUnionId() string {
	if m != nil {
		return m.ThirdPartyUnionId
	}
	return ""
}

func (m *TTAuthEvent) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *TTAuthEvent) GetThirdPartyAppId() string {
	if m != nil {
		return m.ThirdPartyAppId
	}
	return ""
}

func (m *TTAuthEvent) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *TTAuthEvent) GetOsVer() string {
	if m != nil {
		return m.OsVer
	}
	return ""
}

func (m *TTAuthEvent) GetOsType() string {
	if m != nil {
		return m.OsType
	}
	return ""
}

func (m *TTAuthEvent) GetScreenWidth() uint32 {
	if m != nil {
		return m.ScreenWidth
	}
	return 0
}

func (m *TTAuthEvent) GetScreenHeight() uint32 {
	if m != nil {
		return m.ScreenHeight
	}
	return 0
}

func init() {
	proto.RegisterType((*TTAuthEvent)(nil), "kafka_tt_auth_ev.TTAuthEvent")
	proto.RegisterEnum("kafka_tt_auth_ev.ETT_AUTH_EVENT_TYPE", ETT_AUTH_EVENT_TYPE_name, ETT_AUTH_EVENT_TYPE_value)
}
func (m *TTAuthEvent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TTAuthEvent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.EventType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x22
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.Imei)))
	i += copy(dAtA[i:], m.Imei)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.Idfa)))
	i += copy(dAtA[i:], m.Idfa)
	dAtA[i] = 0x30
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.DeviceIdHex)))
	i += copy(dAtA[i:], m.DeviceIdHex)
	dAtA[i] = 0x42
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.Ip)))
	i += copy(dAtA[i:], m.Ip)
	dAtA[i] = 0x48
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.Ts))
	dAtA[i] = 0x50
	i++
	if m.IsAnti {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x58
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.AppId))
	dAtA[i] = 0x60
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.MarketId))
	dAtA[i] = 0x68
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.CmdId))
	dAtA[i] = 0x70
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.ClientVersion))
	dAtA[i] = 0x78
	i++
	if m.IsAutoLogin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.PkgChannel)))
	i += copy(dAtA[i:], m.PkgChannel)
	dAtA[i] = 0x8a
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.SmDeviceId)))
	i += copy(dAtA[i:], m.SmDeviceId)
	dAtA[i] = 0x92
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.DeviceModel)))
	i += copy(dAtA[i:], m.DeviceModel)
	dAtA[i] = 0x98
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.SdkGameId))
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.LastLoginTs))
	dAtA[i] = 0xaa
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.DeviceInfo)))
	i += copy(dAtA[i:], m.DeviceInfo)
	dAtA[i] = 0xb2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.Oaid)))
	i += copy(dAtA[i:], m.Oaid)
	dAtA[i] = 0xba
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.AndroidId)))
	i += copy(dAtA[i:], m.AndroidId)
	dAtA[i] = 0xc0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.ThirdPartyType))
	dAtA[i] = 0xca
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.ThirdPartyOpenId)))
	i += copy(dAtA[i:], m.ThirdPartyOpenId)
	dAtA[i] = 0xd2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.ThirdPartyUnionId)))
	i += copy(dAtA[i:], m.ThirdPartyUnionId)
	dAtA[i] = 0xd8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.Source))
	dAtA[i] = 0xe2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.ThirdPartyAppId)))
	i += copy(dAtA[i:], m.ThirdPartyAppId)
	dAtA[i] = 0xe8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.TerminalType))
	dAtA[i] = 0xf2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.OsVer)))
	i += copy(dAtA[i:], m.OsVer)
	dAtA[i] = 0xfa
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(len(m.OsType)))
	i += copy(dAtA[i:], m.OsType)
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.ScreenWidth))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintKfkTtAuthEv(dAtA, i, uint64(m.ScreenHeight))
	return i, nil
}

func encodeFixed64KfkTtAuthEv(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32KfkTtAuthEv(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintKfkTtAuthEv(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *TTAuthEvent) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovKfkTtAuthEv(uint64(m.EventType))
	n += 1 + sovKfkTtAuthEv(uint64(m.Uid))
	l = len(m.Phone)
	n += 1 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.Imei)
	n += 1 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.Idfa)
	n += 1 + l + sovKfkTtAuthEv(uint64(l))
	n += 1 + sovKfkTtAuthEv(uint64(m.ClientType))
	l = len(m.DeviceIdHex)
	n += 1 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.Ip)
	n += 1 + l + sovKfkTtAuthEv(uint64(l))
	n += 1 + sovKfkTtAuthEv(uint64(m.Ts))
	n += 2
	n += 1 + sovKfkTtAuthEv(uint64(m.AppId))
	n += 1 + sovKfkTtAuthEv(uint64(m.MarketId))
	n += 1 + sovKfkTtAuthEv(uint64(m.CmdId))
	n += 1 + sovKfkTtAuthEv(uint64(m.ClientVersion))
	n += 2
	l = len(m.PkgChannel)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.SmDeviceId)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.DeviceModel)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	n += 2 + sovKfkTtAuthEv(uint64(m.SdkGameId))
	n += 2 + sovKfkTtAuthEv(uint64(m.LastLoginTs))
	l = len(m.DeviceInfo)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.Oaid)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.AndroidId)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	n += 2 + sovKfkTtAuthEv(uint64(m.ThirdPartyType))
	l = len(m.ThirdPartyOpenId)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.ThirdPartyUnionId)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	n += 2 + sovKfkTtAuthEv(uint64(m.Source))
	l = len(m.ThirdPartyAppId)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	n += 2 + sovKfkTtAuthEv(uint64(m.TerminalType))
	l = len(m.OsVer)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	l = len(m.OsType)
	n += 2 + l + sovKfkTtAuthEv(uint64(l))
	n += 2 + sovKfkTtAuthEv(uint64(m.ScreenWidth))
	n += 2 + sovKfkTtAuthEv(uint64(m.ScreenHeight))
	return n
}

func sovKfkTtAuthEv(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozKfkTtAuthEv(x uint64) (n int) {
	return sovKfkTtAuthEv(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *TTAuthEvent) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowKfkTtAuthEv
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt1.Errorf("proto: TTAuthEvent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt1.Errorf("proto: TTAuthEvent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field EventType", wireType)
			}
			m.EventType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EventType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Imei", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Imei = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Idfa", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Idfa = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceIdHex", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceIdHex = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ip", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ip = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsAnti", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAnti = bool(v != 0)
		case 11:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			m.AppId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AppId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field MarketId", wireType)
			}
			m.MarketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MarketId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field CmdId", wireType)
			}
			m.CmdId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CmdId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ClientVersion", wireType)
			}
			m.ClientVersion = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientVersion |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field IsAutoLogin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAutoLogin = bool(v != 0)
		case 16:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field PkgChannel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PkgChannel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SmDeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SmDeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceModel", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceModel = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 19:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field SdkGameId", wireType)
			}
			m.SdkGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SdkGameId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 20:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field LastLoginTs", wireType)
			}
			m.LastLoginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastLoginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 21:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field DeviceInfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DeviceInfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 22:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Oaid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Oaid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field AndroidId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AndroidId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 24:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ThirdPartyType", wireType)
			}
			m.ThirdPartyType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ThirdPartyType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 25:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ThirdPartyOpenId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ThirdPartyOpenId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 26:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ThirdPartyUnionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ThirdPartyUnionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 27:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			m.Source = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Source |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 28:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ThirdPartyAppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ThirdPartyAppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 29:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field TerminalType", wireType)
			}
			m.TerminalType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TerminalType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 30:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OsVer", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsVer = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt1.Errorf("proto: wrong wireType = %d for field OsType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OsType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 32:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ScreenWidth", wireType)
			}
			m.ScreenWidth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScreenWidth |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 33:
			if wireType != 0 {
				return fmt1.Errorf("proto: wrong wireType = %d for field ScreenHeight", wireType)
			}
			m.ScreenHeight = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScreenHeight |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipKfkTtAuthEv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthKfkTtAuthEv
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto.NewRequiredNotSetError("event_type")
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipKfkTtAuthEv(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowKfkTtAuthEv
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowKfkTtAuthEv
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthKfkTtAuthEv
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowKfkTtAuthEv
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipKfkTtAuthEv(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt1.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthKfkTtAuthEv = fmt1.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowKfkTtAuthEv   = fmt1.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kfk_tt_auth_ev.proto", fileDescriptorKfkTtAuthEv)
}

var fileDescriptorKfkTtAuthEv = []byte{
	// 777 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x94, 0xdf, 0x6e, 0xdb, 0x36,
	0x14, 0xc6, 0x2b, 0x27, 0x71, 0xe3, 0x63, 0x3b, 0x71, 0x98, 0xb4, 0x63, 0x9b, 0xc6, 0x75, 0xf7,
	0xaf, 0xde, 0x1f, 0xc4, 0xd8, 0x86, 0xde, 0xcf, 0x5d, 0x8d, 0x44, 0x58, 0x93, 0x16, 0x99, 0x92,
	0x61, 0xbb, 0xe1, 0x38, 0x91, 0xb6, 0x08, 0x49, 0x24, 0x21, 0xd2, 0x59, 0xf3, 0x16, 0x7b, 0xa4,
	0x5d, 0xe6, 0x72, 0x4f, 0x30, 0x0c, 0xd9, 0x8b, 0x0c, 0xa4, 0x94, 0x98, 0xc3, 0x7a, 0x67, 0x7c,
	0x3f, 0x9e, 0xcf, 0xe7, 0x1c, 0x7e, 0x22, 0x7c, 0x69, 0xaa, 0x74, 0x52, 0x0a, 0x99, 0x28, 0x55,
	0xe4, 0xc2, 0x4e, 0x72, 0x3a, 0xcf, 0xe9, 0x44, 0xff, 0x3a, 0xc9, 0xe7, 0x39, 0xb1, 0x96, 0xd0,
	0xa5, 0xcd, 0x08, 0xbf, 0x3c, 0xd4, 0x95, 0xb2, 0x0a, 0x0d, 0x3c, 0x0e, 0xf4, 0x0f, 0xff, 0xe8,
	0x40, 0x37, 0x49, 0xa6, 0x4b, 0x9b, 0xcd, 0x2e, 0xb9, 0xb4, 0xe8, 0x23, 0x00, 0xee, 0x7e, 0x10,
	0x7b, 0xa5, 0x39, 0x8e, 0x46, 0xad, 0x71, 0xff, 0xe5, 0xfa, 0xf5, 0x5f, 0x4f, 0xef, 0x9d, 0x75,
	0xbc, 0x9e, 0x5c, 0x69, 0x8e, 0x1e, 0xc2, 0xda, 0x52, 0x30, 0xdc, 0x1a, 0x45, 0x77, 0xd4, 0x09,
	0xe8, 0x31, 0x6c, 0xe8, 0x4c, 0x49, 0x8e, 0xd7, 0x46, 0xd1, 0xb8, 0xd3, 0x90, 0x5a, 0x42, 0x18,
	0xd6, 0x45, 0xc9, 0x05, 0x5e, 0x0f, 0x90, 0x57, 0x3c, 0x61, 0x73, 0x8a, 0x37, 0xfe, 0x43, 0xd8,
	0x9c, 0xa2, 0x4f, 0xa0, 0x9b, 0x16, 0xe2, 0xae, 0x9b, 0x76, 0xf0, 0x7f, 0x50, 0x03, 0xdf, 0xce,
	0x18, 0xfa, 0x8c, 0x5f, 0x8a, 0x94, 0x13, 0xc1, 0x48, 0xc6, 0xdf, 0xe1, 0xfb, 0x81, 0x53, 0xb7,
	0x46, 0x31, 0x3b, 0xe6, 0xef, 0xd0, 0x1e, 0xb4, 0x84, 0xc6, 0x9b, 0x01, 0x6e, 0x09, 0xed, 0x54,
	0x6b, 0x70, 0x27, 0x70, 0x6f, 0x59, 0x83, 0x0e, 0xe0, 0xbe, 0x30, 0x84, 0x4a, 0x2b, 0x30, 0x8c,
	0xa2, 0xf1, 0x66, 0x83, 0xda, 0xc2, 0x4c, 0xa5, 0x15, 0x68, 0x1f, 0xda, 0x54, 0x6b, 0x22, 0x18,
	0xee, 0x06, 0x85, 0x1b, 0x54, 0xeb, 0x98, 0xa1, 0x67, 0xd0, 0x29, 0x69, 0x95, 0x73, 0xeb, 0x78,
	0x2f, 0xe0, 0x9b, 0xb5, 0x1c, 0x33, 0x57, 0x9f, 0x96, 0xcc, 0xf1, 0x7e, 0x58, 0x9f, 0x96, 0x2c,
	0x66, 0xe8, 0x0b, 0xd8, 0x6a, 0x06, 0xbf, 0xe4, 0x95, 0x11, 0x4a, 0xe2, 0xad, 0xe0, 0x50, 0xbf,
	0x66, 0x17, 0x35, 0x72, 0xe3, 0xbb, 0x46, 0x97, 0x56, 0x91, 0x42, 0x2d, 0x84, 0xc4, 0xdb, 0x41,
	0xbb, 0x5d, 0x61, 0xa6, 0x4b, 0xab, 0x5e, 0x3b, 0xe0, 0xf6, 0xa9, 0xf3, 0x05, 0x49, 0x33, 0x2a,
	0x25, 0x2f, 0xf0, 0x20, 0xd8, 0x03, 0xe8, 0x7c, 0xf1, 0x5d, 0xad, 0xa3, 0x4f, 0xa1, 0x67, 0x4a,
	0x72, 0xb7, 0x52, 0xbc, 0x13, 0x9e, 0x33, 0xe5, 0xab, 0x66, 0xa1, 0xe8, 0x39, 0xf4, 0x9a, 0x43,
	0xa5, 0x62, 0xbc, 0xc0, 0xe8, 0xff, 0x6b, 0x3f, 0x71, 0x00, 0x7d, 0x0c, 0x5d, 0xc3, 0x72, 0xb2,
	0xa0, 0xa5, 0xf7, 0xdb, 0x1d, 0x45, 0xe3, 0xb5, 0xdb, 0x54, 0x19, 0x96, 0x1f, 0xd1, 0xd2, 0xd9,
	0x8d, 0xa1, 0x5f, 0x50, 0x63, 0xeb, 0x21, 0x88, 0x35, 0x78, 0x2f, 0x98, 0xb9, 0xeb, 0x90, 0x9f,
	0x22, 0x31, 0x6e, 0x8e, 0xdb, 0xee, 0xe4, 0x5c, 0xe1, 0x07, 0x61, 0x7f, 0xcd, 0x75, 0xcb, 0xb9,
	0x72, 0xc1, 0x52, 0x54, 0x30, 0xfc, 0x30, 0x0c, 0x96, 0x53, 0x5c, 0xca, 0xa9, 0x64, 0x95, 0x12,
	0xfe, 0x02, 0x3e, 0x08, 0x78, 0xa7, 0xd1, 0x63, 0x86, 0x0e, 0x61, 0x60, 0x33, 0x51, 0x31, 0xa2,
	0x69, 0x65, 0xaf, 0xea, 0x08, 0xe2, 0xa0, 0xa5, 0x2d, 0x4f, 0xdf, 0x3a, 0xe8, 0x63, 0xf8, 0x0d,
	0xec, 0x86, 0xe7, 0x95, 0xe6, 0xd2, 0xb9, 0x3f, 0x0a, 0xdc, 0x07, 0xab, 0x92, 0x37, 0x9a, 0xcb,
	0x98, 0xa1, 0x17, 0xb0, 0x17, 0x16, 0x2d, 0xa5, 0x50, 0xbe, 0xea, 0x71, 0x50, 0xb5, 0xb3, 0xaa,
	0x3a, 0x77, 0x3c, 0x66, 0xe8, 0x09, 0xb4, 0x8d, 0x5a, 0x56, 0x29, 0xc7, 0xfb, 0x41, 0x47, 0x8d,
	0x86, 0xbe, 0x02, 0x14, 0x9a, 0x36, 0x39, 0x7d, 0x12, 0x58, 0x6e, 0xaf, 0x2c, 0xa7, 0x3e, 0xb1,
	0x9f, 0x41, 0xdf, 0xf2, 0xaa, 0x14, 0x92, 0x16, 0xf5, 0xa4, 0x07, 0x81, 0x6f, 0xef, 0x16, 0xf9,
	0x39, 0xf7, 0xa1, 0xad, 0x8c, 0x0b, 0x26, 0x1e, 0x86, 0x9f, 0xb9, 0x32, 0x17, 0xbc, 0x72, 0x5f,
	0x8d, 0x32, 0xb5, 0xc3, 0xd3, 0x80, 0xb6, 0x95, 0xf1, 0xb5, 0xcf, 0xa1, 0x67, 0xd2, 0x8a, 0x73,
	0x49, 0x7e, 0x13, 0xcc, 0x66, 0x78, 0x14, 0x5e, 0x71, 0x4d, 0x7e, 0x74, 0xc0, 0xf5, 0xd3, 0x1c,
	0xcc, 0xb8, 0x58, 0x64, 0x16, 0x3f, 0x0b, 0xfb, 0xa9, 0xd1, 0xb1, 0x27, 0x9f, 0xff, 0x02, 0xbb,
	0xb3, 0x24, 0x21, 0xd3, 0xf3, 0xe4, 0x98, 0xcc, 0x2e, 0x66, 0xa7, 0x09, 0x49, 0x7e, 0x7a, 0x3b,
	0x43, 0xbb, 0xb0, 0x3d, 0x3b, 0x3d, 0x3f, 0x21, 0x49, 0xe2, 0xc9, 0xd9, 0xec, 0x68, 0x10, 0xa1,
	0x07, 0xb0, 0x13, 0x8a, 0xaf, 0xdf, 0x1c, 0xc5, 0xa7, 0x83, 0x16, 0x3a, 0x80, 0x47, 0xa1, 0xfc,
	0xc3, 0xab, 0xef, 0x8f, 0xa6, 0x27, 0xb3, 0x06, 0xaf, 0xbd, 0x3c, 0xbb, 0xbe, 0x19, 0x46, 0x7f,
	0xde, 0x0c, 0xa3, 0xbf, 0x6f, 0x86, 0xd1, 0xef, 0xff, 0x0c, 0xef, 0xfd, 0xfc, 0xed, 0x42, 0x15,
	0x54, 0x2e, 0x0e, 0x5f, 0x7c, 0x6d, 0xed, 0x61, 0xaa, 0xca, 0x89, 0x7f, 0x5f, 0x53, 0x55, 0x4c,
	0x0c, 0xaf, 0x5c, 0x00, 0xcd, 0x7b, 0x1f, 0xe5, 0xd5, 0xc3, 0xfb, 0x6f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x83, 0x61, 0x30, 0x7d, 0xb9, 0x05, 0x00, 0x00,
}
