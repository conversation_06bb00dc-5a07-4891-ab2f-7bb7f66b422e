// Code generated by protoc-gen-go. DO NOT EDIT.
// source: src/minToolkit/kafka/pb/kafka_oss_stat_batch_event/kfk_oss_stat_batch_event.proto

package kfk_oss_stat_batch_event

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type OssColumn struct {
	Idx                  uint32   `protobuf:"varint,1,opt,name=idx,proto3" json:"idx,omitempty"`
	Val                  string   `protobuf:"bytes,2,opt,name=val,proto3" json:"val,omitempty"`
	Key                  string   `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OssColumn) Reset()         { *m = OssColumn{} }
func (m *OssColumn) String() string { return proto.CompactTextString(m) }
func (*OssColumn) ProtoMessage()    {}
func (*OssColumn) Descriptor() ([]byte, []int) {
	return fileDescriptor_kfk_oss_stat_batch_event_42a91dfea9c3f91b, []int{0}
}
func (m *OssColumn) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssColumn.Unmarshal(m, b)
}
func (m *OssColumn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssColumn.Marshal(b, m, deterministic)
}
func (dst *OssColumn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssColumn.Merge(dst, src)
}
func (m *OssColumn) XXX_Size() int {
	return xxx_messageInfo_OssColumn.Size(m)
}
func (m *OssColumn) XXX_DiscardUnknown() {
	xxx_messageInfo_OssColumn.DiscardUnknown(m)
}

var xxx_messageInfo_OssColumn proto.InternalMessageInfo

func (m *OssColumn) GetIdx() uint32 {
	if m != nil {
		return m.Idx
	}
	return 0
}

func (m *OssColumn) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

func (m *OssColumn) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type OssStatisRow struct {
	Values               []*OssColumn `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OssStatisRow) Reset()         { *m = OssStatisRow{} }
func (m *OssStatisRow) String() string { return proto.CompactTextString(m) }
func (*OssStatisRow) ProtoMessage()    {}
func (*OssStatisRow) Descriptor() ([]byte, []int) {
	return fileDescriptor_kfk_oss_stat_batch_event_42a91dfea9c3f91b, []int{1}
}
func (m *OssStatisRow) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssStatisRow.Unmarshal(m, b)
}
func (m *OssStatisRow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssStatisRow.Marshal(b, m, deterministic)
}
func (dst *OssStatisRow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssStatisRow.Merge(dst, src)
}
func (m *OssStatisRow) XXX_Size() int {
	return xxx_messageInfo_OssStatisRow.Size(m)
}
func (m *OssStatisRow) XXX_DiscardUnknown() {
	xxx_messageInfo_OssStatisRow.DiscardUnknown(m)
}

var xxx_messageInfo_OssStatisRow proto.InternalMessageInfo

func (m *OssStatisRow) GetValues() []*OssColumn {
	if m != nil {
		return m.Values
	}
	return nil
}

type OssStatisBatchEvent struct {
	SrcIp                string          `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	ServerTimestamp      uint32          `protobuf:"varint,2,opt,name=server_timestamp,json=serverTimestamp,proto3" json:"server_timestamp,omitempty"`
	BizType              string          `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	RowList              []*OssStatisRow `protobuf:"bytes,4,rep,name=row_list,json=rowList,proto3" json:"row_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *OssStatisBatchEvent) Reset()         { *m = OssStatisBatchEvent{} }
func (m *OssStatisBatchEvent) String() string { return proto.CompactTextString(m) }
func (*OssStatisBatchEvent) ProtoMessage()    {}
func (*OssStatisBatchEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_kfk_oss_stat_batch_event_42a91dfea9c3f91b, []int{2}
}
func (m *OssStatisBatchEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OssStatisBatchEvent.Unmarshal(m, b)
}
func (m *OssStatisBatchEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OssStatisBatchEvent.Marshal(b, m, deterministic)
}
func (dst *OssStatisBatchEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OssStatisBatchEvent.Merge(dst, src)
}
func (m *OssStatisBatchEvent) XXX_Size() int {
	return xxx_messageInfo_OssStatisBatchEvent.Size(m)
}
func (m *OssStatisBatchEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_OssStatisBatchEvent.DiscardUnknown(m)
}

var xxx_messageInfo_OssStatisBatchEvent proto.InternalMessageInfo

func (m *OssStatisBatchEvent) GetSrcIp() string {
	if m != nil {
		return m.SrcIp
	}
	return ""
}

func (m *OssStatisBatchEvent) GetServerTimestamp() uint32 {
	if m != nil {
		return m.ServerTimestamp
	}
	return 0
}

func (m *OssStatisBatchEvent) GetBizType() string {
	if m != nil {
		return m.BizType
	}
	return ""
}

func (m *OssStatisBatchEvent) GetRowList() []*OssStatisRow {
	if m != nil {
		return m.RowList
	}
	return nil
}

func init() {
	proto.RegisterType((*OssColumn)(nil), "kfk_oss_stat_batch_event.OssColumn")
	proto.RegisterType((*OssStatisRow)(nil), "kfk_oss_stat_batch_event.OssStatisRow")
	proto.RegisterType((*OssStatisBatchEvent)(nil), "kfk_oss_stat_batch_event.OssStatisBatchEvent")
}

func init() {
	proto.RegisterFile("src/minToolkit/kafka/pb/kafka_oss_stat_batch_event/kfk_oss_stat_batch_event.proto", fileDescriptor_kfk_oss_stat_batch_event_42a91dfea9c3f91b)
}

var fileDescriptor_kfk_oss_stat_batch_event_42a91dfea9c3f91b = []byte{
	// 290 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x90, 0x4f, 0x4b, 0xfb, 0x30,
	0x18, 0xc7, 0xe9, 0x6f, 0x3f, 0xf7, 0x27, 0x3a, 0x1c, 0x11, 0x21, 0xde, 0xc6, 0x04, 0x99, 0x97,
	0x15, 0xf4, 0xe8, 0x69, 0x8a, 0x07, 0x51, 0x18, 0xc6, 0xdd, 0x43, 0x5a, 0x33, 0x0c, 0x69, 0x9b,
	0x90, 0xe7, 0x59, 0x6b, 0xf7, 0xbe, 0x7c, 0x7f, 0x92, 0x76, 0xf6, 0xe4, 0xbc, 0x7d, 0xf9, 0x3c,
	0xdf, 0x24, 0x9f, 0x3c, 0xe4, 0x15, 0x7c, 0x1a, 0xe7, 0xba, 0x58, 0x5b, 0x9b, 0x19, 0x8d, 0xb1,
	0x91, 0x1b, 0x23, 0x63, 0x97, 0xb4, 0x41, 0x58, 0x00, 0x01, 0x28, 0x51, 0x24, 0x12, 0xd3, 0x0f,
	0xa1, 0x4a, 0x55, 0x60, 0x6c, 0x36, 0xe6, 0xd7, 0xc1, 0xc2, 0x79, 0x8b, 0x96, 0xb2, 0x43, 0xf3,
	0xd9, 0x92, 0x8c, 0x56, 0x00, 0x0f, 0x36, 0xdb, 0xe6, 0x05, 0x9d, 0x90, 0x9e, 0x7e, 0xff, 0x64,
	0xd1, 0x34, 0x9a, 0x8f, 0x79, 0x88, 0x81, 0x94, 0x32, 0x63, 0xff, 0xa6, 0xd1, 0x7c, 0xc4, 0x43,
	0x0c, 0xc4, 0xa8, 0x9a, 0xf5, 0x5a, 0x62, 0x54, 0x3d, 0x7b, 0x26, 0x27, 0x2b, 0x80, 0x37, 0x94,
	0xa8, 0x81, 0xdb, 0x8a, 0xde, 0x91, 0x7e, 0x29, 0xb3, 0xad, 0x02, 0x16, 0x4d, 0x7b, 0xf3, 0xe3,
	0x9b, 0xcb, 0xc5, 0x41, 0xbb, 0xee, 0x69, 0xbe, 0x3f, 0x32, 0xfb, 0x8a, 0xc8, 0x59, 0x77, 0xdb,
	0x7d, 0xa8, 0x3e, 0x86, 0x26, 0x3d, 0x27, 0x7d, 0xf0, 0xa9, 0xd0, 0xae, 0xb1, 0x1b, 0xf1, 0x23,
	0xf0, 0xe9, 0x93, 0xa3, 0xd7, 0x64, 0x02, 0xca, 0x97, 0xca, 0x0b, 0xd4, 0xb9, 0x02, 0x94, 0xb9,
	0x6b, 0x64, 0xc7, 0xfc, 0xb4, 0xe5, 0xeb, 0x1f, 0x4c, 0x2f, 0xc8, 0x30, 0xd1, 0x3b, 0x81, 0xb5,
	0x53, 0x7b, 0xfb, 0x41, 0xa2, 0x77, 0xeb, 0xda, 0x29, 0xba, 0x24, 0x43, 0x6f, 0x2b, 0x91, 0x69,
	0x40, 0xf6, 0xbf, 0x71, 0xbe, 0xfa, 0xd3, 0xb9, 0xfb, 0x2b, 0x1f, 0x78, 0x5b, 0xbd, 0x68, 0xc0,
	0xa4, 0xdf, 0x2c, 0xfa, 0xf6, 0x3b, 0x00, 0x00, 0xff, 0xff, 0x38, 0xb4, 0x44, 0x2a, 0xbd, 0x01,
	0x00, 0x00,
}
