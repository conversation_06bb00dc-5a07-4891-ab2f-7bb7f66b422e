package PublicAccount

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/publicsvr/public.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Public service
const PublicMagic = uint16(15090)

// SvrkitClient API for Public service

type PublicClientInterface interface {
	CreatePublicAccount(ctx context.Context, uin uint32, in *CreatePublicAccountReq, opts ...svrkit.CallOption) (*CreatePublicAccountResp, error)
	GetPublicAccount(ctx context.Context, uin uint32, in *GetPublicAccountReq, opts ...svrkit.CallOption) (*GetPublicAccountResp, error)
	GetPublicAccountByBindedId(ctx context.Context, uin uint32, in *GetPublicAccountByBindedIdReq, opts ...svrkit.CallOption) (*GetPublicAccountByBindedIdResp, error)
	SetPublicAccountAutoReply(ctx context.Context, uin uint32, in *SetPublicAccountAutoReplyReq, opts ...svrkit.CallOption) (*SetPublicAccountAutoReplyResp, error)
	GetPublicAccountAutoReply(ctx context.Context, uin uint32, in *GetPublicAccountAutoReplyReq, opts ...svrkit.CallOption) (*GetPublicAccountAutoReplyResp, error)
	UpdatePublicAccount(ctx context.Context, uin uint32, in *UpdatePublicAccountReq, opts ...svrkit.CallOption) (*UpdatePublicAccountResp, error)
	GetPublicAccountsByIdList(ctx context.Context, uin uint32, in *GetPublicAccountsByIdListReq, opts ...svrkit.CallOption) (*GetPublicAccountsByIdListResp, error)
	GetPublicAccountsByBindedIdList(ctx context.Context, uin uint32, in *GetPublicAccountsByBindedIdListReq, opts ...svrkit.CallOption) (*GetPublicAccountsByBindedIdListResp, error)
	UpdatePublicAccountConfig(ctx context.Context, uin uint32, in *UpdatePublicAccountConfigReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatchGetPublicAccountConfig(ctx context.Context, uin uint32, in *BatchGetPublicAccountConfigReq, opts ...svrkit.CallOption) (*BatchGetPublicAccountConfigResp, error)
	AddOrUpdatePublicAccountDefaultMessage(ctx context.Context, uin uint32, in *AddOrUpdatePublicAccountDefaultMessageReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DeletePublicAccountDefaultMessage(ctx context.Context, uin uint32, in *DeletePublicAccountDefaultMessageReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetPublicAccountDefaultMessages(ctx context.Context, uin uint32, in *GetPublicAccountDefaultMessagesReq, opts ...svrkit.CallOption) (*GetPublicAccountDefaultMessagesResp, error)
}

type PublicSvrkitClient struct {
	cc *svrkit.ClientConn
}

func NewPublicSvrkitClient(cc *svrkit.ClientConn) PublicClientInterface {
	return &PublicSvrkitClient{cc}
}

const (
	commandPublicGetSelfSvnInfo                         = 9995
	commandPublicEcho                                   = 9999
	commandPublicCreatePublicAccount                    = 1
	commandPublicGetPublicAccount                       = 2
	commandPublicGetPublicAccountByBindedId             = 3
	commandPublicSetPublicAccountAutoReply              = 4
	commandPublicGetPublicAccountAutoReply              = 5
	commandPublicUpdatePublicAccount                    = 6
	commandPublicGetPublicAccountsByIdList              = 7
	commandPublicGetPublicAccountsByBindedIdList        = 8
	commandPublicUpdatePublicAccountConfig              = 9
	commandPublicBatchGetPublicAccountConfig            = 10
	commandPublicAddOrUpdatePublicAccountDefaultMessage = 20
	commandPublicDeletePublicAccountDefaultMessage      = 21
	commandPublicGetPublicAccountDefaultMessages        = 22
)

func (c *PublicSvrkitClient) CreatePublicAccount(ctx context.Context, uin uint32, in *CreatePublicAccountReq, opts ...svrkit.CallOption) (*CreatePublicAccountResp, error) {
	out := new(CreatePublicAccountResp)
	err := c.cc.Invoke(ctx, uin, commandPublicCreatePublicAccount, "/PublicAccount.Public/CreatePublicAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) GetPublicAccount(ctx context.Context, uin uint32, in *GetPublicAccountReq, opts ...svrkit.CallOption) (*GetPublicAccountResp, error) {
	out := new(GetPublicAccountResp)
	err := c.cc.Invoke(ctx, uin, commandPublicGetPublicAccount, "/PublicAccount.Public/GetPublicAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) GetPublicAccountByBindedId(ctx context.Context, uin uint32, in *GetPublicAccountByBindedIdReq, opts ...svrkit.CallOption) (*GetPublicAccountByBindedIdResp, error) {
	out := new(GetPublicAccountByBindedIdResp)
	err := c.cc.Invoke(ctx, uin, commandPublicGetPublicAccountByBindedId, "/PublicAccount.Public/GetPublicAccountByBindedId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) SetPublicAccountAutoReply(ctx context.Context, uin uint32, in *SetPublicAccountAutoReplyReq, opts ...svrkit.CallOption) (*SetPublicAccountAutoReplyResp, error) {
	out := new(SetPublicAccountAutoReplyResp)
	err := c.cc.Invoke(ctx, uin, commandPublicSetPublicAccountAutoReply, "/PublicAccount.Public/SetPublicAccountAutoReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) GetPublicAccountAutoReply(ctx context.Context, uin uint32, in *GetPublicAccountAutoReplyReq, opts ...svrkit.CallOption) (*GetPublicAccountAutoReplyResp, error) {
	out := new(GetPublicAccountAutoReplyResp)
	err := c.cc.Invoke(ctx, uin, commandPublicGetPublicAccountAutoReply, "/PublicAccount.Public/GetPublicAccountAutoReply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) UpdatePublicAccount(ctx context.Context, uin uint32, in *UpdatePublicAccountReq, opts ...svrkit.CallOption) (*UpdatePublicAccountResp, error) {
	out := new(UpdatePublicAccountResp)
	err := c.cc.Invoke(ctx, uin, commandPublicUpdatePublicAccount, "/PublicAccount.Public/UpdatePublicAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) GetPublicAccountsByIdList(ctx context.Context, uin uint32, in *GetPublicAccountsByIdListReq, opts ...svrkit.CallOption) (*GetPublicAccountsByIdListResp, error) {
	out := new(GetPublicAccountsByIdListResp)
	err := c.cc.Invoke(ctx, uin, commandPublicGetPublicAccountsByIdList, "/PublicAccount.Public/GetPublicAccountsByIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) GetPublicAccountsByBindedIdList(ctx context.Context, uin uint32, in *GetPublicAccountsByBindedIdListReq, opts ...svrkit.CallOption) (*GetPublicAccountsByBindedIdListResp, error) {
	out := new(GetPublicAccountsByBindedIdListResp)
	err := c.cc.Invoke(ctx, uin, commandPublicGetPublicAccountsByBindedIdList, "/PublicAccount.Public/GetPublicAccountsByBindedIdList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) UpdatePublicAccountConfig(ctx context.Context, uin uint32, in *UpdatePublicAccountConfigReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandPublicUpdatePublicAccountConfig, "/PublicAccount.Public/UpdatePublicAccountConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) BatchGetPublicAccountConfig(ctx context.Context, uin uint32, in *BatchGetPublicAccountConfigReq, opts ...svrkit.CallOption) (*BatchGetPublicAccountConfigResp, error) {
	out := new(BatchGetPublicAccountConfigResp)
	err := c.cc.Invoke(ctx, uin, commandPublicBatchGetPublicAccountConfig, "/PublicAccount.Public/BatchGetPublicAccountConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) AddOrUpdatePublicAccountDefaultMessage(ctx context.Context, uin uint32, in *AddOrUpdatePublicAccountDefaultMessageReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandPublicAddOrUpdatePublicAccountDefaultMessage, "/PublicAccount.Public/AddOrUpdatePublicAccountDefaultMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) DeletePublicAccountDefaultMessage(ctx context.Context, uin uint32, in *DeletePublicAccountDefaultMessageReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandPublicDeletePublicAccountDefaultMessage, "/PublicAccount.Public/DeletePublicAccountDefaultMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *PublicSvrkitClient) GetPublicAccountDefaultMessages(ctx context.Context, uin uint32, in *GetPublicAccountDefaultMessagesReq, opts ...svrkit.CallOption) (*GetPublicAccountDefaultMessagesResp, error) {
	out := new(GetPublicAccountDefaultMessagesResp)
	err := c.cc.Invoke(ctx, uin, commandPublicGetPublicAccountDefaultMessages, "/PublicAccount.Public/GetPublicAccountDefaultMessages", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
