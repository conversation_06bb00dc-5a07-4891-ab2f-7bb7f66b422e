// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-scheme-conf-mgr/channel-scheme-conf-base.proto

package channel_scheme_conf_mgr // import "golang.52tt.com/protocol/services/channel-scheme-conf-mgr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 不管虚拟多少层，虚拟类型的枚举值都可以放在这里
type SchemeVirtualType int32

const (
	SchemeVirtualType_SCHEME_VIRTUAL_TYPE_NO   SchemeVirtualType = 0
	SchemeVirtualType_SCHEME_VIRTUAL_TYPE_GAME SchemeVirtualType = 1
)

var SchemeVirtualType_name = map[int32]string{
	0: "SCHEME_VIRTUAL_TYPE_NO",
	1: "SCHEME_VIRTUAL_TYPE_GAME",
}
var SchemeVirtualType_value = map[string]int32{
	"SCHEME_VIRTUAL_TYPE_NO":   0,
	"SCHEME_VIRTUAL_TYPE_GAME": 1,
}

func (x SchemeVirtualType) String() string {
	return proto.EnumName(SchemeVirtualType_name, int32(x))
}
func (SchemeVirtualType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{0}
}

// 虚拟类型下的玩法之间切换模式，是跟随玩法id还是跟随玩法类型
// 已开黑虚拟类型为例子，例如在王者玩法下，切到文字模式，如果是跟随玩法类型，则所有开黑类型的玩法都变成了文字模式
// 如果是跟随玩法id，则只有王者变成了文字模式
type SchemeVirtualTypeSwitchMode int32

const (
	SchemeVirtualTypeSwitchMode_SCHEME_VIRTUAL_TYPE_SWITCH_UNKNOWN     SchemeVirtualTypeSwitchMode = 0
	SchemeVirtualTypeSwitchMode_SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_TYPE SchemeVirtualTypeSwitchMode = 1
	SchemeVirtualTypeSwitchMode_SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_ID   SchemeVirtualTypeSwitchMode = 2
)

var SchemeVirtualTypeSwitchMode_name = map[int32]string{
	0: "SCHEME_VIRTUAL_TYPE_SWITCH_UNKNOWN",
	1: "SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_TYPE",
	2: "SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_ID",
}
var SchemeVirtualTypeSwitchMode_value = map[string]int32{
	"SCHEME_VIRTUAL_TYPE_SWITCH_UNKNOWN":     0,
	"SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_TYPE": 1,
	"SCHEME_VIRTUAL_TYPE_SWITCH_FOLLOW_ID":   2,
}

func (x SchemeVirtualTypeSwitchMode) String() string {
	return proto.EnumName(SchemeVirtualTypeSwitchMode_name, int32(x))
}
func (SchemeVirtualTypeSwitchMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{1}
}

type EMicControlBit int32

const (
	EMicControlBit_MIC_CONTROL_UNKNOWN                 EMicControlBit = 0
	EMicControlBit_MIC_CONTROL_NOT_KICK_OUT_MIC        EMicControlBit = 1
	EMicControlBit_MIC_CONTROL_NOT_UNLOCK_MIC          EMicControlBit = 2
	EMicControlBit_MIC_CONTROL_UNMUTE_MIC              EMicControlBit = 4
	EMicControlBit_MIC_CONTROL_CAN_CHANGE_MIC          EMicControlBit = 8
	EMicControlBit_MIC_CONTROL_CAN_TAKE_MIC            EMicControlBit = 16
	EMicControlBit_MIC_CONTROL_FORBIDDEN_AUTO_LOCK_MIC EMicControlBit = 32
)

var EMicControlBit_name = map[int32]string{
	0:  "MIC_CONTROL_UNKNOWN",
	1:  "MIC_CONTROL_NOT_KICK_OUT_MIC",
	2:  "MIC_CONTROL_NOT_UNLOCK_MIC",
	4:  "MIC_CONTROL_UNMUTE_MIC",
	8:  "MIC_CONTROL_CAN_CHANGE_MIC",
	16: "MIC_CONTROL_CAN_TAKE_MIC",
	32: "MIC_CONTROL_FORBIDDEN_AUTO_LOCK_MIC",
}
var EMicControlBit_value = map[string]int32{
	"MIC_CONTROL_UNKNOWN":                 0,
	"MIC_CONTROL_NOT_KICK_OUT_MIC":        1,
	"MIC_CONTROL_NOT_UNLOCK_MIC":          2,
	"MIC_CONTROL_UNMUTE_MIC":              4,
	"MIC_CONTROL_CAN_CHANGE_MIC":          8,
	"MIC_CONTROL_CAN_TAKE_MIC":            16,
	"MIC_CONTROL_FORBIDDEN_AUTO_LOCK_MIC": 32,
}

func (x EMicControlBit) String() string {
	return proto.EnumName(EMicControlBit_name, int32(x))
}
func (EMicControlBit) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{2}
}

// 废弃，以后新的玩法定义在channel-scheme_.proto的枚举类型SchemeDetailType，这里不在加新的玩法
type SchemeSvrDetailType int32

const (
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_UNKNOWN             SchemeSvrDetailType = 0
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_FUN                 SchemeSvrDetailType = 1
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_GAME                SchemeSvrDetailType = 2
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_LIVE                SchemeSvrDetailType = 3
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_DATING              SchemeSvrDetailType = 4
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_MINI_GAME           SchemeSvrDetailType = 5
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_MASKED_DATING       SchemeSvrDetailType = 6
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_CP                  SchemeSvrDetailType = 7
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_IDOL                SchemeSvrDetailType = 8
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_CP_BATTLE_GAME      SchemeSvrDetailType = 9
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_SING_A_ROUND        SchemeSvrDetailType = 10
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_KTV                 SchemeSvrDetailType = 11
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_ROLE_PLAY           SchemeSvrDetailType = 12
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_LISTENING           SchemeSvrDetailType = 13
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_MULTI_BATTLE        SchemeSvrDetailType = 14
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_MUSIC_NEST          SchemeSvrDetailType = 15
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_RAP                 SchemeSvrDetailType = 16
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PGC_WEREWOLVES      SchemeSvrDetailType = 17
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PIA_XI              SchemeSvrDetailType = 18
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_WEREWOLVES_GAME     SchemeSvrDetailType = 19
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_MULTI_MIC_MINI_GAME SchemeSvrDetailType = 20
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_MUSIC_CONCERT       SchemeSvrDetailType = 21
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PIA_V2              SchemeSvrDetailType = 22
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_ESCAPE_GAME         SchemeSvrDetailType = 23
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_GAME_RACE           SchemeSvrDetailType = 24
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_OFFICIAL_CHANNEL    SchemeSvrDetailType = 25
	// SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME = 26; //天配玩法26，已废弃，这里保留占位(6.21.0客户端带上了未完成的玩法, 后续也不能使用26这个数值)
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME          SchemeSvrDetailType = 27
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_OFFER_CHANNEL                SchemeSvrDetailType = 28
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_COMMUNITY_CHAT               SchemeSvrDetailType = 29
	SchemeSvrDetailType_SCHEME_SVR_DETAIL_TYPE_COMMUNITY_EXPAND_SOCIAL_CHAT SchemeSvrDetailType = 30
)

var SchemeSvrDetailType_name = map[int32]string{
	0:  "SCHEME_SVR_DETAIL_TYPE_UNKNOWN",
	1:  "SCHEME_SVR_DETAIL_TYPE_FUN",
	2:  "SCHEME_SVR_DETAIL_TYPE_GAME",
	3:  "SCHEME_SVR_DETAIL_TYPE_LIVE",
	4:  "SCHEME_SVR_DETAIL_TYPE_DATING",
	5:  "SCHEME_SVR_DETAIL_TYPE_MINI_GAME",
	6:  "SCHEME_SVR_DETAIL_TYPE_MASKED_DATING",
	7:  "SCHEME_SVR_DETAIL_TYPE_CP",
	8:  "SCHEME_SVR_DETAIL_TYPE_IDOL",
	9:  "SCHEME_SVR_DETAIL_TYPE_CP_BATTLE_GAME",
	10: "SCHEME_SVR_DETAIL_TYPE_SING_A_ROUND",
	11: "SCHEME_SVR_DETAIL_TYPE_KTV",
	12: "SCHEME_SVR_DETAIL_TYPE_ROLE_PLAY",
	13: "SCHEME_SVR_DETAIL_TYPE_LISTENING",
	14: "SCHEME_SVR_DETAIL_TYPE_MULTI_BATTLE",
	15: "SCHEME_SVR_DETAIL_TYPE_MUSIC_NEST",
	16: "SCHEME_SVR_DETAIL_TYPE_RAP",
	17: "SCHEME_SVR_DETAIL_TYPE_PGC_WEREWOLVES",
	18: "SCHEME_SVR_DETAIL_TYPE_PIA_XI",
	19: "SCHEME_SVR_DETAIL_TYPE_WEREWOLVES_GAME",
	20: "SCHEME_SVR_DETAIL_TYPE_MULTI_MIC_MINI_GAME",
	21: "SCHEME_SVR_DETAIL_TYPE_MUSIC_CONCERT",
	22: "SCHEME_SVR_DETAIL_TYPE_PIA_V2",
	23: "SCHEME_SVR_DETAIL_TYPE_ESCAPE_GAME",
	24: "SCHEME_SVR_DETAIL_TYPE_GAME_RACE",
	25: "SCHEME_SVR_DETAIL_TYPE_OFFICIAL_CHANNEL",
	27: "SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME",
	28: "SCHEME_SVR_DETAIL_TYPE_OFFER_CHANNEL",
	29: "SCHEME_SVR_DETAIL_TYPE_COMMUNITY_CHAT",
	30: "SCHEME_SVR_DETAIL_TYPE_COMMUNITY_EXPAND_SOCIAL_CHAT",
}
var SchemeSvrDetailType_value = map[string]int32{
	"SCHEME_SVR_DETAIL_TYPE_UNKNOWN":                      0,
	"SCHEME_SVR_DETAIL_TYPE_FUN":                          1,
	"SCHEME_SVR_DETAIL_TYPE_GAME":                         2,
	"SCHEME_SVR_DETAIL_TYPE_LIVE":                         3,
	"SCHEME_SVR_DETAIL_TYPE_DATING":                       4,
	"SCHEME_SVR_DETAIL_TYPE_MINI_GAME":                    5,
	"SCHEME_SVR_DETAIL_TYPE_MASKED_DATING":                6,
	"SCHEME_SVR_DETAIL_TYPE_CP":                           7,
	"SCHEME_SVR_DETAIL_TYPE_IDOL":                         8,
	"SCHEME_SVR_DETAIL_TYPE_CP_BATTLE_GAME":               9,
	"SCHEME_SVR_DETAIL_TYPE_SING_A_ROUND":                 10,
	"SCHEME_SVR_DETAIL_TYPE_KTV":                          11,
	"SCHEME_SVR_DETAIL_TYPE_ROLE_PLAY":                    12,
	"SCHEME_SVR_DETAIL_TYPE_LISTENING":                    13,
	"SCHEME_SVR_DETAIL_TYPE_MULTI_BATTLE":                 14,
	"SCHEME_SVR_DETAIL_TYPE_MUSIC_NEST":                   15,
	"SCHEME_SVR_DETAIL_TYPE_RAP":                          16,
	"SCHEME_SVR_DETAIL_TYPE_PGC_WEREWOLVES":               17,
	"SCHEME_SVR_DETAIL_TYPE_PIA_XI":                       18,
	"SCHEME_SVR_DETAIL_TYPE_WEREWOLVES_GAME":              19,
	"SCHEME_SVR_DETAIL_TYPE_MULTI_MIC_MINI_GAME":          20,
	"SCHEME_SVR_DETAIL_TYPE_MUSIC_CONCERT":                21,
	"SCHEME_SVR_DETAIL_TYPE_PIA_V2":                       22,
	"SCHEME_SVR_DETAIL_TYPE_ESCAPE_GAME":                  23,
	"SCHEME_SVR_DETAIL_TYPE_GAME_RACE":                    24,
	"SCHEME_SVR_DETAIL_TYPE_OFFICIAL_CHANNEL":             25,
	"SCHEME_SVR_DETAIL_TYPE_PERFECT_COUPLE_GAME":          27,
	"SCHEME_SVR_DETAIL_TYPE_OFFER_CHANNEL":                28,
	"SCHEME_SVR_DETAIL_TYPE_COMMUNITY_CHAT":               29,
	"SCHEME_SVR_DETAIL_TYPE_COMMUNITY_EXPAND_SOCIAL_CHAT": 30,
}

func (x SchemeSvrDetailType) String() string {
	return proto.EnumName(SchemeSvrDetailType_name, int32(x))
}
func (SchemeSvrDetailType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{3}
}

// 废弃
type SchemeChannelCategory int32

const (
	SchemeChannelCategory_CHANNEL_TYPE_UNKNOWN      SchemeChannelCategory = 0
	SchemeChannelCategory_CHANNEL_TYPE_USER         SchemeChannelCategory = 1
	SchemeChannelCategory_CHANNEL_TYPE_PUBLIC_GUILD SchemeChannelCategory = 2
	SchemeChannelCategory_CHANNEL_TYPE_PGC_OTHER    SchemeChannelCategory = 3
	SchemeChannelCategory_CHANNEL_TYPE_COMMUNITY    SchemeChannelCategory = 4
)

var SchemeChannelCategory_name = map[int32]string{
	0: "CHANNEL_TYPE_UNKNOWN",
	1: "CHANNEL_TYPE_USER",
	2: "CHANNEL_TYPE_PUBLIC_GUILD",
	3: "CHANNEL_TYPE_PGC_OTHER",
	4: "CHANNEL_TYPE_COMMUNITY",
}
var SchemeChannelCategory_value = map[string]int32{
	"CHANNEL_TYPE_UNKNOWN":      0,
	"CHANNEL_TYPE_USER":         1,
	"CHANNEL_TYPE_PUBLIC_GUILD": 2,
	"CHANNEL_TYPE_PGC_OTHER":    3,
	"CHANNEL_TYPE_COMMUNITY":    4,
}

func (x SchemeChannelCategory) String() string {
	return proto.EnumName(SchemeChannelCategory_name, int32(x))
}
func (SchemeChannelCategory) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{4}
}

type CreateChannelSchemeReq struct {
	SchemeId                uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName              string   `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	SchemeIcon              string   `protobuf:"bytes,3,opt,name=scheme_icon,json=schemeIcon,proto3" json:"scheme_icon,omitempty"`
	SchemeType              uint32   `protobuf:"varint,4,opt,name=scheme_type,json=schemeType,proto3" json:"scheme_type,omitempty"`
	BusinessCategory        uint32   `protobuf:"varint,5,opt,name=business_category,json=businessCategory,proto3" json:"business_category,omitempty"`
	DefaultSchemeDetailType uint32   `protobuf:"varint,6,opt,name=default_scheme_detail_type,json=defaultSchemeDetailType,proto3" json:"default_scheme_detail_type,omitempty"`
	ThirdBindId             uint32   `protobuf:"varint,7,opt,name=third_bind_id,json=thirdBindId,proto3" json:"third_bind_id,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *CreateChannelSchemeReq) Reset()         { *m = CreateChannelSchemeReq{} }
func (m *CreateChannelSchemeReq) String() string { return proto.CompactTextString(m) }
func (*CreateChannelSchemeReq) ProtoMessage()    {}
func (*CreateChannelSchemeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{0}
}
func (m *CreateChannelSchemeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelSchemeReq.Unmarshal(m, b)
}
func (m *CreateChannelSchemeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelSchemeReq.Marshal(b, m, deterministic)
}
func (dst *CreateChannelSchemeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelSchemeReq.Merge(dst, src)
}
func (m *CreateChannelSchemeReq) XXX_Size() int {
	return xxx_messageInfo_CreateChannelSchemeReq.Size(m)
}
func (m *CreateChannelSchemeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelSchemeReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelSchemeReq proto.InternalMessageInfo

func (m *CreateChannelSchemeReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *CreateChannelSchemeReq) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *CreateChannelSchemeReq) GetSchemeIcon() string {
	if m != nil {
		return m.SchemeIcon
	}
	return ""
}

func (m *CreateChannelSchemeReq) GetSchemeType() uint32 {
	if m != nil {
		return m.SchemeType
	}
	return 0
}

func (m *CreateChannelSchemeReq) GetBusinessCategory() uint32 {
	if m != nil {
		return m.BusinessCategory
	}
	return 0
}

func (m *CreateChannelSchemeReq) GetDefaultSchemeDetailType() uint32 {
	if m != nil {
		return m.DefaultSchemeDetailType
	}
	return 0
}

func (m *CreateChannelSchemeReq) GetThirdBindId() uint32 {
	if m != nil {
		return m.ThirdBindId
	}
	return 0
}

type CreateChannelSchemeResp struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	MicMode              uint32   `protobuf:"varint,2,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelSchemeResp) Reset()         { *m = CreateChannelSchemeResp{} }
func (m *CreateChannelSchemeResp) String() string { return proto.CompactTextString(m) }
func (*CreateChannelSchemeResp) ProtoMessage()    {}
func (*CreateChannelSchemeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{1}
}
func (m *CreateChannelSchemeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelSchemeResp.Unmarshal(m, b)
}
func (m *CreateChannelSchemeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelSchemeResp.Marshal(b, m, deterministic)
}
func (dst *CreateChannelSchemeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelSchemeResp.Merge(dst, src)
}
func (m *CreateChannelSchemeResp) XXX_Size() int {
	return xxx_messageInfo_CreateChannelSchemeResp.Size(m)
}
func (m *CreateChannelSchemeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelSchemeResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelSchemeResp proto.InternalMessageInfo

func (m *CreateChannelSchemeResp) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *CreateChannelSchemeResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

type ModifyChannelSchemeReq struct {
	SchemeId                uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName              string   `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	SchemeIcon              string   `protobuf:"bytes,3,opt,name=scheme_icon,json=schemeIcon,proto3" json:"scheme_icon,omitempty"`
	SchemeType              uint32   `protobuf:"varint,4,opt,name=scheme_type,json=schemeType,proto3" json:"scheme_type,omitempty"`
	BusinessCategory        uint32   `protobuf:"varint,5,opt,name=business_category,json=businessCategory,proto3" json:"business_category,omitempty"`
	DefaultSchemeDetailType uint32   `protobuf:"varint,6,opt,name=default_scheme_detail_type,json=defaultSchemeDetailType,proto3" json:"default_scheme_detail_type,omitempty"`
	ThirdBindId             uint32   `protobuf:"varint,7,opt,name=third_bind_id,json=thirdBindId,proto3" json:"third_bind_id,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *ModifyChannelSchemeReq) Reset()         { *m = ModifyChannelSchemeReq{} }
func (m *ModifyChannelSchemeReq) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelSchemeReq) ProtoMessage()    {}
func (*ModifyChannelSchemeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{2}
}
func (m *ModifyChannelSchemeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelSchemeReq.Unmarshal(m, b)
}
func (m *ModifyChannelSchemeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelSchemeReq.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelSchemeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelSchemeReq.Merge(dst, src)
}
func (m *ModifyChannelSchemeReq) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelSchemeReq.Size(m)
}
func (m *ModifyChannelSchemeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelSchemeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelSchemeReq proto.InternalMessageInfo

func (m *ModifyChannelSchemeReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *ModifyChannelSchemeReq) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *ModifyChannelSchemeReq) GetSchemeIcon() string {
	if m != nil {
		return m.SchemeIcon
	}
	return ""
}

func (m *ModifyChannelSchemeReq) GetSchemeType() uint32 {
	if m != nil {
		return m.SchemeType
	}
	return 0
}

func (m *ModifyChannelSchemeReq) GetBusinessCategory() uint32 {
	if m != nil {
		return m.BusinessCategory
	}
	return 0
}

func (m *ModifyChannelSchemeReq) GetDefaultSchemeDetailType() uint32 {
	if m != nil {
		return m.DefaultSchemeDetailType
	}
	return 0
}

func (m *ModifyChannelSchemeReq) GetThirdBindId() uint32 {
	if m != nil {
		return m.ThirdBindId
	}
	return 0
}

type ModifyChannelSchemeResp struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	MicMode              uint32   `protobuf:"varint,2,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyChannelSchemeResp) Reset()         { *m = ModifyChannelSchemeResp{} }
func (m *ModifyChannelSchemeResp) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelSchemeResp) ProtoMessage()    {}
func (*ModifyChannelSchemeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{3}
}
func (m *ModifyChannelSchemeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelSchemeResp.Unmarshal(m, b)
}
func (m *ModifyChannelSchemeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelSchemeResp.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelSchemeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelSchemeResp.Merge(dst, src)
}
func (m *ModifyChannelSchemeResp) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelSchemeResp.Size(m)
}
func (m *ModifyChannelSchemeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelSchemeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelSchemeResp proto.InternalMessageInfo

func (m *ModifyChannelSchemeResp) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *ModifyChannelSchemeResp) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

type DeleteChannelSchemeReq struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteChannelSchemeReq) Reset()         { *m = DeleteChannelSchemeReq{} }
func (m *DeleteChannelSchemeReq) String() string { return proto.CompactTextString(m) }
func (*DeleteChannelSchemeReq) ProtoMessage()    {}
func (*DeleteChannelSchemeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{4}
}
func (m *DeleteChannelSchemeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteChannelSchemeReq.Unmarshal(m, b)
}
func (m *DeleteChannelSchemeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteChannelSchemeReq.Marshal(b, m, deterministic)
}
func (dst *DeleteChannelSchemeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteChannelSchemeReq.Merge(dst, src)
}
func (m *DeleteChannelSchemeReq) XXX_Size() int {
	return xxx_messageInfo_DeleteChannelSchemeReq.Size(m)
}
func (m *DeleteChannelSchemeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteChannelSchemeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteChannelSchemeReq proto.InternalMessageInfo

func (m *DeleteChannelSchemeReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

type DeleteChannelSchemeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteChannelSchemeResp) Reset()         { *m = DeleteChannelSchemeResp{} }
func (m *DeleteChannelSchemeResp) String() string { return proto.CompactTextString(m) }
func (*DeleteChannelSchemeResp) ProtoMessage()    {}
func (*DeleteChannelSchemeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{5}
}
func (m *DeleteChannelSchemeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteChannelSchemeResp.Unmarshal(m, b)
}
func (m *DeleteChannelSchemeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteChannelSchemeResp.Marshal(b, m, deterministic)
}
func (dst *DeleteChannelSchemeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteChannelSchemeResp.Merge(dst, src)
}
func (m *DeleteChannelSchemeResp) XXX_Size() int {
	return xxx_messageInfo_DeleteChannelSchemeResp.Size(m)
}
func (m *DeleteChannelSchemeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteChannelSchemeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteChannelSchemeResp proto.InternalMessageInfo

type GetChannelSchemeBaseConfListReq struct {
	SchemeName           string   `protobuf:"bytes,1,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageNum              uint32   `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelSchemeBaseConfListReq) Reset()         { *m = GetChannelSchemeBaseConfListReq{} }
func (m *GetChannelSchemeBaseConfListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelSchemeBaseConfListReq) ProtoMessage()    {}
func (*GetChannelSchemeBaseConfListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{6}
}
func (m *GetChannelSchemeBaseConfListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSchemeBaseConfListReq.Unmarshal(m, b)
}
func (m *GetChannelSchemeBaseConfListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSchemeBaseConfListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelSchemeBaseConfListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSchemeBaseConfListReq.Merge(dst, src)
}
func (m *GetChannelSchemeBaseConfListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelSchemeBaseConfListReq.Size(m)
}
func (m *GetChannelSchemeBaseConfListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSchemeBaseConfListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSchemeBaseConfListReq proto.InternalMessageInfo

func (m *GetChannelSchemeBaseConfListReq) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *GetChannelSchemeBaseConfListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetChannelSchemeBaseConfListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

type GetChannelSchemeBaseConfListResp struct {
	BaseConfList         []*ChannelSchemeBaseConf `protobuf:"bytes,1,rep,name=base_conf_list,json=baseConfList,proto3" json:"base_conf_list,omitempty"`
	Total                uint32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetChannelSchemeBaseConfListResp) Reset()         { *m = GetChannelSchemeBaseConfListResp{} }
func (m *GetChannelSchemeBaseConfListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelSchemeBaseConfListResp) ProtoMessage()    {}
func (*GetChannelSchemeBaseConfListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{7}
}
func (m *GetChannelSchemeBaseConfListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSchemeBaseConfListResp.Unmarshal(m, b)
}
func (m *GetChannelSchemeBaseConfListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSchemeBaseConfListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelSchemeBaseConfListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSchemeBaseConfListResp.Merge(dst, src)
}
func (m *GetChannelSchemeBaseConfListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelSchemeBaseConfListResp.Size(m)
}
func (m *GetChannelSchemeBaseConfListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSchemeBaseConfListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSchemeBaseConfListResp proto.InternalMessageInfo

func (m *GetChannelSchemeBaseConfListResp) GetBaseConfList() []*ChannelSchemeBaseConf {
	if m != nil {
		return m.BaseConfList
	}
	return nil
}

func (m *GetChannelSchemeBaseConfListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetChannelSchemeConfCacheReq struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeDetailType     uint32   `protobuf:"varint,2,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelSchemeConfCacheReq) Reset()         { *m = GetChannelSchemeConfCacheReq{} }
func (m *GetChannelSchemeConfCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelSchemeConfCacheReq) ProtoMessage()    {}
func (*GetChannelSchemeConfCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{8}
}
func (m *GetChannelSchemeConfCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSchemeConfCacheReq.Unmarshal(m, b)
}
func (m *GetChannelSchemeConfCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSchemeConfCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelSchemeConfCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSchemeConfCacheReq.Merge(dst, src)
}
func (m *GetChannelSchemeConfCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelSchemeConfCacheReq.Size(m)
}
func (m *GetChannelSchemeConfCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSchemeConfCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSchemeConfCacheReq proto.InternalMessageInfo

func (m *GetChannelSchemeConfCacheReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *GetChannelSchemeConfCacheReq) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

type GetChannelSchemeConfCacheResp struct {
	Conf                 *ChannelSchemeConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetChannelSchemeConfCacheResp) Reset()         { *m = GetChannelSchemeConfCacheResp{} }
func (m *GetChannelSchemeConfCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelSchemeConfCacheResp) ProtoMessage()    {}
func (*GetChannelSchemeConfCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{9}
}
func (m *GetChannelSchemeConfCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelSchemeConfCacheResp.Unmarshal(m, b)
}
func (m *GetChannelSchemeConfCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelSchemeConfCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelSchemeConfCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelSchemeConfCacheResp.Merge(dst, src)
}
func (m *GetChannelSchemeConfCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelSchemeConfCacheResp.Size(m)
}
func (m *GetChannelSchemeConfCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelSchemeConfCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelSchemeConfCacheResp proto.InternalMessageInfo

func (m *GetChannelSchemeConfCacheResp) GetConf() *ChannelSchemeConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type GetAllChannelSchemeConfCacheReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllChannelSchemeConfCacheReq) Reset()         { *m = GetAllChannelSchemeConfCacheReq{} }
func (m *GetAllChannelSchemeConfCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelSchemeConfCacheReq) ProtoMessage()    {}
func (*GetAllChannelSchemeConfCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{10}
}
func (m *GetAllChannelSchemeConfCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelSchemeConfCacheReq.Unmarshal(m, b)
}
func (m *GetAllChannelSchemeConfCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelSchemeConfCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelSchemeConfCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelSchemeConfCacheReq.Merge(dst, src)
}
func (m *GetAllChannelSchemeConfCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelSchemeConfCacheReq.Size(m)
}
func (m *GetAllChannelSchemeConfCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelSchemeConfCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelSchemeConfCacheReq proto.InternalMessageInfo

type GetAllChannelSchemeConfCacheResp struct {
	ConfList             []*ChannelSchemeConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetAllChannelSchemeConfCacheResp) Reset()         { *m = GetAllChannelSchemeConfCacheResp{} }
func (m *GetAllChannelSchemeConfCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelSchemeConfCacheResp) ProtoMessage()    {}
func (*GetAllChannelSchemeConfCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{11}
}
func (m *GetAllChannelSchemeConfCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelSchemeConfCacheResp.Unmarshal(m, b)
}
func (m *GetAllChannelSchemeConfCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelSchemeConfCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelSchemeConfCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelSchemeConfCacheResp.Merge(dst, src)
}
func (m *GetAllChannelSchemeConfCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelSchemeConfCacheResp.Size(m)
}
func (m *GetAllChannelSchemeConfCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelSchemeConfCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelSchemeConfCacheResp proto.InternalMessageInfo

func (m *GetAllChannelSchemeConfCacheResp) GetConfList() []*ChannelSchemeConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type ChannelSchemeConf struct {
	BaseConf             *ChannelSchemeBaseConf    `protobuf:"bytes,1,opt,name=base_conf,json=baseConf,proto3" json:"base_conf,omitempty"`
	ExtraConf            *ChannelSchemeExtraConfV2 `protobuf:"bytes,2,opt,name=extra_conf,json=extraConf,proto3" json:"extra_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ChannelSchemeConf) Reset()         { *m = ChannelSchemeConf{} }
func (m *ChannelSchemeConf) String() string { return proto.CompactTextString(m) }
func (*ChannelSchemeConf) ProtoMessage()    {}
func (*ChannelSchemeConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{12}
}
func (m *ChannelSchemeConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSchemeConf.Unmarshal(m, b)
}
func (m *ChannelSchemeConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSchemeConf.Marshal(b, m, deterministic)
}
func (dst *ChannelSchemeConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSchemeConf.Merge(dst, src)
}
func (m *ChannelSchemeConf) XXX_Size() int {
	return xxx_messageInfo_ChannelSchemeConf.Size(m)
}
func (m *ChannelSchemeConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSchemeConf.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSchemeConf proto.InternalMessageInfo

func (m *ChannelSchemeConf) GetBaseConf() *ChannelSchemeBaseConf {
	if m != nil {
		return m.BaseConf
	}
	return nil
}

func (m *ChannelSchemeConf) GetExtraConf() *ChannelSchemeExtraConfV2 {
	if m != nil {
		return m.ExtraConf
	}
	return nil
}

// 玩法基础信息
type ChannelSchemeBaseConf struct {
	SchemeId                     uint32                        `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName                   string                        `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	SchemeIcon                   string                        `protobuf:"bytes,3,opt,name=scheme_icon,json=schemeIcon,proto3" json:"scheme_icon,omitempty"`
	SchemeType                   uint32                        `protobuf:"varint,4,opt,name=scheme_type,json=schemeType,proto3" json:"scheme_type,omitempty"`
	SchemeChannelCategory        SchemeChannelCategory         `protobuf:"varint,5,opt,name=scheme_channel_category,json=schemeChannelCategory,proto3,enum=channel_scheme_conf_mgr.SchemeChannelCategory" json:"scheme_channel_category,omitempty"`
	Layout                       *LayoutInfo                   `protobuf:"bytes,6,opt,name=layout,proto3" json:"layout,omitempty"`
	MicAudio                     *MicAudioParam                `protobuf:"bytes,7,opt,name=mic_audio,json=micAudio,proto3" json:"mic_audio,omitempty"`
	SchemeSvrDetailType          uint32                        `protobuf:"varint,8,opt,name=scheme_svr_detail_type,json=schemeSvrDetailType,proto3" json:"scheme_svr_detail_type,omitempty"`
	BusinessCategory             uint32                        `protobuf:"varint,9,opt,name=business_category,json=businessCategory,proto3" json:"business_category,omitempty"`
	DefaultSchemeDetailType      uint32                        `protobuf:"varint,10,opt,name=default_scheme_detail_type,json=defaultSchemeDetailType,proto3" json:"default_scheme_detail_type,omitempty"`
	RelationSchemeDetailTypeList *RelationSchemeDetailTypeList `protobuf:"bytes,11,opt,name=relation_scheme_detail_type_list,json=relationSchemeDetailTypeList,proto3" json:"relation_scheme_detail_type_list,omitempty"`
	FallbackSchemeDetailType     uint32                        `protobuf:"varint,12,opt,name=fallback_scheme_detail_type,json=fallbackSchemeDetailType,proto3" json:"fallback_scheme_detail_type,omitempty"`
	ThirdBindId                  uint32                        `protobuf:"varint,13,opt,name=third_bind_id,json=thirdBindId,proto3" json:"third_bind_id,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}                      `json:"-"`
	XXX_unrecognized             []byte                        `json:"-"`
	XXX_sizecache                int32                         `json:"-"`
}

func (m *ChannelSchemeBaseConf) Reset()         { *m = ChannelSchemeBaseConf{} }
func (m *ChannelSchemeBaseConf) String() string { return proto.CompactTextString(m) }
func (*ChannelSchemeBaseConf) ProtoMessage()    {}
func (*ChannelSchemeBaseConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{13}
}
func (m *ChannelSchemeBaseConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSchemeBaseConf.Unmarshal(m, b)
}
func (m *ChannelSchemeBaseConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSchemeBaseConf.Marshal(b, m, deterministic)
}
func (dst *ChannelSchemeBaseConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSchemeBaseConf.Merge(dst, src)
}
func (m *ChannelSchemeBaseConf) XXX_Size() int {
	return xxx_messageInfo_ChannelSchemeBaseConf.Size(m)
}
func (m *ChannelSchemeBaseConf) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSchemeBaseConf.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSchemeBaseConf proto.InternalMessageInfo

func (m *ChannelSchemeBaseConf) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *ChannelSchemeBaseConf) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *ChannelSchemeBaseConf) GetSchemeIcon() string {
	if m != nil {
		return m.SchemeIcon
	}
	return ""
}

func (m *ChannelSchemeBaseConf) GetSchemeType() uint32 {
	if m != nil {
		return m.SchemeType
	}
	return 0
}

func (m *ChannelSchemeBaseConf) GetSchemeChannelCategory() SchemeChannelCategory {
	if m != nil {
		return m.SchemeChannelCategory
	}
	return SchemeChannelCategory_CHANNEL_TYPE_UNKNOWN
}

func (m *ChannelSchemeBaseConf) GetLayout() *LayoutInfo {
	if m != nil {
		return m.Layout
	}
	return nil
}

func (m *ChannelSchemeBaseConf) GetMicAudio() *MicAudioParam {
	if m != nil {
		return m.MicAudio
	}
	return nil
}

func (m *ChannelSchemeBaseConf) GetSchemeSvrDetailType() uint32 {
	if m != nil {
		return m.SchemeSvrDetailType
	}
	return 0
}

func (m *ChannelSchemeBaseConf) GetBusinessCategory() uint32 {
	if m != nil {
		return m.BusinessCategory
	}
	return 0
}

func (m *ChannelSchemeBaseConf) GetDefaultSchemeDetailType() uint32 {
	if m != nil {
		return m.DefaultSchemeDetailType
	}
	return 0
}

func (m *ChannelSchemeBaseConf) GetRelationSchemeDetailTypeList() *RelationSchemeDetailTypeList {
	if m != nil {
		return m.RelationSchemeDetailTypeList
	}
	return nil
}

func (m *ChannelSchemeBaseConf) GetFallbackSchemeDetailType() uint32 {
	if m != nil {
		return m.FallbackSchemeDetailType
	}
	return 0
}

func (m *ChannelSchemeBaseConf) GetThirdBindId() uint32 {
	if m != nil {
		return m.ThirdBindId
	}
	return 0
}

type LayoutInfo struct {
	LayoutType           uint32   `protobuf:"varint,1,opt,name=layout_type,json=layoutType,proto3" json:"layout_type,omitempty"`
	DefaultMicSize       uint32   `protobuf:"varint,2,opt,name=default_mic_size,json=defaultMicSize,proto3" json:"default_mic_size,omitempty"`
	MicMode              uint32   `protobuf:"varint,3,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LayoutInfo) Reset()         { *m = LayoutInfo{} }
func (m *LayoutInfo) String() string { return proto.CompactTextString(m) }
func (*LayoutInfo) ProtoMessage()    {}
func (*LayoutInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{14}
}
func (m *LayoutInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LayoutInfo.Unmarshal(m, b)
}
func (m *LayoutInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LayoutInfo.Marshal(b, m, deterministic)
}
func (dst *LayoutInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LayoutInfo.Merge(dst, src)
}
func (m *LayoutInfo) XXX_Size() int {
	return xxx_messageInfo_LayoutInfo.Size(m)
}
func (m *LayoutInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LayoutInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LayoutInfo proto.InternalMessageInfo

func (m *LayoutInfo) GetLayoutType() uint32 {
	if m != nil {
		return m.LayoutType
	}
	return 0
}

func (m *LayoutInfo) GetDefaultMicSize() uint32 {
	if m != nil {
		return m.DefaultMicSize
	}
	return 0
}

func (m *LayoutInfo) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

// 麦位音频参数
type MicAudioParam struct {
	MicAudioType         uint32   `protobuf:"varint,1,opt,name=mic_audio_type,json=micAudioType,proto3" json:"mic_audio_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MicAudioParam) Reset()         { *m = MicAudioParam{} }
func (m *MicAudioParam) String() string { return proto.CompactTextString(m) }
func (*MicAudioParam) ProtoMessage()    {}
func (*MicAudioParam) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{15}
}
func (m *MicAudioParam) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MicAudioParam.Unmarshal(m, b)
}
func (m *MicAudioParam) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MicAudioParam.Marshal(b, m, deterministic)
}
func (dst *MicAudioParam) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MicAudioParam.Merge(dst, src)
}
func (m *MicAudioParam) XXX_Size() int {
	return xxx_messageInfo_MicAudioParam.Size(m)
}
func (m *MicAudioParam) XXX_DiscardUnknown() {
	xxx_messageInfo_MicAudioParam.DiscardUnknown(m)
}

var xxx_messageInfo_MicAudioParam proto.InternalMessageInfo

func (m *MicAudioParam) GetMicAudioType() uint32 {
	if m != nil {
		return m.MicAudioType
	}
	return 0
}

// 互相关联的玩法详细类型
type RelationSchemeDetailTypeList struct {
	VirtualType          SchemeVirtualType           `protobuf:"varint,1,opt,name=virtual_type,json=virtualType,proto3,enum=channel_scheme_conf_mgr.SchemeVirtualType" json:"virtual_type,omitempty"`
	SwitchMode           SchemeVirtualTypeSwitchMode `protobuf:"varint,2,opt,name=switch_mode,json=switchMode,proto3,enum=channel_scheme_conf_mgr.SchemeVirtualTypeSwitchMode" json:"switch_mode,omitempty"`
	SchemeDetailTypeList []*RelationSchemeDetailType `protobuf:"bytes,3,rep,name=scheme_detail_type_list,json=schemeDetailTypeList,proto3" json:"scheme_detail_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *RelationSchemeDetailTypeList) Reset()         { *m = RelationSchemeDetailTypeList{} }
func (m *RelationSchemeDetailTypeList) String() string { return proto.CompactTextString(m) }
func (*RelationSchemeDetailTypeList) ProtoMessage()    {}
func (*RelationSchemeDetailTypeList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{16}
}
func (m *RelationSchemeDetailTypeList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationSchemeDetailTypeList.Unmarshal(m, b)
}
func (m *RelationSchemeDetailTypeList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationSchemeDetailTypeList.Marshal(b, m, deterministic)
}
func (dst *RelationSchemeDetailTypeList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationSchemeDetailTypeList.Merge(dst, src)
}
func (m *RelationSchemeDetailTypeList) XXX_Size() int {
	return xxx_messageInfo_RelationSchemeDetailTypeList.Size(m)
}
func (m *RelationSchemeDetailTypeList) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationSchemeDetailTypeList.DiscardUnknown(m)
}

var xxx_messageInfo_RelationSchemeDetailTypeList proto.InternalMessageInfo

func (m *RelationSchemeDetailTypeList) GetVirtualType() SchemeVirtualType {
	if m != nil {
		return m.VirtualType
	}
	return SchemeVirtualType_SCHEME_VIRTUAL_TYPE_NO
}

func (m *RelationSchemeDetailTypeList) GetSwitchMode() SchemeVirtualTypeSwitchMode {
	if m != nil {
		return m.SwitchMode
	}
	return SchemeVirtualTypeSwitchMode_SCHEME_VIRTUAL_TYPE_SWITCH_UNKNOWN
}

func (m *RelationSchemeDetailTypeList) GetSchemeDetailTypeList() []*RelationSchemeDetailType {
	if m != nil {
		return m.SchemeDetailTypeList
	}
	return nil
}

type RelationSchemeDetailType struct {
	DefaultSchemeDetailType uint32   `protobuf:"varint,1,opt,name=default_scheme_detail_type,json=defaultSchemeDetailType,proto3" json:"default_scheme_detail_type,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *RelationSchemeDetailType) Reset()         { *m = RelationSchemeDetailType{} }
func (m *RelationSchemeDetailType) String() string { return proto.CompactTextString(m) }
func (*RelationSchemeDetailType) ProtoMessage()    {}
func (*RelationSchemeDetailType) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{17}
}
func (m *RelationSchemeDetailType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RelationSchemeDetailType.Unmarshal(m, b)
}
func (m *RelationSchemeDetailType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RelationSchemeDetailType.Marshal(b, m, deterministic)
}
func (dst *RelationSchemeDetailType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RelationSchemeDetailType.Merge(dst, src)
}
func (m *RelationSchemeDetailType) XXX_Size() int {
	return xxx_messageInfo_RelationSchemeDetailType.Size(m)
}
func (m *RelationSchemeDetailType) XXX_DiscardUnknown() {
	xxx_messageInfo_RelationSchemeDetailType.DiscardUnknown(m)
}

var xxx_messageInfo_RelationSchemeDetailType proto.InternalMessageInfo

func (m *RelationSchemeDetailType) GetDefaultSchemeDetailType() uint32 {
	if m != nil {
		return m.DefaultSchemeDetailType
	}
	return 0
}

// 玩法扩展信息
type ChannelSchemeExtraConfV2 struct {
	SchemeId                uint32           `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName              string           `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	MaxMemberSize           uint32           `protobuf:"varint,3,opt,name=max_member_size,json=maxMemberSize,proto3" json:"max_member_size,omitempty"`
	MinEnterVersionLimit    []*ClientVersion `protobuf:"bytes,4,rep,name=min_enter_version_limit,json=minEnterVersionLimit,proto3" json:"min_enter_version_limit,omitempty"`
	MinMicVersionLimit      []*ClientVersion `protobuf:"bytes,5,rep,name=min_mic_version_limit,json=minMicVersionLimit,proto3" json:"min_mic_version_limit,omitempty"`
	AutoExitByVersion       bool             `protobuf:"varint,6,opt,name=auto_exit_by_version,json=autoExitByVersion,proto3" json:"auto_exit_by_version,omitempty"`
	AutoExitByMaxSize       bool             `protobuf:"varint,7,opt,name=auto_exit_by_max_size,json=autoExitByMaxSize,proto3" json:"auto_exit_by_max_size,omitempty"`
	MicControl              uint32           `protobuf:"varint,8,opt,name=mic_control,json=micControl,proto3" json:"mic_control,omitempty"`
	UseNewControlWhenSwitch bool             `protobuf:"varint,9,opt,name=use_new_control_when_switch,json=useNewControlWhenSwitch,proto3" json:"use_new_control_when_switch,omitempty"`
	// 主持麦,mvp麦
	ChairMicIdList       []uint32 `protobuf:"varint,10,rep,packed,name=chair_mic_id_list,json=chairMicIdList,proto3" json:"chair_mic_id_list,omitempty"`
	MvpMicIdList         []uint32 `protobuf:"varint,11,rep,packed,name=mvp_mic_id_list,json=mvpMicIdList,proto3" json:"mvp_mic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelSchemeExtraConfV2) Reset()         { *m = ChannelSchemeExtraConfV2{} }
func (m *ChannelSchemeExtraConfV2) String() string { return proto.CompactTextString(m) }
func (*ChannelSchemeExtraConfV2) ProtoMessage()    {}
func (*ChannelSchemeExtraConfV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{18}
}
func (m *ChannelSchemeExtraConfV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSchemeExtraConfV2.Unmarshal(m, b)
}
func (m *ChannelSchemeExtraConfV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSchemeExtraConfV2.Marshal(b, m, deterministic)
}
func (dst *ChannelSchemeExtraConfV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSchemeExtraConfV2.Merge(dst, src)
}
func (m *ChannelSchemeExtraConfV2) XXX_Size() int {
	return xxx_messageInfo_ChannelSchemeExtraConfV2.Size(m)
}
func (m *ChannelSchemeExtraConfV2) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSchemeExtraConfV2.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSchemeExtraConfV2 proto.InternalMessageInfo

func (m *ChannelSchemeExtraConfV2) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *ChannelSchemeExtraConfV2) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *ChannelSchemeExtraConfV2) GetMaxMemberSize() uint32 {
	if m != nil {
		return m.MaxMemberSize
	}
	return 0
}

func (m *ChannelSchemeExtraConfV2) GetMinEnterVersionLimit() []*ClientVersion {
	if m != nil {
		return m.MinEnterVersionLimit
	}
	return nil
}

func (m *ChannelSchemeExtraConfV2) GetMinMicVersionLimit() []*ClientVersion {
	if m != nil {
		return m.MinMicVersionLimit
	}
	return nil
}

func (m *ChannelSchemeExtraConfV2) GetAutoExitByVersion() bool {
	if m != nil {
		return m.AutoExitByVersion
	}
	return false
}

func (m *ChannelSchemeExtraConfV2) GetAutoExitByMaxSize() bool {
	if m != nil {
		return m.AutoExitByMaxSize
	}
	return false
}

func (m *ChannelSchemeExtraConfV2) GetMicControl() uint32 {
	if m != nil {
		return m.MicControl
	}
	return 0
}

func (m *ChannelSchemeExtraConfV2) GetUseNewControlWhenSwitch() bool {
	if m != nil {
		return m.UseNewControlWhenSwitch
	}
	return false
}

func (m *ChannelSchemeExtraConfV2) GetChairMicIdList() []uint32 {
	if m != nil {
		return m.ChairMicIdList
	}
	return nil
}

func (m *ChannelSchemeExtraConfV2) GetMvpMicIdList() []uint32 {
	if m != nil {
		return m.MvpMicIdList
	}
	return nil
}

type ClientVersion struct {
	ClientType           uint32   `protobuf:"varint,1,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClientVersion) Reset()         { *m = ClientVersion{} }
func (m *ClientVersion) String() string { return proto.CompactTextString(m) }
func (*ClientVersion) ProtoMessage()    {}
func (*ClientVersion) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{19}
}
func (m *ClientVersion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClientVersion.Unmarshal(m, b)
}
func (m *ClientVersion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClientVersion.Marshal(b, m, deterministic)
}
func (dst *ClientVersion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClientVersion.Merge(dst, src)
}
func (m *ClientVersion) XXX_Size() int {
	return xxx_messageInfo_ClientVersion.Size(m)
}
func (m *ClientVersion) XXX_DiscardUnknown() {
	xxx_messageInfo_ClientVersion.DiscardUnknown(m)
}

var xxx_messageInfo_ClientVersion proto.InternalMessageInfo

func (m *ClientVersion) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *ClientVersion) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

// 特殊的玩法类型信息：指只能通过和其他玩法类型进行关联绑定的玩法类型，例如开黑文字玩法类型绑定到开黑玩法类型下
type GetAllSpecSchemeDetailTypeConfCacheReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSpecSchemeDetailTypeConfCacheReq) Reset() {
	*m = GetAllSpecSchemeDetailTypeConfCacheReq{}
}
func (m *GetAllSpecSchemeDetailTypeConfCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetAllSpecSchemeDetailTypeConfCacheReq) ProtoMessage()    {}
func (*GetAllSpecSchemeDetailTypeConfCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{20}
}
func (m *GetAllSpecSchemeDetailTypeConfCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheReq.Unmarshal(m, b)
}
func (m *GetAllSpecSchemeDetailTypeConfCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetAllSpecSchemeDetailTypeConfCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheReq.Merge(dst, src)
}
func (m *GetAllSpecSchemeDetailTypeConfCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheReq.Size(m)
}
func (m *GetAllSpecSchemeDetailTypeConfCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheReq proto.InternalMessageInfo

type SpeChannelSchemeTypeConf struct {
	SchemeDetailType        uint32           `protobuf:"varint,1,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	Layout                  *LayoutInfo      `protobuf:"bytes,2,opt,name=layout,proto3" json:"layout,omitempty"`
	MicAudio                *MicAudioParam   `protobuf:"bytes,3,opt,name=mic_audio,json=micAudio,proto3" json:"mic_audio,omitempty"`
	MaxMemberSize           uint32           `protobuf:"varint,4,opt,name=max_member_size,json=maxMemberSize,proto3" json:"max_member_size,omitempty"`
	MinEnterVersionLimit    []*ClientVersion `protobuf:"bytes,5,rep,name=min_enter_version_limit,json=minEnterVersionLimit,proto3" json:"min_enter_version_limit,omitempty"`
	MinMicVersionLimit      []*ClientVersion `protobuf:"bytes,6,rep,name=min_mic_version_limit,json=minMicVersionLimit,proto3" json:"min_mic_version_limit,omitempty"`
	AutoExitByVersion       bool             `protobuf:"varint,7,opt,name=auto_exit_by_version,json=autoExitByVersion,proto3" json:"auto_exit_by_version,omitempty"`
	AutoExitByMaxSize       bool             `protobuf:"varint,8,opt,name=auto_exit_by_max_size,json=autoExitByMaxSize,proto3" json:"auto_exit_by_max_size,omitempty"`
	MicControl              uint32           `protobuf:"varint,9,opt,name=mic_control,json=micControl,proto3" json:"mic_control,omitempty"`
	UseNewControlWhenSwitch bool             `protobuf:"varint,10,opt,name=use_new_control_when_switch,json=useNewControlWhenSwitch,proto3" json:"use_new_control_when_switch,omitempty"`
	// 主持麦,mvp麦
	ChairMicIdList           []uint32 `protobuf:"varint,11,rep,packed,name=chair_mic_id_list,json=chairMicIdList,proto3" json:"chair_mic_id_list,omitempty"`
	MvpMicIdList             []uint32 `protobuf:"varint,12,rep,packed,name=mvp_mic_id_list,json=mvpMicIdList,proto3" json:"mvp_mic_id_list,omitempty"`
	FallbackSchemeDetailType uint32   `protobuf:"varint,13,opt,name=fallback_scheme_detail_type,json=fallbackSchemeDetailType,proto3" json:"fallback_scheme_detail_type,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *SpeChannelSchemeTypeConf) Reset()         { *m = SpeChannelSchemeTypeConf{} }
func (m *SpeChannelSchemeTypeConf) String() string { return proto.CompactTextString(m) }
func (*SpeChannelSchemeTypeConf) ProtoMessage()    {}
func (*SpeChannelSchemeTypeConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{21}
}
func (m *SpeChannelSchemeTypeConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpeChannelSchemeTypeConf.Unmarshal(m, b)
}
func (m *SpeChannelSchemeTypeConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpeChannelSchemeTypeConf.Marshal(b, m, deterministic)
}
func (dst *SpeChannelSchemeTypeConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpeChannelSchemeTypeConf.Merge(dst, src)
}
func (m *SpeChannelSchemeTypeConf) XXX_Size() int {
	return xxx_messageInfo_SpeChannelSchemeTypeConf.Size(m)
}
func (m *SpeChannelSchemeTypeConf) XXX_DiscardUnknown() {
	xxx_messageInfo_SpeChannelSchemeTypeConf.DiscardUnknown(m)
}

var xxx_messageInfo_SpeChannelSchemeTypeConf proto.InternalMessageInfo

func (m *SpeChannelSchemeTypeConf) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

func (m *SpeChannelSchemeTypeConf) GetLayout() *LayoutInfo {
	if m != nil {
		return m.Layout
	}
	return nil
}

func (m *SpeChannelSchemeTypeConf) GetMicAudio() *MicAudioParam {
	if m != nil {
		return m.MicAudio
	}
	return nil
}

func (m *SpeChannelSchemeTypeConf) GetMaxMemberSize() uint32 {
	if m != nil {
		return m.MaxMemberSize
	}
	return 0
}

func (m *SpeChannelSchemeTypeConf) GetMinEnterVersionLimit() []*ClientVersion {
	if m != nil {
		return m.MinEnterVersionLimit
	}
	return nil
}

func (m *SpeChannelSchemeTypeConf) GetMinMicVersionLimit() []*ClientVersion {
	if m != nil {
		return m.MinMicVersionLimit
	}
	return nil
}

func (m *SpeChannelSchemeTypeConf) GetAutoExitByVersion() bool {
	if m != nil {
		return m.AutoExitByVersion
	}
	return false
}

func (m *SpeChannelSchemeTypeConf) GetAutoExitByMaxSize() bool {
	if m != nil {
		return m.AutoExitByMaxSize
	}
	return false
}

func (m *SpeChannelSchemeTypeConf) GetMicControl() uint32 {
	if m != nil {
		return m.MicControl
	}
	return 0
}

func (m *SpeChannelSchemeTypeConf) GetUseNewControlWhenSwitch() bool {
	if m != nil {
		return m.UseNewControlWhenSwitch
	}
	return false
}

func (m *SpeChannelSchemeTypeConf) GetChairMicIdList() []uint32 {
	if m != nil {
		return m.ChairMicIdList
	}
	return nil
}

func (m *SpeChannelSchemeTypeConf) GetMvpMicIdList() []uint32 {
	if m != nil {
		return m.MvpMicIdList
	}
	return nil
}

func (m *SpeChannelSchemeTypeConf) GetFallbackSchemeDetailType() uint32 {
	if m != nil {
		return m.FallbackSchemeDetailType
	}
	return 0
}

type GetAllSpecSchemeDetailTypeConfCacheResp struct {
	ConfList             []*SpeChannelSchemeTypeConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetAllSpecSchemeDetailTypeConfCacheResp) Reset() {
	*m = GetAllSpecSchemeDetailTypeConfCacheResp{}
}
func (m *GetAllSpecSchemeDetailTypeConfCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetAllSpecSchemeDetailTypeConfCacheResp) ProtoMessage()    {}
func (*GetAllSpecSchemeDetailTypeConfCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{22}
}
func (m *GetAllSpecSchemeDetailTypeConfCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheResp.Unmarshal(m, b)
}
func (m *GetAllSpecSchemeDetailTypeConfCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetAllSpecSchemeDetailTypeConfCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheResp.Merge(dst, src)
}
func (m *GetAllSpecSchemeDetailTypeConfCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheResp.Size(m)
}
func (m *GetAllSpecSchemeDetailTypeConfCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSpecSchemeDetailTypeConfCacheResp proto.InternalMessageInfo

func (m *GetAllSpecSchemeDetailTypeConfCacheResp) GetConfList() []*SpeChannelSchemeTypeConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type GetAllChannelSchemeBaseConfCacheReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllChannelSchemeBaseConfCacheReq) Reset()         { *m = GetAllChannelSchemeBaseConfCacheReq{} }
func (m *GetAllChannelSchemeBaseConfCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelSchemeBaseConfCacheReq) ProtoMessage()    {}
func (*GetAllChannelSchemeBaseConfCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{23}
}
func (m *GetAllChannelSchemeBaseConfCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelSchemeBaseConfCacheReq.Unmarshal(m, b)
}
func (m *GetAllChannelSchemeBaseConfCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelSchemeBaseConfCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelSchemeBaseConfCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelSchemeBaseConfCacheReq.Merge(dst, src)
}
func (m *GetAllChannelSchemeBaseConfCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelSchemeBaseConfCacheReq.Size(m)
}
func (m *GetAllChannelSchemeBaseConfCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelSchemeBaseConfCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelSchemeBaseConfCacheReq proto.InternalMessageInfo

type GetAllChannelSchemeBaseConfCacheResp struct {
	BaseConfList         []*ChannelSchemeBaseConf `protobuf:"bytes,1,rep,name=base_conf_list,json=baseConfList,proto3" json:"base_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAllChannelSchemeBaseConfCacheResp) Reset()         { *m = GetAllChannelSchemeBaseConfCacheResp{} }
func (m *GetAllChannelSchemeBaseConfCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelSchemeBaseConfCacheResp) ProtoMessage()    {}
func (*GetAllChannelSchemeBaseConfCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{24}
}
func (m *GetAllChannelSchemeBaseConfCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelSchemeBaseConfCacheResp.Unmarshal(m, b)
}
func (m *GetAllChannelSchemeBaseConfCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelSchemeBaseConfCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelSchemeBaseConfCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelSchemeBaseConfCacheResp.Merge(dst, src)
}
func (m *GetAllChannelSchemeBaseConfCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelSchemeBaseConfCacheResp.Size(m)
}
func (m *GetAllChannelSchemeBaseConfCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelSchemeBaseConfCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelSchemeBaseConfCacheResp proto.InternalMessageInfo

func (m *GetAllChannelSchemeBaseConfCacheResp) GetBaseConfList() []*ChannelSchemeBaseConf {
	if m != nil {
		return m.BaseConfList
	}
	return nil
}

type GetAllChannelSchemeExtraConfCacheV2Req struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllChannelSchemeExtraConfCacheV2Req) Reset() {
	*m = GetAllChannelSchemeExtraConfCacheV2Req{}
}
func (m *GetAllChannelSchemeExtraConfCacheV2Req) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelSchemeExtraConfCacheV2Req) ProtoMessage()    {}
func (*GetAllChannelSchemeExtraConfCacheV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{25}
}
func (m *GetAllChannelSchemeExtraConfCacheV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Req.Unmarshal(m, b)
}
func (m *GetAllChannelSchemeExtraConfCacheV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Req.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelSchemeExtraConfCacheV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Req.Merge(dst, src)
}
func (m *GetAllChannelSchemeExtraConfCacheV2Req) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Req.Size(m)
}
func (m *GetAllChannelSchemeExtraConfCacheV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Req proto.InternalMessageInfo

type GetAllChannelSchemeExtraConfCacheV2Resp struct {
	ExtraConfList        []*ChannelSchemeExtraConfV2 `protobuf:"bytes,1,rep,name=extra_conf_list,json=extraConfList,proto3" json:"extra_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetAllChannelSchemeExtraConfCacheV2Resp) Reset() {
	*m = GetAllChannelSchemeExtraConfCacheV2Resp{}
}
func (m *GetAllChannelSchemeExtraConfCacheV2Resp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelSchemeExtraConfCacheV2Resp) ProtoMessage()    {}
func (*GetAllChannelSchemeExtraConfCacheV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9, []int{26}
}
func (m *GetAllChannelSchemeExtraConfCacheV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Resp.Unmarshal(m, b)
}
func (m *GetAllChannelSchemeExtraConfCacheV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Resp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelSchemeExtraConfCacheV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Resp.Merge(dst, src)
}
func (m *GetAllChannelSchemeExtraConfCacheV2Resp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Resp.Size(m)
}
func (m *GetAllChannelSchemeExtraConfCacheV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelSchemeExtraConfCacheV2Resp proto.InternalMessageInfo

func (m *GetAllChannelSchemeExtraConfCacheV2Resp) GetExtraConfList() []*ChannelSchemeExtraConfV2 {
	if m != nil {
		return m.ExtraConfList
	}
	return nil
}

func init() {
	proto.RegisterType((*CreateChannelSchemeReq)(nil), "channel_scheme_conf_mgr.CreateChannelSchemeReq")
	proto.RegisterType((*CreateChannelSchemeResp)(nil), "channel_scheme_conf_mgr.CreateChannelSchemeResp")
	proto.RegisterType((*ModifyChannelSchemeReq)(nil), "channel_scheme_conf_mgr.ModifyChannelSchemeReq")
	proto.RegisterType((*ModifyChannelSchemeResp)(nil), "channel_scheme_conf_mgr.ModifyChannelSchemeResp")
	proto.RegisterType((*DeleteChannelSchemeReq)(nil), "channel_scheme_conf_mgr.DeleteChannelSchemeReq")
	proto.RegisterType((*DeleteChannelSchemeResp)(nil), "channel_scheme_conf_mgr.DeleteChannelSchemeResp")
	proto.RegisterType((*GetChannelSchemeBaseConfListReq)(nil), "channel_scheme_conf_mgr.GetChannelSchemeBaseConfListReq")
	proto.RegisterType((*GetChannelSchemeBaseConfListResp)(nil), "channel_scheme_conf_mgr.GetChannelSchemeBaseConfListResp")
	proto.RegisterType((*GetChannelSchemeConfCacheReq)(nil), "channel_scheme_conf_mgr.GetChannelSchemeConfCacheReq")
	proto.RegisterType((*GetChannelSchemeConfCacheResp)(nil), "channel_scheme_conf_mgr.GetChannelSchemeConfCacheResp")
	proto.RegisterType((*GetAllChannelSchemeConfCacheReq)(nil), "channel_scheme_conf_mgr.GetAllChannelSchemeConfCacheReq")
	proto.RegisterType((*GetAllChannelSchemeConfCacheResp)(nil), "channel_scheme_conf_mgr.GetAllChannelSchemeConfCacheResp")
	proto.RegisterType((*ChannelSchemeConf)(nil), "channel_scheme_conf_mgr.ChannelSchemeConf")
	proto.RegisterType((*ChannelSchemeBaseConf)(nil), "channel_scheme_conf_mgr.ChannelSchemeBaseConf")
	proto.RegisterType((*LayoutInfo)(nil), "channel_scheme_conf_mgr.LayoutInfo")
	proto.RegisterType((*MicAudioParam)(nil), "channel_scheme_conf_mgr.MicAudioParam")
	proto.RegisterType((*RelationSchemeDetailTypeList)(nil), "channel_scheme_conf_mgr.RelationSchemeDetailTypeList")
	proto.RegisterType((*RelationSchemeDetailType)(nil), "channel_scheme_conf_mgr.RelationSchemeDetailType")
	proto.RegisterType((*ChannelSchemeExtraConfV2)(nil), "channel_scheme_conf_mgr.ChannelSchemeExtraConfV2")
	proto.RegisterType((*ClientVersion)(nil), "channel_scheme_conf_mgr.ClientVersion")
	proto.RegisterType((*GetAllSpecSchemeDetailTypeConfCacheReq)(nil), "channel_scheme_conf_mgr.GetAllSpecSchemeDetailTypeConfCacheReq")
	proto.RegisterType((*SpeChannelSchemeTypeConf)(nil), "channel_scheme_conf_mgr.SpeChannelSchemeTypeConf")
	proto.RegisterType((*GetAllSpecSchemeDetailTypeConfCacheResp)(nil), "channel_scheme_conf_mgr.GetAllSpecSchemeDetailTypeConfCacheResp")
	proto.RegisterType((*GetAllChannelSchemeBaseConfCacheReq)(nil), "channel_scheme_conf_mgr.GetAllChannelSchemeBaseConfCacheReq")
	proto.RegisterType((*GetAllChannelSchemeBaseConfCacheResp)(nil), "channel_scheme_conf_mgr.GetAllChannelSchemeBaseConfCacheResp")
	proto.RegisterType((*GetAllChannelSchemeExtraConfCacheV2Req)(nil), "channel_scheme_conf_mgr.GetAllChannelSchemeExtraConfCacheV2Req")
	proto.RegisterType((*GetAllChannelSchemeExtraConfCacheV2Resp)(nil), "channel_scheme_conf_mgr.GetAllChannelSchemeExtraConfCacheV2Resp")
	proto.RegisterEnum("channel_scheme_conf_mgr.SchemeVirtualType", SchemeVirtualType_name, SchemeVirtualType_value)
	proto.RegisterEnum("channel_scheme_conf_mgr.SchemeVirtualTypeSwitchMode", SchemeVirtualTypeSwitchMode_name, SchemeVirtualTypeSwitchMode_value)
	proto.RegisterEnum("channel_scheme_conf_mgr.EMicControlBit", EMicControlBit_name, EMicControlBit_value)
	proto.RegisterEnum("channel_scheme_conf_mgr.SchemeSvrDetailType", SchemeSvrDetailType_name, SchemeSvrDetailType_value)
	proto.RegisterEnum("channel_scheme_conf_mgr.SchemeChannelCategory", SchemeChannelCategory_name, SchemeChannelCategory_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelSchemeConfBaseClient is the client API for ChannelSchemeConfBase service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelSchemeConfBaseClient interface {
	// 给运营后台用的增删改查
	CreateChannelScheme(ctx context.Context, in *CreateChannelSchemeReq, opts ...grpc.CallOption) (*CreateChannelSchemeResp, error)
	ModifyChannelScheme(ctx context.Context, in *ModifyChannelSchemeReq, opts ...grpc.CallOption) (*ModifyChannelSchemeResp, error)
	DeleteChannelScheme(ctx context.Context, in *DeleteChannelSchemeReq, opts ...grpc.CallOption) (*DeleteChannelSchemeResp, error)
	GetChannelSchemeBaseConfList(ctx context.Context, in *GetChannelSchemeBaseConfListReq, opts ...grpc.CallOption) (*GetChannelSchemeBaseConfListResp, error)
	// cache接口
	GetChannelSchemeConfCache(ctx context.Context, in *GetChannelSchemeConfCacheReq, opts ...grpc.CallOption) (*GetChannelSchemeConfCacheResp, error)
	GetAllChannelSchemeConfCache(ctx context.Context, in *GetAllChannelSchemeConfCacheReq, opts ...grpc.CallOption) (*GetAllChannelSchemeConfCacheResp, error)
	GetAllSpecSchemeDetailTypeConfCache(ctx context.Context, in *GetAllSpecSchemeDetailTypeConfCacheReq, opts ...grpc.CallOption) (*GetAllSpecSchemeDetailTypeConfCacheResp, error)
	// 下面的接口后续逐渐废弃
	// rpc GetChannelSchemeBaseConfCache (GetChannelSchemeBaseConfCacheReq) returns (GetChannelSchemeBaseConfCacheResp){
	// }
	// rpc GetChannelSchemeExtraConfCache(GetChannelSchemeExtraConfCacheReq) returns (GetChannelSchemeExtraConfCacheResp){
	// }
	GetAllChannelSchemeBaseConfCache(ctx context.Context, in *GetAllChannelSchemeBaseConfCacheReq, opts ...grpc.CallOption) (*GetAllChannelSchemeBaseConfCacheResp, error)
	GetAllChannelSchemeExtraConfCacheV2(ctx context.Context, in *GetAllChannelSchemeExtraConfCacheV2Req, opts ...grpc.CallOption) (*GetAllChannelSchemeExtraConfCacheV2Resp, error)
}

type channelSchemeConfBaseClient struct {
	cc *grpc.ClientConn
}

func NewChannelSchemeConfBaseClient(cc *grpc.ClientConn) ChannelSchemeConfBaseClient {
	return &channelSchemeConfBaseClient{cc}
}

func (c *channelSchemeConfBaseClient) CreateChannelScheme(ctx context.Context, in *CreateChannelSchemeReq, opts ...grpc.CallOption) (*CreateChannelSchemeResp, error) {
	out := new(CreateChannelSchemeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/CreateChannelScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) ModifyChannelScheme(ctx context.Context, in *ModifyChannelSchemeReq, opts ...grpc.CallOption) (*ModifyChannelSchemeResp, error) {
	out := new(ModifyChannelSchemeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/ModifyChannelScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) DeleteChannelScheme(ctx context.Context, in *DeleteChannelSchemeReq, opts ...grpc.CallOption) (*DeleteChannelSchemeResp, error) {
	out := new(DeleteChannelSchemeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/DeleteChannelScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) GetChannelSchemeBaseConfList(ctx context.Context, in *GetChannelSchemeBaseConfListReq, opts ...grpc.CallOption) (*GetChannelSchemeBaseConfListResp, error) {
	out := new(GetChannelSchemeBaseConfListResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetChannelSchemeBaseConfList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) GetChannelSchemeConfCache(ctx context.Context, in *GetChannelSchemeConfCacheReq, opts ...grpc.CallOption) (*GetChannelSchemeConfCacheResp, error) {
	out := new(GetChannelSchemeConfCacheResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetChannelSchemeConfCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) GetAllChannelSchemeConfCache(ctx context.Context, in *GetAllChannelSchemeConfCacheReq, opts ...grpc.CallOption) (*GetAllChannelSchemeConfCacheResp, error) {
	out := new(GetAllChannelSchemeConfCacheResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllChannelSchemeConfCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) GetAllSpecSchemeDetailTypeConfCache(ctx context.Context, in *GetAllSpecSchemeDetailTypeConfCacheReq, opts ...grpc.CallOption) (*GetAllSpecSchemeDetailTypeConfCacheResp, error) {
	out := new(GetAllSpecSchemeDetailTypeConfCacheResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllSpecSchemeDetailTypeConfCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) GetAllChannelSchemeBaseConfCache(ctx context.Context, in *GetAllChannelSchemeBaseConfCacheReq, opts ...grpc.CallOption) (*GetAllChannelSchemeBaseConfCacheResp, error) {
	out := new(GetAllChannelSchemeBaseConfCacheResp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllChannelSchemeBaseConfCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeConfBaseClient) GetAllChannelSchemeExtraConfCacheV2(ctx context.Context, in *GetAllChannelSchemeExtraConfCacheV2Req, opts ...grpc.CallOption) (*GetAllChannelSchemeExtraConfCacheV2Resp, error) {
	out := new(GetAllChannelSchemeExtraConfCacheV2Resp)
	err := c.cc.Invoke(ctx, "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllChannelSchemeExtraConfCacheV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelSchemeConfBaseServer is the server API for ChannelSchemeConfBase service.
type ChannelSchemeConfBaseServer interface {
	// 给运营后台用的增删改查
	CreateChannelScheme(context.Context, *CreateChannelSchemeReq) (*CreateChannelSchemeResp, error)
	ModifyChannelScheme(context.Context, *ModifyChannelSchemeReq) (*ModifyChannelSchemeResp, error)
	DeleteChannelScheme(context.Context, *DeleteChannelSchemeReq) (*DeleteChannelSchemeResp, error)
	GetChannelSchemeBaseConfList(context.Context, *GetChannelSchemeBaseConfListReq) (*GetChannelSchemeBaseConfListResp, error)
	// cache接口
	GetChannelSchemeConfCache(context.Context, *GetChannelSchemeConfCacheReq) (*GetChannelSchemeConfCacheResp, error)
	GetAllChannelSchemeConfCache(context.Context, *GetAllChannelSchemeConfCacheReq) (*GetAllChannelSchemeConfCacheResp, error)
	GetAllSpecSchemeDetailTypeConfCache(context.Context, *GetAllSpecSchemeDetailTypeConfCacheReq) (*GetAllSpecSchemeDetailTypeConfCacheResp, error)
	// 下面的接口后续逐渐废弃
	// rpc GetChannelSchemeBaseConfCache (GetChannelSchemeBaseConfCacheReq) returns (GetChannelSchemeBaseConfCacheResp){
	// }
	// rpc GetChannelSchemeExtraConfCache(GetChannelSchemeExtraConfCacheReq) returns (GetChannelSchemeExtraConfCacheResp){
	// }
	GetAllChannelSchemeBaseConfCache(context.Context, *GetAllChannelSchemeBaseConfCacheReq) (*GetAllChannelSchemeBaseConfCacheResp, error)
	GetAllChannelSchemeExtraConfCacheV2(context.Context, *GetAllChannelSchemeExtraConfCacheV2Req) (*GetAllChannelSchemeExtraConfCacheV2Resp, error)
}

func RegisterChannelSchemeConfBaseServer(s *grpc.Server, srv ChannelSchemeConfBaseServer) {
	s.RegisterService(&_ChannelSchemeConfBase_serviceDesc, srv)
}

func _ChannelSchemeConfBase_CreateChannelScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChannelSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).CreateChannelScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/CreateChannelScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).CreateChannelScheme(ctx, req.(*CreateChannelSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_ModifyChannelScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyChannelSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).ModifyChannelScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/ModifyChannelScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).ModifyChannelScheme(ctx, req.(*ModifyChannelSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_DeleteChannelScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChannelSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).DeleteChannelScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/DeleteChannelScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).DeleteChannelScheme(ctx, req.(*DeleteChannelSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_GetChannelSchemeBaseConfList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelSchemeBaseConfListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).GetChannelSchemeBaseConfList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetChannelSchemeBaseConfList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).GetChannelSchemeBaseConfList(ctx, req.(*GetChannelSchemeBaseConfListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_GetChannelSchemeConfCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelSchemeConfCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).GetChannelSchemeConfCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetChannelSchemeConfCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).GetChannelSchemeConfCache(ctx, req.(*GetChannelSchemeConfCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_GetAllChannelSchemeConfCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChannelSchemeConfCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).GetAllChannelSchemeConfCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllChannelSchemeConfCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).GetAllChannelSchemeConfCache(ctx, req.(*GetAllChannelSchemeConfCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_GetAllSpecSchemeDetailTypeConfCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSpecSchemeDetailTypeConfCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).GetAllSpecSchemeDetailTypeConfCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllSpecSchemeDetailTypeConfCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).GetAllSpecSchemeDetailTypeConfCache(ctx, req.(*GetAllSpecSchemeDetailTypeConfCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_GetAllChannelSchemeBaseConfCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChannelSchemeBaseConfCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).GetAllChannelSchemeBaseConfCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllChannelSchemeBaseConfCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).GetAllChannelSchemeBaseConfCache(ctx, req.(*GetAllChannelSchemeBaseConfCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelSchemeConfBase_GetAllChannelSchemeExtraConfCacheV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChannelSchemeExtraConfCacheV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeConfBaseServer).GetAllChannelSchemeExtraConfCacheV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme_conf_mgr.ChannelSchemeConfBase/GetAllChannelSchemeExtraConfCacheV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeConfBaseServer).GetAllChannelSchemeExtraConfCacheV2(ctx, req.(*GetAllChannelSchemeExtraConfCacheV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelSchemeConfBase_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_scheme_conf_mgr.ChannelSchemeConfBase",
	HandlerType: (*ChannelSchemeConfBaseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateChannelScheme",
			Handler:    _ChannelSchemeConfBase_CreateChannelScheme_Handler,
		},
		{
			MethodName: "ModifyChannelScheme",
			Handler:    _ChannelSchemeConfBase_ModifyChannelScheme_Handler,
		},
		{
			MethodName: "DeleteChannelScheme",
			Handler:    _ChannelSchemeConfBase_DeleteChannelScheme_Handler,
		},
		{
			MethodName: "GetChannelSchemeBaseConfList",
			Handler:    _ChannelSchemeConfBase_GetChannelSchemeBaseConfList_Handler,
		},
		{
			MethodName: "GetChannelSchemeConfCache",
			Handler:    _ChannelSchemeConfBase_GetChannelSchemeConfCache_Handler,
		},
		{
			MethodName: "GetAllChannelSchemeConfCache",
			Handler:    _ChannelSchemeConfBase_GetAllChannelSchemeConfCache_Handler,
		},
		{
			MethodName: "GetAllSpecSchemeDetailTypeConfCache",
			Handler:    _ChannelSchemeConfBase_GetAllSpecSchemeDetailTypeConfCache_Handler,
		},
		{
			MethodName: "GetAllChannelSchemeBaseConfCache",
			Handler:    _ChannelSchemeConfBase_GetAllChannelSchemeBaseConfCache_Handler,
		},
		{
			MethodName: "GetAllChannelSchemeExtraConfCacheV2",
			Handler:    _ChannelSchemeConfBase_GetAllChannelSchemeExtraConfCacheV2_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-scheme-conf-mgr/channel-scheme-conf-base.proto",
}

func init() {
	proto.RegisterFile("channel-scheme-conf-mgr/channel-scheme-conf-base.proto", fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9)
}

var fileDescriptor_channel_scheme_conf_base_ac6934da682a61b9 = []byte{
	// 2155 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x5a, 0x4f, 0x53, 0xe3, 0xc8,
	0x15, 0x47, 0xfc, 0xb5, 0x9f, 0x31, 0x23, 0x7a, 0x60, 0xf0, 0x00, 0x33, 0xc3, 0x6a, 0xfe, 0xb1,
	0x6c, 0x06, 0x76, 0x99, 0xcc, 0x26, 0x5b, 0xb3, 0x9b, 0xc4, 0xc8, 0x82, 0x51, 0xb0, 0x25, 0x47,
	0x96, 0x61, 0x27, 0x55, 0xa9, 0x2e, 0x21, 0x37, 0xa0, 0x5a, 0x4b, 0x72, 0x24, 0xc1, 0xc0, 0x54,
	0x72, 0x49, 0xe5, 0x98, 0xca, 0x2d, 0xa7, 0x1c, 0x52, 0xb9, 0xe4, 0x90, 0xc3, 0x1e, 0xf2, 0x29,
	0x72, 0xca, 0x47, 0xc9, 0x57, 0x48, 0xaa, 0x5b, 0x92, 0xb1, 0x8d, 0x24, 0xe3, 0xa1, 0xa6, 0x72,
	0xc9, 0x0d, 0xf5, 0xfb, 0xbd, 0xd7, 0xef, 0xfd, 0xfa, 0xbd, 0xd7, 0xdd, 0x6e, 0xe0, 0x4b, 0xf3,
	0xd4, 0x70, 0x1c, 0xd2, 0x7e, 0xe1, 0x9b, 0xa7, 0xc4, 0x26, 0x2f, 0x4c, 0xd7, 0x39, 0x7e, 0x61,
	0x9f, 0x78, 0x5b, 0x49, 0xe3, 0x47, 0x86, 0x4f, 0x36, 0x3b, 0x9e, 0x1b, 0xb8, 0x68, 0x29, 0x92,
	0xe3, 0x50, 0x8e, 0xa9, 0x1c, 0xdb, 0x27, 0x9e, 0xf0, 0xb7, 0x71, 0xb8, 0x27, 0x7a, 0xc4, 0x08,
	0x88, 0x18, 0x22, 0x1a, 0x0c, 0xa0, 0x91, 0x5f, 0xa3, 0x15, 0xc8, 0x47, 0x68, 0xab, 0x55, 0xe2,
	0xd6, 0xb8, 0xf5, 0xa2, 0x96, 0x0b, 0x07, 0xe4, 0x16, 0x7a, 0x04, 0x85, 0x48, 0xe8, 0x18, 0x36,
	0x29, 0x8d, 0xaf, 0x71, 0xeb, 0x79, 0x0d, 0xc2, 0x21, 0xc5, 0xb0, 0x49, 0x0f, 0xc0, 0x32, 0x5d,
	0xa7, 0x34, 0xd1, 0x0b, 0x90, 0x4d, 0xd7, 0xe9, 0x01, 0x04, 0x97, 0x1d, 0x52, 0x9a, 0x64, 0x13,
	0x44, 0x00, 0xfd, 0xb2, 0x43, 0xd0, 0x67, 0x30, 0x7f, 0x74, 0xe6, 0x5b, 0x0e, 0xf1, 0x7d, 0x6c,
	0x1a, 0x01, 0x39, 0x71, 0xbd, 0xcb, 0xd2, 0x14, 0x83, 0xf1, 0xb1, 0x40, 0x8c, 0xc6, 0xd1, 0x6b,
	0x58, 0x6e, 0x91, 0x63, 0xe3, 0xac, 0x1d, 0xc4, 0x21, 0xb6, 0x48, 0x60, 0x58, 0xed, 0xd0, 0xf8,
	0x34, 0xd3, 0x5a, 0x8a, 0x10, 0x61, 0x88, 0x15, 0x26, 0x67, 0x33, 0x09, 0x50, 0x0c, 0x4e, 0x2d,
	0xaf, 0x85, 0x8f, 0x2c, 0xa7, 0x45, 0xa3, 0x9d, 0x61, 0xf8, 0x02, 0x1b, 0xdc, 0xb1, 0x9c, 0x96,
	0xdc, 0x12, 0x7e, 0x01, 0x4b, 0x89, 0x3c, 0xf9, 0x9d, 0x6c, 0xa2, 0xee, 0x43, 0xce, 0xb6, 0x4c,
	0x6c, 0xbb, 0xad, 0x90, 0xa5, 0xa2, 0x36, 0x63, 0x5b, 0x66, 0xcd, 0x6d, 0x11, 0xc6, 0x7d, 0xcd,
	0x6d, 0x59, 0xc7, 0x97, 0xff, 0xe7, 0x7e, 0x18, 0xf7, 0x89, 0x3c, 0xdd, 0x82, 0xfb, 0x57, 0x70,
	0xaf, 0x42, 0xda, 0x64, 0xc4, 0xb4, 0x17, 0xee, 0xc3, 0x52, 0xa2, 0x9a, 0xdf, 0x11, 0xde, 0xc3,
	0xa3, 0x3d, 0x12, 0xf4, 0x8d, 0xef, 0x18, 0x3e, 0x11, 0x5d, 0xe7, 0xb8, 0x6a, 0xf9, 0x01, 0x35,
	0x3d, 0xb0, 0x70, 0xdc, 0xb5, 0x85, 0x5b, 0x81, 0x7c, 0xc7, 0x38, 0x21, 0xd8, 0xb7, 0xde, 0xc7,
	0x1e, 0xe7, 0xe8, 0x40, 0xc3, 0x7a, 0x4f, 0x68, 0x34, 0x4c, 0xe8, 0x9c, 0xd9, 0x6c, 0x49, 0x8b,
	0xda, 0x0c, 0xfd, 0x56, 0xce, 0x6c, 0xe1, 0x8f, 0x1c, 0xac, 0x65, 0x4f, 0xee, 0x77, 0x90, 0x0e,
	0x73, 0xb4, 0x23, 0x84, 0xb5, 0xdf, 0xb6, 0xfc, 0xa0, 0xc4, 0xad, 0x4d, 0xac, 0x17, 0xb6, 0x37,
	0x37, 0x53, 0x9a, 0xc3, 0x66, 0xa2, 0x3d, 0x6d, 0xf6, 0xa8, 0xc7, 0x32, 0x5a, 0x80, 0xa9, 0xc0,
	0x0d, 0x8c, 0x76, 0xe4, 0x6e, 0xf8, 0x21, 0x58, 0xb0, 0x3a, 0xe8, 0x0f, 0xd5, 0x10, 0x0d, 0xf3,
	0x74, 0x78, 0x7e, 0xff, 0x00, 0x50, 0x42, 0x1e, 0x85, 0xf6, 0x79, 0x7f, 0x20, 0x81, 0x04, 0x0c,
	0x0f, 0x32, 0xa6, 0xf2, 0x3b, 0xe8, 0x27, 0x30, 0x49, 0x23, 0x62, 0xd3, 0x14, 0xb6, 0x37, 0x6e,
	0x16, 0x2d, 0x8b, 0x94, 0xe9, 0x09, 0x9f, 0xb0, 0x85, 0x2d, 0xb7, 0xdb, 0xa9, 0xe1, 0x08, 0xdf,
	0x31, 0xfa, 0x33, 0x20, 0x7e, 0x07, 0xed, 0x41, 0x7e, 0x90, 0xf9, 0x51, 0x7c, 0xc9, 0x99, 0x11,
	0xe3, 0xc2, 0x3f, 0x38, 0x98, 0xbf, 0x26, 0x47, 0xfb, 0x90, 0xef, 0xae, 0x6e, 0x14, 0xea, 0xa8,
	0x0b, 0x9b, 0x8b, 0x17, 0x16, 0xd5, 0x01, 0xc8, 0x45, 0xe0, 0x19, 0xa1, 0xb5, 0x71, 0x66, 0xed,
	0x8b, 0x9b, 0x59, 0x93, 0xa8, 0x1e, 0x35, 0x72, 0xb0, 0xad, 0xe5, 0x49, 0xfc, 0x21, 0xfc, 0x6e,
	0x1a, 0x16, 0x13, 0x67, 0xfd, 0x9f, 0xb7, 0xba, 0x63, 0x58, 0x8a, 0x03, 0x8a, 0xe2, 0xeb, 0x6b,
	0x78, 0x73, 0x19, 0x34, 0x46, 0xf4, 0x87, 0xd2, 0xb8, 0x1d, 0x6a, 0x8b, 0x7e, 0xd2, 0x30, 0x7a,
	0x0d, 0xd3, 0x6d, 0xe3, 0xd2, 0x3d, 0x0b, 0x58, 0x47, 0x2c, 0x6c, 0x3f, 0x4e, 0x35, 0x5b, 0x65,
	0x30, 0xd9, 0x39, 0x76, 0xb5, 0x48, 0x05, 0x89, 0x90, 0xa7, 0x9d, 0xcc, 0x38, 0x6b, 0x59, 0x2e,
	0xeb, 0x90, 0x85, 0xed, 0x67, 0xa9, 0xfa, 0x35, 0xcb, 0x2c, 0x53, 0x60, 0xdd, 0xf0, 0x0c, 0x5b,
	0xa3, 0x2d, 0x90, 0x7d, 0xa2, 0x97, 0x70, 0x2f, 0x82, 0xfa, 0xe7, 0x5e, 0x5f, 0x6d, 0xe5, 0x18,
	0x2b, 0x77, 0x43, 0x69, 0xe3, 0xdc, 0xeb, 0xe9, 0xcf, 0x89, 0x3b, 0x41, 0xfe, 0x83, 0x76, 0x02,
	0xc8, 0xde, 0x09, 0x7e, 0x0b, 0x6b, 0x1e, 0x69, 0x1b, 0x81, 0xe5, 0x3a, 0x09, 0xda, 0x61, 0xdd,
	0x14, 0x58, 0xe8, 0xaf, 0x52, 0x43, 0xd7, 0x22, 0x03, 0x83, 0xc6, 0x59, 0x13, 0x5c, 0xf5, 0x32,
	0xa4, 0xe8, 0x1b, 0x58, 0x39, 0x36, 0xda, 0xed, 0x23, 0xc3, 0xfc, 0x2e, 0xc9, 0xf9, 0x59, 0xe6,
	0x7c, 0x29, 0x86, 0x0c, 0xdf, 0xc7, 0x8a, 0xd7, 0xf7, 0x31, 0x0f, 0xe0, 0x6a, 0x6d, 0x69, 0x66,
	0x86, 0xab, 0x1b, 0x4e, 0x10, 0xa6, 0x3e, 0x84, 0x43, 0xcc, 0xe4, 0x3a, 0xf0, 0x31, 0x9b, 0x74,
	0xf1, 0x7b, 0x36, 0x85, 0xb9, 0x68, 0xbc, 0x66, 0x99, 0xf1, 0xd6, 0xd0, 0xdd, 0xe8, 0x26, 0x06,
	0x37, 0xba, 0x62, 0x5f, 0x3e, 0xa0, 0x27, 0x30, 0xd7, 0x4d, 0xa5, 0xde, 0x99, 0x67, 0xe3, 0x3c,
	0x61, 0x5d, 0xf5, 0xfb, 0x71, 0x58, 0xcd, 0x22, 0x13, 0xd5, 0x60, 0xf6, 0xdc, 0xf2, 0x82, 0x33,
	0xa3, 0x7d, 0x65, 0x64, 0x2e, 0xa3, 0xa3, 0x85, 0x46, 0x0e, 0x42, 0x15, 0x6a, 0x45, 0x2b, 0x9c,
	0x5f, 0x7d, 0xa0, 0x26, 0x14, 0xfc, 0x77, 0x56, 0x60, 0x9e, 0x5e, 0xed, 0xd6, 0x73, 0xdb, 0x3f,
	0xbc, 0xb9, 0xb5, 0x06, 0x53, 0xa6, 0x11, 0x6b, 0xe0, 0x77, 0xff, 0x46, 0xa7, 0xdd, 0xe2, 0xbe,
	0x96, 0x4a, 0x13, 0xac, 0x05, 0x7f, 0x31, 0x72, 0x2a, 0x69, 0x0b, 0x7e, 0x02, 0x1f, 0xc2, 0x21,
	0x94, 0xd2, 0x34, 0x86, 0x94, 0x05, 0x97, 0x59, 0x16, 0xc2, 0xbf, 0x26, 0xa1, 0x94, 0xd6, 0x61,
	0x6f, 0xd9, 0x3c, 0x9f, 0xc1, 0x1d, 0xdb, 0xb8, 0xc0, 0x36, 0xb1, 0x8f, 0x88, 0x17, 0xe6, 0x57,
	0x98, 0x3d, 0x45, 0xdb, 0xb8, 0xa8, 0xb1, 0x51, 0x96, 0x5e, 0xbf, 0x82, 0x25, 0xdb, 0x72, 0x30,
	0x71, 0x02, 0xe2, 0xe1, 0x73, 0xe2, 0xf9, 0xb4, 0x44, 0xdb, 0x96, 0x6d, 0x05, 0xa5, 0x49, 0xc6,
	0x62, 0x7a, 0x2f, 0x12, 0xdb, 0x16, 0x71, 0x82, 0x83, 0x50, 0x47, 0x5b, 0xb0, 0x2d, 0x47, 0xa2,
	0x56, 0xa2, 0x81, 0x2a, 0xb5, 0x81, 0xde, 0xc2, 0x22, 0x35, 0x4f, 0xb3, 0xb2, 0xdf, 0xf8, 0xd4,
	0x48, 0xc6, 0x91, 0x6d, 0x39, 0x35, 0xcb, 0xec, 0x33, 0xbd, 0x05, 0x0b, 0xc6, 0x59, 0xe0, 0x62,
	0x72, 0x61, 0x05, 0xf8, 0xe8, 0x32, 0xb6, 0xcf, 0x5a, 0x70, 0x4e, 0x9b, 0xa7, 0x32, 0xe9, 0xc2,
	0x0a, 0x76, 0x2e, 0x23, 0x2d, 0xf4, 0x39, 0x2c, 0xf6, 0x29, 0x50, 0x7e, 0x18, 0x31, 0x33, 0x83,
	0x1a, 0x35, 0xe3, 0x82, 0x91, 0xf3, 0x08, 0x0a, 0xd4, 0x73, 0xd3, 0x75, 0x02, 0xcf, 0x6d, 0x47,
	0xad, 0x14, 0x6c, 0xcb, 0x14, 0xc3, 0x11, 0xf4, 0x35, 0xac, 0x9c, 0xf9, 0x04, 0x3b, 0xe4, 0x5d,
	0x0c, 0xc2, 0xef, 0x4e, 0x89, 0x83, 0xc3, 0x34, 0x65, 0xbd, 0x34, 0xa7, 0x2d, 0x9d, 0xf9, 0x44,
	0x21, 0xef, 0x22, 0x9d, 0xc3, 0x53, 0xe2, 0x84, 0x19, 0x8d, 0x3e, 0x85, 0x79, 0xf3, 0xd4, 0xb0,
	0x3c, 0x46, 0x8f, 0xd5, 0x0a, 0x73, 0x17, 0xd6, 0x26, 0x68, 0x17, 0x60, 0x82, 0x9a, 0x65, 0xca,
	0x2d, 0x56, 0x92, 0x4f, 0xe1, 0x8e, 0x7d, 0xde, 0xe9, 0x03, 0x16, 0x18, 0x70, 0xd6, 0x3e, 0xef,
	0x74, 0x61, 0xc2, 0xcf, 0xa1, 0xd8, 0x47, 0x1c, 0x8d, 0xc0, 0x64, 0x03, 0x7d, 0x8d, 0x28, 0x1c,
	0x62, 0xf9, 0x5b, 0x82, 0x99, 0x98, 0xb8, 0x30, 0x89, 0xe2, 0x4f, 0x61, 0x1d, 0x9e, 0x85, 0x07,
	0x9f, 0x46, 0x87, 0x98, 0x83, 0xa9, 0xdb, 0x77, 0x44, 0xfa, 0xcf, 0x14, 0x94, 0x1a, 0x9d, 0xfe,
	0x73, 0x73, 0x0c, 0x4a, 0x39, 0xf1, 0x71, 0xc9, 0x27, 0xbe, 0x9e, 0x9d, 0x74, 0xfc, 0x96, 0x3b,
	0xe9, 0xc4, 0x07, 0xee, 0xa4, 0x09, 0x85, 0x33, 0x39, 0x62, 0xe1, 0x4c, 0x7d, 0xcc, 0xc2, 0x99,
	0xfe, 0x68, 0x85, 0x33, 0x33, 0x72, 0xe1, 0xe4, 0x6e, 0x58, 0x38, 0xf9, 0x51, 0x0b, 0x07, 0x3e,
	0xa0, 0x70, 0x0a, 0x37, 0x2d, 0x9c, 0xd9, 0xeb, 0x85, 0x33, 0xec, 0x84, 0x50, 0xcc, 0x3e, 0x21,
	0x08, 0x97, 0xf0, 0xfc, 0x46, 0xb5, 0xe2, 0x77, 0x90, 0x72, 0xfd, 0xae, 0x90, 0xbe, 0x51, 0xa5,
	0x55, 0x55, 0xcf, 0x95, 0xe1, 0x29, 0x3c, 0x4e, 0xb8, 0x9f, 0xc4, 0x47, 0xf0, 0x6e, 0x8d, 0xfe,
	0x06, 0x9e, 0x0c, 0x87, 0x7d, 0xac, 0x9b, 0xe4, 0x55, 0x2f, 0x49, 0xde, 0xed, 0xd8, 0xf4, 0x07,
	0xdb, 0xd4, 0xcf, 0xdf, 0x73, 0x31, 0x95, 0x43, 0xa0, 0x7e, 0x07, 0xbd, 0x85, 0x3b, 0x57, 0x57,
	0x99, 0x9b, 0x11, 0x9a, 0x7a, 0x9f, 0x29, 0x76, 0xef, 0x33, 0xd4, 0xe1, 0x8d, 0x1a, 0xcc, 0x5f,
	0x3b, 0x87, 0xa0, 0x65, 0xb8, 0xd7, 0x10, 0xdf, 0x48, 0x35, 0x09, 0x1f, 0xc8, 0x9a, 0xde, 0x2c,
	0x57, 0xb1, 0xfe, 0xb6, 0x2e, 0x61, 0x45, 0xe5, 0xc7, 0xd0, 0x2a, 0x94, 0x92, 0x64, 0x7b, 0xe5,
	0x9a, 0xc4, 0x73, 0x1b, 0x7f, 0xe6, 0x60, 0x25, 0xe3, 0x5c, 0x83, 0x9e, 0x81, 0x90, 0xa4, 0xdd,
	0x38, 0x94, 0x75, 0xf1, 0x0d, 0x6e, 0x2a, 0xfb, 0x8a, 0x7a, 0xa8, 0xf0, 0x63, 0x68, 0x03, 0x9e,
	0x65, 0xe0, 0x76, 0xd5, 0x6a, 0x55, 0x3d, 0x64, 0x43, 0x3c, 0x87, 0xd6, 0xe1, 0xc9, 0x70, 0xac,
	0x5c, 0xe1, 0xc7, 0x37, 0xfe, 0xcd, 0xc1, 0x9c, 0x54, 0xeb, 0x16, 0xe7, 0x8e, 0x15, 0xa0, 0x25,
	0xb8, 0x5b, 0x93, 0x45, 0x2c, 0xaa, 0x8a, 0xae, 0xa9, 0xd5, 0x1e, 0x0f, 0xd6, 0x60, 0xb5, 0x57,
	0xa0, 0xa8, 0x3a, 0xde, 0x97, 0xc5, 0x7d, 0xac, 0x36, 0x75, 0x5c, 0x93, 0x45, 0x9e, 0x43, 0x0f,
	0x61, 0x79, 0x10, 0xd1, 0x54, 0xaa, 0xaa, 0xb8, 0xcf, 0xe4, 0xe3, 0x94, 0xc5, 0x7e, 0xd3, 0xb5,
	0xa6, 0x2e, 0x31, 0xd9, 0xe4, 0xa0, 0xae, 0x58, 0x56, 0xb0, 0xf8, 0xa6, 0xac, 0xec, 0x85, 0xf2,
	0x1c, 0x65, 0x79, 0x50, 0xae, 0x97, 0xf7, 0x43, 0x29, 0x8f, 0x9e, 0xc3, 0xe3, 0x5e, 0xe9, 0xae,
	0xaa, 0xed, 0xc8, 0x95, 0x8a, 0xa4, 0xe0, 0x72, 0x53, 0x57, 0x71, 0xd7, 0x85, 0xb5, 0x8d, 0xef,
	0xf3, 0x70, 0xb7, 0x91, 0x70, 0x21, 0x12, 0xe0, 0x61, 0x44, 0x59, 0xe3, 0x40, 0xc3, 0x15, 0x49,
	0x2f, 0xcb, 0x11, 0x6b, 0x57, 0x04, 0x3c, 0x84, 0xe5, 0x14, 0xcc, 0x6e, 0x53, 0xe1, 0x39, 0xf4,
	0x08, 0x56, 0x52, 0xe4, 0x2c, 0x17, 0xc6, 0x33, 0x00, 0x55, 0xf9, 0x40, 0xe2, 0x27, 0xd0, 0x27,
	0xf0, 0x20, 0x05, 0x50, 0x29, 0xeb, 0xb2, 0xb2, 0xc7, 0x4f, 0xa2, 0x27, 0xb0, 0x96, 0x02, 0xa9,
	0xc9, 0x8a, 0x1c, 0xce, 0x34, 0xd5, 0x93, 0x01, 0xd7, 0x50, 0xe5, 0xc6, 0xbe, 0x54, 0x89, 0xed,
	0x4d, 0xa3, 0x07, 0x70, 0x3f, 0x05, 0x29, 0xd6, 0xf9, 0x99, 0x0c, 0x97, 0xe5, 0x8a, 0x5a, 0xe5,
	0x73, 0xe8, 0x53, 0x78, 0x9a, 0xaa, 0x8f, 0x77, 0xca, 0xba, 0x5e, 0x8d, 0xc2, 0xcf, 0xd3, 0x45,
	0x4a, 0x81, 0x36, 0x64, 0x65, 0x0f, 0x97, 0xb1, 0xa6, 0x36, 0x95, 0x0a, 0x0f, 0x19, 0x44, 0xef,
	0xeb, 0x07, 0x7c, 0x21, 0x83, 0x03, 0x4d, 0xad, 0x4a, 0xb8, 0x5e, 0x2d, 0xbf, 0xe5, 0x67, 0x33,
	0x50, 0x55, 0xb9, 0xa1, 0x4b, 0x0a, 0x8d, 0xbf, 0x98, 0xe1, 0x54, 0xad, 0x59, 0xd5, 0xe5, 0x28,
	0x04, 0x7e, 0x0e, 0x3d, 0x85, 0x4f, 0x52, 0x81, 0x0d, 0x59, 0xc4, 0x8a, 0xd4, 0xd0, 0xf9, 0x3b,
	0x19, 0xbe, 0x6b, 0xe5, 0x3a, 0xcf, 0x67, 0xf0, 0x55, 0xdf, 0x13, 0xf1, 0xa1, 0xa4, 0x49, 0x87,
	0x6a, 0xf5, 0x40, 0x6a, 0xf0, 0xf3, 0x19, 0xd9, 0x50, 0x97, 0xcb, 0xf8, 0x5b, 0x99, 0x47, 0x3d,
	0x5d, 0x61, 0x10, 0x72, 0x65, 0x29, 0xa4, 0xff, 0x2e, 0xda, 0x84, 0x8d, 0xcc, 0x48, 0x69, 0x01,
	0x5d, 0xe5, 0xd0, 0x42, 0x56, 0x0e, 0xb1, 0x80, 0x45, 0x55, 0x11, 0x25, 0x4d, 0xe7, 0x17, 0x87,
	0x38, 0x7a, 0xb0, 0xcd, 0xdf, 0xeb, 0x69, 0x73, 0x83, 0x10, 0xa9, 0x21, 0x96, 0xe3, 0x12, 0x59,
	0xca, 0x58, 0x34, 0x0a, 0xc0, 0x5a, 0x59, 0x94, 0xf8, 0x12, 0xfa, 0x0c, 0x9e, 0xa7, 0xa0, 0xd4,
	0xdd, 0x5d, 0x59, 0x94, 0xcb, 0x55, 0xd6, 0x3c, 0x14, 0xa9, 0xca, 0xdf, 0xcf, 0x88, 0xbb, 0x2e,
	0x69, 0xbb, 0x92, 0xa8, 0x63, 0x51, 0x6d, 0xd6, 0xe3, 0x34, 0x5d, 0xc9, 0x88, 0x5b, 0xdd, 0xdd,
	0x95, 0xb4, 0xae, 0xe5, 0xd5, 0xac, 0xdc, 0x57, 0x6b, 0xb5, 0xa6, 0x22, 0xeb, 0x6f, 0x29, 0x5a,
	0xe7, 0x1f, 0xa0, 0x1f, 0xc1, 0xcb, 0xa1, 0x50, 0xe9, 0xdb, 0x7a, 0x59, 0xa9, 0xe0, 0x86, 0x1a,
	0x07, 0xa0, 0xf3, 0x0f, 0x37, 0xfe, 0xc2, 0xc1, 0x62, 0xe2, 0x2f, 0x52, 0xa8, 0x04, 0x0b, 0x91,
	0x2b, 0x83, 0x8d, 0x6a, 0x11, 0xe6, 0xfb, 0x25, 0x0d, 0x49, 0xe3, 0x39, 0x5a, 0xea, 0x7d, 0xc3,
	0xf5, 0xe6, 0x4e, 0x55, 0x16, 0xf1, 0x5e, 0x53, 0xae, 0x56, 0xc2, 0xee, 0xdc, 0x2f, 0xde, 0x13,
	0xb1, 0xaa, 0xbf, 0x91, 0x34, 0x7e, 0xe2, 0x9a, 0xac, 0xeb, 0x34, 0x3f, 0xb9, 0xfd, 0x4f, 0x18,
	0xf8, 0x11, 0x90, 0x6e, 0xa5, 0xf4, 0x34, 0x80, 0xde, 0xc3, 0xdd, 0x84, 0xd7, 0x15, 0xb4, 0x95,
	0xbe, 0x47, 0x27, 0xbe, 0x59, 0x2d, 0x7f, 0x3e, 0x9a, 0x82, 0xdf, 0x11, 0xc6, 0xe8, 0xdc, 0x09,
	0xaf, 0x0b, 0x19, 0x73, 0x27, 0xbf, 0xd9, 0x64, 0xcc, 0x9d, 0xf2, 0x78, 0x11, 0xce, 0x9d, 0xf0,
	0x9e, 0x90, 0x31, 0x77, 0xf2, 0xa3, 0x45, 0xc6, 0xdc, 0x69, 0xcf, 0x15, 0x63, 0xe8, 0x4f, 0xdc,
	0xf5, 0x1f, 0xe9, 0x7b, 0x1f, 0x0d, 0xd0, 0x8f, 0x53, 0x8d, 0x0e, 0x79, 0xe8, 0x58, 0xfe, 0xea,
	0x03, 0x35, 0x99, 0x5f, 0x7f, 0xe0, 0xe0, 0x7e, 0xea, 0x2f, 0xfa, 0xe8, 0xd5, 0x8d, 0x4d, 0xf7,
	0x1e, 0x6d, 0x97, 0xbf, 0xfc, 0x10, 0xb5, 0x5e, 0x9a, 0x52, 0x7f, 0xdc, 0xcf, 0xa6, 0x29, 0xeb,
	0xd9, 0x20, 0x9b, 0xa6, 0xcc, 0xd7, 0x04, 0x61, 0x0c, 0xfd, 0x9d, 0x8b, 0x0f, 0xf5, 0x99, 0xf7,
	0x09, 0xf4, 0xd3, 0x21, 0x93, 0x0c, 0xbb, 0xb9, 0x2f, 0xff, 0xec, 0x76, 0x06, 0x98, 0xb3, 0x7f,
	0xe5, 0x12, 0x5f, 0x48, 0xfa, 0xae, 0x16, 0xe8, 0xeb, 0x51, 0xe8, 0x18, 0xbc, 0xbc, 0x2c, 0x7f,
	0x73, 0x0b, 0xed, 0x01, 0x42, 0x33, 0x6f, 0x15, 0x43, 0x09, 0x1d, 0x76, 0x7d, 0x19, 0x4a, 0xe8,
	0xd0, 0x4b, 0x8d, 0x30, 0xb6, 0xf3, 0xfa, 0x97, 0x5f, 0x9d, 0xb8, 0x6d, 0xc3, 0x39, 0xd9, 0x7c,
	0xb5, 0x1d, 0x04, 0x9b, 0xa6, 0x6b, 0x6f, 0xb1, 0x97, 0x7e, 0xd3, 0x6d, 0x6f, 0xf9, 0xc4, 0x3b,
	0xb7, 0x4c, 0xe2, 0x6f, 0xa5, 0xfc, 0xb3, 0xc0, 0xd1, 0x34, 0x83, 0xbe, 0xfc, 0x6f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0xa6, 0x8e, 0x1c, 0xd2, 0x4e, 0x20, 0x00, 0x00,
}
