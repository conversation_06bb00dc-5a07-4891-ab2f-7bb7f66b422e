// Code generated by protoc-gen-go. DO NOT EDIT.
// source: you-know-who/you-know-who-write.proto

package youknowwhowrite // import "golang.52tt.com/protocol/services/youknowwho/youknowwhowrite"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 开通状态(该状态受过期时间影响)
type OpenType int32

const (
	OpenType_NO_OPEN OpenType = 0
	OpenType_OPEN    OpenType = 1
	OpenType_FREEZE  OpenType = 2
	OpenType_BAN     OpenType = 3
)

var OpenType_name = map[int32]string{
	0: "NO_OPEN",
	1: "OPEN",
	2: "FREEZE",
	3: "BAN",
}
var OpenType_value = map[string]int32{
	"NO_OPEN": 0,
	"OPEN":    1,
	"FREEZE":  2,
	"BAN":     3,
}

func (x OpenType) String() string {
	return proto.EnumName(OpenType_name, int32(x))
}
func (OpenType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{0}
}

// 开关状态（该状态受开通状态影响）
type SwitchType int32

const (
	SwitchType_SWITCH_OFF SwitchType = 0
	SwitchType_SWITCH_ON  SwitchType = 1
)

var SwitchType_name = map[int32]string{
	0: "SWITCH_OFF",
	1: "SWITCH_ON",
}
var SwitchType_value = map[string]int32{
	"SWITCH_OFF": 0,
	"SWITCH_ON":  1,
}

func (x SwitchType) String() string {
	return proto.EnumName(SwitchType_name, int32(x))
}
func (SwitchType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{1}
}

// 周榜/爱意榜开关（该状态受开关状态影响）
type RankType int32

const (
	RankType_RANK_OFF RankType = 0
	RankType_RANK_ON  RankType = 1
)

var RankType_name = map[int32]string{
	0: "RANK_OFF",
	1: "RANK_ON",
}
var RankType_value = map[string]int32{
	"RANK_OFF": 0,
	"RANK_ON":  1,
}

func (x RankType) String() string {
	return proto.EnumName(RankType_name, int32(x))
}
func (RankType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{2}
}

// 神秘人权限信息
type PermissionInfo struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string     `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Status               OpenType   `protobuf:"varint,3,opt,name=status,proto3,enum=youknowwho.OpenType" json:"status,omitempty"`
	Switch               SwitchType `protobuf:"varint,4,opt,name=switch,proto3,enum=youknowwho.SwitchType" json:"switch,omitempty"`
	ExpireTime           uint32     `protobuf:"varint,5,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	ServerTime           uint32     `protobuf:"varint,6,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	Level                uint32     `protobuf:"varint,7,opt,name=level,proto3" json:"level,omitempty"`
	RankSwitch           RankType   `protobuf:"varint,8,opt,name=rank_switch,json=rankSwitch,proto3,enum=youknowwho.RankType" json:"rank_switch,omitempty"`
	FakeUid              uint32     `protobuf:"varint,9,opt,name=fake_uid,json=fakeUid,proto3" json:"fake_uid,omitempty"`
	EnterNotice          SwitchType `protobuf:"varint,10,opt,name=enter_notice,json=enterNotice,proto3,enum=youknowwho.SwitchType" json:"enter_notice,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *PermissionInfo) Reset()         { *m = PermissionInfo{} }
func (m *PermissionInfo) String() string { return proto.CompactTextString(m) }
func (*PermissionInfo) ProtoMessage()    {}
func (*PermissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{0}
}
func (m *PermissionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PermissionInfo.Unmarshal(m, b)
}
func (m *PermissionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PermissionInfo.Marshal(b, m, deterministic)
}
func (dst *PermissionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PermissionInfo.Merge(dst, src)
}
func (m *PermissionInfo) XXX_Size() int {
	return xxx_messageInfo_PermissionInfo.Size(m)
}
func (m *PermissionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PermissionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PermissionInfo proto.InternalMessageInfo

func (m *PermissionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PermissionInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *PermissionInfo) GetStatus() OpenType {
	if m != nil {
		return m.Status
	}
	return OpenType_NO_OPEN
}

func (m *PermissionInfo) GetSwitch() SwitchType {
	if m != nil {
		return m.Switch
	}
	return SwitchType_SWITCH_OFF
}

func (m *PermissionInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *PermissionInfo) GetServerTime() uint32 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *PermissionInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *PermissionInfo) GetRankSwitch() RankType {
	if m != nil {
		return m.RankSwitch
	}
	return RankType_RANK_OFF
}

func (m *PermissionInfo) GetFakeUid() uint32 {
	if m != nil {
		return m.FakeUid
	}
	return 0
}

func (m *PermissionInfo) GetEnterNotice() SwitchType {
	if m != nil {
		return m.EnterNotice
	}
	return SwitchType_SWITCH_OFF
}

// 用户主动开启和关闭神秘人
type ChangeStatusReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SwitchType           SwitchType `protobuf:"varint,2,opt,name=switch_type,json=switchType,proto3,enum=youknowwho.SwitchType" json:"switch_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChangeStatusReq) Reset()         { *m = ChangeStatusReq{} }
func (m *ChangeStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChangeStatusReq) ProtoMessage()    {}
func (*ChangeStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{1}
}
func (m *ChangeStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeStatusReq.Unmarshal(m, b)
}
func (m *ChangeStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeStatusReq.Marshal(b, m, deterministic)
}
func (dst *ChangeStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeStatusReq.Merge(dst, src)
}
func (m *ChangeStatusReq) XXX_Size() int {
	return xxx_messageInfo_ChangeStatusReq.Size(m)
}
func (m *ChangeStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeStatusReq proto.InternalMessageInfo

func (m *ChangeStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeStatusReq) GetSwitchType() SwitchType {
	if m != nil {
		return m.SwitchType
	}
	return SwitchType_SWITCH_OFF
}

type ChangeStatusResp struct {
	UkwPermissionInfo    *PermissionInfo `protobuf:"bytes,1,opt,name=ukw_permission_info,json=ukwPermissionInfo,proto3" json:"ukw_permission_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ChangeStatusResp) Reset()         { *m = ChangeStatusResp{} }
func (m *ChangeStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeStatusResp) ProtoMessage()    {}
func (*ChangeStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{2}
}
func (m *ChangeStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeStatusResp.Unmarshal(m, b)
}
func (m *ChangeStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeStatusResp.Marshal(b, m, deterministic)
}
func (dst *ChangeStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeStatusResp.Merge(dst, src)
}
func (m *ChangeStatusResp) XXX_Size() int {
	return xxx_messageInfo_ChangeStatusResp.Size(m)
}
func (m *ChangeStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeStatusResp proto.InternalMessageInfo

func (m *ChangeStatusResp) GetUkwPermissionInfo() *PermissionInfo {
	if m != nil {
		return m.UkwPermissionInfo
	}
	return nil
}

// 切换神秘人周榜/爱意榜开关
type ChangeRankSwitchReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RankSwitch           RankType `protobuf:"varint,2,opt,name=rank_switch,json=rankSwitch,proto3,enum=youknowwho.RankType" json:"rank_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeRankSwitchReq) Reset()         { *m = ChangeRankSwitchReq{} }
func (m *ChangeRankSwitchReq) String() string { return proto.CompactTextString(m) }
func (*ChangeRankSwitchReq) ProtoMessage()    {}
func (*ChangeRankSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{3}
}
func (m *ChangeRankSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeRankSwitchReq.Unmarshal(m, b)
}
func (m *ChangeRankSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeRankSwitchReq.Marshal(b, m, deterministic)
}
func (dst *ChangeRankSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeRankSwitchReq.Merge(dst, src)
}
func (m *ChangeRankSwitchReq) XXX_Size() int {
	return xxx_messageInfo_ChangeRankSwitchReq.Size(m)
}
func (m *ChangeRankSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeRankSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeRankSwitchReq proto.InternalMessageInfo

func (m *ChangeRankSwitchReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeRankSwitchReq) GetRankSwitch() RankType {
	if m != nil {
		return m.RankSwitch
	}
	return RankType_RANK_OFF
}

type ChangeRankSwitchResp struct {
	RankSwitch           RankType `protobuf:"varint,1,opt,name=rank_switch,json=rankSwitch,proto3,enum=youknowwho.RankType" json:"rank_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeRankSwitchResp) Reset()         { *m = ChangeRankSwitchResp{} }
func (m *ChangeRankSwitchResp) String() string { return proto.CompactTextString(m) }
func (*ChangeRankSwitchResp) ProtoMessage()    {}
func (*ChangeRankSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{4}
}
func (m *ChangeRankSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeRankSwitchResp.Unmarshal(m, b)
}
func (m *ChangeRankSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeRankSwitchResp.Marshal(b, m, deterministic)
}
func (dst *ChangeRankSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeRankSwitchResp.Merge(dst, src)
}
func (m *ChangeRankSwitchResp) XXX_Size() int {
	return xxx_messageInfo_ChangeRankSwitchResp.Size(m)
}
func (m *ChangeRankSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeRankSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeRankSwitchResp proto.InternalMessageInfo

func (m *ChangeRankSwitchResp) GetRankSwitch() RankType {
	if m != nil {
		return m.RankSwitch
	}
	return RankType_RANK_OFF
}

// 神秘人现身
type ExposureReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExposureReq) Reset()         { *m = ExposureReq{} }
func (m *ExposureReq) String() string { return proto.CompactTextString(m) }
func (*ExposureReq) ProtoMessage()    {}
func (*ExposureReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{5}
}
func (m *ExposureReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExposureReq.Unmarshal(m, b)
}
func (m *ExposureReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExposureReq.Marshal(b, m, deterministic)
}
func (dst *ExposureReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExposureReq.Merge(dst, src)
}
func (m *ExposureReq) XXX_Size() int {
	return xxx_messageInfo_ExposureReq.Size(m)
}
func (m *ExposureReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExposureReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExposureReq proto.InternalMessageInfo

func (m *ExposureReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ExposureRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExposureRsp) Reset()         { *m = ExposureRsp{} }
func (m *ExposureRsp) String() string { return proto.CompactTextString(m) }
func (*ExposureRsp) ProtoMessage()    {}
func (*ExposureRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{6}
}
func (m *ExposureRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExposureRsp.Unmarshal(m, b)
}
func (m *ExposureRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExposureRsp.Marshal(b, m, deterministic)
}
func (dst *ExposureRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExposureRsp.Merge(dst, src)
}
func (m *ExposureRsp) XXX_Size() int {
	return xxx_messageInfo_ExposureRsp.Size(m)
}
func (m *ExposureRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExposureRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ExposureRsp proto.InternalMessageInfo

// 用户主动开启和关闭神秘人进房提醒
type ChangeEnterNoticeReq struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EnterNoticeSwitch    SwitchType `protobuf:"varint,2,opt,name=enter_notice_switch,json=enterNoticeSwitch,proto3,enum=youknowwho.SwitchType" json:"enter_notice_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChangeEnterNoticeReq) Reset()         { *m = ChangeEnterNoticeReq{} }
func (m *ChangeEnterNoticeReq) String() string { return proto.CompactTextString(m) }
func (*ChangeEnterNoticeReq) ProtoMessage()    {}
func (*ChangeEnterNoticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{7}
}
func (m *ChangeEnterNoticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeEnterNoticeReq.Unmarshal(m, b)
}
func (m *ChangeEnterNoticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeEnterNoticeReq.Marshal(b, m, deterministic)
}
func (dst *ChangeEnterNoticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeEnterNoticeReq.Merge(dst, src)
}
func (m *ChangeEnterNoticeReq) XXX_Size() int {
	return xxx_messageInfo_ChangeEnterNoticeReq.Size(m)
}
func (m *ChangeEnterNoticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeEnterNoticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeEnterNoticeReq proto.InternalMessageInfo

func (m *ChangeEnterNoticeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeEnterNoticeReq) GetEnterNoticeSwitch() SwitchType {
	if m != nil {
		return m.EnterNoticeSwitch
	}
	return SwitchType_SWITCH_OFF
}

type ChangeEnterNoticeResp struct {
	EnterNoticeSwitch    SwitchType `protobuf:"varint,1,opt,name=enter_notice_switch,json=enterNoticeSwitch,proto3,enum=youknowwho.SwitchType" json:"enter_notice_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ChangeEnterNoticeResp) Reset()         { *m = ChangeEnterNoticeResp{} }
func (m *ChangeEnterNoticeResp) String() string { return proto.CompactTextString(m) }
func (*ChangeEnterNoticeResp) ProtoMessage()    {}
func (*ChangeEnterNoticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{8}
}
func (m *ChangeEnterNoticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeEnterNoticeResp.Unmarshal(m, b)
}
func (m *ChangeEnterNoticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeEnterNoticeResp.Marshal(b, m, deterministic)
}
func (dst *ChangeEnterNoticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeEnterNoticeResp.Merge(dst, src)
}
func (m *ChangeEnterNoticeResp) XXX_Size() int {
	return xxx_messageInfo_ChangeEnterNoticeResp.Size(m)
}
func (m *ChangeEnterNoticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeEnterNoticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeEnterNoticeResp proto.InternalMessageInfo

func (m *ChangeEnterNoticeResp) GetEnterNoticeSwitch() SwitchType {
	if m != nil {
		return m.EnterNoticeSwitch
	}
	return SwitchType_SWITCH_OFF
}

// 神秘人文案
type ShowMsg struct {
	SendUid              uint32   `protobuf:"varint,1,opt,name=send_uid,json=sendUid,proto3" json:"send_uid,omitempty"`
	SendNickname         string   `protobuf:"bytes,2,opt,name=send_nickname,json=sendNickname,proto3" json:"send_nickname,omitempty"`
	SendAccount          string   `protobuf:"bytes,3,opt,name=send_account,json=sendAccount,proto3" json:"send_account,omitempty"`
	MsgText              string   `protobuf:"bytes,4,opt,name=msg_text,json=msgText,proto3" json:"msg_text,omitempty"`
	Weight               int64    `protobuf:"varint,5,opt,name=weight,proto3" json:"weight,omitempty"`
	Count                uint32   `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowMsg) Reset()         { *m = ShowMsg{} }
func (m *ShowMsg) String() string { return proto.CompactTextString(m) }
func (*ShowMsg) ProtoMessage()    {}
func (*ShowMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{9}
}
func (m *ShowMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowMsg.Unmarshal(m, b)
}
func (m *ShowMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowMsg.Marshal(b, m, deterministic)
}
func (dst *ShowMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowMsg.Merge(dst, src)
}
func (m *ShowMsg) XXX_Size() int {
	return xxx_messageInfo_ShowMsg.Size(m)
}
func (m *ShowMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowMsg.DiscardUnknown(m)
}

var xxx_messageInfo_ShowMsg proto.InternalMessageInfo

func (m *ShowMsg) GetSendUid() uint32 {
	if m != nil {
		return m.SendUid
	}
	return 0
}

func (m *ShowMsg) GetSendNickname() string {
	if m != nil {
		return m.SendNickname
	}
	return ""
}

func (m *ShowMsg) GetSendAccount() string {
	if m != nil {
		return m.SendAccount
	}
	return ""
}

func (m *ShowMsg) GetMsgText() string {
	if m != nil {
		return m.MsgText
	}
	return ""
}

func (m *ShowMsg) GetWeight() int64 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *ShowMsg) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

// 发送神秘人关注消息
type SendShowMsgReq struct {
	Msg                  *ShowMsg `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	ReceiverUid          uint32   `protobuf:"varint,2,opt,name=receiver_uid,json=receiverUid,proto3" json:"receiver_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendShowMsgReq) Reset()         { *m = SendShowMsgReq{} }
func (m *SendShowMsgReq) String() string { return proto.CompactTextString(m) }
func (*SendShowMsgReq) ProtoMessage()    {}
func (*SendShowMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{10}
}
func (m *SendShowMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendShowMsgReq.Unmarshal(m, b)
}
func (m *SendShowMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendShowMsgReq.Marshal(b, m, deterministic)
}
func (dst *SendShowMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendShowMsgReq.Merge(dst, src)
}
func (m *SendShowMsgReq) XXX_Size() int {
	return xxx_messageInfo_SendShowMsgReq.Size(m)
}
func (m *SendShowMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendShowMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendShowMsgReq proto.InternalMessageInfo

func (m *SendShowMsgReq) GetMsg() *ShowMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *SendShowMsgReq) GetReceiverUid() uint32 {
	if m != nil {
		return m.ReceiverUid
	}
	return 0
}

func (m *SendShowMsgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type SendShowMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendShowMsgResp) Reset()         { *m = SendShowMsgResp{} }
func (m *SendShowMsgResp) String() string { return proto.CompactTextString(m) }
func (*SendShowMsgResp) ProtoMessage()    {}
func (*SendShowMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_you_know_who_write_f78e9fcf04eb96fb, []int{11}
}
func (m *SendShowMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendShowMsgResp.Unmarshal(m, b)
}
func (m *SendShowMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendShowMsgResp.Marshal(b, m, deterministic)
}
func (dst *SendShowMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendShowMsgResp.Merge(dst, src)
}
func (m *SendShowMsgResp) XXX_Size() int {
	return xxx_messageInfo_SendShowMsgResp.Size(m)
}
func (m *SendShowMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendShowMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendShowMsgResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*PermissionInfo)(nil), "youknowwho.PermissionInfo")
	proto.RegisterType((*ChangeStatusReq)(nil), "youknowwho.ChangeStatusReq")
	proto.RegisterType((*ChangeStatusResp)(nil), "youknowwho.ChangeStatusResp")
	proto.RegisterType((*ChangeRankSwitchReq)(nil), "youknowwho.ChangeRankSwitchReq")
	proto.RegisterType((*ChangeRankSwitchResp)(nil), "youknowwho.ChangeRankSwitchResp")
	proto.RegisterType((*ExposureReq)(nil), "youknowwho.ExposureReq")
	proto.RegisterType((*ExposureRsp)(nil), "youknowwho.ExposureRsp")
	proto.RegisterType((*ChangeEnterNoticeReq)(nil), "youknowwho.ChangeEnterNoticeReq")
	proto.RegisterType((*ChangeEnterNoticeResp)(nil), "youknowwho.ChangeEnterNoticeResp")
	proto.RegisterType((*ShowMsg)(nil), "youknowwho.ShowMsg")
	proto.RegisterType((*SendShowMsgReq)(nil), "youknowwho.SendShowMsgReq")
	proto.RegisterType((*SendShowMsgResp)(nil), "youknowwho.SendShowMsgResp")
	proto.RegisterEnum("youknowwho.OpenType", OpenType_name, OpenType_value)
	proto.RegisterEnum("youknowwho.SwitchType", SwitchType_name, SwitchType_value)
	proto.RegisterEnum("youknowwho.RankType", RankType_name, RankType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// YouKnowWhoWriteClient is the client API for YouKnowWhoWrite service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type YouKnowWhoWriteClient interface {
	// 用户主动开启和关闭神秘人
	ChangeStatus(ctx context.Context, in *ChangeStatusReq, opts ...grpc.CallOption) (*ChangeStatusResp, error)
	// 切换周榜/爱意值开关状态
	ChangeRankSwitch(ctx context.Context, in *ChangeRankSwitchReq, opts ...grpc.CallOption) (*ChangeRankSwitchResp, error)
	// 用户切换神秘人是否进房询问身份切换提醒开关
	ChangeEnterNotice(ctx context.Context, in *ChangeEnterNoticeReq, opts ...grpc.CallOption) (*ChangeEnterNoticeResp, error)
	// 神秘人现身
	Exposure(ctx context.Context, in *ExposureReq, opts ...grpc.CallOption) (*ExposureRsp, error)
	// 发送用户关注消息
	SendShowMsg(ctx context.Context, in *SendShowMsgReq, opts ...grpc.CallOption) (*SendShowMsgResp, error)
}

type youKnowWhoWriteClient struct {
	cc *grpc.ClientConn
}

func NewYouKnowWhoWriteClient(cc *grpc.ClientConn) YouKnowWhoWriteClient {
	return &youKnowWhoWriteClient{cc}
}

func (c *youKnowWhoWriteClient) ChangeStatus(ctx context.Context, in *ChangeStatusReq, opts ...grpc.CallOption) (*ChangeStatusResp, error) {
	out := new(ChangeStatusResp)
	err := c.cc.Invoke(ctx, "/youknowwho.YouKnowWhoWrite/ChangeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *youKnowWhoWriteClient) ChangeRankSwitch(ctx context.Context, in *ChangeRankSwitchReq, opts ...grpc.CallOption) (*ChangeRankSwitchResp, error) {
	out := new(ChangeRankSwitchResp)
	err := c.cc.Invoke(ctx, "/youknowwho.YouKnowWhoWrite/ChangeRankSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *youKnowWhoWriteClient) ChangeEnterNotice(ctx context.Context, in *ChangeEnterNoticeReq, opts ...grpc.CallOption) (*ChangeEnterNoticeResp, error) {
	out := new(ChangeEnterNoticeResp)
	err := c.cc.Invoke(ctx, "/youknowwho.YouKnowWhoWrite/ChangeEnterNotice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *youKnowWhoWriteClient) Exposure(ctx context.Context, in *ExposureReq, opts ...grpc.CallOption) (*ExposureRsp, error) {
	out := new(ExposureRsp)
	err := c.cc.Invoke(ctx, "/youknowwho.YouKnowWhoWrite/Exposure", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *youKnowWhoWriteClient) SendShowMsg(ctx context.Context, in *SendShowMsgReq, opts ...grpc.CallOption) (*SendShowMsgResp, error) {
	out := new(SendShowMsgResp)
	err := c.cc.Invoke(ctx, "/youknowwho.YouKnowWhoWrite/SendShowMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// YouKnowWhoWriteServer is the server API for YouKnowWhoWrite service.
type YouKnowWhoWriteServer interface {
	// 用户主动开启和关闭神秘人
	ChangeStatus(context.Context, *ChangeStatusReq) (*ChangeStatusResp, error)
	// 切换周榜/爱意值开关状态
	ChangeRankSwitch(context.Context, *ChangeRankSwitchReq) (*ChangeRankSwitchResp, error)
	// 用户切换神秘人是否进房询问身份切换提醒开关
	ChangeEnterNotice(context.Context, *ChangeEnterNoticeReq) (*ChangeEnterNoticeResp, error)
	// 神秘人现身
	Exposure(context.Context, *ExposureReq) (*ExposureRsp, error)
	// 发送用户关注消息
	SendShowMsg(context.Context, *SendShowMsgReq) (*SendShowMsgResp, error)
}

func RegisterYouKnowWhoWriteServer(s *grpc.Server, srv YouKnowWhoWriteServer) {
	s.RegisterService(&_YouKnowWhoWrite_serviceDesc, srv)
}

func _YouKnowWhoWrite_ChangeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YouKnowWhoWriteServer).ChangeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/youknowwho.YouKnowWhoWrite/ChangeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YouKnowWhoWriteServer).ChangeStatus(ctx, req.(*ChangeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _YouKnowWhoWrite_ChangeRankSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeRankSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YouKnowWhoWriteServer).ChangeRankSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/youknowwho.YouKnowWhoWrite/ChangeRankSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YouKnowWhoWriteServer).ChangeRankSwitch(ctx, req.(*ChangeRankSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _YouKnowWhoWrite_ChangeEnterNotice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeEnterNoticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YouKnowWhoWriteServer).ChangeEnterNotice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/youknowwho.YouKnowWhoWrite/ChangeEnterNotice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YouKnowWhoWriteServer).ChangeEnterNotice(ctx, req.(*ChangeEnterNoticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _YouKnowWhoWrite_Exposure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExposureReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YouKnowWhoWriteServer).Exposure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/youknowwho.YouKnowWhoWrite/Exposure",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YouKnowWhoWriteServer).Exposure(ctx, req.(*ExposureReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _YouKnowWhoWrite_SendShowMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendShowMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YouKnowWhoWriteServer).SendShowMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/youknowwho.YouKnowWhoWrite/SendShowMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YouKnowWhoWriteServer).SendShowMsg(ctx, req.(*SendShowMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _YouKnowWhoWrite_serviceDesc = grpc.ServiceDesc{
	ServiceName: "youknowwho.YouKnowWhoWrite",
	HandlerType: (*YouKnowWhoWriteServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChangeStatus",
			Handler:    _YouKnowWhoWrite_ChangeStatus_Handler,
		},
		{
			MethodName: "ChangeRankSwitch",
			Handler:    _YouKnowWhoWrite_ChangeRankSwitch_Handler,
		},
		{
			MethodName: "ChangeEnterNotice",
			Handler:    _YouKnowWhoWrite_ChangeEnterNotice_Handler,
		},
		{
			MethodName: "Exposure",
			Handler:    _YouKnowWhoWrite_Exposure_Handler,
		},
		{
			MethodName: "SendShowMsg",
			Handler:    _YouKnowWhoWrite_SendShowMsg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "you-know-who/you-know-who-write.proto",
}

func init() {
	proto.RegisterFile("you-know-who/you-know-who-write.proto", fileDescriptor_you_know_who_write_f78e9fcf04eb96fb)
}

var fileDescriptor_you_know_who_write_f78e9fcf04eb96fb = []byte{
	// 840 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x55, 0xff, 0x4f, 0xdb, 0x46,
	0x14, 0xc7, 0xa4, 0x8d, 0x9d, 0x67, 0x02, 0xe6, 0x60, 0x5d, 0x96, 0x6e, 0x82, 0x7a, 0x42, 0x42,
	0x6c, 0x04, 0x89, 0xa9, 0x9b, 0x26, 0x4d, 0x95, 0x68, 0x15, 0x54, 0x86, 0x6a, 0x2a, 0x87, 0x09,
	0x0d, 0x4d, 0xb5, 0x3c, 0xe7, 0xb0, 0x4f, 0x89, 0xef, 0x3c, 0xdf, 0xb9, 0x26, 0x3f, 0xee, 0x0f,
	0xd9, 0x9f, 0xb1, 0xff, 0x6f, 0xba, 0xb3, 0x4d, 0x6c, 0x48, 0x3a, 0xfa, 0xdb, 0xbd, 0xf7, 0x3e,
	0xfe, 0xbc, 0xef, 0xcf, 0xb0, 0x37, 0x63, 0xd9, 0xe1, 0x84, 0xb2, 0xfc, 0x30, 0x8f, 0xd8, 0x51,
	0x5d, 0x38, 0xcc, 0x53, 0x22, 0xf0, 0x20, 0x49, 0x99, 0x60, 0x08, 0x66, 0x2c, 0x93, 0x86, 0x3c,
	0x62, 0xf6, 0xdf, 0x2d, 0x58, 0x7f, 0x8f, 0xd3, 0x98, 0x70, 0x4e, 0x18, 0x3d, 0xa3, 0x37, 0x0c,
	0x59, 0xd0, 0xca, 0xc8, 0xb8, 0xa7, 0xed, 0x6a, 0xfb, 0x5d, 0x57, 0x3e, 0x51, 0x1f, 0x0c, 0x4a,
	0x82, 0x09, 0xf5, 0x63, 0xdc, 0x5b, 0xdd, 0xd5, 0xf6, 0x3b, 0xee, 0x9d, 0x8c, 0xbe, 0x87, 0x36,
	0x17, 0xbe, 0xc8, 0x78, 0xaf, 0xb5, 0xab, 0xed, 0xaf, 0x1f, 0x6f, 0x0f, 0xe6, 0xec, 0x83, 0x8b,
	0x04, 0xd3, 0xcb, 0x59, 0x82, 0xdd, 0x12, 0x83, 0x06, 0xd0, 0xe6, 0x39, 0x11, 0x41, 0xd4, 0x7b,
	0xa2, 0xd0, 0xcf, 0xea, 0xe8, 0x91, 0xb2, 0x94, 0x78, 0xf5, 0x46, 0x3b, 0x60, 0xe2, 0xdb, 0x84,
	0xa4, 0xd8, 0x13, 0x24, 0xc6, 0xbd, 0xa7, 0x2a, 0x26, 0x28, 0x54, 0x97, 0x24, 0xc6, 0x12, 0xc0,
	0x71, 0xfa, 0x11, 0xa7, 0x05, 0xa0, 0x5d, 0x00, 0x0a, 0x95, 0x02, 0x6c, 0xc3, 0xd3, 0x29, 0xfe,
	0x88, 0xa7, 0x3d, 0x5d, 0x99, 0x0a, 0x01, 0xbd, 0x04, 0x33, 0xf5, 0xe9, 0xc4, 0x2b, 0x83, 0x31,
	0x1e, 0x86, 0xee, 0xfa, 0x74, 0xa2, 0x42, 0x01, 0x09, 0x2c, 0x42, 0x43, 0x5f, 0x81, 0x71, 0xe3,
	0x4f, 0xb0, 0x27, 0xeb, 0xd3, 0x51, 0x7c, 0xba, 0x94, 0x7f, 0x23, 0x63, 0xf4, 0x33, 0xac, 0x61,
	0x2a, 0x70, 0xea, 0x51, 0x26, 0x48, 0x80, 0x7b, 0xf0, 0xc9, 0xfc, 0x4c, 0x85, 0x75, 0x14, 0xd4,
	0xfe, 0x03, 0x36, 0xde, 0x44, 0x3e, 0x0d, 0xf1, 0x48, 0x15, 0xc9, 0xc5, 0x7f, 0x2d, 0xe8, 0xc1,
	0x4f, 0x60, 0x16, 0xc1, 0x7a, 0x62, 0x96, 0x14, 0x6d, 0x58, 0x4e, 0x0f, 0xfc, 0xee, 0x6d, 0x7f,
	0x00, 0xab, 0xc9, 0xce, 0x13, 0xf4, 0x2b, 0x6c, 0x65, 0x93, 0xdc, 0x4b, 0xee, 0x1a, 0xef, 0x11,
	0x7a, 0xc3, 0x94, 0x3b, 0xf3, 0xb8, 0x5f, 0x27, 0x6d, 0xce, 0x86, 0xbb, 0x99, 0x4d, 0xf2, 0xa6,
	0xca, 0xfe, 0x00, 0x5b, 0x05, 0xbf, 0x7b, 0x57, 0xa7, 0xc5, 0x19, 0xdc, 0xab, 0xf9, 0xea, 0xe3,
	0x6a, 0x6e, 0xbf, 0x83, 0xed, 0x87, 0xfc, 0x3c, 0xb9, 0x4f, 0xa7, 0x3d, 0x92, 0x6e, 0x07, 0xcc,
	0xe1, 0x6d, 0xc2, 0x78, 0x96, 0xe2, 0x85, 0x61, 0xda, 0xdd, 0x1a, 0x80, 0x27, 0x76, 0x52, 0xb9,
	0x1f, 0xce, 0x3b, 0xb6, 0x38, 0xbf, 0x53, 0xd8, 0xaa, 0x4f, 0x40, 0x33, 0xcf, 0x65, 0x9d, 0xda,
	0xac, 0x0d, 0x42, 0x19, 0xa1, 0x07, 0x5f, 0x2c, 0xf0, 0xc8, 0x93, 0x65, 0x0e, 0xb4, 0xcf, 0x75,
	0xf0, 0xaf, 0x06, 0xfa, 0x28, 0x62, 0xf9, 0x3b, 0x1e, 0xca, 0x89, 0xe6, 0x98, 0x8e, 0xbd, 0x79,
	0x2e, 0xba, 0x94, 0xe5, 0x44, 0x7f, 0x0b, 0x5d, 0x65, 0xba, 0xb7, 0xfa, 0x6b, 0x52, 0xe9, 0x54,
	0xeb, 0xff, 0x02, 0x94, 0xec, 0xf9, 0x41, 0xc0, 0x32, 0x2a, 0xd4, 0x11, 0xe8, 0xb8, 0xa6, 0xd4,
	0x9d, 0x14, 0x2a, 0xe9, 0x22, 0xe6, 0xa1, 0x27, 0xf0, 0xad, 0x50, 0x5b, 0xdf, 0x71, 0xf5, 0x98,
	0x87, 0x97, 0xf8, 0x56, 0xa0, 0x67, 0xd0, 0xce, 0x31, 0x09, 0x23, 0xa1, 0x36, 0xbb, 0xe5, 0x96,
	0x92, 0x5c, 0xda, 0x82, 0xae, 0xd8, 0xe7, 0x42, 0xb0, 0x67, 0xb0, 0x3e, 0xc2, 0x74, 0x5c, 0x86,
	0x2e, 0x9b, 0xb0, 0x07, 0xad, 0x98, 0x87, 0xe5, 0xdc, 0x6e, 0x35, 0x2a, 0x50, 0x82, 0xa4, 0x5d,
	0x06, 0x99, 0xe2, 0x00, 0x13, 0x79, 0x26, 0x64, 0xa2, 0xab, 0x8a, 0xd5, 0xac, 0x74, 0x32, 0xd9,
	0x6f, 0x00, 0x82, 0xc8, 0xa7, 0x14, 0x4f, 0x3d, 0x32, 0x56, 0x59, 0x74, 0xdd, 0x4e, 0xa9, 0x39,
	0x1b, 0xdb, 0x9b, 0xb0, 0xd1, 0x70, 0xcd, 0x93, 0x83, 0x1f, 0xc1, 0xa8, 0xce, 0x1b, 0x32, 0x41,
	0x77, 0x2e, 0xbc, 0x8b, 0xf7, 0x43, 0xc7, 0x5a, 0x41, 0x06, 0x3c, 0x51, 0x2f, 0x0d, 0x01, 0xb4,
	0x4f, 0xdd, 0xe1, 0xf0, 0x7a, 0x68, 0xad, 0x22, 0x1d, 0x5a, 0xaf, 0x4f, 0x1c, 0xab, 0x75, 0xf0,
	0x1d, 0xc0, 0xbc, 0x3d, 0x68, 0x1d, 0x60, 0x74, 0x75, 0x76, 0xf9, 0xe6, 0xad, 0x77, 0x71, 0x7a,
	0x6a, 0xad, 0xa0, 0x2e, 0x74, 0x2a, 0xd9, 0xb1, 0xb4, 0x83, 0x3d, 0x30, 0xaa, 0x29, 0x46, 0x6b,
	0x60, 0xb8, 0x27, 0xce, 0x79, 0x09, 0x34, 0x41, 0x2f, 0x24, 0xc7, 0xd2, 0x8e, 0xff, 0x69, 0xc1,
	0xc6, 0xef, 0x2c, 0x3b, 0xa7, 0x2c, 0xbf, 0x8a, 0xd8, 0x95, 0xbc, 0xf5, 0xe8, 0x1c, 0xd6, 0xea,
	0x7b, 0x8f, 0x9e, 0xd7, 0xcb, 0x73, 0xef, 0xde, 0xf4, 0xbf, 0x5e, 0x6e, 0xe4, 0x89, 0xbd, 0x82,
	0xae, 0xaa, 0x23, 0x32, 0x5f, 0x42, 0xb4, 0xf3, 0xf0, 0x9b, 0xc6, 0x09, 0xe8, 0xef, 0x7e, 0x1a,
	0xa0, 0x88, 0xaf, 0x61, 0xf3, 0xc1, 0xb0, 0xa3, 0x05, 0x1f, 0x36, 0xb7, 0xaf, 0xff, 0xe2, 0x7f,
	0x10, 0x8a, 0xfb, 0x15, 0x18, 0xd5, 0x26, 0xa3, 0x2f, 0xeb, 0x1f, 0xd4, 0x0e, 0x40, 0x7f, 0xb1,
	0x41, 0x7d, 0xff, 0x16, 0xcc, 0x5a, 0xd3, 0x51, 0xe3, 0x2e, 0x36, 0x07, 0xb1, 0xff, 0x7c, 0xa9,
	0x4d, 0x32, 0xbd, 0x7e, 0x75, 0xfd, 0x4b, 0xc8, 0xa6, 0x3e, 0x0d, 0x07, 0x2f, 0x8f, 0x85, 0x18,
	0x04, 0x2c, 0x3e, 0x52, 0xbf, 0xe2, 0x80, 0x4d, 0x8f, 0xe4, 0xbf, 0x8a, 0x04, 0x98, 0x1f, 0xcd,
	0x19, 0x6a, 0x4f, 0xf5, 0xdf, 0xfe, 0xb3, 0xad, 0xd0, 0x3f, 0xfc, 0x17, 0x00, 0x00, 0xff, 0xff,
	0x1a, 0xc0, 0x02, 0xe2, 0xe1, 0x07, 0x00, 0x00,
}
