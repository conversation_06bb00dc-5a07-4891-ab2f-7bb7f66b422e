// Code generated by protoc-gen-gogo.
// source: src/presencesvr/presence.proto
// DO NOT EDIT!

/*
	Package Presence is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/presencesvr/presence.proto

	It has these top-level messages:
		Pres
		Proxy
		UpdatePresReq
		UpdatePresResp
		GetPresReq
		GetPresResp
		PresStat
		BatchGetPresReq
		ClientInfo
		ProxyInfo
		SubscribeChannelReq
		BatchSubscribeChannelReq
		GetChannelProxyInfoReq
		GetChannelProxyInfoResp
		BatchGetPresResp
		StatPresReq
		StatPresResp
*/
package Presence

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type PRES_TYPE int32

const (
	PRES_TYPE_PRES_TYPE_ANDROID PRES_TYPE = 0
	PRES_TYPE_PRES_TYPE_IOS     PRES_TYPE = 1
	PRES_TYPE_PRES_TYPE_PC      PRES_TYPE = 2
)

var PRES_TYPE_name = map[int32]string{
	0: "PRES_TYPE_ANDROID",
	1: "PRES_TYPE_IOS",
	2: "PRES_TYPE_PC",
}
var PRES_TYPE_value = map[string]int32{
	"PRES_TYPE_ANDROID": 0,
	"PRES_TYPE_IOS":     1,
	"PRES_TYPE_PC":      2,
}

func (x PRES_TYPE) Enum() *PRES_TYPE {
	p := new(PRES_TYPE)
	*p = x
	return p
}
func (x PRES_TYPE) String() string {
	return proto.EnumName(PRES_TYPE_name, int32(x))
}
func (x *PRES_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PRES_TYPE_value, data, "PRES_TYPE")
	if err != nil {
		return err
	}
	*x = PRES_TYPE(value)
	return nil
}
func (PRES_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorPresence, []int{0} }

type PRES_STATUS int32

const (
	PRES_STATUS_PRES_OFFLINE PRES_STATUS = 0
	PRES_STATUS_PRES_ONLINE  PRES_STATUS = 1
)

var PRES_STATUS_name = map[int32]string{
	0: "PRES_OFFLINE",
	1: "PRES_ONLINE",
}
var PRES_STATUS_value = map[string]int32{
	"PRES_OFFLINE": 0,
	"PRES_ONLINE":  1,
}

func (x PRES_STATUS) Enum() *PRES_STATUS {
	p := new(PRES_STATUS)
	*p = x
	return p
}
func (x PRES_STATUS) String() string {
	return proto.EnumName(PRES_STATUS_name, int32(x))
}
func (x *PRES_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PRES_STATUS_value, data, "PRES_STATUS")
	if err != nil {
		return err
	}
	*x = PRES_STATUS(value)
	return nil
}
func (PRES_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorPresence, []int{1} }

type Pres struct {
	ProxyIp      uint32 `protobuf:"varint,1,req,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort    uint32 `protobuf:"varint,2,req,name=proxy_port,json=proxyPort" json:"proxy_port"`
	Uid          uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
	DeviceId     []byte `protobuf:"bytes,4,req,name=device_id,json=deviceId" json:"device_id"`
	Status       uint32 `protobuf:"varint,5,req,name=status" json:"status"`
	ClientId     uint32 `protobuf:"varint,6,req,name=client_id,json=clientId" json:"client_id"`
	ClientIp     uint32 `protobuf:"varint,7,req,name=client_ip,json=clientIp" json:"client_ip"`
	OnlineTime   uint32 `protobuf:"varint,8,req,name=online_time,json=onlineTime" json:"online_time"`
	TerminalType uint32 `protobuf:"varint,9,opt,name=terminal_type,json=terminalType" json:"terminal_type"`
	OnlineTimeMs uint32 `protobuf:"varint,10,opt,name=online_time_ms,json=onlineTimeMs" json:"online_time_ms"`
	OfflineTime  uint64 `protobuf:"varint,11,opt,name=offline_time,json=offlineTime" json:"offline_time"`
}

func (m *Pres) Reset()                    { *m = Pres{} }
func (m *Pres) String() string            { return proto.CompactTextString(m) }
func (*Pres) ProtoMessage()               {}
func (*Pres) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{0} }

func (m *Pres) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *Pres) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *Pres) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Pres) GetDeviceId() []byte {
	if m != nil {
		return m.DeviceId
	}
	return nil
}

func (m *Pres) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *Pres) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

func (m *Pres) GetClientIp() uint32 {
	if m != nil {
		return m.ClientIp
	}
	return 0
}

func (m *Pres) GetOnlineTime() uint32 {
	if m != nil {
		return m.OnlineTime
	}
	return 0
}

func (m *Pres) GetTerminalType() uint32 {
	if m != nil {
		return m.TerminalType
	}
	return 0
}

func (m *Pres) GetOnlineTimeMs() uint32 {
	if m != nil {
		return m.OnlineTimeMs
	}
	return 0
}

func (m *Pres) GetOfflineTime() uint64 {
	if m != nil {
		return m.OfflineTime
	}
	return 0
}

type Proxy struct {
	ProxyIp   uint32 `protobuf:"varint,1,req,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort uint32 `protobuf:"varint,2,req,name=proxy_port,json=proxyPort" json:"proxy_port"`
	BootTime  uint32 `protobuf:"varint,3,req,name=boot_time,json=bootTime" json:"boot_time"`
}

func (m *Proxy) Reset()                    { *m = Proxy{} }
func (m *Proxy) String() string            { return proto.CompactTextString(m) }
func (*Proxy) ProtoMessage()               {}
func (*Proxy) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{1} }

func (m *Proxy) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *Proxy) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *Proxy) GetBootTime() uint32 {
	if m != nil {
		return m.BootTime
	}
	return 0
}

type UpdatePresReq struct {
	// required uint32 uid = 1;
	// required uint32 prestype = 2;
	Proxy    *Proxy  `protobuf:"bytes,1,req,name=proxy" json:"proxy,omitempty"`
	PresList []*Pres `protobuf:"bytes,4,rep,name=pres_list,json=presList" json:"pres_list,omitempty"`
}

func (m *UpdatePresReq) Reset()                    { *m = UpdatePresReq{} }
func (m *UpdatePresReq) String() string            { return proto.CompactTextString(m) }
func (*UpdatePresReq) ProtoMessage()               {}
func (*UpdatePresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{2} }

func (m *UpdatePresReq) GetProxy() *Proxy {
	if m != nil {
		return m.Proxy
	}
	return nil
}

func (m *UpdatePresReq) GetPresList() []*Pres {
	if m != nil {
		return m.PresList
	}
	return nil
}

type UpdatePresResp struct {
}

func (m *UpdatePresResp) Reset()                    { *m = UpdatePresResp{} }
func (m *UpdatePresResp) String() string            { return proto.CompactTextString(m) }
func (*UpdatePresResp) ProtoMessage()               {}
func (*UpdatePresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{3} }

// ////////////////
type GetPresReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPresReq) Reset()                    { *m = GetPresReq{} }
func (m *GetPresReq) String() string            { return proto.CompactTextString(m) }
func (*GetPresReq) ProtoMessage()               {}
func (*GetPresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{4} }

func (m *GetPresReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPresResp struct {
	PresList []*Pres `protobuf:"bytes,3,rep,name=pres_list,json=presList" json:"pres_list,omitempty"`
}

func (m *GetPresResp) Reset()                    { *m = GetPresResp{} }
func (m *GetPresResp) String() string            { return proto.CompactTextString(m) }
func (*GetPresResp) ProtoMessage()               {}
func (*GetPresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{5} }

func (m *GetPresResp) GetPresList() []*Pres {
	if m != nil {
		return m.PresList
	}
	return nil
}

// ////////////////
type PresStat struct {
	ProxyIp         uint32 `protobuf:"varint,1,req,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort       uint32 `protobuf:"varint,2,req,name=proxy_port,json=proxyPort" json:"proxy_port"`
	OnlineCount     uint32 `protobuf:"varint,3,req,name=online_count,json=onlineCount" json:"online_count"`
	MaxOnlineCount  uint32 `protobuf:"varint,4,req,name=max_online_count,json=maxOnlineCount" json:"max_online_count"`
	MaxOnlineAtTime uint32 `protobuf:"varint,5,req,name=max_online_at_time,json=maxOnlineAtTime" json:"max_online_at_time"`
	BootAt          uint32 `protobuf:"varint,6,opt,name=boot_at,json=bootAt" json:"boot_at"`
}

func (m *PresStat) Reset()                    { *m = PresStat{} }
func (m *PresStat) String() string            { return proto.CompactTextString(m) }
func (*PresStat) ProtoMessage()               {}
func (*PresStat) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{6} }

func (m *PresStat) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *PresStat) GetProxyPort() uint32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

func (m *PresStat) GetOnlineCount() uint32 {
	if m != nil {
		return m.OnlineCount
	}
	return 0
}

func (m *PresStat) GetMaxOnlineCount() uint32 {
	if m != nil {
		return m.MaxOnlineCount
	}
	return 0
}

func (m *PresStat) GetMaxOnlineAtTime() uint32 {
	if m != nil {
		return m.MaxOnlineAtTime
	}
	return 0
}

func (m *PresStat) GetBootAt() uint32 {
	if m != nil {
		return m.BootAt
	}
	return 0
}

type BatchGetPresReq struct {
	UidList []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetPresReq) Reset()                    { *m = BatchGetPresReq{} }
func (m *BatchGetPresReq) String() string            { return proto.CompactTextString(m) }
func (*BatchGetPresReq) ProtoMessage()               {}
func (*BatchGetPresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{7} }

func (m *BatchGetPresReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type ClientInfo struct {
	Uid      int64  `protobuf:"varint,1,req,name=uid" json:"uid"`
	ClientId uint32 `protobuf:"varint,2,req,name=client_id,json=clientId" json:"client_id"`
}

func (m *ClientInfo) Reset()                    { *m = ClientInfo{} }
func (m *ClientInfo) String() string            { return proto.CompactTextString(m) }
func (*ClientInfo) ProtoMessage()               {}
func (*ClientInfo) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{8} }

func (m *ClientInfo) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ClientInfo) GetClientId() uint32 {
	if m != nil {
		return m.ClientId
	}
	return 0
}

type ProxyInfo struct {
	ProxyIp   uint32 `protobuf:"varint,1,req,name=proxy_ip,json=proxyIp" json:"proxy_ip"`
	ProxyPort int32  `protobuf:"varint,2,req,name=proxy_port,json=proxyPort" json:"proxy_port"`
}

func (m *ProxyInfo) Reset()                    { *m = ProxyInfo{} }
func (m *ProxyInfo) String() string            { return proto.CompactTextString(m) }
func (*ProxyInfo) ProtoMessage()               {}
func (*ProxyInfo) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{9} }

func (m *ProxyInfo) GetProxyIp() uint32 {
	if m != nil {
		return m.ProxyIp
	}
	return 0
}

func (m *ProxyInfo) GetProxyPort() int32 {
	if m != nil {
		return m.ProxyPort
	}
	return 0
}

type SubscribeChannelReq struct {
	ChannelId  int64         `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ProxyInfo  *ProxyInfo    `protobuf:"bytes,2,req,name=proxy_info,json=proxyInfo" json:"proxy_info,omitempty"`
	Timestamp  int64         `protobuf:"varint,3,opt,name=timestamp" json:"timestamp"`
	Version    int64         `protobuf:"varint,4,opt,name=version" json:"version"`
	ClientInfo []*ClientInfo `protobuf:"bytes,5,rep,name=client_info,json=clientInfo" json:"client_info,omitempty"`
}

func (m *SubscribeChannelReq) Reset()                    { *m = SubscribeChannelReq{} }
func (m *SubscribeChannelReq) String() string            { return proto.CompactTextString(m) }
func (*SubscribeChannelReq) ProtoMessage()               {}
func (*SubscribeChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{10} }

func (m *SubscribeChannelReq) GetChannelId() int64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SubscribeChannelReq) GetProxyInfo() *ProxyInfo {
	if m != nil {
		return m.ProxyInfo
	}
	return nil
}

func (m *SubscribeChannelReq) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *SubscribeChannelReq) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *SubscribeChannelReq) GetClientInfo() []*ClientInfo {
	if m != nil {
		return m.ClientInfo
	}
	return nil
}

type BatchSubscribeChannelReq struct {
	Seq int64                  `protobuf:"varint,1,req,name=seq" json:"seq"`
	Sub []*SubscribeChannelReq `protobuf:"bytes,2,rep,name=sub" json:"sub,omitempty"`
}

func (m *BatchSubscribeChannelReq) Reset()         { *m = BatchSubscribeChannelReq{} }
func (m *BatchSubscribeChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchSubscribeChannelReq) ProtoMessage()    {}
func (*BatchSubscribeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorPresence, []int{11}
}

func (m *BatchSubscribeChannelReq) GetSeq() int64 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *BatchSubscribeChannelReq) GetSub() []*SubscribeChannelReq {
	if m != nil {
		return m.Sub
	}
	return nil
}

type GetChannelProxyInfoReq struct {
	Seq       int64 `protobuf:"varint,1,req,name=seq" json:"seq"`
	ChannelId int64 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelProxyInfoReq) Reset()                    { *m = GetChannelProxyInfoReq{} }
func (m *GetChannelProxyInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelProxyInfoReq) ProtoMessage()               {}
func (*GetChannelProxyInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{12} }

func (m *GetChannelProxyInfoReq) GetSeq() int64 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *GetChannelProxyInfoReq) GetChannelId() int64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelProxyInfoResp struct {
	Seq       int64        `protobuf:"varint,1,req,name=seq" json:"seq"`
	ChannelId int64        `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ProxyInfo []*ProxyInfo `protobuf:"bytes,3,rep,name=proxy_info,json=proxyInfo" json:"proxy_info,omitempty"`
}

func (m *GetChannelProxyInfoResp) Reset()                    { *m = GetChannelProxyInfoResp{} }
func (m *GetChannelProxyInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetChannelProxyInfoResp) ProtoMessage()               {}
func (*GetChannelProxyInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{13} }

func (m *GetChannelProxyInfoResp) GetSeq() int64 {
	if m != nil {
		return m.Seq
	}
	return 0
}

func (m *GetChannelProxyInfoResp) GetChannelId() int64 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelProxyInfoResp) GetProxyInfo() []*ProxyInfo {
	if m != nil {
		return m.ProxyInfo
	}
	return nil
}

type BatchGetPresResp struct {
	PresList []*Pres `protobuf:"bytes,3,rep,name=pres_list,json=presList" json:"pres_list,omitempty"`
}

func (m *BatchGetPresResp) Reset()                    { *m = BatchGetPresResp{} }
func (m *BatchGetPresResp) String() string            { return proto.CompactTextString(m) }
func (*BatchGetPresResp) ProtoMessage()               {}
func (*BatchGetPresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{14} }

func (m *BatchGetPresResp) GetPresList() []*Pres {
	if m != nil {
		return m.PresList
	}
	return nil
}

type StatPresReq struct {
}

func (m *StatPresReq) Reset()                    { *m = StatPresReq{} }
func (m *StatPresReq) String() string            { return proto.CompactTextString(m) }
func (*StatPresReq) ProtoMessage()               {}
func (*StatPresReq) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{15} }

type StatPresResp struct {
	PresStatList []*PresStat `protobuf:"bytes,2,rep,name=pres_stat_list,json=presStatList" json:"pres_stat_list,omitempty"`
}

func (m *StatPresResp) Reset()                    { *m = StatPresResp{} }
func (m *StatPresResp) String() string            { return proto.CompactTextString(m) }
func (*StatPresResp) ProtoMessage()               {}
func (*StatPresResp) Descriptor() ([]byte, []int) { return fileDescriptorPresence, []int{16} }

func (m *StatPresResp) GetPresStatList() []*PresStat {
	if m != nil {
		return m.PresStatList
	}
	return nil
}

func init() {
	proto.RegisterType((*Pres)(nil), "Presence.Pres")
	proto.RegisterType((*Proxy)(nil), "Presence.Proxy")
	proto.RegisterType((*UpdatePresReq)(nil), "Presence.UpdatePresReq")
	proto.RegisterType((*UpdatePresResp)(nil), "Presence.UpdatePresResp")
	proto.RegisterType((*GetPresReq)(nil), "Presence.GetPresReq")
	proto.RegisterType((*GetPresResp)(nil), "Presence.GetPresResp")
	proto.RegisterType((*PresStat)(nil), "Presence.PresStat")
	proto.RegisterType((*BatchGetPresReq)(nil), "Presence.BatchGetPresReq")
	proto.RegisterType((*ClientInfo)(nil), "Presence.ClientInfo")
	proto.RegisterType((*ProxyInfo)(nil), "Presence.ProxyInfo")
	proto.RegisterType((*SubscribeChannelReq)(nil), "Presence.SubscribeChannelReq")
	proto.RegisterType((*BatchSubscribeChannelReq)(nil), "Presence.BatchSubscribeChannelReq")
	proto.RegisterType((*GetChannelProxyInfoReq)(nil), "Presence.GetChannelProxyInfoReq")
	proto.RegisterType((*GetChannelProxyInfoResp)(nil), "Presence.GetChannelProxyInfoResp")
	proto.RegisterType((*BatchGetPresResp)(nil), "Presence.BatchGetPresResp")
	proto.RegisterType((*StatPresReq)(nil), "Presence.StatPresReq")
	proto.RegisterType((*StatPresResp)(nil), "Presence.StatPresResp")
	proto.RegisterEnum("Presence.PRES_TYPE", PRES_TYPE_name, PRES_TYPE_value)
	proto.RegisterEnum("Presence.PRES_STATUS", PRES_STATUS_name, PRES_STATUS_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Presence service

type PresenceClient interface {
	UpdatePres(ctx context.Context, in *UpdatePresReq, opts ...grpc.CallOption) (*UpdatePresResp, error)
	GetPres(ctx context.Context, in *GetPresReq, opts ...grpc.CallOption) (*GetPresResp, error)
	BatchGetPres(ctx context.Context, in *BatchGetPresReq, opts ...grpc.CallOption) (*BatchGetPresResp, error)
	StatPres(ctx context.Context, in *StatPresReq, opts ...grpc.CallOption) (*StatPresResp, error)
	SubscribeChannel(ctx context.Context, in *SubscribeChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatchSubscribeChannel(ctx context.Context, in *BatchSubscribeChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelSubscriber(ctx context.Context, in *GetChannelProxyInfoReq, opts ...grpc.CallOption) (*GetChannelProxyInfoResp, error)
}

type presenceClient struct {
	cc *grpc.ClientConn
}

func NewPresenceClient(cc *grpc.ClientConn) PresenceClient {
	return &presenceClient{cc}
}

func (c *presenceClient) UpdatePres(ctx context.Context, in *UpdatePresReq, opts ...grpc.CallOption) (*UpdatePresResp, error) {
	out := new(UpdatePresResp)
	err := grpc.Invoke(ctx, "/Presence.Presence/UpdatePres", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceClient) GetPres(ctx context.Context, in *GetPresReq, opts ...grpc.CallOption) (*GetPresResp, error) {
	out := new(GetPresResp)
	err := grpc.Invoke(ctx, "/Presence.Presence/GetPres", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceClient) BatchGetPres(ctx context.Context, in *BatchGetPresReq, opts ...grpc.CallOption) (*BatchGetPresResp, error) {
	out := new(BatchGetPresResp)
	err := grpc.Invoke(ctx, "/Presence.Presence/BatchGetPres", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceClient) StatPres(ctx context.Context, in *StatPresReq, opts ...grpc.CallOption) (*StatPresResp, error) {
	out := new(StatPresResp)
	err := grpc.Invoke(ctx, "/Presence.Presence/StatPres", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceClient) SubscribeChannel(ctx context.Context, in *SubscribeChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Presence.Presence/SubscribeChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceClient) BatchSubscribeChannel(ctx context.Context, in *BatchSubscribeChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Presence.Presence/BatchSubscribeChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presenceClient) GetChannelSubscriber(ctx context.Context, in *GetChannelProxyInfoReq, opts ...grpc.CallOption) (*GetChannelProxyInfoResp, error) {
	out := new(GetChannelProxyInfoResp)
	err := grpc.Invoke(ctx, "/Presence.Presence/GetChannelSubscriber", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Presence service

type PresenceServer interface {
	UpdatePres(context.Context, *UpdatePresReq) (*UpdatePresResp, error)
	GetPres(context.Context, *GetPresReq) (*GetPresResp, error)
	BatchGetPres(context.Context, *BatchGetPresReq) (*BatchGetPresResp, error)
	StatPres(context.Context, *StatPresReq) (*StatPresResp, error)
	SubscribeChannel(context.Context, *SubscribeChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatchSubscribeChannel(context.Context, *BatchSubscribeChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelSubscriber(context.Context, *GetChannelProxyInfoReq) (*GetChannelProxyInfoResp, error)
}

func RegisterPresenceServer(s *grpc.Server, srv PresenceServer) {
	s.RegisterService(&_Presence_serviceDesc, srv)
}

func _Presence_UpdatePres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServer).UpdatePres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.Presence/UpdatePres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServer).UpdatePres(ctx, req.(*UpdatePresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Presence_GetPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServer).GetPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.Presence/GetPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServer).GetPres(ctx, req.(*GetPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Presence_BatchGetPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServer).BatchGetPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.Presence/BatchGetPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServer).BatchGetPres(ctx, req.(*BatchGetPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Presence_StatPres_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatPresReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServer).StatPres(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.Presence/StatPres",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServer).StatPres(ctx, req.(*StatPresReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Presence_SubscribeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubscribeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServer).SubscribeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.Presence/SubscribeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServer).SubscribeChannel(ctx, req.(*SubscribeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Presence_BatchSubscribeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSubscribeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServer).BatchSubscribeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.Presence/BatchSubscribeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServer).BatchSubscribeChannel(ctx, req.(*BatchSubscribeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Presence_GetChannelSubscriber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelProxyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresenceServer).GetChannelSubscriber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Presence.Presence/GetChannelSubscriber",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresenceServer).GetChannelSubscriber(ctx, req.(*GetChannelProxyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Presence_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Presence.Presence",
	HandlerType: (*PresenceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdatePres",
			Handler:    _Presence_UpdatePres_Handler,
		},
		{
			MethodName: "GetPres",
			Handler:    _Presence_GetPres_Handler,
		},
		{
			MethodName: "BatchGetPres",
			Handler:    _Presence_BatchGetPres_Handler,
		},
		{
			MethodName: "StatPres",
			Handler:    _Presence_StatPres_Handler,
		},
		{
			MethodName: "SubscribeChannel",
			Handler:    _Presence_SubscribeChannel_Handler,
		},
		{
			MethodName: "BatchSubscribeChannel",
			Handler:    _Presence_BatchSubscribeChannel_Handler,
		},
		{
			MethodName: "GetChannelSubscriber",
			Handler:    _Presence_GetChannelSubscriber_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/presencesvr/presence.proto",
}

func (m *Pres) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Pres) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyIp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	if m.DeviceId != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintPresence(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	dAtA[i] = 0x28
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x30
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ClientId))
	dAtA[i] = 0x38
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ClientIp))
	dAtA[i] = 0x40
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.OnlineTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.TerminalType))
	dAtA[i] = 0x50
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.OnlineTimeMs))
	dAtA[i] = 0x58
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.OfflineTime))
	return i, nil
}

func (m *Proxy) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Proxy) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyIp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.BootTime))
	return i, nil
}

func (m *UpdatePresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Proxy == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("proxy")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintPresence(dAtA, i, uint64(m.Proxy.Size()))
		n1, err := m.Proxy.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.PresList) > 0 {
		for _, msg := range m.PresList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdatePresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetPresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, msg := range m.PresList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *PresStat) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PresStat) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyIp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyPort))
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.OnlineCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.MaxOnlineCount))
	dAtA[i] = 0x28
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.MaxOnlineAtTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.BootAt))
	return i, nil
}

func (m *BatchGetPresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintPresence(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ClientInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClientInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ClientId))
	return i, nil
}

func (m *ProxyInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ProxyInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyIp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ProxyPort))
	return i, nil
}

func (m *SubscribeChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubscribeChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ChannelId))
	if m.ProxyInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("proxy_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintPresence(dAtA, i, uint64(m.ProxyInfo.Size()))
		n2, err := m.ProxyInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x20
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Version))
	if len(m.ClientInfo) > 0 {
		for _, msg := range m.ClientInfo {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchSubscribeChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSubscribeChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Seq))
	if len(m.Sub) > 0 {
		for _, msg := range m.Sub {
			dAtA[i] = 0x12
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetChannelProxyInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelProxyInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Seq))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelProxyInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelProxyInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.Seq))
	dAtA[i] = 0x10
	i++
	i = encodeVarintPresence(dAtA, i, uint64(m.ChannelId))
	if len(m.ProxyInfo) > 0 {
		for _, msg := range m.ProxyInfo {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetPresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, msg := range m.PresList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StatPresReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StatPresReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *StatPresResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StatPresResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PresStatList) > 0 {
		for _, msg := range m.PresStatList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintPresence(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Presence(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Presence(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintPresence(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Pres) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ProxyIp))
	n += 1 + sovPresence(uint64(m.ProxyPort))
	n += 1 + sovPresence(uint64(m.Uid))
	if m.DeviceId != nil {
		l = len(m.DeviceId)
		n += 1 + l + sovPresence(uint64(l))
	}
	n += 1 + sovPresence(uint64(m.Status))
	n += 1 + sovPresence(uint64(m.ClientId))
	n += 1 + sovPresence(uint64(m.ClientIp))
	n += 1 + sovPresence(uint64(m.OnlineTime))
	n += 1 + sovPresence(uint64(m.TerminalType))
	n += 1 + sovPresence(uint64(m.OnlineTimeMs))
	n += 1 + sovPresence(uint64(m.OfflineTime))
	return n
}

func (m *Proxy) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ProxyIp))
	n += 1 + sovPresence(uint64(m.ProxyPort))
	n += 1 + sovPresence(uint64(m.BootTime))
	return n
}

func (m *UpdatePresReq) Size() (n int) {
	var l int
	_ = l
	if m.Proxy != nil {
		l = m.Proxy.Size()
		n += 1 + l + sovPresence(uint64(l))
	}
	if len(m.PresList) > 0 {
		for _, e := range m.PresList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *UpdatePresResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetPresReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	return n
}

func (m *GetPresResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, e := range m.PresList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *PresStat) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ProxyIp))
	n += 1 + sovPresence(uint64(m.ProxyPort))
	n += 1 + sovPresence(uint64(m.OnlineCount))
	n += 1 + sovPresence(uint64(m.MaxOnlineCount))
	n += 1 + sovPresence(uint64(m.MaxOnlineAtTime))
	n += 1 + sovPresence(uint64(m.BootAt))
	return n
}

func (m *BatchGetPresReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovPresence(uint64(e))
		}
	}
	return n
}

func (m *ClientInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Uid))
	n += 1 + sovPresence(uint64(m.ClientId))
	return n
}

func (m *ProxyInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ProxyIp))
	n += 1 + sovPresence(uint64(m.ProxyPort))
	return n
}

func (m *SubscribeChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.ChannelId))
	if m.ProxyInfo != nil {
		l = m.ProxyInfo.Size()
		n += 1 + l + sovPresence(uint64(l))
	}
	n += 1 + sovPresence(uint64(m.Timestamp))
	n += 1 + sovPresence(uint64(m.Version))
	if len(m.ClientInfo) > 0 {
		for _, e := range m.ClientInfo {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *BatchSubscribeChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Seq))
	if len(m.Sub) > 0 {
		for _, e := range m.Sub {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *GetChannelProxyInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Seq))
	n += 1 + sovPresence(uint64(m.ChannelId))
	return n
}

func (m *GetChannelProxyInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovPresence(uint64(m.Seq))
	n += 1 + sovPresence(uint64(m.ChannelId))
	if len(m.ProxyInfo) > 0 {
		for _, e := range m.ProxyInfo {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *BatchGetPresResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PresList) > 0 {
		for _, e := range m.PresList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func (m *StatPresReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *StatPresResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PresStatList) > 0 {
		for _, e := range m.PresStatList {
			l = e.Size()
			n += 1 + l + sovPresence(uint64(l))
		}
	}
	return n
}

func sovPresence(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozPresence(x uint64) (n int) {
	return sovPresence(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Pres) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Pres: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Pres: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			m.ProxyIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = append(m.DeviceId[:0], dAtA[iNdEx:postIndex]...)
			if m.DeviceId == nil {
				m.DeviceId = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientIp", wireType)
			}
			m.ClientIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineTime", wireType)
			}
			m.OnlineTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TerminalType", wireType)
			}
			m.TerminalType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TerminalType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineTimeMs", wireType)
			}
			m.OnlineTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineTimeMs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OfflineTime", wireType)
			}
			m.OfflineTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfflineTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_port")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("device_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_ip")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("online_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *Proxy) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: Proxy: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: Proxy: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			m.ProxyIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BootTime", wireType)
			}
			m.BootTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BootTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_port")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("boot_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePresReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Proxy", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Proxy == nil {
				m.Proxy = &Proxy{}
			}
			if err := m.Proxy.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresList = append(m.PresList, &Pres{})
			if err := m.PresList[len(m.PresList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresList = append(m.PresList, &Pres{})
			if err := m.PresList[len(m.PresList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PresStat) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PresStat: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PresStat: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			m.ProxyIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OnlineCount", wireType)
			}
			m.OnlineCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OnlineCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxOnlineCount", wireType)
			}
			m.MaxOnlineCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxOnlineCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxOnlineAtTime", wireType)
			}
			m.MaxOnlineAtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxOnlineAtTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BootAt", wireType)
			}
			m.BootAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BootAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_port")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("online_count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("max_online_count")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("max_online_at_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPresReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPresence
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowPresence
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthPresence
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowPresence
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClientInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClientInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClientInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientId", wireType)
			}
			m.ClientId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ProxyInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ProxyInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ProxyInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyIp", wireType)
			}
			m.ProxyIp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyIp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyPort", wireType)
			}
			m.ProxyPort = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ProxyPort |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_ip")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_port")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubscribeChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SubscribeChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SubscribeChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.ProxyInfo == nil {
				m.ProxyInfo = &ProxyInfo{}
			}
			if err := m.ProxyInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientInfo = append(m.ClientInfo, &ClientInfo{})
			if err := m.ClientInfo[len(m.ClientInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("proxy_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSubscribeChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSubscribeChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSubscribeChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seq", wireType)
			}
			m.Seq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seq |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sub", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Sub = append(m.Sub, &SubscribeChannelReq{})
			if err := m.Sub[len(m.Sub)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelProxyInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelProxyInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelProxyInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seq", wireType)
			}
			m.Seq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seq |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelProxyInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelProxyInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelProxyInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seq", wireType)
			}
			m.Seq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seq |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ProxyInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ProxyInfo = append(m.ProxyInfo, &ProxyInfo{})
			if err := m.ProxyInfo[len(m.ProxyInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresList = append(m.PresList, &Pres{})
			if err := m.PresList[len(m.PresList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StatPresReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StatPresReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StatPresReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StatPresResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StatPresResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StatPresResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PresStatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthPresence
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PresStatList = append(m.PresStatList, &PresStat{})
			if err := m.PresStatList[len(m.PresStatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipPresence(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthPresence
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipPresence(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowPresence
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowPresence
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthPresence
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowPresence
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipPresence(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthPresence = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowPresence   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/presencesvr/presence.proto", fileDescriptorPresence) }

var fileDescriptorPresence = []byte{
	// 1151 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0x4f, 0x73, 0xdb, 0x44,
	0x14, 0xb7, 0x2c, 0xff, 0x7d, 0xb6, 0x13, 0x77, 0xdb, 0xa6, 0xaa, 0xa7, 0x75, 0x55, 0xb5, 0x1d,
	0x42, 0x8b, 0x1d, 0xf0, 0x0c, 0x33, 0x8c, 0xc7, 0x63, 0x26, 0x49, 0xd3, 0x60, 0x28, 0x8d, 0xb1,
	0xd3, 0x03, 0xd3, 0x29, 0x46, 0x91, 0x36, 0xd3, 0xa5, 0xb6, 0xb4, 0xd5, 0xae, 0x32, 0xcd, 0x8d,
	0x23, 0xd3, 0x13, 0xf4, 0x0b, 0x70, 0xc9, 0x87, 0xe9, 0x91, 0x4f, 0xc0, 0x40, 0xb9, 0x84, 0xcf,
	0xc0, 0x85, 0xd9, 0x95, 0x6c, 0xad, 0x8d, 0x69, 0x86, 0xd2, 0x9b, 0xf4, 0x7b, 0x3f, 0xbd, 0xdf,
	0xd3, 0xef, 0xbd, 0xb7, 0x12, 0xd4, 0x59, 0xe0, 0x6c, 0xd0, 0x00, 0x33, 0xec, 0x39, 0x98, 0x1d,
	0x05, 0xb3, 0xeb, 0x26, 0x0d, 0x7c, 0xee, 0xa3, 0x42, 0x3f, 0xbe, 0xaf, 0xdd, 0x74, 0xfc, 0xc9,
	0xc4, 0xf7, 0x36, 0xf8, 0xf8, 0x88, 0x12, 0xe7, 0xe9, 0x18, 0x6f, 0xb0, 0xa7, 0x07, 0x21, 0x19,
	0x73, 0xe2, 0xf1, 0x63, 0x1a, 0xf3, 0xad, 0x97, 0x3a, 0x64, 0xc4, 0x23, 0xe8, 0x1a, 0x14, 0x68,
	0xe0, 0x3f, 0x3f, 0x1e, 0x11, 0x6a, 0x68, 0x66, 0x7a, 0xbd, 0xb2, 0x95, 0x79, 0xf5, 0xeb, 0xb5,
	0xd4, 0x20, 0x2f, 0xd1, 0x1e, 0x45, 0x37, 0x00, 0x22, 0x02, 0xf5, 0x03, 0x6e, 0xa4, 0x15, 0x4a,
	0x51, 0xe2, 0x7d, 0x3f, 0xe0, 0x68, 0x0d, 0xf4, 0x90, 0xb8, 0x86, 0xae, 0x44, 0x05, 0x80, 0xae,
	0x43, 0xd1, 0xc5, 0x47, 0xc4, 0xc1, 0x23, 0xe2, 0x1a, 0x19, 0x33, 0xbd, 0x5e, 0x8e, 0xa3, 0x85,
	0x08, 0xee, 0xb9, 0xe8, 0x0a, 0xe4, 0x18, 0xb7, 0x79, 0xc8, 0x8c, 0xac, 0xf2, 0x74, 0x8c, 0x89,
	0x04, 0xce, 0x98, 0x60, 0x8f, 0x8b, 0x04, 0x39, 0x85, 0x50, 0x88, 0xe0, 0x9e, 0xab, 0x52, 0xa8,
	0x91, 0x5f, 0x42, 0xa1, 0xe8, 0x16, 0x94, 0x7c, 0x6f, 0x4c, 0x3c, 0x3c, 0xe2, 0x64, 0x82, 0x8d,
	0x82, 0x42, 0x82, 0x28, 0xb0, 0x4f, 0x26, 0x18, 0xbd, 0x0f, 0x15, 0x8e, 0x83, 0x09, 0xf1, 0xec,
	0xf1, 0x48, 0x78, 0x65, 0x14, 0x4d, 0x6d, 0x46, 0x2c, 0x4f, 0x43, 0xfb, 0xc7, 0x14, 0xa3, 0xdb,
	0xb0, 0xa2, 0x64, 0x1c, 0x4d, 0x98, 0x01, 0x2a, 0x37, 0x49, 0xfa, 0x25, 0x43, 0xef, 0x41, 0xd9,
	0x3f, 0x3c, 0x4c, 0xe4, 0x4b, 0xa6, 0xb6, 0x9e, 0x89, 0x99, 0xa5, 0x38, 0x22, 0xa8, 0x16, 0x85,
	0x6c, 0x5f, 0x58, 0xfa, 0x8e, 0x9a, 0x72, 0x1d, 0x8a, 0x07, 0xbe, 0xcf, 0x23, 0x51, 0xb5, 0x35,
	0x05, 0x01, 0x4b, 0x45, 0x07, 0x2a, 0x0f, 0xa9, 0x6b, 0x73, 0x2c, 0x66, 0x61, 0x80, 0x9f, 0xa1,
	0x5b, 0x90, 0x95, 0x09, 0xa4, 0x6c, 0xa9, 0xb5, 0xda, 0x9c, 0xce, 0x55, 0x53, 0x56, 0x36, 0x88,
	0xa2, 0xe8, 0x0e, 0x14, 0xc5, 0x00, 0x8e, 0xc6, 0x84, 0x71, 0x23, 0x63, 0xea, 0xeb, 0xa5, 0xd6,
	0x8a, 0x4a, 0xc5, 0x6c, 0x50, 0x10, 0x84, 0xfb, 0x84, 0x71, 0xab, 0x0a, 0x2b, 0xaa, 0x08, 0xa3,
	0xd6, 0x4d, 0x80, 0x5d, 0xcc, 0xa7, 0x9a, 0xf1, 0xf0, 0x68, 0x0b, 0xc3, 0x63, 0xb5, 0xa1, 0x34,
	0x63, 0x31, 0x3a, 0xaf, 0xa9, 0x9f, 0xa1, 0xf9, 0x97, 0x06, 0x72, 0x25, 0x86, 0xdc, 0xe6, 0xef,
	0xc8, 0x4e, 0xd1, 0xc6, 0xa8, 0xe5, 0x8e, 0x1f, 0x7a, 0x7c, 0xce, 0xd1, 0x78, 0xbc, 0xb6, 0x45,
	0x00, 0x35, 0xa1, 0x3a, 0xb1, 0x9f, 0x8f, 0xe6, 0xc8, 0x19, 0x85, 0xbc, 0x32, 0xb1, 0x9f, 0xef,
	0x29, 0xfc, 0x8f, 0x00, 0x29, 0x7c, 0x3b, 0x6e, 0x98, 0xba, 0x0d, 0xab, 0xb3, 0x27, 0x36, 0x65,
	0xdf, 0xd0, 0x55, 0xc8, 0xcb, 0xd6, 0xda, 0xdc, 0xc8, 0x29, 0x73, 0x97, 0x13, 0xe0, 0x26, 0xb7,
	0x3e, 0x80, 0xd5, 0x2d, 0x9b, 0x3b, 0x4f, 0x14, 0x93, 0x2f, 0x43, 0x21, 0x24, 0x6e, 0x64, 0x9e,
	0x66, 0xea, 0xeb, 0x95, 0x41, 0x3e, 0x24, 0xae, 0xf4, 0x6a, 0x17, 0x60, 0x3b, 0xda, 0x14, 0xef,
	0xd0, 0x57, 0xbb, 0xa1, 0x2f, 0xac, 0x72, 0xb2, 0x89, 0xe9, 0x65, 0x9b, 0x68, 0x7d, 0x05, 0x45,
	0x39, 0x25, 0x32, 0xcf, 0x5b, 0x98, 0x9e, 0xfd, 0x87, 0xe9, 0xd6, 0x9f, 0x1a, 0x9c, 0x1f, 0x86,
	0x07, 0xcc, 0x09, 0xc8, 0x01, 0xde, 0x7e, 0x62, 0x7b, 0x1e, 0x1e, 0x8b, 0xd7, 0xb9, 0x01, 0xe0,
	0x44, 0x77, 0xa3, 0x85, 0x62, 0x8b, 0x31, 0xde, 0x73, 0x51, 0x6b, 0xaa, 0x40, 0xbc, 0x43, 0x5f,
	0x2a, 0x94, 0x5a, 0xe7, 0x17, 0x26, 0x5a, 0xd4, 0x1a, 0x0b, 0xca, 0xb2, 0x2d, 0x28, 0x0a, 0xfb,
	0x19, 0xb7, 0x27, 0xd4, 0xd0, 0x4d, 0x2d, 0xc9, 0x3b, 0x83, 0x51, 0x1d, 0xf2, 0x47, 0x38, 0x60,
	0xc4, 0xf7, 0x8c, 0x8c, 0xc2, 0x98, 0x82, 0xe8, 0x63, 0x28, 0x4d, 0xad, 0x12, 0xc2, 0x59, 0x39,
	0xab, 0x17, 0x12, 0xe1, 0xc4, 0xed, 0x01, 0x38, 0xb3, 0x6b, 0xcb, 0x01, 0x43, 0x76, 0x6d, 0xd9,
	0xfb, 0xae, 0x81, 0xce, 0xf0, 0xb3, 0xf9, 0xae, 0x30, 0xfc, 0x0c, 0x6d, 0x80, 0xce, 0xc2, 0x03,
	0x23, 0x2d, 0x25, 0xae, 0x26, 0x12, 0x4b, 0x72, 0x0c, 0x04, 0xd3, 0x7a, 0x08, 0x6b, 0xbb, 0x98,
	0xc7, 0x68, 0xe2, 0xc0, 0x1b, 0x24, 0xe6, 0xad, 0x4e, 0x2f, 0xb5, 0xda, 0x7a, 0xa1, 0xc1, 0xa5,
	0xa5, 0x79, 0x19, 0xfd, 0x5f, 0x89, 0x17, 0x7a, 0x18, 0xad, 0xfd, 0x19, 0x3d, 0xb4, 0x3e, 0x85,
	0xea, 0xfc, 0xf8, 0xff, 0xd7, 0xd3, 0xa3, 0x02, 0x25, 0x71, 0x70, 0xc4, 0xbb, 0x63, 0x7d, 0x06,
	0xe5, 0xe4, 0x96, 0x51, 0xf4, 0x09, 0xac, 0xc8, 0x5c, 0xe2, 0x1b, 0x15, 0x25, 0x8c, 0xfc, 0x47,
	0xf3, 0x09, 0xc5, 0x33, 0x83, 0x32, 0x8d, 0xaf, 0x44, 0xe2, 0xdb, 0xbb, 0x50, 0xec, 0x0f, 0x76,
	0x86, 0xa3, 0xfd, 0xaf, 0xfb, 0x3b, 0xe8, 0x22, 0x9c, 0x9b, 0xdd, 0x8c, 0x36, 0x1f, 0xdc, 0x1d,
	0xec, 0xf5, 0xee, 0x56, 0x53, 0xe8, 0x1c, 0x54, 0x12, 0xb8, 0xb7, 0x37, 0xac, 0x6a, 0xa8, 0x0a,
	0xe5, 0x04, 0xea, 0x6f, 0x57, 0xd3, 0xb7, 0x3f, 0x84, 0x92, 0x44, 0x86, 0xfb, 0x9b, 0xfb, 0x0f,
	0x87, 0x33, 0xc2, 0xde, 0xbd, 0x7b, 0xf7, 0x7b, 0x0f, 0x76, 0xaa, 0x29, 0xb4, 0x1a, 0x13, 0xf6,
	0x1e, 0x48, 0x40, 0x6b, 0xfd, 0x94, 0x83, 0xd9, 0x4f, 0x02, 0xfa, 0x59, 0x03, 0x48, 0xce, 0x64,
	0x74, 0x29, 0x29, 0x7c, 0xee, 0x73, 0x50, 0x33, 0x96, 0x07, 0x18, 0xb5, 0xbe, 0xf9, 0xfe, 0xe4,
	0x54, 0xd7, 0x5e, 0x9c, 0x9c, 0xea, 0x10, 0xb6, 0x59, 0xdb, 0x6d, 0x93, 0x36, 0x6d, 0xbf, 0x3c,
	0x39, 0xd5, 0x77, 0x1a, 0xa1, 0xd9, 0x09, 0x89, 0xdb, 0x35, 0x1b, 0xcc, 0xec, 0x44, 0x9f, 0xf1,
	0xae, 0xd9, 0x70, 0xcd, 0x4e, 0xf4, 0xc9, 0x97, 0x11, 0x62, 0x76, 0xe2, 0xc1, 0xbf, 0xdb, 0x35,
	0x1b, 0xd4, 0xec, 0xc8, 0xe6, 0xdd, 0x89, 0x30, 0x93, 0xd0, 0x2e, 0xc2, 0x90, 0x8f, 0xdb, 0x87,
	0x94, 0xcd, 0x49, 0x0e, 0xb4, 0xda, 0xc5, 0x25, 0x28, 0xa3, 0x56, 0x43, 0xd4, 0x95, 0x16, 0x75,
	0x65, 0xc2, 0xb6, 0x27, 0x2b, 0xaa, 0x3d, 0x9a, 0x96, 0xf4, 0xd8, 0x7c, 0xd4, 0xf0, 0xcc, 0x4e,
	0xc8, 0x70, 0xe0, 0xd9, 0x13, 0xdc, 0x7d, 0x8c, 0xc6, 0x50, 0x56, 0x47, 0x05, 0x5d, 0x4e, 0xb2,
	0x2e, 0x9c, 0xa0, 0xb5, 0xda, 0xbf, 0x85, 0x18, 0xb5, 0x6e, 0x09, 0x55, 0x3d, 0x56, 0x65, 0x52,
	0x15, 0xcd, 0xf9, 0x10, 0x12, 0xb7, 0xd5, 0x45, 0x9f, 0x43, 0x61, 0x3a, 0x48, 0x48, 0xa9, 0x5f,
	0x99, 0xb5, 0xda, 0xda, 0x32, 0x98, 0x51, 0x6b, 0x55, 0x28, 0x64, 0x84, 0x42, 0x4a, 0x64, 0x4f,
	0xa1, 0x6f, 0xa1, 0xba, 0xb8, 0xe4, 0xe8, 0xcd, 0x07, 0x40, 0xed, 0x4a, 0x73, 0xf6, 0x53, 0xd8,
	0x1c, 0x7e, 0xb1, 0x15, 0xfd, 0x14, 0xee, 0x4c, 0x28, 0x3f, 0x1e, 0xf5, 0xb7, 0x22, 0x85, 0xac,
	0xa2, 0xf0, 0x1d, 0x5c, 0x5c, 0x7a, 0x1e, 0x21, 0x6b, 0xc1, 0x89, 0xb7, 0xd4, 0xca, 0x29, 0x5a,
	0x63, 0xb8, 0x90, 0x1c, 0x1f, 0xb3, 0x7c, 0x01, 0x32, 0xe7, 0xba, 0xbc, 0xe4, 0xd8, 0xaa, 0x5d,
	0x3f, 0x83, 0x31, 0xf5, 0x2e, 0x9f, 0xa8, 0xd5, 0x72, 0x3f, 0x9c, 0x9c, 0xea, 0xbf, 0x3f, 0xde,
	0xaa, 0xbe, 0x7a, 0x5d, 0xd7, 0x7e, 0x79, 0x5d, 0xd7, 0x7e, 0x7b, 0x5d, 0xd7, 0x7e, 0xfc, 0xa3,
	0x9e, 0xfa, 0x3b, 0x00, 0x00, 0xff, 0xff, 0x78, 0x62, 0xfd, 0x19, 0x68, 0x0b, 0x00, 0x00,
}
