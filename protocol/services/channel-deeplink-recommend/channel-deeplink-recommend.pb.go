// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-deeplink-recommend/channel-deeplink-recommend.proto

package channel_deeplink_recommend // import "golang.52tt.com/protocol/services/channel-deeplink-recommend"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// bitmap 二进制移位操作求和
// 例如 1 << 1 代表 情感
type SubTagIdBitMap int32

const (
	SubTagIdBitMap_MusicSubTagId            SubTagIdBitMap = 0
	SubTagIdBitMap_EmotionSubTagId          SubTagIdBitMap = 1
	SubTagIdBitMap_QuadraticElementSubTagId SubTagIdBitMap = 2
	SubTagIdBitMap_StorySubTagId            SubTagIdBitMap = 3
	SubTagIdBitMap_TalkShowSubTagId         SubTagIdBitMap = 4
)

var SubTagIdBitMap_name = map[int32]string{
	0: "MusicSubTagId",
	1: "EmotionSubTagId",
	2: "QuadraticElementSubTagId",
	3: "StorySubTagId",
	4: "TalkShowSubTagId",
}
var SubTagIdBitMap_value = map[string]int32{
	"MusicSubTagId":            0,
	"EmotionSubTagId":          1,
	"QuadraticElementSubTagId": 2,
	"StorySubTagId":            3,
	"TalkShowSubTagId":         4,
}

func (x SubTagIdBitMap) String() string {
	return proto.EnumName(SubTagIdBitMap_name, int32(x))
}
func (SubTagIdBitMap) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{0}
}

type GetRecommendChannelByRulesReq_TagId int32

const (
	GetRecommendChannelByRulesReq_UnknownTagId GetRecommendChannelByRulesReq_TagId = 0
	GetRecommendChannelByRulesReq_YuYinTagId   GetRecommendChannelByRulesReq_TagId = 1
)

var GetRecommendChannelByRulesReq_TagId_name = map[int32]string{
	0: "UnknownTagId",
	1: "YuYinTagId",
}
var GetRecommendChannelByRulesReq_TagId_value = map[string]int32{
	"UnknownTagId": 0,
	"YuYinTagId":   1,
}

func (x GetRecommendChannelByRulesReq_TagId) String() string {
	return proto.EnumName(GetRecommendChannelByRulesReq_TagId_name, int32(x))
}
func (GetRecommendChannelByRulesReq_TagId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{0, 0}
}

type GetRecommendChannelByRulesReq_SexMode int32

const (
	GetRecommendChannelByRulesReq_NotLimit    GetRecommendChannelByRulesReq_SexMode = 0
	GetRecommendChannelByRulesReq_OppositeSex GetRecommendChannelByRulesReq_SexMode = 1
	GetRecommendChannelByRulesReq_SameSex     GetRecommendChannelByRulesReq_SexMode = 2
	GetRecommendChannelByRulesReq_Female      GetRecommendChannelByRulesReq_SexMode = 3
	GetRecommendChannelByRulesReq_Male        GetRecommendChannelByRulesReq_SexMode = 4
)

var GetRecommendChannelByRulesReq_SexMode_name = map[int32]string{
	0: "NotLimit",
	1: "OppositeSex",
	2: "SameSex",
	3: "Female",
	4: "Male",
}
var GetRecommendChannelByRulesReq_SexMode_value = map[string]int32{
	"NotLimit":    0,
	"OppositeSex": 1,
	"SameSex":     2,
	"Female":      3,
	"Male":        4,
}

func (x GetRecommendChannelByRulesReq_SexMode) String() string {
	return proto.EnumName(GetRecommendChannelByRulesReq_SexMode_name, int32(x))
}
func (GetRecommendChannelByRulesReq_SexMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{0, 1}
}

type GetRecommendChannelByRulesReq_DiversionMode int32

const (
	GetRecommendChannelByRulesReq_UnknownDiversionMode  GetRecommendChannelByRulesReq_DiversionMode = 0
	GetRecommendChannelByRulesReq_LivingChannelMemCnt   GetRecommendChannelByRulesReq_DiversionMode = 1
	GetRecommendChannelByRulesReq_DiversionConf         GetRecommendChannelByRulesReq_DiversionMode = 2
	GetRecommendChannelByRulesReq_DiversionConfWithRate GetRecommendChannelByRulesReq_DiversionMode = 3
)

var GetRecommendChannelByRulesReq_DiversionMode_name = map[int32]string{
	0: "UnknownDiversionMode",
	1: "LivingChannelMemCnt",
	2: "DiversionConf",
	3: "DiversionConfWithRate",
}
var GetRecommendChannelByRulesReq_DiversionMode_value = map[string]int32{
	"UnknownDiversionMode":  0,
	"LivingChannelMemCnt":   1,
	"DiversionConf":         2,
	"DiversionConfWithRate": 3,
}

func (x GetRecommendChannelByRulesReq_DiversionMode) String() string {
	return proto.EnumName(GetRecommendChannelByRulesReq_DiversionMode_name, int32(x))
}
func (GetRecommendChannelByRulesReq_DiversionMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{0, 2}
}

type GetRecommendChannelByRulesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	SubTagIdList         []uint32 `protobuf:"varint,3,rep,packed,name=sub_tag_id_list,json=subTagIdList,proto3" json:"sub_tag_id_list,omitempty"`
	SexMode              uint32   `protobuf:"varint,4,opt,name=sex_mode,json=sexMode,proto3" json:"sex_mode,omitempty"`
	DiversionMode        uint32   `protobuf:"varint,5,opt,name=diversion_mode,json=diversionMode,proto3" json:"diversion_mode,omitempty"`
	BeginRank            uint32   `protobuf:"varint,6,opt,name=begin_rank,json=beginRank,proto3" json:"begin_rank,omitempty"`
	EndRank              uint32   `protobuf:"varint,7,opt,name=end_rank,json=endRank,proto3" json:"end_rank,omitempty"`
	DivisionConfIdx      uint32   `protobuf:"varint,8,opt,name=division_conf_idx,json=divisionConfIdx,proto3" json:"division_conf_idx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelByRulesReq) Reset()         { *m = GetRecommendChannelByRulesReq{} }
func (m *GetRecommendChannelByRulesReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelByRulesReq) ProtoMessage()    {}
func (*GetRecommendChannelByRulesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{0}
}
func (m *GetRecommendChannelByRulesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelByRulesReq.Unmarshal(m, b)
}
func (m *GetRecommendChannelByRulesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelByRulesReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelByRulesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelByRulesReq.Merge(dst, src)
}
func (m *GetRecommendChannelByRulesReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelByRulesReq.Size(m)
}
func (m *GetRecommendChannelByRulesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelByRulesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelByRulesReq proto.InternalMessageInfo

func (m *GetRecommendChannelByRulesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendChannelByRulesReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetRecommendChannelByRulesReq) GetSubTagIdList() []uint32 {
	if m != nil {
		return m.SubTagIdList
	}
	return nil
}

func (m *GetRecommendChannelByRulesReq) GetSexMode() uint32 {
	if m != nil {
		return m.SexMode
	}
	return 0
}

func (m *GetRecommendChannelByRulesReq) GetDiversionMode() uint32 {
	if m != nil {
		return m.DiversionMode
	}
	return 0
}

func (m *GetRecommendChannelByRulesReq) GetBeginRank() uint32 {
	if m != nil {
		return m.BeginRank
	}
	return 0
}

func (m *GetRecommendChannelByRulesReq) GetEndRank() uint32 {
	if m != nil {
		return m.EndRank
	}
	return 0
}

func (m *GetRecommendChannelByRulesReq) GetDivisionConfIdx() uint32 {
	if m != nil {
		return m.DivisionConfIdx
	}
	return 0
}

type GetRecommendChannelByRulesResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendChannelByRulesResp) Reset()         { *m = GetRecommendChannelByRulesResp{} }
func (m *GetRecommendChannelByRulesResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelByRulesResp) ProtoMessage()    {}
func (*GetRecommendChannelByRulesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{1}
}
func (m *GetRecommendChannelByRulesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendChannelByRulesResp.Unmarshal(m, b)
}
func (m *GetRecommendChannelByRulesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendChannelByRulesResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendChannelByRulesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendChannelByRulesResp.Merge(dst, src)
}
func (m *GetRecommendChannelByRulesResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendChannelByRulesResp.Size(m)
}
func (m *GetRecommendChannelByRulesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendChannelByRulesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendChannelByRulesResp proto.InternalMessageInfo

func (m *GetRecommendChannelByRulesResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type Diversion struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Rate                 uint32   `protobuf:"varint,2,opt,name=rate,proto3" json:"rate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Diversion) Reset()         { *m = Diversion{} }
func (m *Diversion) String() string { return proto.CompactTextString(m) }
func (*Diversion) ProtoMessage()    {}
func (*Diversion) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{2}
}
func (m *Diversion) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Diversion.Unmarshal(m, b)
}
func (m *Diversion) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Diversion.Marshal(b, m, deterministic)
}
func (dst *Diversion) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Diversion.Merge(dst, src)
}
func (m *Diversion) XXX_Size() int {
	return xxx_messageInfo_Diversion.Size(m)
}
func (m *Diversion) XXX_DiscardUnknown() {
	xxx_messageInfo_Diversion.DiscardUnknown(m)
}

var xxx_messageInfo_Diversion proto.InternalMessageInfo

func (m *Diversion) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *Diversion) GetRate() uint32 {
	if m != nil {
		return m.Rate
	}
	return 0
}

// 新增分流配置列表
type AddRecommendDiversionConfReq struct {
	DiversionConf        []*Diversion `protobuf:"bytes,1,rep,name=diversion_conf,json=diversionConf,proto3" json:"diversion_conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AddRecommendDiversionConfReq) Reset()         { *m = AddRecommendDiversionConfReq{} }
func (m *AddRecommendDiversionConfReq) String() string { return proto.CompactTextString(m) }
func (*AddRecommendDiversionConfReq) ProtoMessage()    {}
func (*AddRecommendDiversionConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{3}
}
func (m *AddRecommendDiversionConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRecommendDiversionConfReq.Unmarshal(m, b)
}
func (m *AddRecommendDiversionConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRecommendDiversionConfReq.Marshal(b, m, deterministic)
}
func (dst *AddRecommendDiversionConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRecommendDiversionConfReq.Merge(dst, src)
}
func (m *AddRecommendDiversionConfReq) XXX_Size() int {
	return xxx_messageInfo_AddRecommendDiversionConfReq.Size(m)
}
func (m *AddRecommendDiversionConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRecommendDiversionConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRecommendDiversionConfReq proto.InternalMessageInfo

func (m *AddRecommendDiversionConfReq) GetDiversionConf() []*Diversion {
	if m != nil {
		return m.DiversionConf
	}
	return nil
}

type AddRecommendDiversionConfResp struct {
	ConfIdx              uint32   `protobuf:"varint,1,opt,name=conf_idx,json=confIdx,proto3" json:"conf_idx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRecommendDiversionConfResp) Reset()         { *m = AddRecommendDiversionConfResp{} }
func (m *AddRecommendDiversionConfResp) String() string { return proto.CompactTextString(m) }
func (*AddRecommendDiversionConfResp) ProtoMessage()    {}
func (*AddRecommendDiversionConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580, []int{4}
}
func (m *AddRecommendDiversionConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRecommendDiversionConfResp.Unmarshal(m, b)
}
func (m *AddRecommendDiversionConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRecommendDiversionConfResp.Marshal(b, m, deterministic)
}
func (dst *AddRecommendDiversionConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRecommendDiversionConfResp.Merge(dst, src)
}
func (m *AddRecommendDiversionConfResp) XXX_Size() int {
	return xxx_messageInfo_AddRecommendDiversionConfResp.Size(m)
}
func (m *AddRecommendDiversionConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRecommendDiversionConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRecommendDiversionConfResp proto.InternalMessageInfo

func (m *AddRecommendDiversionConfResp) GetConfIdx() uint32 {
	if m != nil {
		return m.ConfIdx
	}
	return 0
}

func init() {
	proto.RegisterType((*GetRecommendChannelByRulesReq)(nil), "channel_deeplink_recommend.GetRecommendChannelByRulesReq")
	proto.RegisterType((*GetRecommendChannelByRulesResp)(nil), "channel_deeplink_recommend.GetRecommendChannelByRulesResp")
	proto.RegisterType((*Diversion)(nil), "channel_deeplink_recommend.Diversion")
	proto.RegisterType((*AddRecommendDiversionConfReq)(nil), "channel_deeplink_recommend.AddRecommendDiversionConfReq")
	proto.RegisterType((*AddRecommendDiversionConfResp)(nil), "channel_deeplink_recommend.AddRecommendDiversionConfResp")
	proto.RegisterEnum("channel_deeplink_recommend.SubTagIdBitMap", SubTagIdBitMap_name, SubTagIdBitMap_value)
	proto.RegisterEnum("channel_deeplink_recommend.GetRecommendChannelByRulesReq_TagId", GetRecommendChannelByRulesReq_TagId_name, GetRecommendChannelByRulesReq_TagId_value)
	proto.RegisterEnum("channel_deeplink_recommend.GetRecommendChannelByRulesReq_SexMode", GetRecommendChannelByRulesReq_SexMode_name, GetRecommendChannelByRulesReq_SexMode_value)
	proto.RegisterEnum("channel_deeplink_recommend.GetRecommendChannelByRulesReq_DiversionMode", GetRecommendChannelByRulesReq_DiversionMode_name, GetRecommendChannelByRulesReq_DiversionMode_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelDeeplinkRecommendClient is the client API for ChannelDeeplinkRecommend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelDeeplinkRecommendClient interface {
	// 根据规则获取推荐的直播房
	GetRecommendChannelByRules(ctx context.Context, in *GetRecommendChannelByRulesReq, opts ...grpc.CallOption) (*GetRecommendChannelByRulesResp, error)
	// 新增推荐房分流配置列表
	AddRecommendDiversionConf(ctx context.Context, in *AddRecommendDiversionConfReq, opts ...grpc.CallOption) (*AddRecommendDiversionConfResp, error)
}

type channelDeeplinkRecommendClient struct {
	cc *grpc.ClientConn
}

func NewChannelDeeplinkRecommendClient(cc *grpc.ClientConn) ChannelDeeplinkRecommendClient {
	return &channelDeeplinkRecommendClient{cc}
}

func (c *channelDeeplinkRecommendClient) GetRecommendChannelByRules(ctx context.Context, in *GetRecommendChannelByRulesReq, opts ...grpc.CallOption) (*GetRecommendChannelByRulesResp, error) {
	out := new(GetRecommendChannelByRulesResp)
	err := c.cc.Invoke(ctx, "/channel_deeplink_recommend.ChannelDeeplinkRecommend/GetRecommendChannelByRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDeeplinkRecommendClient) AddRecommendDiversionConf(ctx context.Context, in *AddRecommendDiversionConfReq, opts ...grpc.CallOption) (*AddRecommendDiversionConfResp, error) {
	out := new(AddRecommendDiversionConfResp)
	err := c.cc.Invoke(ctx, "/channel_deeplink_recommend.ChannelDeeplinkRecommend/AddRecommendDiversionConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelDeeplinkRecommendServer is the server API for ChannelDeeplinkRecommend service.
type ChannelDeeplinkRecommendServer interface {
	// 根据规则获取推荐的直播房
	GetRecommendChannelByRules(context.Context, *GetRecommendChannelByRulesReq) (*GetRecommendChannelByRulesResp, error)
	// 新增推荐房分流配置列表
	AddRecommendDiversionConf(context.Context, *AddRecommendDiversionConfReq) (*AddRecommendDiversionConfResp, error)
}

func RegisterChannelDeeplinkRecommendServer(s *grpc.Server, srv ChannelDeeplinkRecommendServer) {
	s.RegisterService(&_ChannelDeeplinkRecommend_serviceDesc, srv)
}

func _ChannelDeeplinkRecommend_GetRecommendChannelByRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendChannelByRulesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDeeplinkRecommendServer).GetRecommendChannelByRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_deeplink_recommend.ChannelDeeplinkRecommend/GetRecommendChannelByRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDeeplinkRecommendServer).GetRecommendChannelByRules(ctx, req.(*GetRecommendChannelByRulesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDeeplinkRecommend_AddRecommendDiversionConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRecommendDiversionConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDeeplinkRecommendServer).AddRecommendDiversionConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_deeplink_recommend.ChannelDeeplinkRecommend/AddRecommendDiversionConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDeeplinkRecommendServer).AddRecommendDiversionConf(ctx, req.(*AddRecommendDiversionConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelDeeplinkRecommend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_deeplink_recommend.ChannelDeeplinkRecommend",
	HandlerType: (*ChannelDeeplinkRecommendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecommendChannelByRules",
			Handler:    _ChannelDeeplinkRecommend_GetRecommendChannelByRules_Handler,
		},
		{
			MethodName: "AddRecommendDiversionConf",
			Handler:    _ChannelDeeplinkRecommend_AddRecommendDiversionConf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-deeplink-recommend/channel-deeplink-recommend.proto",
}

func init() {
	proto.RegisterFile("channel-deeplink-recommend/channel-deeplink-recommend.proto", fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580)
}

var fileDescriptor_channel_deeplink_recommend_65d9371dcb45f580 = []byte{
	// 658 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x54, 0x5d, 0x4b, 0x1b, 0x4d,
	0x18, 0xcd, 0x26, 0x31, 0x89, 0x8f, 0xc6, 0xac, 0xa3, 0xf2, 0xae, 0x41, 0x5f, 0x64, 0x41, 0xb0,
	0x82, 0x11, 0x2c, 0x85, 0x6a, 0x8b, 0xa5, 0x7e, 0xb4, 0x48, 0x93, 0x96, 0x6e, 0x2c, 0xc5, 0xde,
	0x2c, 0x93, 0x9d, 0x31, 0x0e, 0xd9, 0x9d, 0xd9, 0xee, 0xcc, 0xc6, 0x78, 0x55, 0xfa, 0x17, 0x0a,
	0xfd, 0x4d, 0xfd, 0x5b, 0x65, 0x67, 0x37, 0x8b, 0xb9, 0xc8, 0x16, 0xbc, 0x9b, 0x39, 0xcf, 0x99,
	0xf3, 0x7c, 0x1d, 0x06, 0x5e, 0x79, 0x77, 0x98, 0x73, 0xea, 0x1f, 0x10, 0x4a, 0x43, 0x9f, 0xf1,
	0xd1, 0x41, 0x44, 0x3d, 0x11, 0x04, 0x94, 0x93, 0xc3, 0xf9, 0xa1, 0x4e, 0x18, 0x09, 0x25, 0x50,
	0x3b, 0x63, 0xb8, 0x53, 0x86, 0x9b, 0x33, 0xec, 0x9f, 0x55, 0xd8, 0x7e, 0x4f, 0x95, 0x33, 0x05,
	0xce, 0x53, 0xea, 0xd9, 0x83, 0x13, 0xfb, 0x54, 0x3a, 0xf4, 0x3b, 0x32, 0xa1, 0x12, 0x33, 0x62,
	0x19, 0x3b, 0xc6, 0x5e, 0xd3, 0x49, 0x8e, 0x68, 0x03, 0x6a, 0x0a, 0x0f, 0x5d, 0x46, 0xac, 0xb2,
	0x06, 0x17, 0x14, 0x1e, 0x5e, 0x11, 0xb4, 0x0b, 0x2d, 0x19, 0x0f, 0xdc, 0x34, 0xe4, 0xfa, 0x4c,
	0x2a, 0xab, 0xb2, 0x53, 0xd9, 0x6b, 0x3a, 0xcb, 0x32, 0x1e, 0x5c, 0x27, 0x94, 0x2e, 0x93, 0x0a,
	0x6d, 0x42, 0x43, 0xd2, 0x89, 0x1b, 0x08, 0x42, 0xad, 0xaa, 0x7e, 0x5f, 0x97, 0x74, 0xd2, 0x13,
	0x84, 0xa2, 0x5d, 0x58, 0x21, 0x6c, 0x4c, 0x23, 0xc9, 0x04, 0x4f, 0x09, 0x0b, 0x9a, 0xd0, 0xcc,
	0x51, 0x4d, 0xdb, 0x06, 0x18, 0xd0, 0x21, 0xe3, 0x6e, 0x84, 0xf9, 0xc8, 0xaa, 0x69, 0xca, 0xa2,
	0x46, 0x1c, 0xcc, 0x47, 0x49, 0x02, 0xca, 0x49, 0x1a, 0xac, 0xa7, 0x09, 0x28, 0x27, 0x3a, 0xb4,
	0x0f, 0xab, 0x84, 0x8d, 0x99, 0xd6, 0xf7, 0x04, 0xbf, 0x75, 0x19, 0x99, 0x58, 0x0d, 0xcd, 0x69,
	0x4d, 0x03, 0xe7, 0x82, 0xdf, 0x5e, 0x91, 0x89, 0xfd, 0x0c, 0x16, 0x74, 0xd1, 0xc8, 0x84, 0xe5,
	0x2f, 0x7c, 0xc4, 0xc5, 0x3d, 0xd7, 0x77, 0xb3, 0x84, 0x56, 0x00, 0x6e, 0xe2, 0x1b, 0x96, 0xdd,
	0x0d, 0xfb, 0x03, 0xd4, 0xfb, 0x59, 0x0b, 0xcb, 0xd0, 0xf8, 0x28, 0x54, 0x97, 0x05, 0x4c, 0x99,
	0x25, 0xd4, 0x82, 0xa5, 0x4f, 0x61, 0x28, 0x24, 0x53, 0xb4, 0x4f, 0x27, 0xa6, 0x81, 0x96, 0xa0,
	0xde, 0xc7, 0x81, 0xbe, 0x94, 0x11, 0x40, 0xed, 0x1d, 0x0d, 0xb0, 0x4f, 0xcd, 0x0a, 0x6a, 0x40,
	0xb5, 0x97, 0x9c, 0xaa, 0x76, 0x08, 0xcd, 0x8b, 0x99, 0x76, 0x2d, 0x58, 0xcf, 0xf2, 0xcf, 0xe0,
	0x66, 0x09, 0xfd, 0x07, 0x6b, 0x5d, 0x36, 0x66, 0x7c, 0x98, 0x6d, 0xad, 0x47, 0x83, 0x73, 0xae,
	0x4c, 0x03, 0xad, 0x3e, 0xd2, 0x48, 0xfa, 0x31, 0xcb, 0x68, 0x13, 0x36, 0x66, 0xa0, 0xaf, 0x4c,
	0xdd, 0x39, 0x58, 0x51, 0xb3, 0x62, 0xbf, 0x81, 0xff, 0x8b, 0x2c, 0x20, 0xc3, 0x64, 0xe2, 0x53,
	0x0f, 0xe5, 0x56, 0x58, 0xcc, 0x90, 0x2b, 0x62, 0x9f, 0xc2, 0x62, 0xae, 0xfd, 0x0f, 0x2e, 0x42,
	0x50, 0x8d, 0xb0, 0xa2, 0x99, 0x75, 0xf4, 0xd9, 0xf6, 0x61, 0xeb, 0x2d, 0x21, 0x79, 0x01, 0x33,
	0x75, 0x26, 0x16, 0xec, 0x3e, 0xf6, 0x45, 0xb2, 0x37, 0xcb, 0xd8, 0xa9, 0xec, 0x2d, 0x1d, 0xed,
	0x76, 0xe6, 0x3b, 0xbb, 0x93, 0xab, 0x3c, 0xb2, 0x4f, 0x22, 0x68, 0x9f, 0xc0, 0x76, 0x41, 0x36,
	0x19, 0x26, 0x06, 0xca, 0xcd, 0x91, 0xd6, 0x5f, 0xf7, 0x52, 0x53, 0xec, 0xff, 0x80, 0x95, 0x7e,
	0x66, 0xe6, 0x33, 0xa6, 0x7a, 0x38, 0x4c, 0x46, 0xdd, 0x8b, 0x25, 0xf3, 0xa6, 0xb0, 0x59, 0x42,
	0x6b, 0xd0, 0xba, 0x0c, 0x84, 0x62, 0x82, 0xe7, 0xa0, 0x81, 0xb6, 0xc0, 0xfa, 0x1c, 0x63, 0x12,
	0x61, 0xc5, 0xbc, 0x4b, 0x9f, 0x06, 0x94, 0xab, 0x3c, 0x5a, 0x4e, 0x54, 0xfa, 0x4a, 0x44, 0x0f,
	0x39, 0x54, 0x41, 0xeb, 0x60, 0x5e, 0x63, 0x7f, 0xd4, 0xbf, 0x13, 0xf7, 0x39, 0x5a, 0x3d, 0xfa,
	0x53, 0x06, 0x2b, 0x5b, 0xd0, 0x45, 0xd6, 0x73, 0xde, 0x09, 0xfa, 0x6d, 0x40, 0x7b, 0xfe, 0x26,
	0xd1, 0x71, 0xd1, 0xb8, 0x0a, 0x3f, 0x81, 0xf6, 0xc9, 0x53, 0x9f, 0xca, 0xd0, 0x2e, 0xa1, 0x5f,
	0x06, 0x6c, 0xce, 0x1d, 0x39, 0x7a, 0x59, 0xa4, 0x5d, 0xe4, 0x8b, 0xf6, 0xf1, 0x13, 0x5f, 0x26,
	0x45, 0x9d, 0x9d, 0x7e, 0x7b, 0x3d, 0x14, 0x3e, 0xe6, 0xc3, 0xce, 0x8b, 0x23, 0xa5, 0x3a, 0x9e,
	0x08, 0x0e, 0xf5, 0x77, 0xe9, 0x09, 0xff, 0x50, 0xd2, 0x68, 0xcc, 0x3c, 0x2a, 0x0b, 0xfe, 0xd6,
	0x41, 0x4d, 0xb3, 0x9f, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x2e, 0xfc, 0x44, 0x30, 0x9b, 0x05,
	0x00, 0x00,
}
