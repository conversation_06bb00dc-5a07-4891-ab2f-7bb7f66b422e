// Code generated by protoc-gen-go. DO NOT EDIT.
// source: comm-search-index/comm-search-index.proto

package comm_search_index // import "golang.52tt.com/protocol/services/comm-search-index"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type MusicInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Oid                  string   `protobuf:"bytes,2,opt,name=oid,proto3" json:"oid,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Artist               string   `protobuf:"bytes,4,opt,name=artist,proto3" json:"artist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicInfo) Reset()         { *m = MusicInfo{} }
func (m *MusicInfo) String() string { return proto.CompactTextString(m) }
func (*MusicInfo) ProtoMessage()    {}
func (*MusicInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{0}
}
func (m *MusicInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicInfo.Unmarshal(m, b)
}
func (m *MusicInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicInfo.Marshal(b, m, deterministic)
}
func (dst *MusicInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicInfo.Merge(dst, src)
}
func (m *MusicInfo) XXX_Size() int {
	return xxx_messageInfo_MusicInfo.Size(m)
}
func (m *MusicInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicInfo proto.InternalMessageInfo

func (m *MusicInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MusicInfo) GetOid() string {
	if m != nil {
		return m.Oid
	}
	return ""
}

func (m *MusicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicInfo) GetArtist() string {
	if m != nil {
		return m.Artist
	}
	return ""
}

type MusicIdInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Oid                  string   `protobuf:"bytes,2,opt,name=oid,proto3" json:"oid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MusicIdInfo) Reset()         { *m = MusicIdInfo{} }
func (m *MusicIdInfo) String() string { return proto.CompactTextString(m) }
func (*MusicIdInfo) ProtoMessage()    {}
func (*MusicIdInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{1}
}
func (m *MusicIdInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicIdInfo.Unmarshal(m, b)
}
func (m *MusicIdInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicIdInfo.Marshal(b, m, deterministic)
}
func (dst *MusicIdInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicIdInfo.Merge(dst, src)
}
func (m *MusicIdInfo) XXX_Size() int {
	return xxx_messageInfo_MusicIdInfo.Size(m)
}
func (m *MusicIdInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicIdInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MusicIdInfo proto.InternalMessageInfo

func (m *MusicIdInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MusicIdInfo) GetOid() string {
	if m != nil {
		return m.Oid
	}
	return ""
}

type AddChannelMusicSearchIndexReq struct {
	MusicInfo            *MusicInfo `protobuf:"bytes,1,opt,name=music_info,json=musicInfo,proto3" json:"music_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AddChannelMusicSearchIndexReq) Reset()         { *m = AddChannelMusicSearchIndexReq{} }
func (m *AddChannelMusicSearchIndexReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelMusicSearchIndexReq) ProtoMessage()    {}
func (*AddChannelMusicSearchIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{2}
}
func (m *AddChannelMusicSearchIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelMusicSearchIndexReq.Unmarshal(m, b)
}
func (m *AddChannelMusicSearchIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelMusicSearchIndexReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelMusicSearchIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelMusicSearchIndexReq.Merge(dst, src)
}
func (m *AddChannelMusicSearchIndexReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelMusicSearchIndexReq.Size(m)
}
func (m *AddChannelMusicSearchIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelMusicSearchIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelMusicSearchIndexReq proto.InternalMessageInfo

func (m *AddChannelMusicSearchIndexReq) GetMusicInfo() *MusicInfo {
	if m != nil {
		return m.MusicInfo
	}
	return nil
}

type AddChannelMusicSearchIndexResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelMusicSearchIndexResp) Reset()         { *m = AddChannelMusicSearchIndexResp{} }
func (m *AddChannelMusicSearchIndexResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelMusicSearchIndexResp) ProtoMessage()    {}
func (*AddChannelMusicSearchIndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{3}
}
func (m *AddChannelMusicSearchIndexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelMusicSearchIndexResp.Unmarshal(m, b)
}
func (m *AddChannelMusicSearchIndexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelMusicSearchIndexResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelMusicSearchIndexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelMusicSearchIndexResp.Merge(dst, src)
}
func (m *AddChannelMusicSearchIndexResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelMusicSearchIndexResp.Size(m)
}
func (m *AddChannelMusicSearchIndexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelMusicSearchIndexResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelMusicSearchIndexResp proto.InternalMessageInfo

type DelChannelMusicSearchIndexReq struct {
	MusicIdList          []*MusicIdInfo `protobuf:"bytes,1,rep,name=music_id_list,json=musicIdList,proto3" json:"music_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *DelChannelMusicSearchIndexReq) Reset()         { *m = DelChannelMusicSearchIndexReq{} }
func (m *DelChannelMusicSearchIndexReq) String() string { return proto.CompactTextString(m) }
func (*DelChannelMusicSearchIndexReq) ProtoMessage()    {}
func (*DelChannelMusicSearchIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{4}
}
func (m *DelChannelMusicSearchIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelMusicSearchIndexReq.Unmarshal(m, b)
}
func (m *DelChannelMusicSearchIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelMusicSearchIndexReq.Marshal(b, m, deterministic)
}
func (dst *DelChannelMusicSearchIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelMusicSearchIndexReq.Merge(dst, src)
}
func (m *DelChannelMusicSearchIndexReq) XXX_Size() int {
	return xxx_messageInfo_DelChannelMusicSearchIndexReq.Size(m)
}
func (m *DelChannelMusicSearchIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelMusicSearchIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelMusicSearchIndexReq proto.InternalMessageInfo

func (m *DelChannelMusicSearchIndexReq) GetMusicIdList() []*MusicIdInfo {
	if m != nil {
		return m.MusicIdList
	}
	return nil
}

type DelChannelMusicSearchIndexResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelChannelMusicSearchIndexResp) Reset()         { *m = DelChannelMusicSearchIndexResp{} }
func (m *DelChannelMusicSearchIndexResp) String() string { return proto.CompactTextString(m) }
func (*DelChannelMusicSearchIndexResp) ProtoMessage()    {}
func (*DelChannelMusicSearchIndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{5}
}
func (m *DelChannelMusicSearchIndexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelChannelMusicSearchIndexResp.Unmarshal(m, b)
}
func (m *DelChannelMusicSearchIndexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelChannelMusicSearchIndexResp.Marshal(b, m, deterministic)
}
func (dst *DelChannelMusicSearchIndexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelChannelMusicSearchIndexResp.Merge(dst, src)
}
func (m *DelChannelMusicSearchIndexResp) XXX_Size() int {
	return xxx_messageInfo_DelChannelMusicSearchIndexResp.Size(m)
}
func (m *DelChannelMusicSearchIndexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelChannelMusicSearchIndexResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelChannelMusicSearchIndexResp proto.InternalMessageInfo

type UpdateChannelMusicSearchIndexReq struct {
	MusicInfo            *MusicInfo `protobuf:"bytes,1,opt,name=music_info,json=musicInfo,proto3" json:"music_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateChannelMusicSearchIndexReq) Reset()         { *m = UpdateChannelMusicSearchIndexReq{} }
func (m *UpdateChannelMusicSearchIndexReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelMusicSearchIndexReq) ProtoMessage()    {}
func (*UpdateChannelMusicSearchIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{6}
}
func (m *UpdateChannelMusicSearchIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelMusicSearchIndexReq.Unmarshal(m, b)
}
func (m *UpdateChannelMusicSearchIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelMusicSearchIndexReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelMusicSearchIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelMusicSearchIndexReq.Merge(dst, src)
}
func (m *UpdateChannelMusicSearchIndexReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelMusicSearchIndexReq.Size(m)
}
func (m *UpdateChannelMusicSearchIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelMusicSearchIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelMusicSearchIndexReq proto.InternalMessageInfo

func (m *UpdateChannelMusicSearchIndexReq) GetMusicInfo() *MusicInfo {
	if m != nil {
		return m.MusicInfo
	}
	return nil
}

type UpdateChannelMusicSearchIndexResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelMusicSearchIndexResp) Reset()         { *m = UpdateChannelMusicSearchIndexResp{} }
func (m *UpdateChannelMusicSearchIndexResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelMusicSearchIndexResp) ProtoMessage()    {}
func (*UpdateChannelMusicSearchIndexResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_comm_search_index_88717b9215828974, []int{7}
}
func (m *UpdateChannelMusicSearchIndexResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelMusicSearchIndexResp.Unmarshal(m, b)
}
func (m *UpdateChannelMusicSearchIndexResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelMusicSearchIndexResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelMusicSearchIndexResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelMusicSearchIndexResp.Merge(dst, src)
}
func (m *UpdateChannelMusicSearchIndexResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelMusicSearchIndexResp.Size(m)
}
func (m *UpdateChannelMusicSearchIndexResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelMusicSearchIndexResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelMusicSearchIndexResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*MusicInfo)(nil), "comm_search_index.MusicInfo")
	proto.RegisterType((*MusicIdInfo)(nil), "comm_search_index.MusicIdInfo")
	proto.RegisterType((*AddChannelMusicSearchIndexReq)(nil), "comm_search_index.AddChannelMusicSearchIndexReq")
	proto.RegisterType((*AddChannelMusicSearchIndexResp)(nil), "comm_search_index.AddChannelMusicSearchIndexResp")
	proto.RegisterType((*DelChannelMusicSearchIndexReq)(nil), "comm_search_index.DelChannelMusicSearchIndexReq")
	proto.RegisterType((*DelChannelMusicSearchIndexResp)(nil), "comm_search_index.DelChannelMusicSearchIndexResp")
	proto.RegisterType((*UpdateChannelMusicSearchIndexReq)(nil), "comm_search_index.UpdateChannelMusicSearchIndexReq")
	proto.RegisterType((*UpdateChannelMusicSearchIndexResp)(nil), "comm_search_index.UpdateChannelMusicSearchIndexResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CommSearchIndexClient is the client API for CommSearchIndex service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CommSearchIndexClient interface {
	// music search index
	AddChannelMusicSearchIndex(ctx context.Context, in *AddChannelMusicSearchIndexReq, opts ...grpc.CallOption) (*AddChannelMusicSearchIndexResp, error)
	DelChannelMusicSearchIndex(ctx context.Context, in *DelChannelMusicSearchIndexReq, opts ...grpc.CallOption) (*DelChannelMusicSearchIndexResp, error)
	UpdateChannelMusicSearchIndex(ctx context.Context, in *UpdateChannelMusicSearchIndexReq, opts ...grpc.CallOption) (*UpdateChannelMusicSearchIndexResp, error)
}

type commSearchIndexClient struct {
	cc *grpc.ClientConn
}

func NewCommSearchIndexClient(cc *grpc.ClientConn) CommSearchIndexClient {
	return &commSearchIndexClient{cc}
}

func (c *commSearchIndexClient) AddChannelMusicSearchIndex(ctx context.Context, in *AddChannelMusicSearchIndexReq, opts ...grpc.CallOption) (*AddChannelMusicSearchIndexResp, error) {
	out := new(AddChannelMusicSearchIndexResp)
	err := c.cc.Invoke(ctx, "/comm_search_index.CommSearchIndex/AddChannelMusicSearchIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commSearchIndexClient) DelChannelMusicSearchIndex(ctx context.Context, in *DelChannelMusicSearchIndexReq, opts ...grpc.CallOption) (*DelChannelMusicSearchIndexResp, error) {
	out := new(DelChannelMusicSearchIndexResp)
	err := c.cc.Invoke(ctx, "/comm_search_index.CommSearchIndex/DelChannelMusicSearchIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commSearchIndexClient) UpdateChannelMusicSearchIndex(ctx context.Context, in *UpdateChannelMusicSearchIndexReq, opts ...grpc.CallOption) (*UpdateChannelMusicSearchIndexResp, error) {
	out := new(UpdateChannelMusicSearchIndexResp)
	err := c.cc.Invoke(ctx, "/comm_search_index.CommSearchIndex/UpdateChannelMusicSearchIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommSearchIndexServer is the server API for CommSearchIndex service.
type CommSearchIndexServer interface {
	// music search index
	AddChannelMusicSearchIndex(context.Context, *AddChannelMusicSearchIndexReq) (*AddChannelMusicSearchIndexResp, error)
	DelChannelMusicSearchIndex(context.Context, *DelChannelMusicSearchIndexReq) (*DelChannelMusicSearchIndexResp, error)
	UpdateChannelMusicSearchIndex(context.Context, *UpdateChannelMusicSearchIndexReq) (*UpdateChannelMusicSearchIndexResp, error)
}

func RegisterCommSearchIndexServer(s *grpc.Server, srv CommSearchIndexServer) {
	s.RegisterService(&_CommSearchIndex_serviceDesc, srv)
}

func _CommSearchIndex_AddChannelMusicSearchIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelMusicSearchIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommSearchIndexServer).AddChannelMusicSearchIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/comm_search_index.CommSearchIndex/AddChannelMusicSearchIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommSearchIndexServer).AddChannelMusicSearchIndex(ctx, req.(*AddChannelMusicSearchIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommSearchIndex_DelChannelMusicSearchIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelMusicSearchIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommSearchIndexServer).DelChannelMusicSearchIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/comm_search_index.CommSearchIndex/DelChannelMusicSearchIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommSearchIndexServer).DelChannelMusicSearchIndex(ctx, req.(*DelChannelMusicSearchIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CommSearchIndex_UpdateChannelMusicSearchIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelMusicSearchIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommSearchIndexServer).UpdateChannelMusicSearchIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/comm_search_index.CommSearchIndex/UpdateChannelMusicSearchIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommSearchIndexServer).UpdateChannelMusicSearchIndex(ctx, req.(*UpdateChannelMusicSearchIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _CommSearchIndex_serviceDesc = grpc.ServiceDesc{
	ServiceName: "comm_search_index.CommSearchIndex",
	HandlerType: (*CommSearchIndexServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddChannelMusicSearchIndex",
			Handler:    _CommSearchIndex_AddChannelMusicSearchIndex_Handler,
		},
		{
			MethodName: "DelChannelMusicSearchIndex",
			Handler:    _CommSearchIndex_DelChannelMusicSearchIndex_Handler,
		},
		{
			MethodName: "UpdateChannelMusicSearchIndex",
			Handler:    _CommSearchIndex_UpdateChannelMusicSearchIndex_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "comm-search-index/comm-search-index.proto",
}

func init() {
	proto.RegisterFile("comm-search-index/comm-search-index.proto", fileDescriptor_comm_search_index_88717b9215828974)
}

var fileDescriptor_comm_search_index_88717b9215828974 = []byte{
	// 377 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x94, 0x4d, 0x4f, 0xfa, 0x40,
	0x10, 0xc6, 0x29, 0x25, 0x24, 0x4c, 0xc3, 0xff, 0x65, 0x0f, 0xa6, 0x21, 0x42, 0x6a, 0xbd, 0xe0,
	0x81, 0x56, 0x8b, 0x9c, 0x3c, 0x09, 0x5e, 0x48, 0xf4, 0x52, 0xe3, 0x41, 0x63, 0xd2, 0xd4, 0xee,
	0x02, 0x9b, 0x74, 0x77, 0x6b, 0xb7, 0x1a, 0xef, 0x5e, 0xfd, 0x28, 0x7e, 0x48, 0xb3, 0x0b, 0x1a,
	0x13, 0x4a, 0x91, 0x83, 0xb7, 0xe9, 0x4c, 0xe7, 0x99, 0xdf, 0x3c, 0x93, 0x16, 0x8e, 0x12, 0xc1,
	0xd8, 0x40, 0x92, 0x38, 0x4f, 0x16, 0x03, 0xca, 0x31, 0x79, 0xf1, 0xd7, 0x32, 0x5e, 0x96, 0x8b,
	0x42, 0xa0, 0xff, 0xaa, 0x10, 0x2d, 0x0b, 0x91, 0x2e, 0xb8, 0xb7, 0xd0, 0xba, 0x7a, 0x92, 0x34,
	0x99, 0xf2, 0x99, 0x40, 0x7f, 0xa0, 0x4e, 0xb1, 0x6d, 0x38, 0x46, 0xbf, 0x1d, 0xd6, 0x29, 0x46,
	0xff, 0xc0, 0x14, 0x14, 0xdb, 0x75, 0xc7, 0xe8, 0xb7, 0x42, 0x15, 0x22, 0x04, 0x0d, 0x1e, 0x33,
	0x62, 0x9b, 0x3a, 0xa5, 0x63, 0xb4, 0x07, 0xcd, 0x38, 0x2f, 0xa8, 0x2c, 0xec, 0x86, 0xce, 0xae,
	0x9e, 0x5c, 0x1f, 0xac, 0xa5, 0x34, 0xfe, 0x99, 0xb8, 0x7b, 0x0f, 0xdd, 0x73, 0x8c, 0x27, 0x8b,
	0x98, 0x73, 0x92, 0xea, 0xd6, 0x6b, 0x8d, 0x3a, 0x55, 0xa4, 0x21, 0x79, 0x44, 0x67, 0x00, 0x4c,
	0xa5, 0x23, 0xca, 0x67, 0x42, 0x4b, 0x59, 0xc1, 0xbe, 0xb7, 0xb6, 0x94, 0xf7, 0xb5, 0x51, 0xd8,
	0x62, 0x9f, 0xa1, 0xeb, 0x40, 0xaf, 0x4a, 0x5d, 0x66, 0x6e, 0x02, 0xdd, 0x0b, 0x92, 0x56, 0xcc,
	0x1f, 0x43, 0x7b, 0x35, 0x1f, 0x47, 0xa9, 0x5a, 0xd8, 0x70, 0xcc, 0xbe, 0x15, 0xf4, 0x36, 0x22,
	0xe8, 0xcd, 0x43, 0x6b, 0x09, 0x81, 0x2f, 0x95, 0x2b, 0x0e, 0xf4, 0xaa, 0x86, 0xc8, 0xcc, 0x8d,
	0xc0, 0xb9, 0xc9, 0x70, 0x5c, 0x90, 0xdf, 0x72, 0xe2, 0x10, 0x0e, 0xb6, 0x0c, 0x90, 0x59, 0xf0,
	0x6e, 0xc2, 0xdf, 0x89, 0x60, 0xec, 0x5b, 0x1e, 0xbd, 0x1a, 0xd0, 0xd9, 0xec, 0x21, 0x3a, 0x2e,
	0x01, 0xa8, 0x3c, 0x68, 0xe7, 0x64, 0xc7, 0x0e, 0x99, 0xb9, 0x35, 0x4d, 0xb1, 0xd9, 0xc2, 0x52,
	0x8a, 0xca, 0xb3, 0x96, 0x52, 0x6c, 0xb9, 0x51, 0x0d, 0xbd, 0x19, 0xd0, 0xad, 0x74, 0x11, 0x0d,
	0x4b, 0x64, 0xb7, 0x1d, 0xb6, 0x73, 0xba, 0x7b, 0x93, 0xc2, 0x19, 0x8f, 0xee, 0x86, 0x73, 0x91,
	0xc6, 0x7c, 0xee, 0x8d, 0x82, 0xa2, 0x50, 0x22, 0xbe, 0xfe, 0xe6, 0x13, 0x91, 0xfa, 0x92, 0xe4,
	0xcf, 0x34, 0x21, 0x72, 0xfd, 0xbf, 0xf0, 0xd0, 0xd4, 0x2f, 0x0d, 0x3f, 0x02, 0x00, 0x00, 0xff,
	0xff, 0xb6, 0x36, 0x39, 0x78, 0x45, 0x04, 0x00, 0x00,
}
