package activitypresent

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: services/activitypresent/activitypresent.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for ActivityPresent service
const ActivityPresentMagic = uint16(15610)

// Client API for ActivityPresent service

type ActivityPresentClientInterface interface {
	GetPresentActivityInfo(ctx context.Context, uin uint32, in *GetPresentActivityInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityInfoResp, error)
	GetPresentActivityRankingList(ctx context.Context, uin uint32, in *GetPresentActivityRankingListReq, opts ...svrkit.CallOption) (*GetPresentActivityRankingListResp, error)
	GetPresentActivityRankingByUid(ctx context.Context, uin uint32, in *GetPresentActivityRankingByUidReq, opts ...svrkit.CallOption) (*GetPresentActivityRankingByUidResp, error)
	RecordActivitySendPresent(ctx context.Context, uin uint32, in *RecordActivitySendPresentReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	RecalculateUserActivityInfo(ctx context.Context, uin uint32, in *RecalculateUserActivityInfoReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	NotifyActivityPhaseChange(ctx context.Context, uin uint32, in *NotifyActivityPhaseChangeReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetPresentActivityUserInfo(ctx context.Context, uin uint32, in *GetPresentActivityUserInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityUserInfoResp, error)
	RecalculateActivityInfo(ctx context.Context, uin uint32, in *RecalculateActivityInfoReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetBattleProgressList(ctx context.Context, uin uint32, in *GetBattleProgressListReq, opts ...svrkit.CallOption) (*GetBattleProgressListResp, error)
	ModifyUserActivityInfo(ctx context.Context, uin uint32, in *ModifyUserActivityInfoReq, opts ...svrkit.CallOption) (*ModifyUserActivityInfoResp, error)
	GetPresentActivityUserExtentInfo(ctx context.Context, uin uint32, in *GetPresentActivityUserExtentInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityUserExtentInfoResp, error)
	GetPresentActivityExtentInfo(ctx context.Context, uin uint32, in *GetPresentActivityExtentInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityExtentInfoResp, error)
	ModifyLetter(ctx context.Context, uin uint32, in *ModifyLoveLetterReq, opts ...svrkit.CallOption) (*ModifyLoveLetterResp, error)
	DoTest(ctx context.Context, uin uint32, in *DoTestReq, opts ...svrkit.CallOption) (*DoTestResp, error)
	// 获取当前 各种送礼/收礼活动通用的 排行榜黑名单用户列表
	GetRankBlackUidList(ctx context.Context, uin uint32, in *GetRankBlackUidListReq, opts ...svrkit.CallOption) (*GetRankBlackUidListResp, error)
}

type ActivityPresentClient struct {
	cc *svrkit.ClientConn
}

func NewActivityPresentClient(cc *svrkit.ClientConn) ActivityPresentClientInterface {
	return &ActivityPresentClient{cc}
}

const (
	commandActivityPresentGetSelfSvnInfo                   = 9995
	commandActivityPresentEcho                             = 9999
	commandActivityPresentGetPresentActivityInfo           = 1
	commandActivityPresentGetPresentActivityRankingList    = 2
	commandActivityPresentGetPresentActivityRankingByUid   = 3
	commandActivityPresentRecordActivitySendPresent        = 4
	commandActivityPresentRecalculateUserActivityInfo      = 5
	commandActivityPresentNotifyActivityPhaseChange        = 6
	commandActivityPresentGetPresentActivityUserInfo       = 7
	commandActivityPresentRecalculateActivityInfo          = 8
	commandActivityPresentGetBattleProgressList            = 9
	commandActivityPresentModifyUserActivityInfo           = 10
	commandActivityPresentGetPresentActivityUserExtentInfo = 11
	commandActivityPresentGetPresentActivityExtentInfo     = 12
	commandActivityPresentmodifyLetter                     = 13
	commandActivityPresentDoTest                           = 14
	commandActivityPresentGetRankBlackUidList              = 30
)

func (c *ActivityPresentClient) GetPresentActivityInfo(ctx context.Context, uin uint32, in *GetPresentActivityInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityInfoResp, error) {
	out := new(GetPresentActivityInfoResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetPresentActivityInfo, "/activitypresent.ActivityPresent/GetPresentActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) GetPresentActivityRankingList(ctx context.Context, uin uint32, in *GetPresentActivityRankingListReq, opts ...svrkit.CallOption) (*GetPresentActivityRankingListResp, error) {
	out := new(GetPresentActivityRankingListResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetPresentActivityRankingList, "/activitypresent.ActivityPresent/GetPresentActivityRankingList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) GetPresentActivityRankingByUid(ctx context.Context, uin uint32, in *GetPresentActivityRankingByUidReq, opts ...svrkit.CallOption) (*GetPresentActivityRankingByUidResp, error) {
	out := new(GetPresentActivityRankingByUidResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetPresentActivityRankingByUid, "/activitypresent.ActivityPresent/GetPresentActivityRankingByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) RecordActivitySendPresent(ctx context.Context, uin uint32, in *RecordActivitySendPresentReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentRecordActivitySendPresent, "/activitypresent.ActivityPresent/RecordActivitySendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) RecalculateUserActivityInfo(ctx context.Context, uin uint32, in *RecalculateUserActivityInfoReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentRecalculateUserActivityInfo, "/activitypresent.ActivityPresent/RecalculateUserActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) NotifyActivityPhaseChange(ctx context.Context, uin uint32, in *NotifyActivityPhaseChangeReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentNotifyActivityPhaseChange, "/activitypresent.ActivityPresent/NotifyActivityPhaseChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) GetPresentActivityUserInfo(ctx context.Context, uin uint32, in *GetPresentActivityUserInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityUserInfoResp, error) {
	out := new(GetPresentActivityUserInfoResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetPresentActivityUserInfo, "/activitypresent.ActivityPresent/GetPresentActivityUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) RecalculateActivityInfo(ctx context.Context, uin uint32, in *RecalculateActivityInfoReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentRecalculateActivityInfo, "/activitypresent.ActivityPresent/RecalculateActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) GetBattleProgressList(ctx context.Context, uin uint32, in *GetBattleProgressListReq, opts ...svrkit.CallOption) (*GetBattleProgressListResp, error) {
	out := new(GetBattleProgressListResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetBattleProgressList, "/activitypresent.ActivityPresent/GetBattleProgressList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) ModifyUserActivityInfo(ctx context.Context, uin uint32, in *ModifyUserActivityInfoReq, opts ...svrkit.CallOption) (*ModifyUserActivityInfoResp, error) {
	out := new(ModifyUserActivityInfoResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentModifyUserActivityInfo, "/activitypresent.ActivityPresent/ModifyUserActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) GetPresentActivityUserExtentInfo(ctx context.Context, uin uint32, in *GetPresentActivityUserExtentInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityUserExtentInfoResp, error) {
	out := new(GetPresentActivityUserExtentInfoResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetPresentActivityUserExtentInfo, "/activitypresent.ActivityPresent/GetPresentActivityUserExtentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) GetPresentActivityExtentInfo(ctx context.Context, uin uint32, in *GetPresentActivityExtentInfoReq, opts ...svrkit.CallOption) (*GetPresentActivityExtentInfoResp, error) {
	out := new(GetPresentActivityExtentInfoResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetPresentActivityExtentInfo, "/activitypresent.ActivityPresent/GetPresentActivityExtentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) ModifyLetter(ctx context.Context, uin uint32, in *ModifyLoveLetterReq, opts ...svrkit.CallOption) (*ModifyLoveLetterResp, error) {
	out := new(ModifyLoveLetterResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentmodifyLetter, "/activitypresent.ActivityPresent/ModifyLetter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) DoTest(ctx context.Context, uin uint32, in *DoTestReq, opts ...svrkit.CallOption) (*DoTestResp, error) {
	out := new(DoTestResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentDoTest, "/activitypresent.ActivityPresent/DoTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ActivityPresentClient) GetRankBlackUidList(ctx context.Context, uin uint32, in *GetRankBlackUidListReq, opts ...svrkit.CallOption) (*GetRankBlackUidListResp, error) {
	out := new(GetRankBlackUidListResp)
	err := c.cc.Invoke(ctx, uin, commandActivityPresentGetRankBlackUidList, "/activitypresent.ActivityPresent/GetRankBlackUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
