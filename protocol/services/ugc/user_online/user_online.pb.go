// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc/user_online.proto

package user_online // import "golang.52tt.com/protocol/services/ugc/user_online"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetUserOnlineReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=Uids,proto3" json:"Uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOnlineReq) Reset()         { *m = GetUserOnlineReq{} }
func (m *GetUserOnlineReq) String() string { return proto.CompactTextString(m) }
func (*GetUserOnlineReq) ProtoMessage()    {}
func (*GetUserOnlineReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_online_e6d336aa0813cc1f, []int{0}
}
func (m *GetUserOnlineReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOnlineReq.Unmarshal(m, b)
}
func (m *GetUserOnlineReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOnlineReq.Marshal(b, m, deterministic)
}
func (dst *GetUserOnlineReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOnlineReq.Merge(dst, src)
}
func (m *GetUserOnlineReq) XXX_Size() int {
	return xxx_messageInfo_GetUserOnlineReq.Size(m)
}
func (m *GetUserOnlineReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOnlineReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOnlineReq proto.InternalMessageInfo

func (m *GetUserOnlineReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetUserOnlineResp struct {
	Status               map[uint32]uint32 `protobuf:"bytes,1,rep,name=Status,proto3" json:"Status,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetUserOnlineResp) Reset()         { *m = GetUserOnlineResp{} }
func (m *GetUserOnlineResp) String() string { return proto.CompactTextString(m) }
func (*GetUserOnlineResp) ProtoMessage()    {}
func (*GetUserOnlineResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_online_e6d336aa0813cc1f, []int{1}
}
func (m *GetUserOnlineResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOnlineResp.Unmarshal(m, b)
}
func (m *GetUserOnlineResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOnlineResp.Marshal(b, m, deterministic)
}
func (dst *GetUserOnlineResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOnlineResp.Merge(dst, src)
}
func (m *GetUserOnlineResp) XXX_Size() int {
	return xxx_messageInfo_GetUserOnlineResp.Size(m)
}
func (m *GetUserOnlineResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOnlineResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOnlineResp proto.InternalMessageInfo

func (m *GetUserOnlineResp) GetStatus() map[uint32]uint32 {
	if m != nil {
		return m.Status
	}
	return nil
}

func init() {
	proto.RegisterType((*GetUserOnlineReq)(nil), "ugc.user_online.GetUserOnlineReq")
	proto.RegisterType((*GetUserOnlineResp)(nil), "ugc.user_online.GetUserOnlineResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "ugc.user_online.GetUserOnlineResp.StatusEntry")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserOnlineClient is the client API for UserOnline service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserOnlineClient interface {
	GetUserOnlineStatus(ctx context.Context, in *GetUserOnlineReq, opts ...grpc.CallOption) (*GetUserOnlineResp, error)
}

type userOnlineClient struct {
	cc *grpc.ClientConn
}

func NewUserOnlineClient(cc *grpc.ClientConn) UserOnlineClient {
	return &userOnlineClient{cc}
}

func (c *userOnlineClient) GetUserOnlineStatus(ctx context.Context, in *GetUserOnlineReq, opts ...grpc.CallOption) (*GetUserOnlineResp, error) {
	out := new(GetUserOnlineResp)
	err := c.cc.Invoke(ctx, "/ugc.user_online.UserOnline/GetUserOnlineStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserOnlineServer is the server API for UserOnline service.
type UserOnlineServer interface {
	GetUserOnlineStatus(context.Context, *GetUserOnlineReq) (*GetUserOnlineResp, error)
}

func RegisterUserOnlineServer(s *grpc.Server, srv UserOnlineServer) {
	s.RegisterService(&_UserOnline_serviceDesc, srv)
}

func _UserOnline_GetUserOnlineStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOnlineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserOnlineServer).GetUserOnlineStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc.user_online.UserOnline/GetUserOnlineStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserOnlineServer).GetUserOnlineStatus(ctx, req.(*GetUserOnlineReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserOnline_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc.user_online.UserOnline",
	HandlerType: (*UserOnlineServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserOnlineStatus",
			Handler:    _UserOnline_GetUserOnlineStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc/user_online.proto",
}

func init() { proto.RegisterFile("ugc/user_online.proto", fileDescriptor_user_online_e6d336aa0813cc1f) }

var fileDescriptor_user_online_e6d336aa0813cc1f = []byte{
	// 237 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x2d, 0x4d, 0x4f, 0xd6,
	0x2f, 0x2d, 0x4e, 0x2d, 0x8a, 0xcf, 0xcf, 0xcb, 0xc9, 0xcc, 0x4b, 0xd5, 0x2b, 0x28, 0xca, 0x2f,
	0xc9, 0x17, 0xe2, 0x2f, 0x4d, 0x4f, 0xd6, 0x43, 0x12, 0x56, 0x52, 0xe3, 0x12, 0x70, 0x4f, 0x2d,
	0x09, 0x2d, 0x4e, 0x2d, 0xf2, 0x07, 0x0b, 0x04, 0xa5, 0x16, 0x0a, 0x09, 0x71, 0xb1, 0x84, 0x66,
	0xa6, 0x14, 0x4b, 0x30, 0x2a, 0x30, 0x6b, 0xf0, 0x06, 0x81, 0xd9, 0x4a, 0xd3, 0x18, 0xb9, 0x04,
	0xd1, 0x14, 0x16, 0x17, 0x08, 0xb9, 0x71, 0xb1, 0x05, 0x97, 0x24, 0x96, 0x94, 0x42, 0xd4, 0x72,
	0x1b, 0xe9, 0xe9, 0xa1, 0x99, 0xaf, 0x87, 0xa1, 0x47, 0x0f, 0xa2, 0xc1, 0x35, 0xaf, 0xa4, 0xa8,
	0x32, 0x08, 0xaa, 0x5b, 0xca, 0x92, 0x8b, 0x1b, 0x49, 0x58, 0x48, 0x80, 0x8b, 0x39, 0x3b, 0xb5,
	0x52, 0x82, 0x51, 0x81, 0x51, 0x83, 0x37, 0x08, 0xc4, 0x14, 0x12, 0xe1, 0x62, 0x2d, 0x4b, 0xcc,
	0x29, 0x4d, 0x95, 0x60, 0x02, 0x8b, 0x41, 0x38, 0x56, 0x4c, 0x16, 0x8c, 0x46, 0x59, 0x5c, 0x5c,
	0x08, 0x0b, 0x84, 0x62, 0xb8, 0x84, 0x51, 0x6c, 0x84, 0x98, 0x2a, 0xa4, 0x48, 0xc8, 0x5d, 0x85,
	0x52, 0x4a, 0x84, 0x9d, 0xee, 0x64, 0x1c, 0x65, 0x98, 0x9e, 0x9f, 0x93, 0x98, 0x97, 0xae, 0x67,
	0x6a, 0x54, 0x52, 0xa2, 0x97, 0x9c, 0x9f, 0xab, 0x0f, 0x0e, 0xd6, 0xe4, 0xfc, 0x1c, 0xfd, 0xe2,
	0xd4, 0xa2, 0xb2, 0xcc, 0xe4, 0xd4, 0x62, 0x7d, 0xb4, 0x80, 0x4f, 0x62, 0x03, 0x2b, 0x31, 0x06,
	0x04, 0x00, 0x00, 0xff, 0xff, 0x8a, 0x09, 0xe2, 0x80, 0x92, 0x01, 0x00, 0x00,
}
