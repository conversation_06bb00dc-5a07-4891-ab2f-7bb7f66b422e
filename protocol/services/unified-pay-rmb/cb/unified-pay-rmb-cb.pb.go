// Code generated by protoc-gen-go. DO NOT EDIT.
// source: unified-pay-rmb/unified-pay-rmb-cb.proto

package cb // import "golang.52tt.com/protocol/services/unified-pay-rmb/cb"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import unified_pay_rmb "golang.52tt.com/protocol/services/unified-pay-rmb"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 以下业务服务实现-支付结果回调功能
type PayRmbResultNotifyReq struct {
	OrderResult          *unified_pay_rmb.RmbPayOrderInfo `protobuf:"bytes,1,opt,name=order_result,json=orderResult,proto3" json:"order_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *PayRmbResultNotifyReq) Reset()         { *m = PayRmbResultNotifyReq{} }
func (m *PayRmbResultNotifyReq) String() string { return proto.CompactTextString(m) }
func (*PayRmbResultNotifyReq) ProtoMessage()    {}
func (*PayRmbResultNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_rmb_cb_2f47f6d2918f3c5e, []int{0}
}
func (m *PayRmbResultNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRmbResultNotifyReq.Unmarshal(m, b)
}
func (m *PayRmbResultNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRmbResultNotifyReq.Marshal(b, m, deterministic)
}
func (dst *PayRmbResultNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRmbResultNotifyReq.Merge(dst, src)
}
func (m *PayRmbResultNotifyReq) XXX_Size() int {
	return xxx_messageInfo_PayRmbResultNotifyReq.Size(m)
}
func (m *PayRmbResultNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRmbResultNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_PayRmbResultNotifyReq proto.InternalMessageInfo

func (m *PayRmbResultNotifyReq) GetOrderResult() *unified_pay_rmb.RmbPayOrderInfo {
	if m != nil {
		return m.OrderResult
	}
	return nil
}

type PayRmbResultNotifyRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PayRmbResultNotifyRsp) Reset()         { *m = PayRmbResultNotifyRsp{} }
func (m *PayRmbResultNotifyRsp) String() string { return proto.CompactTextString(m) }
func (*PayRmbResultNotifyRsp) ProtoMessage()    {}
func (*PayRmbResultNotifyRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_pay_rmb_cb_2f47f6d2918f3c5e, []int{1}
}
func (m *PayRmbResultNotifyRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PayRmbResultNotifyRsp.Unmarshal(m, b)
}
func (m *PayRmbResultNotifyRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PayRmbResultNotifyRsp.Marshal(b, m, deterministic)
}
func (dst *PayRmbResultNotifyRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PayRmbResultNotifyRsp.Merge(dst, src)
}
func (m *PayRmbResultNotifyRsp) XXX_Size() int {
	return xxx_messageInfo_PayRmbResultNotifyRsp.Size(m)
}
func (m *PayRmbResultNotifyRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PayRmbResultNotifyRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PayRmbResultNotifyRsp proto.InternalMessageInfo

func (m *PayRmbResultNotifyRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *PayRmbResultNotifyRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func init() {
	proto.RegisterType((*PayRmbResultNotifyReq)(nil), "unified_pay_rmb_cb.PayRmbResultNotifyReq")
	proto.RegisterType((*PayRmbResultNotifyRsp)(nil), "unified_pay_rmb_cb.PayRmbResultNotifyRsp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PayRmbCallbackClient is the client API for PayRmbCallback service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PayRmbCallbackClient interface {
	PayRmbResultNotify(ctx context.Context, in *PayRmbResultNotifyReq, opts ...grpc.CallOption) (*PayRmbResultNotifyRsp, error)
}

type payRmbCallbackClient struct {
	cc *grpc.ClientConn
}

func NewPayRmbCallbackClient(cc *grpc.ClientConn) PayRmbCallbackClient {
	return &payRmbCallbackClient{cc}
}

func (c *payRmbCallbackClient) PayRmbResultNotify(ctx context.Context, in *PayRmbResultNotifyReq, opts ...grpc.CallOption) (*PayRmbResultNotifyRsp, error) {
	out := new(PayRmbResultNotifyRsp)
	err := c.cc.Invoke(ctx, "/unified_pay_rmb_cb.PayRmbCallback/PayRmbResultNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PayRmbCallbackServer is the server API for PayRmbCallback service.
type PayRmbCallbackServer interface {
	PayRmbResultNotify(context.Context, *PayRmbResultNotifyReq) (*PayRmbResultNotifyRsp, error)
}

func RegisterPayRmbCallbackServer(s *grpc.Server, srv PayRmbCallbackServer) {
	s.RegisterService(&_PayRmbCallback_serviceDesc, srv)
}

func _PayRmbCallback_PayRmbResultNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PayRmbResultNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayRmbCallbackServer).PayRmbResultNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/unified_pay_rmb_cb.PayRmbCallback/PayRmbResultNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayRmbCallbackServer).PayRmbResultNotify(ctx, req.(*PayRmbResultNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PayRmbCallback_serviceDesc = grpc.ServiceDesc{
	ServiceName: "unified_pay_rmb_cb.PayRmbCallback",
	HandlerType: (*PayRmbCallbackServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PayRmbResultNotify",
			Handler:    _PayRmbCallback_PayRmbResultNotify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "unified-pay-rmb/unified-pay-rmb-cb.proto",
}

func init() {
	proto.RegisterFile("unified-pay-rmb/unified-pay-rmb-cb.proto", fileDescriptor_unified_pay_rmb_cb_2f47f6d2918f3c5e)
}

var fileDescriptor_unified_pay_rmb_cb_2f47f6d2918f3c5e = []byte{
	// 254 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x90, 0xcf, 0x4b, 0xc4, 0x30,
	0x10, 0x85, 0xa9, 0x8a, 0x60, 0x56, 0x45, 0x02, 0xc2, 0xb2, 0xa7, 0xb2, 0x20, 0xd4, 0x43, 0x53,
	0xa8, 0x3f, 0x6e, 0x5e, 0xdc, 0x93, 0x17, 0x5d, 0x72, 0x14, 0xa1, 0x64, 0xd2, 0xb4, 0x54, 0x9b,
	0x4e, 0x4c, 0xb2, 0x42, 0xc0, 0x3f, 0x5e, 0x36, 0xf5, 0xd4, 0x5d, 0xd0, 0xdb, 0x84, 0x79, 0xdf,
	0x17, 0xe6, 0x91, 0x6c, 0x33, 0x74, 0x4d, 0xa7, 0xea, 0xdc, 0x88, 0x90, 0x5b, 0x0d, 0xc5, 0xe4,
	0x9d, 0x4b, 0x60, 0xc6, 0xa2, 0x47, 0x4a, 0x7f, 0x37, 0x95, 0x11, 0xa1, 0xb2, 0x1a, 0x2a, 0x09,
	0x8b, 0xab, 0x3f, 0xe8, 0x11, 0x5d, 0xbe, 0x91, 0xcb, 0xb5, 0x08, 0x5c, 0x03, 0x57, 0x6e, 0xd3,
	0xfb, 0x67, 0xf4, 0x5d, 0x13, 0xb8, 0xfa, 0xa4, 0x2b, 0x72, 0x8a, 0xb6, 0x56, 0xb6, 0xb2, 0x71,
	0x31, 0x4f, 0xd2, 0x24, 0x9b, 0x95, 0x29, 0x9b, 0x7c, 0xc5, 0xb8, 0x86, 0xb5, 0x08, 0x2f, 0xdb,
	0xe8, 0xd3, 0xd0, 0x20, 0x9f, 0x45, 0x6a, 0xb4, 0x2d, 0x1f, 0xf6, 0xda, 0x9d, 0xa1, 0x94, 0x1c,
	0x49, 0xac, 0x55, 0xb4, 0x9e, 0xf1, 0x38, 0xd3, 0x0b, 0x72, 0xa8, 0x5d, 0x3b, 0x3f, 0x48, 0x93,
	0xec, 0x84, 0x6f, 0xc7, 0xf2, 0x9b, 0x9c, 0x8f, 0xf8, 0x4a, 0xf4, 0x3d, 0x08, 0xf9, 0x41, 0xdf,
	0x09, 0xdd, 0x15, 0xd2, 0x6b, 0xb6, 0x5b, 0x00, 0xdb, 0x7b, 0xd6, 0xe2, 0xbf, 0x51, 0x67, 0x1e,
	0xef, 0x5f, 0x6f, 0x5b, 0xec, 0xc5, 0xd0, 0xb2, 0xbb, 0xd2, 0x7b, 0x26, 0x51, 0x17, 0xb1, 0x33,
	0x89, 0x7d, 0xe1, 0x94, 0xfd, 0xea, 0xa4, 0x72, 0xd3, 0x56, 0x0b, 0x09, 0x70, 0x1c, 0x53, 0x37,
	0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xb3, 0xe0, 0xcd, 0x80, 0xc0, 0x01, 0x00, 0x00,
}
