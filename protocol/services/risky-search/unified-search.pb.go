// Code generated by protoc-gen-go. DO NOT EDIT.
// source: unified-search/unified-search.proto

package risky_search // import "golang.52tt.com/protocol/services/risky-search"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ObjectType int32

const (
	ObjectType_USER    ObjectType = 0
	ObjectType_CHANNEL ObjectType = 1
	ObjectType_GUILD   ObjectType = 2
)

var ObjectType_name = map[int32]string{
	0: "USER",
	1: "CHANNEL",
	2: "GUILD",
}
var ObjectType_value = map[string]int32{
	"USER":    0,
	"CHANNEL": 1,
	"GUILD":   2,
}

func (x ObjectType) String() string {
	return proto.EnumName(ObjectType_name, int32(x))
}
func (ObjectType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{0}
}

type RiskyObject struct {
	Id                   uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ObjectType           ObjectType `protobuf:"varint,2,opt,name=object_type,json=objectType,proto3,enum=risky_search.ObjectType" json:"object_type,omitempty"`
	ExpireAt             int64      `protobuf:"varint,3,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RiskyObject) Reset()         { *m = RiskyObject{} }
func (m *RiskyObject) String() string { return proto.CompactTextString(m) }
func (*RiskyObject) ProtoMessage()    {}
func (*RiskyObject) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{0}
}
func (m *RiskyObject) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RiskyObject.Unmarshal(m, b)
}
func (m *RiskyObject) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RiskyObject.Marshal(b, m, deterministic)
}
func (dst *RiskyObject) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RiskyObject.Merge(dst, src)
}
func (m *RiskyObject) XXX_Size() int {
	return xxx_messageInfo_RiskyObject.Size(m)
}
func (m *RiskyObject) XXX_DiscardUnknown() {
	xxx_messageInfo_RiskyObject.DiscardUnknown(m)
}

var xxx_messageInfo_RiskyObject proto.InternalMessageInfo

func (m *RiskyObject) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RiskyObject) GetObjectType() ObjectType {
	if m != nil {
		return m.ObjectType
	}
	return ObjectType_USER
}

func (m *RiskyObject) GetExpireAt() int64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

type AddRiskyReq struct {
	Objects              []*RiskyObject `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AddRiskyReq) Reset()         { *m = AddRiskyReq{} }
func (m *AddRiskyReq) String() string { return proto.CompactTextString(m) }
func (*AddRiskyReq) ProtoMessage()    {}
func (*AddRiskyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{1}
}
func (m *AddRiskyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRiskyReq.Unmarshal(m, b)
}
func (m *AddRiskyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRiskyReq.Marshal(b, m, deterministic)
}
func (dst *AddRiskyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRiskyReq.Merge(dst, src)
}
func (m *AddRiskyReq) XXX_Size() int {
	return xxx_messageInfo_AddRiskyReq.Size(m)
}
func (m *AddRiskyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRiskyReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRiskyReq proto.InternalMessageInfo

func (m *AddRiskyReq) GetObjects() []*RiskyObject {
	if m != nil {
		return m.Objects
	}
	return nil
}

type AddRiskyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRiskyResp) Reset()         { *m = AddRiskyResp{} }
func (m *AddRiskyResp) String() string { return proto.CompactTextString(m) }
func (*AddRiskyResp) ProtoMessage()    {}
func (*AddRiskyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{2}
}
func (m *AddRiskyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRiskyResp.Unmarshal(m, b)
}
func (m *AddRiskyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRiskyResp.Marshal(b, m, deterministic)
}
func (dst *AddRiskyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRiskyResp.Merge(dst, src)
}
func (m *AddRiskyResp) XXX_Size() int {
	return xxx_messageInfo_AddRiskyResp.Size(m)
}
func (m *AddRiskyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRiskyResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRiskyResp proto.InternalMessageInfo

type RemoveRiskyReq struct {
	Objects              []*RiskyObject `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RemoveRiskyReq) Reset()         { *m = RemoveRiskyReq{} }
func (m *RemoveRiskyReq) String() string { return proto.CompactTextString(m) }
func (*RemoveRiskyReq) ProtoMessage()    {}
func (*RemoveRiskyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{3}
}
func (m *RemoveRiskyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRiskyReq.Unmarshal(m, b)
}
func (m *RemoveRiskyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRiskyReq.Marshal(b, m, deterministic)
}
func (dst *RemoveRiskyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRiskyReq.Merge(dst, src)
}
func (m *RemoveRiskyReq) XXX_Size() int {
	return xxx_messageInfo_RemoveRiskyReq.Size(m)
}
func (m *RemoveRiskyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRiskyReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRiskyReq proto.InternalMessageInfo

func (m *RemoveRiskyReq) GetObjects() []*RiskyObject {
	if m != nil {
		return m.Objects
	}
	return nil
}

type RemoveRiskyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveRiskyResp) Reset()         { *m = RemoveRiskyResp{} }
func (m *RemoveRiskyResp) String() string { return proto.CompactTextString(m) }
func (*RemoveRiskyResp) ProtoMessage()    {}
func (*RemoveRiskyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{4}
}
func (m *RemoveRiskyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRiskyResp.Unmarshal(m, b)
}
func (m *RemoveRiskyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRiskyResp.Marshal(b, m, deterministic)
}
func (dst *RemoveRiskyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRiskyResp.Merge(dst, src)
}
func (m *RemoveRiskyResp) XXX_Size() int {
	return xxx_messageInfo_RemoveRiskyResp.Size(m)
}
func (m *RemoveRiskyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRiskyResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRiskyResp proto.InternalMessageInfo

type CheckRiskyReq struct {
	Objects              []*RiskyObject `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CheckRiskyReq) Reset()         { *m = CheckRiskyReq{} }
func (m *CheckRiskyReq) String() string { return proto.CompactTextString(m) }
func (*CheckRiskyReq) ProtoMessage()    {}
func (*CheckRiskyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{5}
}
func (m *CheckRiskyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRiskyReq.Unmarshal(m, b)
}
func (m *CheckRiskyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRiskyReq.Marshal(b, m, deterministic)
}
func (dst *CheckRiskyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRiskyReq.Merge(dst, src)
}
func (m *CheckRiskyReq) XXX_Size() int {
	return xxx_messageInfo_CheckRiskyReq.Size(m)
}
func (m *CheckRiskyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRiskyReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRiskyReq proto.InternalMessageInfo

func (m *CheckRiskyReq) GetObjects() []*RiskyObject {
	if m != nil {
		return m.Objects
	}
	return nil
}

type CheckRiskyResp struct {
	Hits                 []bool   `protobuf:"varint,1,rep,packed,name=hits,proto3" json:"hits,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRiskyResp) Reset()         { *m = CheckRiskyResp{} }
func (m *CheckRiskyResp) String() string { return proto.CompactTextString(m) }
func (*CheckRiskyResp) ProtoMessage()    {}
func (*CheckRiskyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_unified_search_342186b30961c205, []int{6}
}
func (m *CheckRiskyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRiskyResp.Unmarshal(m, b)
}
func (m *CheckRiskyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRiskyResp.Marshal(b, m, deterministic)
}
func (dst *CheckRiskyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRiskyResp.Merge(dst, src)
}
func (m *CheckRiskyResp) XXX_Size() int {
	return xxx_messageInfo_CheckRiskyResp.Size(m)
}
func (m *CheckRiskyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRiskyResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRiskyResp proto.InternalMessageInfo

func (m *CheckRiskyResp) GetHits() []bool {
	if m != nil {
		return m.Hits
	}
	return nil
}

func init() {
	proto.RegisterType((*RiskyObject)(nil), "risky_search.RiskyObject")
	proto.RegisterType((*AddRiskyReq)(nil), "risky_search.AddRiskyReq")
	proto.RegisterType((*AddRiskyResp)(nil), "risky_search.AddRiskyResp")
	proto.RegisterType((*RemoveRiskyReq)(nil), "risky_search.RemoveRiskyReq")
	proto.RegisterType((*RemoveRiskyResp)(nil), "risky_search.RemoveRiskyResp")
	proto.RegisterType((*CheckRiskyReq)(nil), "risky_search.CheckRiskyReq")
	proto.RegisterType((*CheckRiskyResp)(nil), "risky_search.CheckRiskyResp")
	proto.RegisterEnum("risky_search.ObjectType", ObjectType_name, ObjectType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RiskySearchClient is the client API for RiskySearch service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RiskySearchClient interface {
	// 开黑列表自定义筛选器
	AddRisky(ctx context.Context, in *AddRiskyReq, opts ...grpc.CallOption) (*AddRiskyResp, error)
	RemoveRisky(ctx context.Context, in *RemoveRiskyReq, opts ...grpc.CallOption) (*RemoveRiskyResp, error)
	CheckRisky(ctx context.Context, in *CheckRiskyReq, opts ...grpc.CallOption) (*CheckRiskyResp, error)
}

type riskySearchClient struct {
	cc *grpc.ClientConn
}

func NewRiskySearchClient(cc *grpc.ClientConn) RiskySearchClient {
	return &riskySearchClient{cc}
}

func (c *riskySearchClient) AddRisky(ctx context.Context, in *AddRiskyReq, opts ...grpc.CallOption) (*AddRiskyResp, error) {
	out := new(AddRiskyResp)
	err := c.cc.Invoke(ctx, "/risky_search.RiskySearch/AddRisky", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskySearchClient) RemoveRisky(ctx context.Context, in *RemoveRiskyReq, opts ...grpc.CallOption) (*RemoveRiskyResp, error) {
	out := new(RemoveRiskyResp)
	err := c.cc.Invoke(ctx, "/risky_search.RiskySearch/RemoveRisky", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskySearchClient) CheckRisky(ctx context.Context, in *CheckRiskyReq, opts ...grpc.CallOption) (*CheckRiskyResp, error) {
	out := new(CheckRiskyResp)
	err := c.cc.Invoke(ctx, "/risky_search.RiskySearch/CheckRisky", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RiskySearchServer is the server API for RiskySearch service.
type RiskySearchServer interface {
	// 开黑列表自定义筛选器
	AddRisky(context.Context, *AddRiskyReq) (*AddRiskyResp, error)
	RemoveRisky(context.Context, *RemoveRiskyReq) (*RemoveRiskyResp, error)
	CheckRisky(context.Context, *CheckRiskyReq) (*CheckRiskyResp, error)
}

func RegisterRiskySearchServer(s *grpc.Server, srv RiskySearchServer) {
	s.RegisterService(&_RiskySearch_serviceDesc, srv)
}

func _RiskySearch_AddRisky_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRiskyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskySearchServer).AddRisky(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risky_search.RiskySearch/AddRisky",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskySearchServer).AddRisky(ctx, req.(*AddRiskyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskySearch_RemoveRisky_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRiskyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskySearchServer).RemoveRisky(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risky_search.RiskySearch/RemoveRisky",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskySearchServer).RemoveRisky(ctx, req.(*RemoveRiskyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskySearch_CheckRisky_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRiskyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskySearchServer).CheckRisky(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/risky_search.RiskySearch/CheckRisky",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskySearchServer).CheckRisky(ctx, req.(*CheckRiskyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RiskySearch_serviceDesc = grpc.ServiceDesc{
	ServiceName: "risky_search.RiskySearch",
	HandlerType: (*RiskySearchServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddRisky",
			Handler:    _RiskySearch_AddRisky_Handler,
		},
		{
			MethodName: "RemoveRisky",
			Handler:    _RiskySearch_RemoveRisky_Handler,
		},
		{
			MethodName: "CheckRisky",
			Handler:    _RiskySearch_CheckRisky_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "unified-search/unified-search.proto",
}

func init() {
	proto.RegisterFile("unified-search/unified-search.proto", fileDescriptor_unified_search_342186b30961c205)
}

var fileDescriptor_unified_search_342186b30961c205 = []byte{
	// 375 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x92, 0x4d, 0xcf, 0xd2, 0x40,
	0x10, 0xc7, 0x6d, 0x79, 0x94, 0x32, 0x85, 0x8a, 0x73, 0xaa, 0x05, 0x93, 0xa6, 0x7a, 0x68, 0x4c,
	0x6c, 0x4d, 0x89, 0x07, 0x8f, 0xe5, 0x25, 0xa8, 0x21, 0x98, 0x2c, 0x72, 0xf1, 0x42, 0xa0, 0x5d,
	0x61, 0xe5, 0x65, 0xd7, 0x6e, 0x21, 0xf2, 0x81, 0xfd, 0x1e, 0xc6, 0x16, 0xa4, 0x25, 0xe1, 0xb9,
	0x70, 0xdb, 0xdd, 0xff, 0xcc, 0x6f, 0x66, 0xfe, 0x3b, 0xf0, 0x7a, 0xbf, 0x63, 0x3f, 0x18, 0x8d,
	0xdf, 0x49, 0x3a, 0x4f, 0xa2, 0x95, 0x5f, 0xbe, 0x7a, 0x22, 0xe1, 0x29, 0xc7, 0x7a, 0xc2, 0xe4,
	0xfa, 0x38, 0xcb, 0xdf, 0x9c, 0x3d, 0xe8, 0xe4, 0xdf, 0xfd, 0xeb, 0xe2, 0x27, 0x8d, 0x52, 0x34,
	0x40, 0x65, 0xb1, 0xa9, 0xd8, 0x8a, 0xdb, 0x20, 0x2a, 0x8b, 0xf1, 0x23, 0xe8, 0x3c, 0x53, 0x66,
	0xe9, 0x51, 0x50, 0x53, 0xb5, 0x15, 0xd7, 0x08, 0x4c, 0xaf, 0x88, 0xf0, 0xf2, 0xd4, 0x6f, 0x47,
	0x41, 0x09, 0xf0, 0xff, 0x67, 0x6c, 0x41, 0x8d, 0xfe, 0x16, 0x2c, 0xa1, 0xb3, 0x79, 0x6a, 0x56,
	0x6c, 0xc5, 0xad, 0x10, 0x2d, 0x7f, 0x08, 0x53, 0xa7, 0x0b, 0x7a, 0x18, 0xc7, 0x59, 0x65, 0x42,
	0x7f, 0x61, 0x07, 0xaa, 0x79, 0xa6, 0x34, 0x15, 0xbb, 0xe2, 0xea, 0xc1, 0xcb, 0x72, 0x89, 0x42,
	0x8b, 0xe4, 0x1c, 0xe9, 0x18, 0x50, 0xbf, 0x30, 0xa4, 0x70, 0x06, 0x60, 0x10, 0xba, 0xe5, 0x07,
	0x7a, 0x1f, 0xf6, 0x05, 0x3c, 0x2f, 0x61, 0xa4, 0x70, 0xfa, 0xd0, 0xe8, 0xad, 0x68, 0xb4, 0xbe,
	0x0f, 0xfc, 0x06, 0x8c, 0x22, 0x45, 0x0a, 0x44, 0x78, 0x58, 0xb1, 0x13, 0x43, 0x23, 0xd9, 0xf9,
	0xad, 0x07, 0x70, 0x31, 0x14, 0x35, 0x78, 0x98, 0x4e, 0x06, 0xa4, 0xf9, 0x04, 0x75, 0xa8, 0xf6,
	0x3e, 0x85, 0xe3, 0xf1, 0x60, 0xd4, 0x54, 0xb0, 0x06, 0x4f, 0x87, 0xd3, 0xcf, 0xa3, 0x7e, 0x53,
	0x0d, 0xfe, 0x28, 0xa7, 0x1f, 0x9c, 0x64, 0xa5, 0x31, 0x04, 0xed, 0xec, 0x0a, 0x5e, 0x75, 0x55,
	0x70, 0xdc, 0xb2, 0x6e, 0x49, 0x52, 0xe0, 0x17, 0xd0, 0x0b, 0x0e, 0x60, 0xfb, 0x6a, 0xb6, 0x92,
	0xc7, 0xd6, 0xab, 0x47, 0x54, 0x29, 0x70, 0x08, 0x70, 0x19, 0x1a, 0x5b, 0xe5, 0xe0, 0x92, 0xa9,
	0x56, 0xfb, 0xb6, 0x28, 0x45, 0xf7, 0xfd, 0x77, 0x6f, 0xc9, 0x37, 0xf3, 0xdd, 0xd2, 0xfb, 0x10,
	0xa4, 0xa9, 0x17, 0xf1, 0xad, 0x9f, 0xed, 0x73, 0xc4, 0x37, 0xbe, 0xa4, 0xc9, 0x81, 0x45, 0x54,
	0xfa, 0x19, 0xe0, 0xb4, 0xee, 0x8b, 0x67, 0x99, 0xde, 0xf9, 0x1b, 0x00, 0x00, 0xff, 0xff, 0x11,
	0x47, 0x4e, 0x54, 0x16, 0x03, 0x00, 0x00,
}
