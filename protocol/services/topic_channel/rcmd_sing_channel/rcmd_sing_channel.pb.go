// Code generated by protoc-gen-go. DO NOT EDIT.
// source: topic_channel/rcmd_sing_channel.proto

package rcmd_sing_channel // import "golang.52tt.com/protocol/services/topic_channel/rcmd_sing_channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import event "golang.52tt.com/protocol/services/sing-a-round/event"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GetQuickMatchSingChannelReq struct {
	Uid                  uint32                    `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagId                uint32                    `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	NumLimit             uint32                    `protobuf:"varint,3,opt,name=num_limit,json=numLimit,proto3" json:"num_limit,omitempty"`
	InvalidChannelList   []*event.SingChannelEvent `protobuf:"bytes,4,rep,name=invalid_channel_list,json=invalidChannelList,proto3" json:"invalid_channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetQuickMatchSingChannelReq) Reset()         { *m = GetQuickMatchSingChannelReq{} }
func (m *GetQuickMatchSingChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchSingChannelReq) ProtoMessage()    {}
func (*GetQuickMatchSingChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_sing_channel_c9bad5c5286841ce, []int{0}
}
func (m *GetQuickMatchSingChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchSingChannelReq.Unmarshal(m, b)
}
func (m *GetQuickMatchSingChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchSingChannelReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchSingChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchSingChannelReq.Merge(dst, src)
}
func (m *GetQuickMatchSingChannelReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchSingChannelReq.Size(m)
}
func (m *GetQuickMatchSingChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchSingChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchSingChannelReq proto.InternalMessageInfo

func (m *GetQuickMatchSingChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetQuickMatchSingChannelReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetQuickMatchSingChannelReq) GetNumLimit() uint32 {
	if m != nil {
		return m.NumLimit
	}
	return 0
}

func (m *GetQuickMatchSingChannelReq) GetInvalidChannelList() []*event.SingChannelEvent {
	if m != nil {
		return m.InvalidChannelList
	}
	return nil
}

type GetQuickMatchSingChannelResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetQuickMatchSingChannelResp) Reset()         { *m = GetQuickMatchSingChannelResp{} }
func (m *GetQuickMatchSingChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchSingChannelResp) ProtoMessage()    {}
func (*GetQuickMatchSingChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_sing_channel_c9bad5c5286841ce, []int{1}
}
func (m *GetQuickMatchSingChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchSingChannelResp.Unmarshal(m, b)
}
func (m *GetQuickMatchSingChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchSingChannelResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchSingChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchSingChannelResp.Merge(dst, src)
}
func (m *GetQuickMatchSingChannelResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchSingChannelResp.Size(m)
}
func (m *GetQuickMatchSingChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchSingChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchSingChannelResp proto.InternalMessageInfo

func (m *GetQuickMatchSingChannelResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ReportUserScoreEvent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                uint32   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ScoreTime            uint64   `protobuf:"varint,4,opt,name=score_time,json=scoreTime,proto3" json:"score_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserScoreEvent) Reset()         { *m = ReportUserScoreEvent{} }
func (m *ReportUserScoreEvent) String() string { return proto.CompactTextString(m) }
func (*ReportUserScoreEvent) ProtoMessage()    {}
func (*ReportUserScoreEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_sing_channel_c9bad5c5286841ce, []int{2}
}
func (m *ReportUserScoreEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserScoreEvent.Unmarshal(m, b)
}
func (m *ReportUserScoreEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserScoreEvent.Marshal(b, m, deterministic)
}
func (dst *ReportUserScoreEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserScoreEvent.Merge(dst, src)
}
func (m *ReportUserScoreEvent) XXX_Size() int {
	return xxx_messageInfo_ReportUserScoreEvent.Size(m)
}
func (m *ReportUserScoreEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserScoreEvent.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserScoreEvent proto.InternalMessageInfo

func (m *ReportUserScoreEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportUserScoreEvent) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ReportUserScoreEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportUserScoreEvent) GetScoreTime() uint64 {
	if m != nil {
		return m.ScoreTime
	}
	return 0
}

func init() {
	proto.RegisterType((*GetQuickMatchSingChannelReq)(nil), "topic_channel.rcmd_topic_channel.GetQuickMatchSingChannelReq")
	proto.RegisterType((*GetQuickMatchSingChannelResp)(nil), "topic_channel.rcmd_topic_channel.GetQuickMatchSingChannelResp")
	proto.RegisterType((*ReportUserScoreEvent)(nil), "topic_channel.rcmd_topic_channel.ReportUserScoreEvent")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RCMDSingChannelClient is the client API for RCMDSingChannel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RCMDSingChannelClient interface {
	GetQuickMatchChannel(ctx context.Context, in *GetQuickMatchSingChannelReq, opts ...grpc.CallOption) (*GetQuickMatchSingChannelResp, error)
}

type rCMDSingChannelClient struct {
	cc *grpc.ClientConn
}

func NewRCMDSingChannelClient(cc *grpc.ClientConn) RCMDSingChannelClient {
	return &rCMDSingChannelClient{cc}
}

func (c *rCMDSingChannelClient) GetQuickMatchChannel(ctx context.Context, in *GetQuickMatchSingChannelReq, opts ...grpc.CallOption) (*GetQuickMatchSingChannelResp, error) {
	out := new(GetQuickMatchSingChannelResp)
	err := c.cc.Invoke(ctx, "/topic_channel.rcmd_topic_channel.RCMDSingChannel/GetQuickMatchChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RCMDSingChannelServer is the server API for RCMDSingChannel service.
type RCMDSingChannelServer interface {
	GetQuickMatchChannel(context.Context, *GetQuickMatchSingChannelReq) (*GetQuickMatchSingChannelResp, error)
}

func RegisterRCMDSingChannelServer(s *grpc.Server, srv RCMDSingChannelServer) {
	s.RegisterService(&_RCMDSingChannel_serviceDesc, srv)
}

func _RCMDSingChannel_GetQuickMatchChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickMatchSingChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDSingChannelServer).GetQuickMatchChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/topic_channel.rcmd_topic_channel.RCMDSingChannel/GetQuickMatchChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDSingChannelServer).GetQuickMatchChannel(ctx, req.(*GetQuickMatchSingChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RCMDSingChannel_serviceDesc = grpc.ServiceDesc{
	ServiceName: "topic_channel.rcmd_topic_channel.RCMDSingChannel",
	HandlerType: (*RCMDSingChannelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetQuickMatchChannel",
			Handler:    _RCMDSingChannel_GetQuickMatchChannel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "topic_channel/rcmd_sing_channel.proto",
}

func init() {
	proto.RegisterFile("topic_channel/rcmd_sing_channel.proto", fileDescriptor_rcmd_sing_channel_c9bad5c5286841ce)
}

var fileDescriptor_rcmd_sing_channel_c9bad5c5286841ce = []byte{
	// 371 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x52, 0x41, 0x4b, 0xf3, 0x40,
	0x10, 0x25, 0x5f, 0xda, 0xf2, 0x75, 0x45, 0x94, 0x25, 0x42, 0x68, 0x15, 0x42, 0x51, 0xe8, 0xa5,
	0x1b, 0xa8, 0x78, 0xac, 0xa0, 0x55, 0xa4, 0xd0, 0x1e, 0x4c, 0xd5, 0x83, 0x97, 0xb0, 0x6e, 0x96,
	0x74, 0x30, 0xd9, 0x8d, 0xd9, 0x4d, 0x4f, 0xfe, 0x0d, 0x7f, 0x84, 0xbf, 0xc1, 0x3f, 0x27, 0xd9,
	0x36, 0xd2, 0x16, 0xb5, 0x07, 0x6f, 0xbb, 0xef, 0xcd, 0x9b, 0x79, 0x8f, 0x19, 0x74, 0xa2, 0x65,
	0x06, 0x2c, 0x64, 0x33, 0x2a, 0x04, 0x4f, 0xfc, 0x9c, 0xa5, 0x51, 0xa8, 0x40, 0xc4, 0x15, 0x42,
	0xb2, 0x5c, 0x6a, 0x89, 0xbd, 0xb5, 0x32, 0x62, 0xca, 0xd6, 0xa0, 0x96, 0x5b, 0xaa, 0x7a, 0xb4,
	0x97, 0xcb, 0x42, 0x44, 0x3e, 0x9f, 0x73, 0xa1, 0x17, 0xda, 0xce, 0x87, 0x85, 0xda, 0x37, 0x5c,
	0xdf, 0x16, 0xc0, 0x9e, 0x27, 0x54, 0xb3, 0xd9, 0x14, 0x44, 0x3c, 0x5c, 0xc8, 0x02, 0xfe, 0x82,
	0xf7, 0x91, 0x5d, 0x40, 0xe4, 0x5a, 0x9e, 0xd5, 0xdd, 0x0d, 0xca, 0x27, 0x3e, 0x40, 0x0d, 0x4d,
	0xe3, 0x10, 0x22, 0xf7, 0x9f, 0x01, 0xeb, 0x9a, 0xc6, 0xa3, 0x08, 0xb7, 0x51, 0x53, 0x14, 0x69,
	0x98, 0x40, 0x0a, 0xda, 0xb5, 0x0d, 0xf3, 0x5f, 0x14, 0xe9, 0xb8, 0xfc, 0xe3, 0x07, 0xe4, 0x80,
	0x98, 0xd3, 0x04, 0xa2, 0xca, 0x52, 0x98, 0x80, 0xd2, 0x6e, 0xcd, 0xb3, 0xbb, 0x3b, 0xfd, 0x63,
	0x62, 0x42, 0xd1, 0xd0, 0xd8, 0x23, 0x0b, 0x7b, 0x2b, 0x3e, 0xae, 0x4b, 0x20, 0xc0, 0xcb, 0x0e,
	0x4b, 0x70, 0x0c, 0x4a, 0x77, 0x06, 0xe8, 0xf0, 0x67, 0xf3, 0x2a, 0xc3, 0x47, 0x08, 0x55, 0xf3,
	0xbe, 0x42, 0x34, 0x97, 0xc8, 0x28, 0xea, 0xbc, 0x22, 0x27, 0xe0, 0x99, 0xcc, 0xf5, 0xbd, 0xe2,
	0xf9, 0x94, 0xc9, 0x9c, 0x9b, 0x51, 0xdf, 0x84, 0x76, 0x50, 0x5d, 0x95, 0x7c, 0x95, 0xd9, 0x7c,
	0x36, 0xda, 0xdb, 0x1b, 0xed, 0x4b, 0xda, 0xd4, 0x85, 0x1a, 0x52, 0xee, 0xd6, 0x3c, 0xab, 0x5b,
	0x0b, 0x9a, 0x06, 0xb9, 0x83, 0x94, 0xf7, 0xdf, 0x2d, 0xb4, 0x17, 0x0c, 0x27, 0x57, 0x2b, 0xa6,
	0xf1, 0x9b, 0x85, 0x9c, 0xb5, 0x44, 0x15, 0x31, 0x20, 0xdb, 0x96, 0x4c, 0x7e, 0x59, 0x63, 0xeb,
	0xfc, 0x2f, 0x72, 0x95, 0x5d, 0x0e, 0x1f, 0x2f, 0x62, 0x99, 0x50, 0x11, 0x93, 0xb3, 0xbe, 0xd6,
	0x84, 0xc9, 0xd4, 0x37, 0xf7, 0xc3, 0x64, 0xe2, 0x2b, 0x9e, 0xcf, 0x81, 0x71, 0xe5, 0x6f, 0xb9,
	0xd6, 0xa7, 0x86, 0x91, 0x9c, 0x7e, 0x06, 0x00, 0x00, 0xff, 0xff, 0x69, 0xf4, 0xd8, 0x86, 0xd7,
	0x02, 0x00, 0x00,
}
