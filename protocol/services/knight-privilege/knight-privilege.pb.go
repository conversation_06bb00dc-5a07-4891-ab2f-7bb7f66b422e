// Code generated by protoc-gen-go. DO NOT EDIT.
// source: knight-privilege/knight-privilege.proto

package knight_privilege // import "golang.52tt.com/protocol/services/knight-privilege"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 开通状态is_chief
type KnightStatusType int32

const (
	KnightStatusType_KNIGHT_NO     KnightStatusType = 0
	KnightStatusType_KNIGHT_COMMON KnightStatusType = 1
	KnightStatusType_KNIGHT_CHIEF  KnightStatusType = 2
)

var KnightStatusType_name = map[int32]string{
	0: "KNIGHT_NO",
	1: "KNIGHT_COMMON",
	2: "KNIGHT_CHIEF",
}
var KnightStatusType_value = map[string]int32{
	"KNIGHT_NO":     0,
	"KNIGHT_COMMON": 1,
	"KNIGHT_CHIEF":  2,
}

func (x KnightStatusType) String() string {
	return proto.EnumName(KnightStatusType_name, int32(x))
}
func (KnightStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{0}
}

type RequestType int32

const (
	RequestType_ALL                           RequestType = 0
	RequestType_SCEEN_RESOURCE_URL            RequestType = 1
	RequestType_KNIGHT_NAMEPLATE_RESOURCE_URL RequestType = 2
	RequestType_KNIGHT_CARD_RESOURCE_URL      RequestType = 3
	RequestType_ANCHOR_UIDS                   RequestType = 4
)

var RequestType_name = map[int32]string{
	0: "ALL",
	1: "SCEEN_RESOURCE_URL",
	2: "KNIGHT_NAMEPLATE_RESOURCE_URL",
	3: "KNIGHT_CARD_RESOURCE_URL",
	4: "ANCHOR_UIDS",
}
var RequestType_value = map[string]int32{
	"ALL":                           0,
	"SCEEN_RESOURCE_URL":            1,
	"KNIGHT_NAMEPLATE_RESOURCE_URL": 2,
	"KNIGHT_CARD_RESOURCE_URL":      3,
	"ANCHOR_UIDS":                   4,
}

func (x RequestType) String() string {
	return proto.EnumName(RequestType_name, int32(x))
}
func (RequestType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{1}
}

type GetKnightPrivilegeInfoReq struct {
	KnightUid            uint32   `protobuf:"varint,1,opt,name=knight_uid,json=knightUid,proto3" json:"knight_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RequestType          uint32   `protobuf:"varint,3,opt,name=request_type,json=requestType,proto3" json:"request_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKnightPrivilegeInfoReq) Reset()         { *m = GetKnightPrivilegeInfoReq{} }
func (m *GetKnightPrivilegeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightPrivilegeInfoReq) ProtoMessage()    {}
func (*GetKnightPrivilegeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{0}
}
func (m *GetKnightPrivilegeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightPrivilegeInfoReq.Unmarshal(m, b)
}
func (m *GetKnightPrivilegeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightPrivilegeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetKnightPrivilegeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightPrivilegeInfoReq.Merge(dst, src)
}
func (m *GetKnightPrivilegeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightPrivilegeInfoReq.Size(m)
}
func (m *GetKnightPrivilegeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightPrivilegeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightPrivilegeInfoReq proto.InternalMessageInfo

func (m *GetKnightPrivilegeInfoReq) GetKnightUid() uint32 {
	if m != nil {
		return m.KnightUid
	}
	return 0
}

func (m *GetKnightPrivilegeInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetKnightPrivilegeInfoReq) GetRequestType() uint32 {
	if m != nil {
		return m.RequestType
	}
	return 0
}

type GetKnightPrivilegeInfoResp struct {
	Status                     uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	SceenResourceUrl           string   `protobuf:"bytes,2,opt,name=sceen_resource_url,json=sceenResourceUrl,proto3" json:"sceen_resource_url,omitempty"`
	SceenResourceUrlMd5        string   `protobuf:"bytes,3,opt,name=sceen_resource_url_md5,json=sceenResourceUrlMd5,proto3" json:"sceen_resource_url_md5,omitempty"`
	KnightNameplateResourceUrl string   `protobuf:"bytes,4,opt,name=knight_nameplate_resource_url,json=knightNameplateResourceUrl,proto3" json:"knight_nameplate_resource_url,omitempty"`
	KnightCardResourceUrl      string   `protobuf:"bytes,5,opt,name=knight_card_resource_url,json=knightCardResourceUrl,proto3" json:"knight_card_resource_url,omitempty"`
	AnchorUids                 []uint32 `protobuf:"varint,6,rep,packed,name=anchor_uids,json=anchorUids,proto3" json:"anchor_uids,omitempty"`
	KnightChannelMsgBgUrl      string   `protobuf:"bytes,7,opt,name=knight_channel_msg_bg_url,json=knightChannelMsgBgUrl,proto3" json:"knight_channel_msg_bg_url,omitempty"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *GetKnightPrivilegeInfoResp) Reset()         { *m = GetKnightPrivilegeInfoResp{} }
func (m *GetKnightPrivilegeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightPrivilegeInfoResp) ProtoMessage()    {}
func (*GetKnightPrivilegeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{1}
}
func (m *GetKnightPrivilegeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightPrivilegeInfoResp.Unmarshal(m, b)
}
func (m *GetKnightPrivilegeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightPrivilegeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetKnightPrivilegeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightPrivilegeInfoResp.Merge(dst, src)
}
func (m *GetKnightPrivilegeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightPrivilegeInfoResp.Size(m)
}
func (m *GetKnightPrivilegeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightPrivilegeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightPrivilegeInfoResp proto.InternalMessageInfo

func (m *GetKnightPrivilegeInfoResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetKnightPrivilegeInfoResp) GetSceenResourceUrl() string {
	if m != nil {
		return m.SceenResourceUrl
	}
	return ""
}

func (m *GetKnightPrivilegeInfoResp) GetSceenResourceUrlMd5() string {
	if m != nil {
		return m.SceenResourceUrlMd5
	}
	return ""
}

func (m *GetKnightPrivilegeInfoResp) GetKnightNameplateResourceUrl() string {
	if m != nil {
		return m.KnightNameplateResourceUrl
	}
	return ""
}

func (m *GetKnightPrivilegeInfoResp) GetKnightCardResourceUrl() string {
	if m != nil {
		return m.KnightCardResourceUrl
	}
	return ""
}

func (m *GetKnightPrivilegeInfoResp) GetAnchorUids() []uint32 {
	if m != nil {
		return m.AnchorUids
	}
	return nil
}

func (m *GetKnightPrivilegeInfoResp) GetKnightChannelMsgBgUrl() string {
	if m != nil {
		return m.KnightChannelMsgBgUrl
	}
	return ""
}

type KnightGroupDetialInfo struct {
	PicUrl               string   `protobuf:"bytes,1,opt,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KnightGroupDetialInfo) Reset()         { *m = KnightGroupDetialInfo{} }
func (m *KnightGroupDetialInfo) String() string { return proto.CompactTextString(m) }
func (*KnightGroupDetialInfo) ProtoMessage()    {}
func (*KnightGroupDetialInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{2}
}
func (m *KnightGroupDetialInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KnightGroupDetialInfo.Unmarshal(m, b)
}
func (m *KnightGroupDetialInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KnightGroupDetialInfo.Marshal(b, m, deterministic)
}
func (dst *KnightGroupDetialInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KnightGroupDetialInfo.Merge(dst, src)
}
func (m *KnightGroupDetialInfo) XXX_Size() int {
	return xxx_messageInfo_KnightGroupDetialInfo.Size(m)
}
func (m *KnightGroupDetialInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_KnightGroupDetialInfo.DiscardUnknown(m)
}

var xxx_messageInfo_KnightGroupDetialInfo proto.InternalMessageInfo

func (m *KnightGroupDetialInfo) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *KnightGroupDetialInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *KnightGroupDetialInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type GetKnightGroupDetialInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKnightGroupDetialInfoReq) Reset()         { *m = GetKnightGroupDetialInfoReq{} }
func (m *GetKnightGroupDetialInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupDetialInfoReq) ProtoMessage()    {}
func (*GetKnightGroupDetialInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{3}
}
func (m *GetKnightGroupDetialInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupDetialInfoReq.Unmarshal(m, b)
}
func (m *GetKnightGroupDetialInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupDetialInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetKnightGroupDetialInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupDetialInfoReq.Merge(dst, src)
}
func (m *GetKnightGroupDetialInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupDetialInfoReq.Size(m)
}
func (m *GetKnightGroupDetialInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupDetialInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupDetialInfoReq proto.InternalMessageInfo

type GetKnightGroupDetialInfoResp struct {
	InfoList             []*KnightGroupDetialInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetKnightGroupDetialInfoResp) Reset()         { *m = GetKnightGroupDetialInfoResp{} }
func (m *GetKnightGroupDetialInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupDetialInfoResp) ProtoMessage()    {}
func (*GetKnightGroupDetialInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{4}
}
func (m *GetKnightGroupDetialInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupDetialInfoResp.Unmarshal(m, b)
}
func (m *GetKnightGroupDetialInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupDetialInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetKnightGroupDetialInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupDetialInfoResp.Merge(dst, src)
}
func (m *GetKnightGroupDetialInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupDetialInfoResp.Size(m)
}
func (m *GetKnightGroupDetialInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupDetialInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupDetialInfoResp proto.InternalMessageInfo

func (m *GetKnightGroupDetialInfoResp) GetInfoList() []*KnightGroupDetialInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GetKnightGroupMemberWithNameplateReq struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetKnightGroupMemberWithNameplateReq) Reset()         { *m = GetKnightGroupMemberWithNameplateReq{} }
func (m *GetKnightGroupMemberWithNameplateReq) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupMemberWithNameplateReq) ProtoMessage()    {}
func (*GetKnightGroupMemberWithNameplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{5}
}
func (m *GetKnightGroupMemberWithNameplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupMemberWithNameplateReq.Unmarshal(m, b)
}
func (m *GetKnightGroupMemberWithNameplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupMemberWithNameplateReq.Marshal(b, m, deterministic)
}
func (dst *GetKnightGroupMemberWithNameplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupMemberWithNameplateReq.Merge(dst, src)
}
func (m *GetKnightGroupMemberWithNameplateReq) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupMemberWithNameplateReq.Size(m)
}
func (m *GetKnightGroupMemberWithNameplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupMemberWithNameplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupMemberWithNameplateReq proto.InternalMessageInfo

func (m *GetKnightGroupMemberWithNameplateReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetKnightGroupMemberWithNameplateReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetKnightGroupMemberWithNameplateResp struct {
	UidList                          []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ChiefUid                         uint32   `protobuf:"varint,2,opt,name=chief_uid,json=chiefUid,proto3" json:"chief_uid,omitempty"`
	KnightCommonNameplateResourceUrl string   `protobuf:"bytes,3,opt,name=knight_common_nameplate_resource_url,json=knightCommonNameplateResourceUrl,proto3" json:"knight_common_nameplate_resource_url,omitempty"`
	KnightChiefNameplateResourceUrl  string   `protobuf:"bytes,4,opt,name=knight_chief_nameplate_resource_url,json=knightChiefNameplateResourceUrl,proto3" json:"knight_chief_nameplate_resource_url,omitempty"`
	XXX_NoUnkeyedLiteral             struct{} `json:"-"`
	XXX_unrecognized                 []byte   `json:"-"`
	XXX_sizecache                    int32    `json:"-"`
}

func (m *GetKnightGroupMemberWithNameplateResp) Reset()         { *m = GetKnightGroupMemberWithNameplateResp{} }
func (m *GetKnightGroupMemberWithNameplateResp) String() string { return proto.CompactTextString(m) }
func (*GetKnightGroupMemberWithNameplateResp) ProtoMessage()    {}
func (*GetKnightGroupMemberWithNameplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_knight_privilege_d33b6a10d6e1018b, []int{6}
}
func (m *GetKnightGroupMemberWithNameplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetKnightGroupMemberWithNameplateResp.Unmarshal(m, b)
}
func (m *GetKnightGroupMemberWithNameplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetKnightGroupMemberWithNameplateResp.Marshal(b, m, deterministic)
}
func (dst *GetKnightGroupMemberWithNameplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetKnightGroupMemberWithNameplateResp.Merge(dst, src)
}
func (m *GetKnightGroupMemberWithNameplateResp) XXX_Size() int {
	return xxx_messageInfo_GetKnightGroupMemberWithNameplateResp.Size(m)
}
func (m *GetKnightGroupMemberWithNameplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetKnightGroupMemberWithNameplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetKnightGroupMemberWithNameplateResp proto.InternalMessageInfo

func (m *GetKnightGroupMemberWithNameplateResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetKnightGroupMemberWithNameplateResp) GetChiefUid() uint32 {
	if m != nil {
		return m.ChiefUid
	}
	return 0
}

func (m *GetKnightGroupMemberWithNameplateResp) GetKnightCommonNameplateResourceUrl() string {
	if m != nil {
		return m.KnightCommonNameplateResourceUrl
	}
	return ""
}

func (m *GetKnightGroupMemberWithNameplateResp) GetKnightChiefNameplateResourceUrl() string {
	if m != nil {
		return m.KnightChiefNameplateResourceUrl
	}
	return ""
}

func init() {
	proto.RegisterType((*GetKnightPrivilegeInfoReq)(nil), "knight_privilege.GetKnightPrivilegeInfoReq")
	proto.RegisterType((*GetKnightPrivilegeInfoResp)(nil), "knight_privilege.GetKnightPrivilegeInfoResp")
	proto.RegisterType((*KnightGroupDetialInfo)(nil), "knight_privilege.KnightGroupDetialInfo")
	proto.RegisterType((*GetKnightGroupDetialInfoReq)(nil), "knight_privilege.GetKnightGroupDetialInfoReq")
	proto.RegisterType((*GetKnightGroupDetialInfoResp)(nil), "knight_privilege.GetKnightGroupDetialInfoResp")
	proto.RegisterType((*GetKnightGroupMemberWithNameplateReq)(nil), "knight_privilege.GetKnightGroupMemberWithNameplateReq")
	proto.RegisterType((*GetKnightGroupMemberWithNameplateResp)(nil), "knight_privilege.GetKnightGroupMemberWithNameplateResp")
	proto.RegisterEnum("knight_privilege.KnightStatusType", KnightStatusType_name, KnightStatusType_value)
	proto.RegisterEnum("knight_privilege.RequestType", RequestType_name, RequestType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// KnightPrivilegeClient is the client API for KnightPrivilege service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type KnightPrivilegeClient interface {
	GetKnightPrivilegeInfo(ctx context.Context, in *GetKnightPrivilegeInfoReq, opts ...grpc.CallOption) (*GetKnightPrivilegeInfoResp, error)
	GetKnightGroupDetialInfo(ctx context.Context, in *GetKnightGroupDetialInfoReq, opts ...grpc.CallOption) (*GetKnightGroupDetialInfoResp, error)
	GetKnightGroupMemberWithNameplate(ctx context.Context, in *GetKnightGroupMemberWithNameplateReq, opts ...grpc.CallOption) (*GetKnightGroupMemberWithNameplateResp, error)
}

type knightPrivilegeClient struct {
	cc *grpc.ClientConn
}

func NewKnightPrivilegeClient(cc *grpc.ClientConn) KnightPrivilegeClient {
	return &knightPrivilegeClient{cc}
}

func (c *knightPrivilegeClient) GetKnightPrivilegeInfo(ctx context.Context, in *GetKnightPrivilegeInfoReq, opts ...grpc.CallOption) (*GetKnightPrivilegeInfoResp, error) {
	out := new(GetKnightPrivilegeInfoResp)
	err := c.cc.Invoke(ctx, "/knight_privilege.KnightPrivilege/GetKnightPrivilegeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightPrivilegeClient) GetKnightGroupDetialInfo(ctx context.Context, in *GetKnightGroupDetialInfoReq, opts ...grpc.CallOption) (*GetKnightGroupDetialInfoResp, error) {
	out := new(GetKnightGroupDetialInfoResp)
	err := c.cc.Invoke(ctx, "/knight_privilege.KnightPrivilege/GetKnightGroupDetialInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knightPrivilegeClient) GetKnightGroupMemberWithNameplate(ctx context.Context, in *GetKnightGroupMemberWithNameplateReq, opts ...grpc.CallOption) (*GetKnightGroupMemberWithNameplateResp, error) {
	out := new(GetKnightGroupMemberWithNameplateResp)
	err := c.cc.Invoke(ctx, "/knight_privilege.KnightPrivilege/GetKnightGroupMemberWithNameplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KnightPrivilegeServer is the server API for KnightPrivilege service.
type KnightPrivilegeServer interface {
	GetKnightPrivilegeInfo(context.Context, *GetKnightPrivilegeInfoReq) (*GetKnightPrivilegeInfoResp, error)
	GetKnightGroupDetialInfo(context.Context, *GetKnightGroupDetialInfoReq) (*GetKnightGroupDetialInfoResp, error)
	GetKnightGroupMemberWithNameplate(context.Context, *GetKnightGroupMemberWithNameplateReq) (*GetKnightGroupMemberWithNameplateResp, error)
}

func RegisterKnightPrivilegeServer(s *grpc.Server, srv KnightPrivilegeServer) {
	s.RegisterService(&_KnightPrivilege_serviceDesc, srv)
}

func _KnightPrivilege_GetKnightPrivilegeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnightPrivilegeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightPrivilegeServer).GetKnightPrivilegeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knight_privilege.KnightPrivilege/GetKnightPrivilegeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightPrivilegeServer).GetKnightPrivilegeInfo(ctx, req.(*GetKnightPrivilegeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightPrivilege_GetKnightGroupDetialInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnightGroupDetialInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightPrivilegeServer).GetKnightGroupDetialInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knight_privilege.KnightPrivilege/GetKnightGroupDetialInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightPrivilegeServer).GetKnightGroupDetialInfo(ctx, req.(*GetKnightGroupDetialInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnightPrivilege_GetKnightGroupMemberWithNameplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnightGroupMemberWithNameplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnightPrivilegeServer).GetKnightGroupMemberWithNameplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/knight_privilege.KnightPrivilege/GetKnightGroupMemberWithNameplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnightPrivilegeServer).GetKnightGroupMemberWithNameplate(ctx, req.(*GetKnightGroupMemberWithNameplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _KnightPrivilege_serviceDesc = grpc.ServiceDesc{
	ServiceName: "knight_privilege.KnightPrivilege",
	HandlerType: (*KnightPrivilegeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetKnightPrivilegeInfo",
			Handler:    _KnightPrivilege_GetKnightPrivilegeInfo_Handler,
		},
		{
			MethodName: "GetKnightGroupDetialInfo",
			Handler:    _KnightPrivilege_GetKnightGroupDetialInfo_Handler,
		},
		{
			MethodName: "GetKnightGroupMemberWithNameplate",
			Handler:    _KnightPrivilege_GetKnightGroupMemberWithNameplate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "knight-privilege.proto",
}

func init() {
	proto.RegisterFile("knight-privilege.proto", fileDescriptor_knight_privilege_d33b6a10d6e1018b)
}

var fileDescriptor_knight_privilege_d33b6a10d6e1018b = []byte{
	// 758 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x55, 0xdd, 0x4e, 0xdb, 0x48,
	0x14, 0xc6, 0x09, 0x24, 0xe4, 0x84, 0x08, 0xef, 0xec, 0x92, 0x35, 0x81, 0x88, 0xe0, 0x65, 0xb5,
	0x88, 0x85, 0x20, 0x85, 0x65, 0xe9, 0x6d, 0x48, 0x02, 0x44, 0xe4, 0x07, 0x39, 0x44, 0x95, 0x7a,
	0xe3, 0x1a, 0x7b, 0xe2, 0x8c, 0xea, 0xd8, 0xc6, 0x33, 0x46, 0x42, 0xed, 0x45, 0x9f, 0xa2, 0x97,
	0x7d, 0xc4, 0xbe, 0x41, 0xa5, 0xca, 0x63, 0x3b, 0x90, 0x90, 0x00, 0xed, 0xe5, 0x7c, 0xe7, 0xe7,
	0x3b, 0x73, 0xce, 0x77, 0x66, 0x20, 0xff, 0xc1, 0x26, 0xe6, 0x90, 0x1d, 0xb8, 0x1e, 0xb9, 0x23,
	0x16, 0x36, 0x71, 0xd9, 0xf5, 0x1c, 0xe6, 0x20, 0x31, 0xc4, 0xd5, 0x31, 0x2e, 0x7f, 0x82, 0xf5,
	0x73, 0xcc, 0x2e, 0x39, 0x7c, 0x15, 0xa3, 0x4d, 0x7b, 0xe0, 0x28, 0xf8, 0x16, 0x15, 0x01, 0xa2,
	0x00, 0x9f, 0x18, 0x92, 0x50, 0x12, 0x76, 0x73, 0x4a, 0x26, 0x44, 0xfa, 0xc4, 0x08, 0xcc, 0xfa,
	0x50, 0xb3, 0x6d, 0x6c, 0xa9, 0xc4, 0x90, 0x12, 0xa1, 0x39, 0x42, 0x9a, 0x06, 0xda, 0x86, 0x15,
	0x0f, 0xdf, 0xfa, 0x98, 0x32, 0x95, 0xdd, 0xbb, 0x58, 0x4a, 0x72, 0x87, 0x6c, 0x84, 0x5d, 0xdf,
	0xbb, 0x58, 0xfe, 0x96, 0x80, 0xc2, 0x3c, 0x7a, 0xea, 0xa2, 0x3c, 0xa4, 0x28, 0xd3, 0x98, 0x4f,
	0x23, 0xee, 0xe8, 0x84, 0xf6, 0x01, 0x51, 0x1d, 0x63, 0x5b, 0xf5, 0x30, 0x75, 0x7c, 0x4f, 0xc7,
	0xaa, 0xef, 0x59, 0xbc, 0x80, 0x8c, 0x22, 0x72, 0x8b, 0x12, 0x19, 0xfa, 0x9e, 0x85, 0x8e, 0x20,
	0xff, 0xd4, 0x5b, 0x1d, 0x19, 0xc7, 0xbc, 0xa2, 0x8c, 0xf2, 0xfb, 0x74, 0x44, 0xdb, 0x38, 0x46,
	0x55, 0x28, 0x46, 0x57, 0xb7, 0xb5, 0x11, 0x76, 0x2d, 0x8d, 0xe1, 0x49, 0xb6, 0x45, 0x1e, 0x5b,
	0x08, 0x9d, 0x3a, 0xb1, 0xcf, 0x63, 0xde, 0x13, 0x90, 0xa2, 0x14, 0xba, 0xe6, 0x19, 0x93, 0xd1,
	0x4b, 0x3c, 0x7a, 0x2d, 0xb4, 0xd7, 0x34, 0xcf, 0x78, 0x1c, 0xb8, 0x05, 0x59, 0xcd, 0xd6, 0x87,
	0x8e, 0x17, 0xb4, 0x9d, 0x4a, 0xa9, 0x52, 0x72, 0x37, 0xa7, 0x40, 0x08, 0xf5, 0x89, 0x41, 0xd1,
	0x1b, 0x58, 0x8f, 0x33, 0x47, 0xfd, 0x1f, 0x51, 0x53, 0xbd, 0x31, 0x79, 0xea, 0xf4, 0x44, 0xea,
	0xd0, 0xde, 0xa6, 0xe6, 0xa9, 0xd9, 0xf7, 0x2c, 0xf9, 0x3d, 0xac, 0x85, 0xcd, 0x3e, 0xf7, 0x1c,
	0xdf, 0xad, 0x63, 0x46, 0x34, 0x2b, 0x68, 0x37, 0xfa, 0x13, 0xd2, 0x2e, 0xd1, 0x79, 0x02, 0x81,
	0x27, 0x48, 0xb9, 0x44, 0x0f, 0x8a, 0xf9, 0x03, 0x96, 0x18, 0x61, 0x16, 0x8e, 0xda, 0x1b, 0x1e,
	0x90, 0x04, 0x69, 0xdd, 0xb1, 0x19, 0xb6, 0x59, 0xd4, 0xc4, 0xf8, 0x28, 0x17, 0x61, 0x63, 0x3c,
	0xd1, 0x29, 0x12, 0x05, 0xdf, 0xca, 0x06, 0x6c, 0xce, 0x37, 0x53, 0x17, 0xd5, 0x21, 0x43, 0xec,
	0x81, 0xa3, 0x5a, 0x84, 0x32, 0x49, 0x28, 0x25, 0x77, 0xb3, 0x95, 0x7f, 0xca, 0xd3, 0xaa, 0x2d,
	0xcf, 0x8e, 0x5f, 0x0e, 0x22, 0x5b, 0x84, 0x32, 0xd9, 0x80, 0x9d, 0x49, 0x96, 0x36, 0x1e, 0xdd,
	0x60, 0xef, 0x2d, 0x61, 0xc3, 0x47, 0xa3, 0xe2, 0x02, 0x7f, 0xe8, 0x74, 0x2c, 0xf0, 0x71, 0xa3,
	0x5f, 0x10, 0xb8, 0xfc, 0x5d, 0x80, 0xbf, 0x5f, 0x41, 0x43, 0x5d, 0xb4, 0x0e, 0xcb, 0x3e, 0x31,
	0x1e, 0x2e, 0x95, 0x53, 0xd2, 0x3e, 0x31, 0x82, 0x52, 0xd1, 0x06, 0x64, 0xf4, 0x21, 0xc1, 0x03,
	0x5e, 0x41, 0x48, 0xb1, 0xcc, 0x81, 0xa0, 0x80, 0x0e, 0xec, 0xc4, 0x83, 0x76, 0x46, 0x23, 0xc7,
	0x9e, 0x27, 0xc6, 0x70, 0x06, 0xa5, 0x68, 0xe6, 0xdc, 0x75, 0xa6, 0x24, 0x5b, 0xf0, 0xd7, 0x58,
	0x38, 0x01, 0xe7, 0xb3, 0xda, 0xde, 0x8a, 0x25, 0x44, 0xf0, 0x60, 0x56, 0xb6, 0xbd, 0x33, 0x10,
	0xc3, 0xbb, 0xf7, 0xf8, 0x5a, 0x06, 0x1b, 0x8d, 0x72, 0x90, 0xb9, 0xec, 0x34, 0xcf, 0x2f, 0xae,
	0xd5, 0x4e, 0x57, 0x5c, 0x40, 0xbf, 0x41, 0x2e, 0x3a, 0xd6, 0xba, 0xed, 0x76, 0xb7, 0x23, 0x0a,
	0x48, 0x84, 0x95, 0x18, 0xba, 0x68, 0x36, 0xce, 0xc4, 0xc4, 0xde, 0x67, 0x01, 0xb2, 0xca, 0xc3,
	0xab, 0x80, 0xd2, 0x90, 0xac, 0xb6, 0x5a, 0xe2, 0x02, 0xca, 0x03, 0xea, 0xd5, 0x1a, 0x8d, 0x8e,
	0xaa, 0x34, 0x7a, 0xdd, 0xbe, 0x52, 0x6b, 0xa8, 0x7d, 0xa5, 0x25, 0x0a, 0x68, 0x1b, 0x8a, 0x31,
	0x49, 0xb5, 0xdd, 0xb8, 0x6a, 0x55, 0xaf, 0x1b, 0x93, 0x2e, 0x09, 0xb4, 0x09, 0x52, 0xcc, 0x52,
	0x55, 0xea, 0x93, 0xd6, 0x24, 0x5a, 0x85, 0x6c, 0xb5, 0x53, 0xbb, 0xe8, 0x2a, 0x6a, 0xbf, 0x59,
	0xef, 0x89, 0x8b, 0x95, 0xaf, 0x49, 0x58, 0x9d, 0x7a, 0x85, 0x90, 0x0f, 0xf9, 0xd9, 0x6f, 0x13,
	0xfa, 0xf7, 0xa9, 0x22, 0xe7, 0x3e, 0xa2, 0x85, 0xfd, 0xd7, 0x3b, 0x53, 0x57, 0x5e, 0x40, 0x1f,
	0x41, 0x9a, 0xb7, 0x21, 0xe8, 0xe0, 0x99, 0x5c, 0x4f, 0x97, 0xad, 0x50, 0xfe, 0x19, 0x77, 0x4e,
	0xfe, 0x45, 0x80, 0xed, 0x17, 0x25, 0x8d, 0xfe, 0x7f, 0x29, 0xef, 0xec, 0x75, 0x2b, 0x9c, 0xfc,
	0x52, 0x5c, 0x50, 0xd8, 0xe9, 0x7f, 0xef, 0x2a, 0xa6, 0x63, 0x69, 0xb6, 0x59, 0x3e, 0xae, 0x30,
	0x56, 0xd6, 0x9d, 0xd1, 0x21, 0xff, 0xd2, 0x74, 0xc7, 0x3a, 0xa4, 0xd8, 0xbb, 0x23, 0x3a, 0xa6,
	0x87, 0xd3, 0xbf, 0xde, 0x4d, 0x8a, 0xfb, 0x1c, 0xfd, 0x08, 0x00, 0x00, 0xff, 0xff, 0xc1, 0x53,
	0x44, 0xd1, 0x10, 0x07, 0x00, 0x00,
}
