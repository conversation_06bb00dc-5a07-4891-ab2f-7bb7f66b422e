// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/esport-customer (interfaces: CustomerServiceClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_customer "golang.52tt.com/protocol/services/esport-customer"
	grpc "google.golang.org/grpc"
)

// MockCustomerServiceClient is a mock of CustomerServiceClient interface.
type MockCustomerServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockCustomerServiceClientMockRecorder
}

// MockCustomerServiceClientMockRecorder is the mock recorder for MockCustomerServiceClient.
type MockCustomerServiceClientMockRecorder struct {
	mock *MockCustomerServiceClient
}

// NewMockCustomerServiceClient creates a new mock instance.
func NewMockCustomerServiceClient(ctrl *gomock.Controller) *MockCustomerServiceClient {
	mock := &MockCustomerServiceClient{ctrl: ctrl}
	mock.recorder = &MockCustomerServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCustomerServiceClient) EXPECT() *MockCustomerServiceClientMockRecorder {
	return m.recorder
}

// AddManagedGod mocks base method.
func (m *MockCustomerServiceClient) AddManagedGod(arg0 context.Context, arg1 *esport_customer.AddManagedGodRequest, arg2 ...grpc.CallOption) (*esport_customer.AddManagedGodResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddManagedGod", varargs...)
	ret0, _ := ret[0].(*esport_customer.AddManagedGodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddManagedGod indicates an expected call of AddManagedGod.
func (mr *MockCustomerServiceClientMockRecorder) AddManagedGod(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddManagedGod", reflect.TypeOf((*MockCustomerServiceClient)(nil).AddManagedGod), varargs...)
}

// BanCustomerAccount mocks base method.
func (m *MockCustomerServiceClient) BanCustomerAccount(arg0 context.Context, arg1 *esport_customer.BanCustomerAccountRequest, arg2 ...grpc.CallOption) (*esport_customer.BanCustomerAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BanCustomerAccount", varargs...)
	ret0, _ := ret[0].(*esport_customer.BanCustomerAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BanCustomerAccount indicates an expected call of BanCustomerAccount.
func (mr *MockCustomerServiceClientMockRecorder) BanCustomerAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanCustomerAccount", reflect.TypeOf((*MockCustomerServiceClient)(nil).BanCustomerAccount), varargs...)
}

// BatGetHostingStatus mocks base method.
func (m *MockCustomerServiceClient) BatGetHostingStatus(arg0 context.Context, arg1 *esport_customer.BatGetHostingStatusRequest, arg2 ...grpc.CallOption) (*esport_customer.BatGetHostingStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatGetHostingStatus", varargs...)
	ret0, _ := ret[0].(*esport_customer.BatGetHostingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatGetHostingStatus indicates an expected call of BatGetHostingStatus.
func (mr *MockCustomerServiceClientMockRecorder) BatGetHostingStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatGetHostingStatus", reflect.TypeOf((*MockCustomerServiceClient)(nil).BatGetHostingStatus), varargs...)
}

// BatchCheckGuildHasCustomer mocks base method.
func (m *MockCustomerServiceClient) BatchCheckGuildHasCustomer(arg0 context.Context, arg1 *esport_customer.BatchCheckGuildHasCustomerRequest, arg2 ...grpc.CallOption) (*esport_customer.BatchCheckGuildHasCustomerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchCheckGuildHasCustomer", varargs...)
	ret0, _ := ret[0].(*esport_customer.BatchCheckGuildHasCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckGuildHasCustomer indicates an expected call of BatchCheckGuildHasCustomer.
func (mr *MockCustomerServiceClientMockRecorder) BatchCheckGuildHasCustomer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckGuildHasCustomer", reflect.TypeOf((*MockCustomerServiceClient)(nil).BatchCheckGuildHasCustomer), varargs...)
}

// BatchGetCustomerByUids mocks base method.
func (m *MockCustomerServiceClient) BatchGetCustomerByUids(arg0 context.Context, arg1 *esport_customer.BatchGetCustomerByUidsRequest, arg2 ...grpc.CallOption) (*esport_customer.BatchGetCustomerByUidsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetCustomerByUids", varargs...)
	ret0, _ := ret[0].(*esport_customer.BatchGetCustomerByUidsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCustomerByUids indicates an expected call of BatchGetCustomerByUids.
func (mr *MockCustomerServiceClientMockRecorder) BatchGetCustomerByUids(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCustomerByUids", reflect.TypeOf((*MockCustomerServiceClient)(nil).BatchGetCustomerByUids), varargs...)
}

// DisableCustomerHosting mocks base method.
func (m *MockCustomerServiceClient) DisableCustomerHosting(arg0 context.Context, arg1 *esport_customer.DisableCustomerHostingRequest, arg2 ...grpc.CallOption) (*esport_customer.DisableCustomerHostingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DisableCustomerHosting", varargs...)
	ret0, _ := ret[0].(*esport_customer.DisableCustomerHostingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DisableCustomerHosting indicates an expected call of DisableCustomerHosting.
func (mr *MockCustomerServiceClientMockRecorder) DisableCustomerHosting(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableCustomerHosting", reflect.TypeOf((*MockCustomerServiceClient)(nil).DisableCustomerHosting), varargs...)
}

// EnableCustomerHosting mocks base method.
func (m *MockCustomerServiceClient) EnableCustomerHosting(arg0 context.Context, arg1 *esport_customer.EnableCustomerHostingRequest, arg2 ...grpc.CallOption) (*esport_customer.EnableCustomerHostingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EnableCustomerHosting", varargs...)
	ret0, _ := ret[0].(*esport_customer.EnableCustomerHostingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EnableCustomerHosting indicates an expected call of EnableCustomerHosting.
func (mr *MockCustomerServiceClientMockRecorder) EnableCustomerHosting(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EnableCustomerHosting", reflect.TypeOf((*MockCustomerServiceClient)(nil).EnableCustomerHosting), varargs...)
}

// GetCustomerAccountDetails mocks base method.
func (m *MockCustomerServiceClient) GetCustomerAccountDetails(arg0 context.Context, arg1 *esport_customer.GetCustomerAccountDetailsRequest, arg2 ...grpc.CallOption) (*esport_customer.GetCustomerAccountDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCustomerAccountDetails", varargs...)
	ret0, _ := ret[0].(*esport_customer.GetCustomerAccountDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerAccountDetails indicates an expected call of GetCustomerAccountDetails.
func (mr *MockCustomerServiceClientMockRecorder) GetCustomerAccountDetails(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerAccountDetails", reflect.TypeOf((*MockCustomerServiceClient)(nil).GetCustomerAccountDetails), varargs...)
}

// GetCustomerAccounts mocks base method.
func (m *MockCustomerServiceClient) GetCustomerAccounts(arg0 context.Context, arg1 *esport_customer.GetCustomerAccountsRequest, arg2 ...grpc.CallOption) (*esport_customer.GetCustomerAccountsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCustomerAccounts", varargs...)
	ret0, _ := ret[0].(*esport_customer.GetCustomerAccountsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerAccounts indicates an expected call of GetCustomerAccounts.
func (mr *MockCustomerServiceClientMockRecorder) GetCustomerAccounts(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerAccounts", reflect.TypeOf((*MockCustomerServiceClient)(nil).GetCustomerAccounts), varargs...)
}

// GetCustomerByCoachUid mocks base method.
func (m *MockCustomerServiceClient) GetCustomerByCoachUid(arg0 context.Context, arg1 *esport_customer.GetCustomerByCoachUidRequest, arg2 ...grpc.CallOption) (*esport_customer.GetCustomerByCoachUidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCustomerByCoachUid", varargs...)
	ret0, _ := ret[0].(*esport_customer.GetCustomerByCoachUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerByCoachUid indicates an expected call of GetCustomerByCoachUid.
func (mr *MockCustomerServiceClientMockRecorder) GetCustomerByCoachUid(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerByCoachUid", reflect.TypeOf((*MockCustomerServiceClient)(nil).GetCustomerByCoachUid), varargs...)
}

// GetCustomersByGuildId mocks base method.
func (m *MockCustomerServiceClient) GetCustomersByGuildId(arg0 context.Context, arg1 *esport_customer.GetCustomerByGuildIdRequest, arg2 ...grpc.CallOption) (*esport_customer.GetCustomerByGuildIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCustomersByGuildId", varargs...)
	ret0, _ := ret[0].(*esport_customer.GetCustomerByGuildIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomersByGuildId indicates an expected call of GetCustomersByGuildId.
func (mr *MockCustomerServiceClientMockRecorder) GetCustomersByGuildId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomersByGuildId", reflect.TypeOf((*MockCustomerServiceClient)(nil).GetCustomersByGuildId), varargs...)
}

// GetHostingStatus mocks base method.
func (m *MockCustomerServiceClient) GetHostingStatus(arg0 context.Context, arg1 *esport_customer.GetHostingStatusRequest, arg2 ...grpc.CallOption) (*esport_customer.GetHostingStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHostingStatus", varargs...)
	ret0, _ := ret[0].(*esport_customer.GetHostingStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHostingStatus indicates an expected call of GetHostingStatus.
func (mr *MockCustomerServiceClientMockRecorder) GetHostingStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHostingStatus", reflect.TypeOf((*MockCustomerServiceClient)(nil).GetHostingStatus), varargs...)
}

// GetManagedGods mocks base method.
func (m *MockCustomerServiceClient) GetManagedGods(arg0 context.Context, arg1 *esport_customer.GetManagedGodsRequest, arg2 ...grpc.CallOption) (*esport_customer.GetManagedGodsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetManagedGods", varargs...)
	ret0, _ := ret[0].(*esport_customer.GetManagedGodsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManagedGods indicates an expected call of GetManagedGods.
func (mr *MockCustomerServiceClientMockRecorder) GetManagedGods(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManagedGods", reflect.TypeOf((*MockCustomerServiceClient)(nil).GetManagedGods), varargs...)
}

// RemoveManagedGod mocks base method.
func (m *MockCustomerServiceClient) RemoveManagedGod(arg0 context.Context, arg1 *esport_customer.RemoveManagedGodRequest, arg2 ...grpc.CallOption) (*esport_customer.RemoveManagedGodResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RemoveManagedGod", varargs...)
	ret0, _ := ret[0].(*esport_customer.RemoveManagedGodResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveManagedGod indicates an expected call of RemoveManagedGod.
func (mr *MockCustomerServiceClientMockRecorder) RemoveManagedGod(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveManagedGod", reflect.TypeOf((*MockCustomerServiceClient)(nil).RemoveManagedGod), varargs...)
}

// SaveCustomerAccount mocks base method.
func (m *MockCustomerServiceClient) SaveCustomerAccount(arg0 context.Context, arg1 *esport_customer.SaveCustomerAccountRequest, arg2 ...grpc.CallOption) (*esport_customer.SaveCustomerAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveCustomerAccount", varargs...)
	ret0, _ := ret[0].(*esport_customer.SaveCustomerAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveCustomerAccount indicates an expected call of SaveCustomerAccount.
func (mr *MockCustomerServiceClientMockRecorder) SaveCustomerAccount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveCustomerAccount", reflect.TypeOf((*MockCustomerServiceClient)(nil).SaveCustomerAccount), varargs...)
}
