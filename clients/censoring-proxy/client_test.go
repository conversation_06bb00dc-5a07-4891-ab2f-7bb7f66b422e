package censoring_proxy

import (
	"context"
	"os"
	"strconv"
	"testing"
	"time"

	"google.golang.org/grpc/grpclog"

	censoringProxy "golang.52tt.com/protocol/services/censoring-proxy"
)

func init() {
	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))
}

func TestClient_ScanAudioStream(t *testing.T) {
	c := NewClient()
	defer c.Close()

	const (
		uid       = 500001
		channelID = "26165256-xiaxiaobai"
		streamID  = "i-500009-1564128119620"
		tokenID   = "3NxIecSxMUbaz19TZzD6eTgNjaCvi1CtroH+z94+cFXLqNwChQHw/hvjn3SOuPI0N9QkFl+WZCIg0wRtyFHIXNDYrc1iw0vBaHFYSGWgpoGDNd5rAmc6rxPlmQQv4PtEjeRCB+PVtMIEYSUPYzs3CdT2qKAqocensoringProxyLznLqqOQj4XRFmF2lIbJs+skZDT4C/XGmuDBOFBV08UUS6bHG9dbSx3F3NZi/FLinAN+m74RNM2S/FexOSon8FGWhBbkZIKIK9PCQIvV0kEKkc7+Y5frDe3Cgq2bjdCeiPeg2hsEd5+NzUxXC9Z358KXcrK0RMjTd3uopnlTlH0VQzGUb/P+Xy0N47n8s2vuN5gGdZlz07p0="
		testEnv   = false
	)

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()

	resp, err := c.Audio().AsyncScanAudio(ctx, &censoringProxy.ScanAudioReq{
		Context: &censoringProxy.TaskContext{
			AppId:    AppId,
			UserInfo: &censoringProxy.User{Id: uid},
			Category: "AS_ENT_ROOM",
			Source: &censoringProxy.SourceInfo{
				Id: channelID,
			},
		},
		AudioData: &censoringProxy.AudioData{
			Metadata: &censoringProxy.Metadata{
				DataId: streamID,
				Name:   "Thanos",
			},
			AudioData: &censoringProxy.AudioData_AudioStream{
				AudioStream: &censoringProxy.AudioStream{
					StreamType: &censoringProxy.AudioStream_ZegoStream{
						ZegoStream: &censoringProxy.ZegoStream{
							TestEnv:  testEnv,
							StreamId: streamID,
							TokenId:  tokenID,
						},
					},
				},
			},
		},
		Callback: &censoringProxy.Callback{
			Callback: &censoringProxy.Callback_KafkaCallback_{
				KafkaCallback: &censoringProxy.Callback_KafkaCallback{},
			},
			Params: map[string]string{
				"uid":        strconv.FormatUint(uint64(uid), 10),
				"stream_id":  streamID,
				"channel_id": channelID,
			},
		},
	})

	if err != nil {
		t.Fatalf("scan failed: %s", err)
	}

	t.Logf("cancel scan %q after 5 seconds", resp.GetTaskId())
	<-time.After(time.Second * 5)

	_, err = c.Audio().CancelScanTask(ctx, &censoringProxy.CancelScanTaskReq{
		TaskId: resp.GetTaskId(),
	})

	if err != nil {
		t.Fatalf("cancel failed: %s", err)
	}

}
