// Code generated by quicksilver-cli. DO NOT EDIT.
package backpacksender

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "context"
	pb "golang.52tt.com/protocol/services/backpacksender"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	AddBusiness(ctx context.Context, req *pb.AddBusinessReq) (*pb.AddBusinessResp,protocol.ServerError)
	AddBusinessRiskControlConf(ctx context.Context, req *pb.AddBusinessRiskControlConfReq) (*pb.AddBusinessRiskControlConfResp,protocol.ServerError)
	DeductUserBackpackWithRiskControl(ctx context.Context, req *pb.DeductUserBackpackWithRiskControlReq) (*pb.DeductUserBackpackWithRiskControlResp,protocol.ServerError)
	GetAllBusiness(ctx context.Context, req *pb.GetAllBusinessReq) (*pb.GetAllBusinessResp,protocol.ServerError)
	GetBusinessRiskControlConf(ctx context.Context, req *pb.GetBusinessRiskControlConfReq) (*pb.GetBusinessRiskControlConfResp,protocol.ServerError)
	ModBusinessRiskControlConf(ctx context.Context, req *pb.ModBusinessRiskControlConfReq) (*pb.ModBusinessRiskControlConfResp,protocol.ServerError)
	PreCheckBusinessRiskControl(ctx context.Context, req *pb.PreCheckBussinessRiskControlReq) (*pb.PreCheckBussinessRiskControlResp,protocol.ServerError)
	SendBackpackWithRiskControl(ctx context.Context, req *pb.SendBackpackWithRiskControlReq) (*pb.SendBackpackWithRiskControlResp,protocol.ServerError)
	SendOneBackpackWithRiskControl(ctx context.Context, req *pb.SendOneBackpackWithRiskControlReq) (*pb.SendOneBackpackWithRiskControlResp,protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
