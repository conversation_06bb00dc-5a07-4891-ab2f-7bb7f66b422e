package accountlogic

import (
	"fmt"
	"google.golang.org/grpc/encoding/gzip"
	"io"
	"os"
	"testing"
	"time"

	"github.com/opentracing/opentracing-go"
	"github.com/uber/jaeger-client-go/config"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/grpclog"
)

var (
	tracer opentracing.Tracer
	closer io.Closer
)

func init() {

	grpclog.SetLoggerV2(grpclog.NewLoggerV2(os.Stdout, os.Stdout, os.Stdout))

	tracer, closer, _ = (&config.Configuration{
		Sampler: &config.SamplerConfig{
			Type:  "const",
			Param: 1,
		},
		Reporter: &config.ReporterConfig{
			LogSpans:            false,
			BufferFlushInterval: 1 * time.Second,
		},
	}).New("accountlogic_client_test")
}

func TestClient_GetUserDetail(t *testing.T) {
	client, err := NewTracedClient(tracer, grpc.WithBlock())
	if err != nil {
		panic(err)
	}

	resp, err := client.GetUserDetail(context.Background(), **********, grpc.UseCompressor(gzip.Name))
	if err != nil {
		fmt.Printf("err:%+v\n", err)
		t.Error(err)
	}
	fmt.Printf("resp:%v\n", resp)
}

func TestClient_CloseTracer(t *testing.T) {
	closer.Close()
}
