package banserver

import (
	"context"

	"google.golang.org/grpc"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"

	pb "golang.52tt.com/protocol/services/banserver"
)

const (
	serviceName = "banserver"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewBanServerClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.BanServerClient { return c.Stub().(pb.BanServerClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

// GetBannedCheckRecord
func (c *Client) GetBannedCheckRecord(ctx context.Context, req *pb.GetBannedCheckRecordReq) (*pb.GetBannedCheckRecordResp, error) {
	resp, err := c.typedStub().GetBannedCheckRecord(ctx, req)
	return resp, protocol.ToServerError(err)
}

// UpdateBannedCheckRecord
func (c *Client) UpdateBannedCheckRecord(ctx context.Context, req *pb.UpdateBannedCheckRecordReq) (*pb.UpdateBannedCheckRecordResp, error) {
	resp, err := c.typedStub().UpdateBannedCheckRecord(ctx, req)
	return resp, protocol.ToServerError(err)
}

// AddBannedCheckRecord
func (c *Client) AddBannedCheckRecord(ctx context.Context, req *pb.AddBannedCheckRecordReq) (*pb.AddBannedCheckRecordResp, error) {
	resp, err := c.typedStub().AddBannedCheckRecord(ctx, req)
	return resp, protocol.ToServerError(err)
}

// GetBannedCheckOperator
func (c *Client) GetBannedCheckOperator(ctx context.Context, req *pb.GetBannedCheckOperatorReq) (*pb.GetBannedCheckOperatorResp, error) {
	resp, err := c.typedStub().GetBannedCheckOperator(ctx, req)
	return resp, protocol.ToServerError(err)
}

// SetBannedCheckOperator
func (c *Client) SetBannedCheckOperator(ctx context.Context, req *pb.SetBannedCheckOperatorReq) (*pb.SetBannedCheckOperatorResp, error) {
	resp, err := c.typedStub().SetBannedCheckOperator(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SubmitBannedAppeal(ctx context.Context, uid, reasonCode uint32) (bool, error) {
	req := &pb.SubmitBannedAppealReq{
		Uid:        uid,
		ReasonCode: reasonCode,
	}
	resp, err := c.typedStub().SubmitBannedAppeal(ctx, req)
	if err != nil {
		return false, protocol.ToServerError(err)
	}
	return resp.GetExisted(), nil
}

func (c *Client) RemoveBannedAppeal(ctx context.Context, uid, reasonCode uint32) error {
	req := &pb.RemoveBannedAppealReq{
		Uid:        uid,
		ReasonCode: reasonCode,
	}
	_, err := c.typedStub().RemoveBannedAppeal(ctx, req)
	return protocol.ToServerError(err)
}
