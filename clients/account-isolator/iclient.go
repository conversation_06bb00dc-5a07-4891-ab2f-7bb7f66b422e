package account_isolator

import (
	"context"

	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"

	pb "golang.52tt.com/protocol/services/account-isolator"
)

type IClient interface {
	client.BaseClient
	GetIsolationSequence(ctx context.Context, req *pb.GetIsolationSequenceReq, opts ...grpc.CallOption) (*pb.GetIsolationSequenceResp, error)
	TryLockUidIsolation(ctx context.Context, req *pb.TryLockUidIsolationReq, opts ...grpc.CallOption) (*pb.TryLockUidIsolationResp, error)
	CheckUidIsolation(ctx context.Context, req *pb.CheckUidIsolationReq, opts ...grpc.CallOption) (*pb.CheckUidIsolationResp, error)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
