// Code generated by quicksilver-cli. DO NOT EDIT.
package usual_device

import(
	"golang.52tt.com/pkg/client"
	"google.golang.org/grpc"
	context "golang.org/x/net/context"
	pb "golang.52tt.com/protocol/services/usual-device-svr"
	protocol "golang.52tt.com/pkg/protocol"
)

type IClient interface {
	client.BaseClient
	CheckMessage(ctx context.Context, phone, content string, checkType uint32) (*pb.CheckMessageResp,protocol.ServerError)
	CheckUsualDevice(ctx context.Context, deviceID string, uid, checkType, deviceType uint32) (*pb.CheckUsualDeviceResp,protocol.ServerError)
	CheckVerifyMessage(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.CheckVerifyMessageResp,protocol.ServerError)
	GetDeviceAuthError(ctx context.Context, uid uint64, clientType uint16, clientVersion uint32) protocol.ServerError
	GetMsgContent(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.GetMsgContentResp,protocol.ServerError)
	GetRandString(ctx context.Context, phone string, checkType uint32) (*pb.GetRandStringResp,protocol.ServerError)
	GetUserUsualDevice(ctx context.Context, uid uint64, start, end int64, limit int32) (*GetUserUsualDeviceResp,protocol.ServerError)
	InConsumeVerifyUidList(ctx context.Context, uid uint64) (bool,protocol.ServerError)
	RecordCheckResult(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.RecordCheckResultResp,protocol.ServerError)
	RecordDeviceCheck(ctx context.Context, deviceID []byte, uid, checkType uint32) (*pb.RecordDeviceCheckResp,protocol.ServerError)
	RecordMessageCheck(ctx context.Context, phone, content string, checkType uint32) protocol.ServerError
	RecordRandString(ctx context.Context, phone, rs string, checkType uint32) (*pb.RecordRandStringResp,protocol.ServerError)
	UpdateConsumeVerifyUidList(ctx context.Context, in *pb.UpdateConsumeVerifyUidListReq) (*pb.UpdateConsumeVerifyUidListResp,protocol.ServerError)
	UpdateUsualDevice(ctx context.Context, deviceID []byte, uid, checkType uint32) protocol.ServerError
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli := NewClient(dopts...)
	return cli
}
