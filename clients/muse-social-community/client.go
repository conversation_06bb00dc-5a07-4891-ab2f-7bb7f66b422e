package muse_social_community

import (
	"context"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/muse-social-community"

	"google.golang.org/grpc"
)

const (
	serviceName = "muse-social-community"
)

type Client struct {
	client.BaseClient
}

func (c *Client) typedStub() pb.MuseSocialCommunityClient {
	return c.Stub().(pb.MuseSocialCommunityClient)
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName, func(cc *grpc.ClientConn) interface{} {
			return pb.NewMuseSocialCommunityClient(cc)
		}, dopts...),
	}, nil
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

//func (c *Client) ListBrandChannelByBrandId(ctx context.Context, req *pb.ListBrandChannelByBrandIdReq) (*pb.ListBrandChannelByBrandIdResp, protocol.ServerError) {
//	resp, err := c.typedStub().ListBrandChannelByBrandId(ctx, req)
//	return resp, protocol.ToServerError(err)
//}

func IsKernelRole(role pb.BrandMemberRole) bool {
	return role == pb.BrandMemberRole_Brand_Role_Captain || role == pb.BrandMemberRole_Brand_Role_Kernel ||
		role == pb.BrandMemberRole_Brand_Role_Vice_Captain || role == pb.BrandMemberRole_Brand_Producer
}

func (c *Client) ListBrandMembersByUid(ctx context.Context, req *pb.ListBrandMembersByUidReq) (*pb.ListBrandMembersByUidResp, protocol.ServerError) {
	resp, err := c.typedStub().ListBrandMembersByUid(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchCategoriesByIds(ctx context.Context, categoryIds []string) (*pb.BatchCategoriesByIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchCategoriesByIds(ctx, &pb.BatchCategoriesByIdsReq{Ids: categoryIds})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchBrandTypes(ctx context.Context, req *pb.SearchBrandTypesReq) (*pb.SearchBrandTypesResp, protocol.ServerError) {
	resp, err := c.typedStub().SearchBrandTypes(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchBrands(ctx context.Context, req *pb.SearchBrandsReq) (*pb.SearchBrandsResp, protocol.ServerError) {
	resp, err := c.typedStub().SearchBrands(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchBrandTypesByIds(ctx context.Context, categoryIds []string) (*pb.BatchBrandTypesByIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchBrandTypesByIds(ctx, &pb.BatchBrandTypesByIdsReq{Ids: categoryIds})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchBrandChannelByChannelIds(ctx context.Context, req *pb.BatchBrandChannelByChannelIdsReq) (*pb.BatchBrandChannelByChannelIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchBrandChannelByChannelIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchBrandMembersByUids(ctx context.Context, req *pb.BatchBrandMembersByUidsReq) (*pb.BatchBrandMembersByUidsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchBrandMembersByUids(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchBrandsByIds(ctx context.Context, req *pb.BatchBrandsByIdsReq) (*pb.BatchBrandsByIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchBrandsByIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchBrandChannelsByBrandIds(ctx context.Context, req *pb.BatchBrandChannelsByBrandIdsReq) (*pb.BatchBrandChannelsByBrandIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchBrandChannelsByBrandIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) JoinSocialCommunityFans(ctx context.Context, req *pb.JoinSocialCommunityFansReq) (*pb.JoinSocialCommunityFansResp, protocol.ServerError) {
	resp, err := c.typedStub().JoinSocialCommunityFans(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityFansCount(ctx context.Context, req *pb.GetSocialCommunityFansCountReq) (*pb.GetSocialCommunityFansCountResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityFansCount(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchBrandBackgroundExtras(ctx context.Context, req *pb.BatchBrandBackgroundExtrasReq) (*pb.BatchBrandBackgroundExtrasResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchBrandBackgroundExtras(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityCaption(ctx context.Context, req *pb.GetSocialCommunityCaptionReq) (*pb.GetSocialCommunityCaptionResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityCaption(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchCategoriesByBrandIds(ctx context.Context, req *pb.BatchCategoriesByBrandIdsReq) (*pb.BatchCategoriesByBrandIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchCategoriesByBrandIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityDetailInfo(ctx context.Context, req *pb.GetSocialCommunityDetailInfoReq) (*pb.GetSocialCommunityDetailInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityDetailInfo(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchCategoryTypesByIds(ctx context.Context, req *pb.BatchCategoryTypesByIdsReq) (*pb.BatchCategoryTypesByIdsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchCategoryTypesByIds(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListMuseSocialCommunityNavBars(ctx context.Context, req *pb.ListMuseSocialCommunityNavBarsReq) (*pb.ListMuseSocialCommunityNavBarsResp, protocol.ServerError) {
	resp, err := c.typedStub().ListMuseSocialCommunityNavBars(ctx, req)
	return resp, protocol.ToServerError(err)
}
func (c *Client) ListMuseSocialCommunityNavSecondaryBars(ctx context.Context, req *pb.ListMuseSocialCommunityNavSecondaryBarsReq) (*pb.ListMuseSocialCommunityNavSecondaryBarsResp, protocol.ServerError) {
	resp, err := c.typedStub().ListMuseSocialCommunityNavSecondaryBars(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityProfilePages(ctx context.Context, req *pb.GetSocialCommunityProfilePagesReq) (*pb.GetSocialCommunityProfilePagesResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityProfilePages(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchBrandMembers(ctx context.Context, req *pb.SearchBrandMembersReq) (*pb.SearchBrandMembersResp, protocol.ServerError) {
	resp, err := c.typedStub().SearchBrandMembers(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityAllMembersCount(ctx context.Context,
	req *pb.GetSocialCommunityAllMembersCountReq) (*pb.GetSocialCommunityAllMembersCountResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityAllMembersCount(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityKernelCount(ctx context.Context,
	req *pb.GetSocialCommunityKernelCountReq) (*pb.GetSocialCommunityKernelCountResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityKernelCount(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpsertBrandMembers(ctx context.Context,
	req *pb.UpsertBrandMembersReq) (*pb.UpsertBrandMembersResp, protocol.ServerError) {
	resp, err := c.typedStub().UpsertBrandMembers(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchMemberWorkerOrders(ctx context.Context,
	req *pb.SearchMemberWorkerOrdersReq) (*pb.SearchMemberWorkerOrdersResp, protocol.ServerError) {
	resp, err := c.typedStub().SearchMemberWorkerOrders(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpsertMemberWorkerOrders(ctx context.Context,
	req *pb.UpsertMemberWorkerOrdersReq) (*pb.UpsertMemberWorkerOrdersResp, protocol.ServerError) {
	resp, err := c.typedStub().UpsertMemberWorkerOrders(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetOpenCategoryTypes(ctx context.Context,
	req *pb.BatchGetOpenCategoryTypesReq) (*pb.BatchGetOpenCategoryTypesResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetOpenCategoryTypes(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetCategoriesByCategoryTypeId(ctx context.Context,
	req *pb.BatchGetCategoriesByCategoryTypeIdReq) (*pb.BatchGetCategoriesByCategoryTypeIdResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetCategoriesByCategoryTypeId(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ApplyCreateSocialCommunity(ctx context.Context,
	req *pb.ApplyCreateSocialCommunityReq) (*pb.ApplyCreateSocialCommunityResp, protocol.ServerError) {
	resp, err := c.typedStub().ApplyCreateSocialCommunity(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetUserRecentSocialCommunityOrder(ctx context.Context,
	req *pb.GetUserRecentSocialCommunityOrderReq) (*pb.GetUserRecentSocialCommunityOrderResp,
	protocol.ServerError) {
	resp, err := c.typedStub().GetUserRecentSocialCommunityOrder(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListUserKernelMembers(ctx context.Context,
	req *pb.ListUserKernelMembersReq) (*pb.ListUserKernelMembersResp, protocol.ServerError) {
	resp, err := c.typedStub().ListUserKernelMembers(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListUserFansMembers(ctx context.Context,
	req *pb.ListUserFansMembersReq) (*pb.ListUserFansMembersResp, protocol.ServerError) {
	resp, err := c.typedStub().ListUserFansMembers(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) JoinSocialCommunityKernel(ctx context.Context,
	req *pb.JoinSocialCommunityKernelReq) (*pb.JoinSocialCommunityKernelResp, protocol.ServerError) {
	resp, err := c.typedStub().JoinSocialCommunityKernel(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityFloat(ctx context.Context, in *pb.GetSocialCommunityFloatRequest) (*pb.GetSocialCommunityFloatResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityFloat(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityBase(ctx context.Context, in *pb.GetSocialCommunityBaseReq) (*pb.GetSocialCommunityBaseResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityBase(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGroupMemberLimit(ctx context.Context,
	in *pb.GetGroupMemberLimitReq) (*pb.GetGroupMemberLimitResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGroupMemberLimit(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityKernelMembers(ctx context.Context,
	socialCommunityIds []string) (*pb.GetSocialCommunityKernelMembersResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityKernelMembers(ctx, &pb.GetSocialCommunityKernelMembersReq{SocialCommunityIds: socialCommunityIds})
	return resp, protocol.ToServerError(err)
}

func (c *Client) MuseSocialPreviewGroupMessage(ctx context.Context, groupId, uid uint32) (*pb.MuseSocialPreviewGroupMessageResponse, protocol.ServerError) {
	resp, err := c.typedStub().MuseSocialPreviewGroupMessage(ctx, &pb.MuseSocialPreviewGroupMessageRequest{GroupId: groupId, Uid: uid})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityMemberList(ctx context.Context, SocialCommunityId, offsetId string, count uint32) (*pb.GetSocialCommunityMemberListResp, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityMemberList(ctx, &pb.GetSocialCommunityMemberListReq{SocialCommunityId: SocialCommunityId, OffsetId: offsetId, Count: count})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SetMuseSocialAnnounceInterest(ctx context.Context, announceId string, uid, interestType uint32) (*pb.SetMuseSocialAnnounceInterestResponse, protocol.ServerError) {
	resp, err := c.typedStub().SetMuseSocialAnnounceInterest(ctx, &pb.SetMuseSocialAnnounceInterestRequest{MuseAnnounceId: announceId, Uid: uid, InterestType: interestType})
	return resp, protocol.ToServerError(err)
}

func (c *Client) RemoveMuseSocialAnnounce(ctx context.Context, announceId string) (*pb.RemoveMuseSocialAnnounceResponse, protocol.ServerError) {
	resp, err := c.typedStub().RemoveMuseSocialAnnounce(ctx, &pb.RemoveMuseSocialAnnounceRequest{MuseAnnounceId: announceId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListMuseSocialAnnounceInterestUsers(ctx context.Context, announceId, offsetId string, limit uint32) (*pb.ListMuseSocialAnnounceInterestUsersResponse, protocol.ServerError) {
	resp, err := c.typedStub().ListMuseSocialAnnounceInterestUsers(ctx, &pb.ListMuseSocialAnnounceInterestUsersRequest{MuseAnnounceId: announceId, Limit: limit, OffsetId: offsetId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchMuseSocialAnnounceByAnnounceIds(ctx context.Context, announceId []string) (*pb.BatchMuseSocialAnnounceByAnnounceIdsResponse, protocol.ServerError) {
	resp, err := c.typedStub().BatchMuseSocialAnnounceByAnnounceIds(ctx, &pb.BatchMuseSocialAnnounceByAnnounceIdsRequest{AnnounceIds: announceId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpsertMuseSocialAnnounce(ctx context.Context, request *pb.UpsertMuseSocialAnnounceRequest) (*pb.UpsertMuseSocialAnnounceResponse, protocol.ServerError) {
	resp, err := c.typedStub().UpsertMuseSocialAnnounce(ctx, request)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListAnnounceDestinations(ctx context.Context, request *pb.ListAnnounceDestinationsRequest) (*pb.ListAnnounceDestinationsResponse, protocol.ServerError) {
	resp, err := c.typedStub().ListAnnounceDestinations(ctx, request)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListMuseSocialAnnounces(ctx context.Context, request *pb.ListMuseSocialAnnouncesRequest) (*pb.ListMuseSocialAnnouncesResponse, protocol.ServerError) {
	resp, err := c.typedStub().ListMuseSocialAnnounces(ctx, request)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityAnnounceNewsCountMap(ctx context.Context, request *pb.GetSocialCommunityAnnounceNewsCountMapRequest) (*pb.GetSocialCommunityAnnounceNewsCountMapResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityAnnounceNewsCountMap(ctx, request)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ValidateUserHasCreateAnnouncePermissions(ctx context.Context, socialCommunityId string) (*pb.ValidateUserHasCreateAnnouncePermissionsResponse, protocol.ServerError) {
	resp, err := c.typedStub().ValidateUserHasCreateAnnouncePermissions(ctx, &pb.ValidateUserHasCreateAnnouncePermissionsRequest{SocialCommunityId: socialCommunityId})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpsertBrands(ctx context.Context, brands []*pb.Brand) (*pb.UpsertBrandsResp, protocol.ServerError) {
	resp, err := c.typedStub().UpsertBrands(ctx, &pb.UpsertBrandsReq{Brands: brands})
	return resp, protocol.ToServerError(err)
}

func (c *Client) UpsertApplyJoinCommunityWorkOrder(ctx context.Context, uid, status uint32, socialCommunityId,
	id, reason string) (*pb.UpsertApplyJoinCommunityWorkOrderResponse, protocol.ServerError) {
	resp, err := c.typedStub().UpsertApplyJoinCommunityWorkOrder(ctx, &pb.UpsertApplyJoinCommunityWorkOrderRequest{Id: id,
		SocialCommunityId: socialCommunityId, Uid: uid, Reason: reason, Status: status})
	return resp, protocol.ToServerError(err)
}

func (c *Client) SearchApplyJoinCommunityWorkOrder(ctx context.Context, uid, limit uint32, status []uint32, socialCommunityId,
	id, offsetId string) (*pb.SearchApplyJoinCommunityWorkOrderResponse, protocol.ServerError) {
	resp, err := c.typedStub().SearchApplyJoinCommunityWorkOrder(ctx, &pb.SearchApplyJoinCommunityWorkOrderRequest{Id: id,
		SocialCommunityId: socialCommunityId, Uid: uid, Status: status, OffsetId: offsetId, Limit: limit})
	return resp, protocol.ToServerError(err)
}

func (c *Client) ListApplyJoinCommunityWorkOrders(ctx context.Context,
	req *pb.ListApplyJoinCommunityWorkOrdersRequest) (*pb.ListApplyJoinCommunityWorkOrdersResponse, protocol.ServerError) {
	resp, err := c.typedStub().ListApplyJoinCommunityWorkOrders(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) SubmitApplicationToJoinCommunity(ctx context.Context,
	req *pb.SubmitApplicationToJoinCommunityRequest) (*pb.SubmitApplicationToJoinCommunityResponse, protocol.ServerError) {
	resp, err := c.typedStub().SubmitApplicationToJoinCommunity(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetSocialCommunityContentStreamNewsCount(ctx context.Context,
	req *pb.GetSocialCommunityContentStreamNewsCountRequest) (*pb.GetSocialCommunityContentStreamNewsCountResponse, protocol.ServerError) {
	resp, err := c.typedStub().GetSocialCommunityContentStreamNewsCount(ctx, req)
	return resp, protocol.ToServerError(err)
}
