package userBlackListService

import (
	"context"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/user-black-list"
	"google.golang.org/grpc"
)

type IClient interface {
	client.BaseClient
	GetUserBlackList(ctx context.Context, activeUid, page, count uint32) (*pb.GetUserBlackListResp, protocol.ServerError)
	AddUserBlackList(ctx context.Context, activeUid, passiveUid uint32) (*pb.AddUserBlackListResp, protocol.ServerError)
	DelUserBlackList(ctx context.Context, activeUid, passiveUid uint32) (*pb.DelUserBlackListResp, protocol.ServerError)
	CheckIsInBlackList(ctx context.Context, activeUid, passiveUid uint32) (*pb.CheckIsInBlackListResp, protocol.ServerError)
	IsInBlackList(ctx context.Context, activeUid, passiveUid uint32) (bool, protocol.ServerError)
}

func NewIClient(dopts ...grpc.DialOption) IClient {
	cli, _ := NewClient(dopts...)
	return cli
}
