package user_decoration

import (
	"context"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"

	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/user-decoration"

	"google.golang.org/grpc"
)

const (
	serviceName = "user-decoration"
)

// Client -
type Client struct {
	client.BaseClient
}

// newClient -
func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewUserDecorationClient(cc)
			},
			dopts...,
		),
	}, nil
}

// NewTracedClient -
func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(
		grpcClient.UnaryClientInterceptor,
		traceGRPC.TracedUnaryClientInterceptor(
			tracing.UsingTracer(t), tracing.LogPayloads(true),
		),
	)
	dopts = append(dopts, grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

// NewClient -
func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) typedStub() pb.UserDecorationClient {
	return c.Stub().(pb.UserDecorationClient)
}

// Upsert 用于为用户添加或更新一个装饰品。
func (c *Client) Upsert(ctx context.Context, uid uint32, typ pb.Type, decId string, version string,
	timeOverlayType pb.TimeOverlayType, duration int64, orderId string) protocol.ServerError {
	in := &pb.UpsertReq{
		Uid: uid,
		Dec: &pb.Decoration{
			Typ:     typ,
			Id:      decId,
			Version: version,
		},
		Typ:     timeOverlayType,
		Dur:     duration,
		OrderId: orderId,
	}

	_, err := c.typedStub().Upsert(ctx, in)

	return protocol.ToServerError(err)
}

// UpsertDec 用于为用户添加或更新一个装饰品。
func (c *Client) UpsertDec(ctx context.Context, req *pb.UpsertReq) protocol.ServerError {

	_, err := c.typedStub().Upsert(ctx, req)

	return protocol.ToServerError(err)
}

// UserDecorations 用于返回用户所拥有的某类装饰品。
func (c *Client) UserDecorations(ctx context.Context, uid uint32,
	typ pb.Type) ([]*pb.DecInfo, protocol.ServerError) {

	in := &pb.UserDecorationsReq{
		Uid: uid,
		Typ: typ,
	}

	resp, err := c.typedStub().UserDecorations(ctx, in)

	return resp.GetDecInfos(), protocol.ToServerError(err)
}

// Current 用于获取用户当前佩戴的装饰品。
func (c *Client) Current(ctx context.Context, uid uint32, typ pb.Type) (*pb.CurrentResp, protocol.ServerError) {

	in := &pb.CurrentReq{
		Uid: uid,
		Typ: typ,
	}

	resp, err := c.typedStub().Current(ctx, in)

	return resp, protocol.ToServerError(err)
}

// Adorn 用于用户佩戴装饰品
func (c *Client) Adorn(ctx context.Context, uid uint32, typ pb.Type, id string,
	version string, fusionId string) protocol.ServerError {

	in := &pb.AdornReq{
		Uid: uid,
		Dec: &pb.Decoration{
			Typ:        typ,
			Id:         id,
			Version:    version,
			FusionTtid: fusionId,
		},
	}
	_, err := c.typedStub().Adorn(ctx, in)

	return protocol.ToServerError(err)
}

// Remove 用于用户卸下装饰品
func (c *Client) Remove(ctx context.Context, uid uint32, typ pb.Type) protocol.ServerError {

	in := &pb.RemoveReq{
		Uid: uid,
		Typ: typ,
	}

	_, err := c.typedStub().Remove(ctx, in)

	return protocol.ToServerError(err)
}

type ctxKey string

const CtxKeyGetAllDecorations ctxKey = "get_all_decorations"

// 查询配置信息
func (c *Client) Decorations(ctx context.Context, typ pb.Type) ([]*pb.DecorationInfo, protocol.ServerError) {

	in := &pb.DecorationsReq{
		Typ: typ,
	}

	if ctx.Value(CtxKeyGetAllDecorations) != nil {
		in.All = true
	}

	resp, err := c.typedStub().Decorations(ctx, in)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp.DecorationInfos, protocol.ToServerError(err)
}

func (c *Client) InsertDecoration(ctx context.Context, req *pb.InsertDecorationReq) (*pb.InsertDecorationResp, protocol.ServerError) {

	resp, err := c.typedStub().InsertDecoration(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) DelDecoration(ctx context.Context, req *pb.DelDecorationReq) (*pb.DelDecorationResp, protocol.ServerError) {

	resp, err := c.typedStub().DelDecoration(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchUpsertDecoration(ctx context.Context, req *pb.BatchUpsertDecorationReq) (*pb.BatchUpsertDecorationResp, protocol.ServerError) {

	resp, err := c.typedStub().BatchUpsertDecoration(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, protocol.ToServerError(err)
}

func (c *Client) GetDecorationById(ctx context.Context, req *pb.GetDecorationByIdReq) (*pb.GetDecorationByIdResp, protocol.ServerError) {

	resp, err := c.typedStub().GetDecorationById(ctx, req)
	if err != nil {
		return nil, protocol.ToServerError(err)
	}

	return resp, protocol.ToServerError(err)
}
