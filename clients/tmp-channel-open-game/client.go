package tmp_channel_open_game

import (
	"context"

	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	pb "golang.52tt.com/protocol/services/tmp-channel-open-game"
	"google.golang.org/grpc"
)

const (
	serviceName = "tmp-channel-open-game"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewTmpChannelOpenGameClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.TmpChannelOpenGameClient { return c.Stub().(pb.TmpChannelOpenGameClient) }

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) AllocChannel(ctx context.Context, in *pb.AllocChannelReq) (*pb.AllocChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().AllocChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) ReleaseChannel(ctx context.Context, in *pb.ReleaseChannelReq) (*pb.ReleaseChannelResp, protocol.ServerError) {
	resp, err := c.typedStub().ReleaseChannel(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) AllocJoystick(ctx context.Context, in *pb.AllocJoystickReq) (*pb.AllocJoystickResp, protocol.ServerError) {
	resp, err := c.typedStub().AllocJoystick(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) RandomAllocJoystick(ctx context.Context, in *pb.RandomAllocJoystickReq) (*pb.RandomAllocJoystickResp, protocol.ServerError) {
	resp, err := c.typedStub().RandomAllocJoystick(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) QueryJoystick(ctx context.Context, in *pb.QueryJoystickReq) (*pb.QueryJoystickResp, protocol.ServerError) {
	resp, err := c.typedStub().QueryJoystick(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetChannelInfo(ctx context.Context, in *pb.GetChannelInfoReq) (*pb.GetChannelInfoResp, protocol.ServerError) {
	resp, err := c.typedStub().GetChannelInfo(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) JoinBegin(ctx context.Context, in *pb.JoinBeginReq) (*pb.JoinBeginResp, protocol.ServerError) {
	resp, err := c.typedStub().JoinBegin(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) JoinEnd(ctx context.Context, in *pb.JoinEndReq) (*pb.JoinEndResp, protocol.ServerError) {
	resp, err := c.typedStub().JoinEnd(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) QueryTick(ctx context.Context, in *pb.QueryTickReq) (*pb.QueryTickResp, protocol.ServerError) {
	resp, err := c.typedStub().QueryTick(ctx, in)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchJoinBegin(ctx context.Context, in *pb.BatchJoinBeginReq) (*pb.BatchJoinBeginResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchJoinBegin(ctx, in)
	return resp, protocol.ToServerError(err)
}




