// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/clients/apicenter/apiserver (interfaces: IClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	"golang.52tt.com/pkg/client"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	protocol "golang.52tt.com/pkg/protocol"
	apicenter "golang.52tt.com/protocol/services/apicenter/apiserver"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

func (m *MockIClient) CC() client.Conn {
    //TODO implement me
    panic("implement me")
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BanUser mocks base method.
func (m *MockIClient) BanUser(arg0 context.Context, arg1 uint32, arg2 *apicenter.BanUserReq) (*apicenter.BanUserResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BanUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(*apicenter.BanUserResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// BanUser indicates an expected call of BanUser.
func (mr *MockIClientMockRecorder) BanUser(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanUser", reflect.TypeOf((*MockIClient)(nil).BanUser), arg0, arg1, arg2)
}

// ClearSecurityQuestion mocks base method.
func (m *MockIClient) ClearSecurityQuestion(arg0 context.Context, arg1, arg2 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearSecurityQuestion", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ClearSecurityQuestion indicates an expected call of ClearSecurityQuestion.
func (mr *MockIClientMockRecorder) ClearSecurityQuestion(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearSecurityQuestion", reflect.TypeOf((*MockIClient)(nil).ClearSecurityQuestion), arg0, arg1, arg2)
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DetachThirdpart mocks base method.
func (m *MockIClient) DetachThirdpart(arg0 context.Context, arg1, arg2 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DetachThirdpart", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// DetachThirdpart indicates an expected call of DetachThirdpart.
func (mr *MockIClientMockRecorder) DetachThirdpart(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DetachThirdpart", reflect.TypeOf((*MockIClient)(nil).DetachThirdpart), arg0, arg1, arg2)
}

// FollowOrUnFollowYuYinFuWuHao mocks base method.
func (m *MockIClient) FollowOrUnFollowYuYinFuWuHao(arg0 context.Context, arg1 uint32, arg2 bool) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FollowOrUnFollowYuYinFuWuHao", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// FollowOrUnFollowYuYinFuWuHao indicates an expected call of FollowOrUnFollowYuYinFuWuHao.
func (mr *MockIClientMockRecorder) FollowOrUnFollowYuYinFuWuHao(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FollowOrUnFollowYuYinFuWuHao", reflect.TypeOf((*MockIClient)(nil).FollowOrUnFollowYuYinFuWuHao), arg0, arg1, arg2)
}

// GuildInfoUpdateSync mocks base method.
func (m *MockIClient) GuildInfoUpdateSync(arg0 context.Context, arg1 uint32, arg2 *apicenter.GuildInfoUpdateSyncReq) (*apicenter.GuildInfoUpdateSyncResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GuildInfoUpdateSync", arg0, arg1, arg2)
	ret0, _ := ret[0].(*apicenter.GuildInfoUpdateSyncResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GuildInfoUpdateSync indicates an expected call of GuildInfoUpdateSync.
func (mr *MockIClientMockRecorder) GuildInfoUpdateSync(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GuildInfoUpdateSync", reflect.TypeOf((*MockIClient)(nil).GuildInfoUpdateSync), arg0, arg1, arg2)
}

// KickoutChannelMember mocks base method.
func (m *MockIClient) KickoutChannelMember(arg0 context.Context, arg1 uint32, arg2 []uint32, arg3, arg4, arg5 uint32, arg6 string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KickoutChannelMember", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// KickoutChannelMember indicates an expected call of KickoutChannelMember.
func (mr *MockIClientMockRecorder) KickoutChannelMember(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KickoutChannelMember", reflect.TypeOf((*MockIClient)(nil).KickoutChannelMember), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// KickoutUser mocks base method.
func (m *MockIClient) KickoutUser(arg0 context.Context, arg1, arg2 uint32, arg3 string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KickoutUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// KickoutUser indicates an expected call of KickoutUser.
func (mr *MockIClientMockRecorder) KickoutUser(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KickoutUser", reflect.TypeOf((*MockIClient)(nil).KickoutUser), arg0, arg1, arg2, arg3)
}

// NotifyGrowInfoSync mocks base method.
func (m *MockIClient) NotifyGrowInfoSync(arg0 context.Context, arg1 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NotifyGrowInfoSync", arg0, arg1)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// NotifyGrowInfoSync indicates an expected call of NotifyGrowInfoSync.
func (mr *MockIClientMockRecorder) NotifyGrowInfoSync(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyGrowInfoSync", reflect.TypeOf((*MockIClient)(nil).NotifyGrowInfoSync), arg0, arg1)
}

// PushCommonBreakingNews mocks base method.
func (m *MockIClient) PushCommonBreakingNews(arg0 context.Context, arg1 uint32, arg2 []byte) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushCommonBreakingNews", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// PushCommonBreakingNews indicates an expected call of PushCommonBreakingNews.
func (mr *MockIClientMockRecorder) PushCommonBreakingNews(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushCommonBreakingNews", reflect.TypeOf((*MockIClient)(nil).PushCommonBreakingNews), arg0, arg1, arg2)
}

// PushMsgToChannel mocks base method.
func (m *MockIClient) PushMsgToChannel(arg0 context.Context, arg1, arg2, arg3 uint32, arg4 string, arg5 []byte) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PushMsgToChannel", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// PushMsgToChannel indicates an expected call of PushMsgToChannel.
func (mr *MockIClientMockRecorder) PushMsgToChannel(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushMsgToChannel", reflect.TypeOf((*MockIClient)(nil).PushMsgToChannel), arg0, arg1, arg2, arg3, arg4, arg5)
}

// ResetPassword mocks base method.
func (m *MockIClient) ResetPassword(arg0 context.Context, arg1, arg2 uint32, arg3 string, arg4 uint32) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetPassword", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// ResetPassword indicates an expected call of ResetPassword.
func (mr *MockIClientMockRecorder) ResetPassword(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetPassword", reflect.TypeOf((*MockIClient)(nil).ResetPassword), arg0, arg1, arg2, arg3, arg4)
}

// SendImMsg mocks base method.
func (m *MockIClient) SendImMsg(arg0 context.Context, arg1 uint32, arg2 protocol.AppID, arg3 []*apicenter.ImMsg, arg4 bool) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendImMsg", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SendImMsg indicates an expected call of SendImMsg.
func (mr *MockIClientMockRecorder) SendImMsg(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendImMsg", reflect.TypeOf((*MockIClient)(nil).SendImMsg), arg0, arg1, arg2, arg3, arg4)
}

// SendTTHelperMsg mocks base method.
func (m *MockIClient) SendTTHelperMsg(arg0 context.Context, arg1 uint32, arg2 string) protocol.ServerError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendTTHelperMsg", arg0, arg1, arg2)
	ret0, _ := ret[0].(protocol.ServerError)
	return ret0
}

// SendTTHelperMsg indicates an expected call of SendTTHelperMsg.
func (mr *MockIClientMockRecorder) SendTTHelperMsg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendTTHelperMsg", reflect.TypeOf((*MockIClient)(nil).SendTTHelperMsg), arg0, arg1, arg2)
}

// SendTTHelperMsgWithUrl mocks base method.
func (m *MockIClient) SendTTHelperMsgWithUrl(arg0 uint32, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendTTHelperMsgWithUrl", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendTTHelperMsgWithUrl indicates an expected call of SendTTHelperMsgWithUrl.
func (mr *MockIClientMockRecorder) SendTTHelperMsgWithUrl(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendTTHelperMsgWithUrl", reflect.TypeOf((*MockIClient)(nil).SendTTHelperMsgWithUrl), arg0, arg1, arg2, arg3)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}

// UnbindPhone mocks base method.
func (m *MockIClient) UnbindPhone(arg0 context.Context, arg1, arg2 uint32) (*apicenter.UnbindPhoneResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnbindPhone", arg0, arg1, arg2)
	ret0, _ := ret[0].(*apicenter.UnbindPhoneResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// UnbindPhone indicates an expected call of UnbindPhone.
func (mr *MockIClientMockRecorder) UnbindPhone(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnbindPhone", reflect.TypeOf((*MockIClient)(nil).UnbindPhone), arg0, arg1, arg2)
}
