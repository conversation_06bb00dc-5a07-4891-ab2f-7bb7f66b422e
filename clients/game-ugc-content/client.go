package game_ugc_content

import (
	"context"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	grpcClient "golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/game-ugc-content"
	"google.golang.org/grpc"
)

const serviceName = "game-ugc-content"

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewGameUgcContentClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.GameUgcContentClient {
	return c.Stub().(pb.GameUgcContentClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) GetConfigTabInfoById(ctx context.Context, req *pb.GetConfigTabInfoByIdReq) (*pb.GetConfigTabInfoByIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetConfigTabInfoById(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGameConfigTabDetailByTabId(ctx context.Context, req *pb.GetGameConfigTabDetailByTabIdReq) (*pb.GetGameConfigTabDetailByTabIdResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGameConfigTabDetailByTabId(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetActiveBanUserPostConfigWithCache(ctx context.Context, req *pb.GetActiveBanUserPostConfigWithCacheReq) (*pb.GetActiveBanUserPostConfigWithCacheResp, protocol.ServerError) {
	resp, err := c.typedStub().GetActiveBanUserPostConfigWithCache(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetGamePalTabs(ctx context.Context, req *pb.GetGamePalTabsReq) (*pb.GetGamePalTabsResp, protocol.ServerError) {
	resp, err := c.typedStub().GetGamePalTabs(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) InsertDefaultConfigTabs(ctx context.Context, req *pb.InsertDefaultConfigTabsReq) (*pb.InsertDefaultConfigTabsResp, protocol.ServerError) {
	resp, err := c.typedStub().InsertDefaultConfigTabs(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) DelGameConfigTab(ctx context.Context, req *pb.DelGameConfigTabReq) (*pb.DelGameConfigTabResp, protocol.ServerError) {
	resp, err := c.typedStub().DelGameConfigTab(ctx, req)
	return resp, protocol.ToServerError(err)
}

func (c *Client) BatchGetGameFeedPbs(ctx context.Context, req *pb.BatchGetGameFeedPbsReq) (*pb.BatchGetGameFeedPbsResp, protocol.ServerError) {
	resp, err := c.typedStub().BatchGetGameFeedPbs(ctx, req)
	return resp, protocol.ToServerError(err)
}
