package dark_gift_bonus

import (
	"context"
	"strconv"

	"github.com/grpc-ecosystem/go-grpc-middleware"
	"golang.52tt.com/pkg/client"
	"golang.52tt.com/pkg/foundation/grpc/client"
	"golang.52tt.com/pkg/protocol"
	"golang.52tt.com/pkg/tracing"
	traceGRPC "golang.52tt.com/pkg/tracing/grpc"
	pb "golang.52tt.com/protocol/services/dark-gift-bonus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

const (
	serviceName = "dark-gift-bonus"
)

type Client struct {
	client.BaseClient
}

func newClient(dopts ...grpc.DialOption) (*Client, error) {
	return &Client{
		BaseClient: client.NewInsecureGRPCClient(
			serviceName,
			func(cc *grpc.ClientConn) interface{} {
				return pb.NewDarkGiftBonusClient(cc)
			}, dopts...,
		),
	}, nil
}

func (c *Client) typedStub() pb.DarkGiftBonusClient {
	return c.Stub().(pb.DarkGiftBonusClient)
}

func NewTracedClient(t tracing.Tracer, dopts ...grpc.DialOption) (*Client, error) {
	unaryInt := grpc_middleware.ChainUnaryClient(grpcClient.UnaryClientInterceptor, traceGRPC.TracedUnaryClientInterceptor(
		tracing.UsingTracer(t), tracing.LogPayloads(true),
	))
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(unaryInt))
	return newClient(dopts...)
}

func NewClient(dopts ...grpc.DialOption) (*Client, error) {
	dopts = append(dopts, grpc.WithInsecure(), grpc.WithUnaryInterceptor(grpcClient.UnaryClientInterceptor))
	return newClient(dopts...)
}

func (c *Client) AddBuffConf(ctx context.Context, opUid uint32, conf *pb.BuffConf) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	_, err := c.typedStub().AddBuffConf(ctx, &pb.AddBuffConfReq{Conf: conf})
	return protocol.ToServerError(err)
}

func (c *Client) UpdateBuffConf(ctx context.Context, opUid uint32, conf *pb.BuffConf) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	_, err := c.typedStub().UpdateBuffConf(ctx, &pb.UpdateBuffConfReq{Conf: conf})
	return protocol.ToServerError(err)
}

func (c *Client) DelBuffConf(ctx context.Context, opUid, confId, businessSource uint32) protocol.ServerError {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	_, err := c.typedStub().DelBuffConf(ctx, &pb.DelBuffConfReq{ConfId: confId, Source: businessSource})
	return protocol.ToServerError(err)
}

func (c *Client) GetBuffConf(ctx context.Context, opUid, businessSource uint32) (*pb.GetBuffConfResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetBuffConf(ctx, &pb.GetBuffConfReq{Source: businessSource})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetDarkGiftBonusRecord(ctx context.Context, opUid, businessSource, offset, limit uint32) (*pb.GetDarkGiftBonusRecordResp, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetDarkGiftBonusRecord(ctx, &pb.GetDarkGiftBonusRecordReq{
		BonusSource: businessSource,
		Uid:         opUid,
		Offset:      offset,
		Limit:       limit,
	})
	return resp, protocol.ToServerError(err)
}

func (c *Client) GetDarkGiftBonusSummary(ctx context.Context, opUid, businessSource, begin, end uint32) (int64, protocol.ServerError) {
	ctx = metadata.NewOutgoingContext(ctx, metadata.Pairs("req_uid", strconv.Itoa(int(opUid))))
	resp, err := c.typedStub().GetDarkGiftBonusSummary(ctx, &pb.GetDarkGiftBonusSummaryReq{BonusSource: businessSource, BeginTs: begin, EndTs: end})
	return resp.GetTotalWorth(), protocol.ToServerError(err)
}
