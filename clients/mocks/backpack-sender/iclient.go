// Code generated by MockGen. DO NOT EDIT.
// Source: D:\go-tt\griffin\clients\backpack-sender\iclient.go

// Package backpacksender is a generated GoMock package.
package backpacksender

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	backpacksender "golang.52tt.com/protocol/services/backpacksender"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// AddBusiness mocks base method.
func (m *MockIClient) AddBusiness(ctx context.Context, req *backpacksender.AddBusinessReq) (*backpacksender.AddBusinessResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBusiness", ctx, req)
	ret0, _ := ret[0].(*backpacksender.AddBusinessResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddBusiness indicates an expected call of AddBusiness.
func (mr *MockIClientMockRecorder) AddBusiness(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBusiness", reflect.TypeOf((*MockIClient)(nil).AddBusiness), ctx, req)
}

// AddBusinessRiskControlConf mocks base method.
func (m *MockIClient) AddBusinessRiskControlConf(ctx context.Context, req *backpacksender.AddBusinessRiskControlConfReq) (*backpacksender.AddBusinessRiskControlConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddBusinessRiskControlConf", ctx, req)
	ret0, _ := ret[0].(*backpacksender.AddBusinessRiskControlConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// AddBusinessRiskControlConf indicates an expected call of AddBusinessRiskControlConf.
func (mr *MockIClientMockRecorder) AddBusinessRiskControlConf(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddBusinessRiskControlConf", reflect.TypeOf((*MockIClient)(nil).AddBusinessRiskControlConf), ctx, req)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// DeductUserBackpackWithRiskControl mocks base method.
func (m *MockIClient) DeductUserBackpackWithRiskControl(ctx context.Context, req *backpacksender.DeductUserBackpackWithRiskControlReq) (*backpacksender.DeductUserBackpackWithRiskControlResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeductUserBackpackWithRiskControl", ctx, req)
	ret0, _ := ret[0].(*backpacksender.DeductUserBackpackWithRiskControlResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// DeductUserBackpackWithRiskControl indicates an expected call of DeductUserBackpackWithRiskControl.
func (mr *MockIClientMockRecorder) DeductUserBackpackWithRiskControl(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeductUserBackpackWithRiskControl", reflect.TypeOf((*MockIClient)(nil).DeductUserBackpackWithRiskControl), ctx, req)
}

// GetAllBusiness mocks base method.
func (m *MockIClient) GetAllBusiness(ctx context.Context, req *backpacksender.GetAllBusinessReq) (*backpacksender.GetAllBusinessResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllBusiness", ctx, req)
	ret0, _ := ret[0].(*backpacksender.GetAllBusinessResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetAllBusiness indicates an expected call of GetAllBusiness.
func (mr *MockIClientMockRecorder) GetAllBusiness(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllBusiness", reflect.TypeOf((*MockIClient)(nil).GetAllBusiness), ctx, req)
}

// GetBusinessRiskControlConf mocks base method.
func (m *MockIClient) GetBusinessRiskControlConf(ctx context.Context, req *backpacksender.GetBusinessRiskControlConfReq) (*backpacksender.GetBusinessRiskControlConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBusinessRiskControlConf", ctx, req)
	ret0, _ := ret[0].(*backpacksender.GetBusinessRiskControlConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetBusinessRiskControlConf indicates an expected call of GetBusinessRiskControlConf.
func (mr *MockIClientMockRecorder) GetBusinessRiskControlConf(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessRiskControlConf", reflect.TypeOf((*MockIClient)(nil).GetBusinessRiskControlConf), ctx, req)
}

// ModBusinessRiskControlConf mocks base method.
func (m *MockIClient) ModBusinessRiskControlConf(ctx context.Context, req *backpacksender.ModBusinessRiskControlConfReq) (*backpacksender.ModBusinessRiskControlConfResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModBusinessRiskControlConf", ctx, req)
	ret0, _ := ret[0].(*backpacksender.ModBusinessRiskControlConfResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ModBusinessRiskControlConf indicates an expected call of ModBusinessRiskControlConf.
func (mr *MockIClientMockRecorder) ModBusinessRiskControlConf(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModBusinessRiskControlConf", reflect.TypeOf((*MockIClient)(nil).ModBusinessRiskControlConf), ctx, req)
}

// PreCheckBusinessRiskControl mocks base method.
func (m *MockIClient) PreCheckBusinessRiskControl(ctx context.Context, req *backpacksender.PreCheckBussinessRiskControlReq) (*backpacksender.PreCheckBussinessRiskControlResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PreCheckBusinessRiskControl", ctx, req)
	ret0, _ := ret[0].(*backpacksender.PreCheckBussinessRiskControlResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// PreCheckBusinessRiskControl indicates an expected call of PreCheckBusinessRiskControl.
func (mr *MockIClientMockRecorder) PreCheckBusinessRiskControl(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PreCheckBusinessRiskControl", reflect.TypeOf((*MockIClient)(nil).PreCheckBusinessRiskControl), ctx, req)
}

// SendBackpackWithRiskControl mocks base method.
func (m *MockIClient) SendBackpackWithRiskControl(ctx context.Context, req *backpacksender.SendBackpackWithRiskControlReq) (*backpacksender.SendBackpackWithRiskControlResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendBackpackWithRiskControl", ctx, req)
	ret0, _ := ret[0].(*backpacksender.SendBackpackWithRiskControlResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SendBackpackWithRiskControl indicates an expected call of SendBackpackWithRiskControl.
func (mr *MockIClientMockRecorder) SendBackpackWithRiskControl(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBackpackWithRiskControl", reflect.TypeOf((*MockIClient)(nil).SendBackpackWithRiskControl), ctx, req)
}

// SendOneBackpackWithRiskControl mocks base method.
func (m *MockIClient) SendOneBackpackWithRiskControl(ctx context.Context, req *backpacksender.SendOneBackpackWithRiskControlReq) (*backpacksender.SendOneBackpackWithRiskControlResp, protocol.ServerError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendOneBackpackWithRiskControl", ctx, req)
	ret0, _ := ret[0].(*backpacksender.SendOneBackpackWithRiskControlResp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// SendOneBackpackWithRiskControl indicates an expected call of SendOneBackpackWithRiskControl.
func (mr *MockIClientMockRecorder) SendOneBackpackWithRiskControl(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOneBackpackWithRiskControl", reflect.TypeOf((*MockIClient)(nil).SendOneBackpackWithRiskControl), ctx, req)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
