// Code generated by MockGen. DO NOT EDIT.
// Source: /Users/<USER>/project/quicksilver/clients/channelapi/iclient.go

// Package channelapi is a generated GoMock package.
package channelapi

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	channelapi "golang.52tt.com/protocol/services/channelApiSvr"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// ModifyName mocks base method.
func (m *MockIClient) ModifyName(ctx context.Context, uin, channelId, appId, marketId uint32, name string) (*channelapi.ModifyNameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyName", ctx, uin, channelId, appId, marketId, name)
	ret0, _ := ret[0].(*channelapi.ModifyNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyName indicates an expected call of ModifyName.
func (mr *MockIClientMockRecorder) ModifyName(ctx, uin, channelId, appId, marketId, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyName", reflect.TypeOf((*MockIClient)(nil).ModifyName), ctx, uin, channelId, appId, marketId, name)
}

// ModifyNameWithAntiCheck mocks base method.
func (m *MockIClient) ModifyNameWithAntiCheck(ctx context.Context, uin, channelId, appId, marketId uint32, name string) (*channelapi.ModifyNameResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyNameWithAntiCheck", ctx, uin, channelId, appId, marketId, name)
	ret0, _ := ret[0].(*channelapi.ModifyNameResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ModifyNameWithAntiCheck indicates an expected call of ModifyNameWithAntiCheck.
func (mr *MockIClientMockRecorder) ModifyNameWithAntiCheck(ctx, uin, channelId, appId, marketId, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyNameWithAntiCheck", reflect.TypeOf((*MockIClient)(nil).ModifyNameWithAntiCheck), ctx, uin, channelId, appId, marketId, name)
}

// SetMicMode mocks base method.
func (m *MockIClient) SetMicMode(ctx context.Context, uin, channelId, micMod, appId, marketId uint32) (*channelapi.SetMicModeResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMicMode", ctx, uin, channelId, micMod, appId, marketId)
	ret0, _ := ret[0].(*channelapi.SetMicModeResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetMicMode indicates an expected call of SetMicMode.
func (mr *MockIClientMockRecorder) SetMicMode(ctx, uin, channelId, micMod, appId, marketId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMicMode", reflect.TypeOf((*MockIClient)(nil).SetMicMode), ctx, uin, channelId, micMod, appId, marketId)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
