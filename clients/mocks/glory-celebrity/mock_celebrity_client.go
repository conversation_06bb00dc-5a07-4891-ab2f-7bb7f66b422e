// Code generated by MockGen. DO NOT EDIT.
// Source: ../../glory-celebrity/iclient.go

// Package glory_celebrity is a generated GoMock package.
package glory_celebrity

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	protocol "golang.52tt.com/pkg/protocol"
	glory_celebrity "golang.52tt.com/protocol/services/glory-celebrity"
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetCelebrityLastWeekRank mocks base method.
func (m *MockIClient) GetCelebrityLastWeekRank(ctx context.Context, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityWeekRankRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCelebrityLastWeekRank", varargs...)
	ret0, _ := ret[0].(*glory_celebrity.GetCelebrityWeekRankRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCelebrityLastWeekRank indicates an expected call of GetCelebrityLastWeekRank.
func (mr *MockIClientMockRecorder) GetCelebrityLastWeekRank(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCelebrityLastWeekRank", reflect.TypeOf((*MockIClient)(nil).GetCelebrityLastWeekRank), varargs...)
}

// GetCelebrityPalaceInfoList mocks base method.
func (m *MockIClient) GetCelebrityPalaceInfoList(ctx context.Context, isCurPeriods bool, lastId, limit uint32, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityPalaceInfoListRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, isCurPeriods, lastId, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCelebrityPalaceInfoList", varargs...)
	ret0, _ := ret[0].(*glory_celebrity.GetCelebrityPalaceInfoListRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCelebrityPalaceInfoList indicates an expected call of GetCelebrityPalaceInfoList.
func (mr *MockIClientMockRecorder) GetCelebrityPalaceInfoList(ctx, isCurPeriods, lastId, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, isCurPeriods, lastId, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCelebrityPalaceInfoList", reflect.TypeOf((*MockIClient)(nil).GetCelebrityPalaceInfoList), varargs...)
}

// GetCelebrityPalaceTopInfo mocks base method.
func (m *MockIClient) GetCelebrityPalaceTopInfo(ctx context.Context, isCurPeriods bool, uid uint32, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityPalaceTopInfoRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, isCurPeriods, uid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCelebrityPalaceTopInfo", varargs...)
	ret0, _ := ret[0].(*glory_celebrity.GetCelebrityPalaceTopInfoRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCelebrityPalaceTopInfo indicates an expected call of GetCelebrityPalaceTopInfo.
func (mr *MockIClientMockRecorder) GetCelebrityPalaceTopInfo(ctx, isCurPeriods, uid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, isCurPeriods, uid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCelebrityPalaceTopInfo", reflect.TypeOf((*MockIClient)(nil).GetCelebrityPalaceTopInfo), varargs...)
}

// GetCelebrityWeekRank mocks base method.
func (m *MockIClient) GetCelebrityWeekRank(ctx context.Context, uid, t uint32, opts ...grpc.CallOption) (*glory_celebrity.GetCelebrityWeekRankRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, uid, t}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCelebrityWeekRank", varargs...)
	ret0, _ := ret[0].(*glory_celebrity.GetCelebrityWeekRankRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// GetCelebrityWeekRank indicates an expected call of GetCelebrityWeekRank.
func (mr *MockIClientMockRecorder) GetCelebrityWeekRank(ctx, uid, t interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, uid, t}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCelebrityWeekRank", reflect.TypeOf((*MockIClient)(nil).GetCelebrityWeekRank), varargs...)
}

// ReplayCelebrityPalace mocks base method.
func (m *MockIClient) ReplayCelebrityPalace(ctx context.Context, uid uint32, isCurPeriods bool, celebrityId uint32, opts ...grpc.CallOption) (*glory_celebrity.ReplayCelebrityPalaceRsp, protocol.ServerError) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, uid, isCurPeriods, celebrityId}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReplayCelebrityPalace", varargs...)
	ret0, _ := ret[0].(*glory_celebrity.ReplayCelebrityPalaceRsp)
	ret1, _ := ret[1].(protocol.ServerError)
	return ret0, ret1
}

// ReplayCelebrityPalace indicates an expected call of ReplayCelebrityPalace.
func (mr *MockIClientMockRecorder) ReplayCelebrityPalace(ctx, uid, isCurPeriods, celebrityId interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, uid, isCurPeriods, celebrityId}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplayCelebrityPalace", reflect.TypeOf((*MockIClient)(nil).ReplayCelebrityPalace), varargs...)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
