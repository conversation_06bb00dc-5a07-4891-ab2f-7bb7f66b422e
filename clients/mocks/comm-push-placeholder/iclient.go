// Code generated by MockGen. DO NOT EDIT.
// Source: .\iclient.go

// Package mock_comm_push_placeholder is a generated GoMock package.
package comm_push_placeholder

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	client "golang.52tt.com/pkg/client"
	comm_push_placeholder "golang.52tt.com/protocol/services/comm-push-placeholder"
)

// MockIClient is a mock of IClient interface.
type MockIClient struct {
	ctrl     *gomock.Controller
	recorder *MockIClientMockRecorder
}

// MockIClientMockRecorder is the mock recorder for MockIClient.
type MockIClientMockRecorder struct {
	mock *MockIClient
}

// NewMockIClient creates a new mock instance.
func NewMockIClient(ctrl *gomock.Controller) *MockIClient {
	mock := &MockIClient{ctrl: ctrl}
	mock.recorder = &MockIClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIClient) EXPECT() *MockIClientMockRecorder {
	return m.recorder
}

// BatchGetUserPushCopyWriting mocks base method.
func (m *MockIClient) BatchGetUserPushCopyWriting(ctx context.Context, in *comm_push_placeholder.BatchGetUserPushCopyWritingReq) (*comm_push_placeholder.BatchGetUserPushCopyWritingResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetUserPushCopyWriting", ctx, in)
	ret0, _ := ret[0].(*comm_push_placeholder.BatchGetUserPushCopyWritingResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetUserPushCopyWriting indicates an expected call of BatchGetUserPushCopyWriting.
func (mr *MockIClientMockRecorder) BatchGetUserPushCopyWriting(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetUserPushCopyWriting", reflect.TypeOf((*MockIClient)(nil).BatchGetUserPushCopyWriting), ctx, in)
}

// CC mocks base method.
func (m *MockIClient) CC() client.Conn {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CC")
	ret0, _ := ret[0].(client.Conn)
	return ret0
}

// CC indicates an expected call of CC.
func (mr *MockIClientMockRecorder) CC() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CC", reflect.TypeOf((*MockIClient)(nil).CC))
}

// Close mocks base method.
func (m *MockIClient) Close() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Close")
}

// Close indicates an expected call of Close.
func (mr *MockIClientMockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockIClient)(nil).Close))
}

// GetCopyWritingTemplateByLibId mocks base method.
func (m *MockIClient) GetCopyWritingTemplateByLibId(ctx context.Context, libId string) (*comm_push_placeholder.GetCopyWritingTemplateByLibIdResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCopyWritingTemplateByLibId", ctx, libId)
	ret0, _ := ret[0].(*comm_push_placeholder.GetCopyWritingTemplateByLibIdResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCopyWritingTemplateByLibId indicates an expected call of GetCopyWritingTemplateByLibId.
func (mr *MockIClientMockRecorder) GetCopyWritingTemplateByLibId(ctx, libId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCopyWritingTemplateByLibId", reflect.TypeOf((*MockIClient)(nil).GetCopyWritingTemplateByLibId), ctx, libId)
}

// GetPlaceHold mocks base method.
func (m *MockIClient) GetPlaceHold(ctx context.Context, in *comm_push_placeholder.GetPlaceHoldReq) (*comm_push_placeholder.GetPlaceHoldResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlaceHold", ctx, in)
	ret0, _ := ret[0].(*comm_push_placeholder.GetPlaceHoldResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlaceHold indicates an expected call of GetPlaceHold.
func (mr *MockIClientMockRecorder) GetPlaceHold(ctx, in interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaceHold", reflect.TypeOf((*MockIClient)(nil).GetPlaceHold), ctx, in)
}

// Stub mocks base method.
func (m *MockIClient) Stub() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stub")
	ret0, _ := ret[0].(interface{})
	return ret0
}

// Stub indicates an expected call of Stub.
func (mr *MockIClientMockRecorder) Stub() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stub", reflect.TypeOf((*MockIClient)(nil).Stub))
}
